# 第一阶段：构建阶段
FROM golang:1.23.9 AS builder

# 安装必要的构建工具
# RUN apk add --no-cache gcc musl-dev

# 设置工作目录
WORKDIR /app

# 设置Go环境变量
ENV GO111MODULE=on
ENV GOPROXY=https://goproxy.cn

# 复制go.mod和go.sum文件，并下载依赖
# 这一步利用Docker缓存机制，如果依赖没有变化，就不会重新下载
COPY go.mod go.sum ./

# RUN export GO111MODULE=on
# RUN export GOPROXY=https://goproxy.cn

RUN go mod download

# 复制源代码
COPY . .

# 构建应用
# CGO_ENABLED=0 禁用CGO，使用纯Go实现
# -ldflags="-s -w" 减小二进制文件大小
RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-s -w" -o /app/device-service

# 第二阶段：运行阶段
FROM alpine:latest

ARG OSTZ=Asia/Shanghai

# 安装运行时依赖
# RUN apk --no-cache add ca-certificates tzdata
RUN apk --no-cache add tzdata && \
    cp /usr/share/zoneinfo/$OSTZ /etc/localtime && \
    echo "$OSTZ" > /etc/timezone 
    # && \
    # apk del tzdata

# 设置时区
ENV TZ=$OSTZ

RUN mkdir -p /app/logs
RUN mkdir -p /app/configs

# 设置工作目录
WORKDIR /app


# 从构建阶段复制二进制文件
COPY --from=builder /app/device-service /app/
COPY --from=builder /app/configs/ /app/configs

# 创建非root用户
RUN addgroup -S appgroup && adduser -S appuser -G appgroup && \
    chown -R appuser:appgroup /app

# 切换到非root用户
USER appuser

# 设置数据卷
VOLUME ["/app/data"]

# 运行应用
CMD ["/app/device-service"]
