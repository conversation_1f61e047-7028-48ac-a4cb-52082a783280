FROM harbor.3irobotix.net/base/base_jdk11:v1
COPY target/device-service.jar /usr/local/src
COPY bin/startup.sh /usr/local/src
RUN adduser -u 2000 -g 2000 -D  www && \
    mkdir -p /data/log/device-service && \
    chown -R www:www /data/log/device-service /usr/local/src  && \
    chmod +x /usr/local/src/startup.sh
EXPOSE 8304
USER www
ENV PROFILE=""
WORKDIR /usr/local/src
ENTRYPOINT /bin/sh startup.sh ${PROFILE}