FROM harbor.3irobotix.net/base/base_jdk17:v2
COPY target/device-service-encrypted.jar /usr/local/src
COPY bin/encrypted_startup.sh /usr/local/src
RUN mkdir -p /data/log/device-service-encrypted && \
    chown -R www:www /data/log/device-service-encrypted /usr/local/src  && \
    chmod +x /usr/local/src/encrypted_startup.sh
EXPOSE 8304
USER www
ENV PROFILE=""
WORKDIR /usr/local/src
ENTRYPOINT /bin/sh encrypted_startup.sh ${PROFILE}