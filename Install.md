# device-service
设备服务配置说明

## 1、配置相关

### 1.1、redis配置
```yaml
spring:
  redis:
      cluster:
        enabled: true
        nodes:
          - host1:port1
          - host2:port2
          - host3:port3
          - host4:port4
          - host5:port5
          - host6:port6
        max-redirects: 3  # 获取失败 最大重定向次数
      lettuce:
        pool:
          max-active: 1000  #连接池最大连接数（使用负值表示没有限制）
          max-idle: 10 # 连接池中的最大空闲连接
          min-idle: 5 # 连接池中的最小空闲连接
          max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
```

### 1.2、数据源配置 cockroadDB
```yaml
spring:
  datasource:
      driver-class-name: org.postgresql.Driver
      url: *************************************
      username: 用户名
      password: 密码
      hikari:
        poolName: HikariPool
        maximumPoolSize: 10
        minimumIdle: 2
        idleTimeout: 15000
        maxLifetime: 500000
        autoCommit: true
```
### 1.3、雪花算法生成主键id的配置
```yaml
snowflake:
  work_id: 1
  datacenter_id: 1
```

### 1.4、kafka配置
```yaml
spring:
  kafka:
    bootstrap-servers: ***********:9092,***********:9092,***********:9092
    producer: #生产者配置
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: 1
```

### 1.5、租户id配置
```yaml
tenant:
  #开启动态拼接sql
  enbale: true
  tablePre: t_device_
  idColumn: tenant_id
  idCamel: tenantId
```

### 1.6、LocalDateTime序列化配置
```yaml
# 接口 localdatetime 类型字段序列化转换
timeTransform:
  enable: true
```

### 1.7、emq配置
```yaml
emq:
  auth:
    jwt:
      secret: 123456
```

## 2、依赖打包

### 2.1、bravo-parent-aiot 
基础依赖


## 3、服务关系

### 3.1、device-shadow-service 设备影子服务
设备影子相关操作

### 3.2、geoip-service ip分析服务
登录或注册分析ip

### 3.3、jpush-service 极光推送服务
删除极光推送记录以及消息设置

### 3.4、log-service 日志服务
app用户登录登出日志、删除app登录登出日志

### 3.5、smart-home-service 智能家居
初始化家庭、家庭用户设备绑定关系等

### 3.6、product-service 产品服务

## 3.7、user-center 用户服务

## 4、初始化数据表结构
device-service/SQL/device-service.sql




