# device-service

设备服务

# 服务端口

8304

# 环境配置

JDK1.8、<PERSON><PERSON>(集群)、<PERSON><PERSON><PERSON>(集群)、cockroadDB

# 服务描述

设备服务，提供设备相关基本接口

# 变更记录

|  日期   | Tag  | 更新内容  | 作者  |
|  ----  | ----  | ----  | ----  |
| 2021.12.20  | v0.2.1 |1.增加数据库初始化文件device-service.sql以及install.md文件 | 黄文安 |
| 2021.12.20  | v0.2.1 |1.修改共享设备逻辑，可以多选设备分享给指定的用户<br> 2.获取用户指定的分享信息接口增加设备id作为参数 | 黄文安 |
| 2021.12.21  | v0.2.1 |1.增加内部调用接口，删除共享设备时，删除对应的分享记录 | 黄文安 |
| 2021.12.22  | v0.2.1 |1.增加获取分享历史的接口 | 黄文安 |
| 2021.12.22  | v0.2.1 |1.优化获取分享历史与获取用户分享的接口，增加产品图片的返回 | 黄文安 |
| 2021.12.23  | v0.2.1 |1.配网时，增加产品id的绑定<br> 2.回应分享时，增加产品id的绑定 | 黄文安 |
| 2021.12.27  | v0.2.1 |1.设备分享记录和家庭分享记录合并 | 黄文安 |
| 2021.12.27  | v0.2.1 |1.app最终确认设备的绑定接口增加家庭id参数 | 黄文安 |
| 2022.01.06  | v0.2.2 |1.设备信息增加defaultNickname作为设备的默认昵称，nickname作为设备绑定关系里面的昵称 <br> 2.共享设备、共享家庭、回应分享增加极光推送 <br> 3.增加修改设备昵称的接口 <br> 4.设备登录时，token增加robot_type作为token的组成部分，用于忽略多端登录的校验 | 黄文安 |
| 2022.01.11  | v0.2.2 |1.运营后台获取设备信息接口，增加优先使用参数中的租户id作为查询的条件 | 黄文安 |
| 2022.01.12  | v0.2.2 |1.从pgsql迁移到cockroadDB <br> 2.更新数据库初始化文件 | 黄文安 |
| 2022.01.27  | v0.2.3 |1.修复远程调用实体转JSON问题 | 李纪彪 |
| 2022.02.07  | v0.2.3 |1、删除分享信息时，增加异常处理 <br> 2、获取分享记录时，修复处理分享信息的问题 <br> 3、分享历史的图片属性修改为photoUrl <br> 4、增加单元测试 | 黄文安 |
| 2022.02.16  | v0.2.4 |1、优化代码逻辑，查询增加缓存，部分调用增加异步 | 黄文安 |
| 2022.02.23  | v0.2.4 |1、从小强DB迁移到TiDB | 黄文安 |