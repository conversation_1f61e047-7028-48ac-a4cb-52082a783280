-- d_device.t_device_info definition

CREATE TABLE `t_device_info` (
  `id` varchar(64) NOT NULL COMMENT 't_sn_detail的id',
  `mac` varchar(64) DEFAULT NULL COMMENT 'mac地址',
  `versions` varchar(255) DEFAULT NULL,
  `nickname` varchar(255) DEFAULT NULL COMMENT '设备昵称',
  `reset_status` varchar(255) DEFAULT NULL COMMENT '测试设备是否已重置：0已重置，1未重置',
  `online_time` timestamp NULL DEFAULT NULL COMMENT '最近在线时间',
  `offline_time` timestamp NULL DEFAULT NULL COMMENT '最近离线时间',
  `ip` varchar(255) DEFAULT NULL COMMENT '登录IP',
  `city` varchar(255) DEFAULT NULL COMMENT '最近登录的城市',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者 用户ID',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户id',
  `zone` varchar(255) DEFAULT NULL COMMENT '区域',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `product_mode_code` varchar(64) DEFAULT NULL COMMENT '产品型号代码，同原来的工程类型',
  `product_id` varchar(64) DEFAULT NULL COMMENT '产品id',
  `sn` varchar(128) DEFAULT NULL COMMENT '设备sn',
  `default_nickname` varchar(255) DEFAULT NULL COMMENT '设备默认昵称，由前缀+sn后n位组成',
  PRIMARY KEY (`id`(64)),
  UNIQUE KEY `t_device_info_UN` (`sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='设备信息表';


-- d_device.t_device_invite_share definition

CREATE TABLE `t_device_invite_share` (
  `id` varchar(64) NOT NULL COMMENT '主键id，通过雪花算法生产',
  `invite_id` varchar(64) DEFAULT NULL COMMENT '邀请人的用户id',
  `be_invite_id` varchar(64) DEFAULT NULL COMMENT '被邀请人的用户id',
  `target_id` varchar(64) DEFAULT NULL COMMENT '设备id或家庭id',
  `status` smallint(6) DEFAULT NULL COMMENT '0-正常；1-已同意；2-已拒绝',
  `removed` smallint(6) DEFAULT NULL COMMENT '删除 1-邀请者删除；2-被邀请者删除',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` timestamp(6) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '修改人',
  `update_time` timestamp(6) NULL DEFAULT NULL COMMENT '修改时间',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户id',
  `zone` varchar(255) DEFAULT NULL COMMENT '区域',
  `type` smallint(6) DEFAULT NULL COMMENT '0-共享设备；1-共享家庭',
  PRIMARY KEY (`id`(64))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='设备分享邀请信息表';


-- d_device.t_device_bind definition

CREATE TABLE d_device.t_device_bind (
	`id` varchar(64) NOT NULL COMMENT '主键id',
	`device_id` varchar(64) NOT NULL COMMENT '设备id',
	`sn` varchar(128) NOT NULL COMMENT '设备sn',
	`user_id` varchar(64) NOT NULL COMMENT '用户id',
	`owner` varchar(64) DEFAULT NULL COMMENT '拥有者id',
	`bind_time` timestamp DEFAULT NULL COMMENT '绑定时间',
	`create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
	`create_time` timestamp DEFAULT NULL COMMENT '创建时间',
	`update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
	`update_time` timestamp DEFAULT NULL COMMENT '更新时间',
	`tenant_id` varchar(64) NOT NULL COMMENT '租户id',
	`zone` varchar(255) DEFAULT NULL COMMENT '区域',
	primary key(`id`),
	UNIQUE KEY `t_device_bind_sn_tenant_id_IDX` (`sn`,`tenant_id`)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_bin
COMMENT='第三方用户设备绑定关系信息表';