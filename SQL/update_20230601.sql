CREATE TABLE `t_device_reset_audit`
(
    `id`             varchar(100) NOT NULL,
    `tenant_id`      varchar(100) DEFAULT NULL COMMENT '租户id',
    `apply_sn_json`  json         DEFAULT NULL COMMENT '申请SN列表',
    `del_sn_json`    json         DEFAULT NULL COMMENT '需要删除SN列表',
    `no_del_sn_json` json         DEFAULT NULL COMMENT '未删除SN列表',
    `audit_status`   smallint(6) NOT NULL COMMENT '审核状态（0：未审核，1：审核通过，2：审核不通过，3:部分审核通过）',
    `audit_desc`     text         DEFAULT NULL COMMENT '审核说明',
    `apply_by`       varchar(100) DEFAULT NULL COMMENT '申请人',
    `apply_time`     timestamp NULL DEFAULT NULL COMMENT '申请时间',
    `audit_by`       varchar(100) DEFAULT NULL COMMENT '审核人',
    `audit_time`     timestamp NULL DEFAULT NULL COMMENT '审核时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='设备出厂初始化审核表';