#!/bin/sh

PROJECT_NAME=device-service-encrypted

source /etc/profile
JAVA_HOME=$JAVA_HOME

mkdir -p /data/log/$PROJECT_NAME

JAVA_OPTS="${JAVA_OPTS} ${JAVA_AGENT}"
JAVA_OPTS="$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/data/log/$PROJECT_NAME/oom.hprof"
JAVA_OPTS="$JAVA_OPTS -Xlog:gc:/data/log/$PROJECT_NAME/gc.log"

echo -n "Starting $PROJECT_NAME ......"
$JAVA_HOME/bin/java -javaagent:$PROJECT_NAME.jar -jar $JAVA_OPTS ${PROFILE} $PROJECT_NAME.jar