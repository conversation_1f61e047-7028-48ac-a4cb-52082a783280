#!/bin/sh
#该脚本为Linux下启动java程序的通用脚本。即可以作为开机自启动service脚本被调用，
#也可以作为启动java程序的独立脚本来使用。
#
#
###################################
#环境变量及程序执行参数JAVA_HOME
###################################

PROJECT_NAME=device-service

#JDK所在路径
source /etc/profile
JAVA_HOME=$JAVA_HOME

PROFILE=$1
#当前目录
mkdir -p /data/log/$PROJECT_NAME


JAVA_OPTS="${JAVA_OPTS} ${JAVA_AGENT}"


JAVA_OPTS="$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/data/log/$PROJECT_NAME/oom.hprof"
##升级为-Xlog:gc
JAVA_OPTS="$JAVA_OPTS -Xlog:gc:/data/log/$PROJECT_NAME/gc.log"
#JAVA_OPTS="$JAVA_OPTS -Djava.security.egd=file:/dev/./urandom"
#JAVA_OPTS="$JAVA_OPTS -Des.set.netty.runtime.available.processors=false"
###################################
# 启动
###################################

echo -n "Starting $PROJECT_NAME ......"
$JAVA_HOME/bin/java -jar $JAVA_OPTS ${PROFILE} $PROJECT_NAME.jar

