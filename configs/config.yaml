server:
  name: device-service
  port: 8304
  mode: debug

nacos:
  host: nacos-headless.aiot
  port: 8848
  namespace: aiot-go
  dataId:
  group:
log:
  level: debug
  path: ./logs
  maxSize: 100
  maxBackups: 10
  maxAge: 30
  compress: true

#/inner/device/getDeviceInfosByIds

#/device-service/app/bind/orCode
#/device-service/app/shared/del
#/device-service/app/bind/untie
#/device-service/app/shared/reply
#/device-service/app/modify/nickname
#/device-service/app/share
#/device-service/app/video/getKey
#/device-service/device/rtc/activateDevice
#/device-service/device/bind/app
#/device-service/app/bind/listByUserId
#/device-service/app/shared/history
#/device-service/app/bind/confirm
#/device-service/auth/login
