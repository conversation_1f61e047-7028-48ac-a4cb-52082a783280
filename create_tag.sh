#!/bin/bash

# 创建git tag脚本
# 格式: test-release-YYYYMMDDHHMM

# 获取当前时间并格式化为YYYYMMDDHHMM
timestamp=$(date +"%Y%m%d%H%M")

# 生成tag名称
tag_name="test-release-${timestamp}"

echo "正在创建git tag: ${tag_name}"

# 创建tag
git tag ${tag_name}

# 推送tag到远程仓库
git push origin ${tag_name}

echo "✅ Git tag创建成功: ${tag_name}"
echo "Tag已推送到远程仓库"

# 显示所有tag（可选）
echo ""
echo "最近的tag列表:"
git tag --sort=-version:refname | head -5 