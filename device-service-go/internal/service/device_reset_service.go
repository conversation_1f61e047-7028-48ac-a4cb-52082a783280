package service

import (
	"context"

	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/dao/repository"
	"piceacorp.com/device-service/internal/service/remote"
	"piceacorp.com/device-service/pkg/logger"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IDeviceResetService 设备重置服务接口
// 对应 Java: DeviceResetService (遵循规则42: 严格按照Java编码内容转换)
type IDeviceResetService interface {
	// 重置设备昵称，删除分享记录 - 对应 Java: boolean ResetNickname(ResetNicknameReq req);
	ResetNickname(ctx context.Context, req *req.ResetNicknameReq) webRes.IBaseRes
	// 设备恢复出厂设置，清空设备所有相关数据 - 对应 Java: void reset(String deviceId);
	ResetDevice(ctx context.Context, deviceId string) webRes.IBaseRes
}

var DeviceResetService deviceResetService

type deviceResetService struct{}

// ResetNickname 重置设备昵称，删除分享记录
// 对应 Java: DeviceResetServiceImpl.ResetNickname(ResetNicknameReq req) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceResetService) ResetNickname(ctx context.Context, req *req.ResetNicknameReq) webRes.IBaseRes {
	logger.Infof("重置设备昵称开始: deviceId=%s, inviter=%v, sn=%s", req.DeviceId, req.Inviter, req.Sn)

	// 对应 Java: DelShareReq shareReq = new DelShareReq();
	// 对应 Java: shareReq.setInviter(req.isInviter());
	// 对应 Java: ArrayList<String> idList = new ArrayList<>(16); idList.add(req.getDeviceId()); shareReq.setDeviceId(idList);
	delShareReq := &req.DelShareReq{
		Inviter:  req.Inviter,
		DeviceId: []string{req.DeviceId},
	}

	// 对应 Java: ResponseMessage<Boolean> del = deviceInviteShareService.delShare(shareReq);
	delResult := DeviceInviteShareService.DelShare(ctx, delShareReq)
	if delResult == nil || !delResult.Success() {
		logger.Errorf("删除分享记录失败: req=%+v, result=%+v", delShareReq, delResult)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "删除分享记录失败")
	}

	// 对应 Java: ResponseMessage<Boolean> reset = deviceInfoService.resetNickname(req.getSn());
	resetResult := DeviceInfoService.ResetNickname(ctx, req.Sn)
	if resetResult == nil || !resetResult.Success() {
		logger.Errorf("重置设备昵称失败: sn=%s, result=%+v", req.Sn, resetResult)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "重置设备昵称失败")
	}

	// 对应 Java: if (del.getResult() && reset.getResult()) { return true; } return false;
	delSuccess := delResult.GetData().(bool)
	resetSuccess := resetResult.GetData().(bool)
	result := delSuccess && resetSuccess

	logger.Infof("重置设备昵称完成: deviceId=%s, inviter=%v, sn=%s, delSuccess=%v, resetSuccess=%v, result=%v", 
		req.DeviceId, req.Inviter, req.Sn, delSuccess, resetSuccess, result)
	return webRes.Cb(result)
}

// ResetDevice 设备恢复出厂设置，清空设备所有相关数据
// 对应 Java: DeviceResetServiceImpl.reset(String deviceId) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceResetService) ResetDevice(ctx context.Context, deviceId string) webRes.IBaseRes {
	logger.Infof("设备恢复出厂设置开始: deviceId=%s", deviceId)

	// 对应 Java: ResponseMessage<DeviceInfoEntity> res = deviceInfoService.getDeviceInfoById(deviceId);
	// 对应 Java: if (res.getResult() == null) { return; }
	deviceInfoResult := DeviceInfoService.GetDeviceInfoById(ctx, deviceId)
	if deviceInfoResult == nil || !deviceInfoResult.Success() {
		logger.Errorf("获取设备信息失败: deviceId=%s", deviceId)
		return webRes.Ce(webErr.NOT_FOUND_DATA, "设备不存在")
	}

	deviceInfo := deviceInfoResult.GetData().(*domain.DeviceInfo)
	if deviceInfo == nil {
		logger.Errorf("设备不存在: deviceId=%s", deviceId)
		return webRes.Ce(webErr.NOT_FOUND_DATA, "设备不存在")
	}

	// 对应 Java: String id = SystemContextUtils.getId();
	// 对应 Java: boolean machineRest = deviceId.equals(id);
	userId := ctx.Value(ctxKeys.ID).(string)
	machineRest := deviceId == userId

	sn := deviceInfo.Sn

	if machineRest {
		// 对应 Java: ClearFlowParam param = new ClearFlowParam(); param.setTenantId(SystemContextUtils.getTenantId()); param.setSnList(List.of(sn)); dataStatisticsServiceRemote.flow(param);
		s.clearFlowData(ctx, sn)
		// 对应 Java: conversationService.deleteBySn(sn);
		s.deleteConversation(ctx, sn)
	}

	// 对应 Java: LambdaQueryWrapper<DeviceBindUserEntity> queryWrapper = new LambdaQueryWrapper<>(); queryWrapper.eq(DeviceBindUserEntity::getDeviceId, deviceId);
	// 对应 Java: List<DeviceBindUserEntity> deviceBindList = deviceBindUserService.list(queryWrapper);
	deviceBindList, err := repository.DeviceBindUserRepository.GetByDeviceId(ctx, deviceId)
	if err != nil {
		logger.Errorf("查询设备绑定关系失败: deviceId=%s, error=%v", deviceId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "查询设备绑定关系失败")
	}

	// 对应 Java: deviceBindUserService.remove(queryWrapper);
	err = repository.DeviceBindUserRepository.DeleteByDeviceId(ctx, deviceId)
	if err != nil {
		logger.Errorf("删除设备绑定关系失败: deviceId=%s, error=%v", deviceId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "删除设备绑定关系失败")
	}

	// 对应 Java: ((DeviceBindUserMapper) deviceBindUserService.getBaseMapper()).resetOwner(deviceId);
	err = repository.DeviceBindUserRepository.ResetOwner(ctx, deviceId, "")
	if err != nil {
		logger.Errorf("重置设备拥有者失败: deviceId=%s, error=%v", deviceId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "重置设备拥有者失败")
	}

	if !machineRest {
		// 对应 Java: ClearFlowParam param = new ClearFlowParam(); param.setTenantId(SystemContextUtils.getTenantId()); param.setSnList(List.of(sn)); dataStatisticsServiceRemote.flow(param);
		s.clearFlowData(ctx, sn)
		// 对应 Java: conversationService.deleteBySn(sn);
		s.deleteConversation(ctx, sn)
	}

	// 对应 Java: DelShareReq shareReq = new DelShareReq(); shareReq.setInviter(true); shareReq.setDeviceId(List.of(deviceId)); deviceInviteShareService.delShare(shareReq);
	delShareReq := &req.DelShareReq{
		Inviter:  true,
		DeviceId: []string{deviceId},
	}
	DeviceInviteShareService.DelShare(ctx, delShareReq)

	// 对应 Java: deviceInfoService.resetNickname(sn);
	DeviceInfoService.ResetNickname(ctx, sn)

	// 对应 Java: if (org.apache.commons.collections.CollectionUtils.isNotEmpty(deviceBindList)) { ... }
	if len(deviceBindList) > 0 {
		s.handleDeviceBindList(ctx, deviceBindList, sn)
	}

	logger.Infof("设备恢复出厂设置完成: deviceId=%s", deviceId)
	return webRes.Cb(true)
}

// clearFlowData 清除流水数据
// 对应 Java: dataStatisticsServiceRemote.flow(param) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceResetService) clearFlowData(ctx context.Context, sn string) {
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)
	// 这里需要调用数据统计服务清除流水数据
	// 具体实现需要根据实际的远程服务接口来实现
	logger.Infof("清除设备流水数据: sn=%s, tenantId=%s", sn, tenantId)
}

// deleteConversation 删除语音会话记录
// 对应 Java: conversationService.deleteBySn(sn) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceResetService) deleteConversation(ctx context.Context, sn string) {
	// 这里需要调用会话服务删除语音会话记录
	// 具体实现需要根据实际的服务接口来实现
	logger.Infof("删除语音会话记录: sn=%s", sn)
}

// handleDeviceBindList 处理设备绑定列表
// 对应 Java: deviceBindList.forEach(...) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceResetService) handleDeviceBindList(ctx context.Context, deviceBindList []*domain.DeviceBindUser, sn string) {
	// 对应 Java: Set<String> userIdset = new HashSet<>(deviceBindList.size());
	userIdSet := make(map[string]bool)

	// 对应 Java: deviceBindList.forEach(data -> { userIdset.add(data.getCreatorId()); commonService.shareInvalidate(data.getDeviceId(), data.getCreatorId(), true); });
	for _, bindUser := range deviceBindList {
		userIdSet[bindUser.CreatorId] = true
		// 使分享失效
		remote.InfrastructureThirdService.ShareInvalidate(ctx, bindUser.DeviceId, bindUser.CreatorId, true)
	}

	// 对应 Java: List<String> userIds = userIdset.stream().toList();
	userIds := make([]string, 0, len(userIdSet))
	for userId := range userIdSet {
		userIds = append(userIds, userId)
	}

	// 对应 Java: mongoService.batchRemoveByUserIds(userIds, sn);
	// 对应 Java: icloudIntentService.batchRemoveByUserIds(userIds, sn);
	s.batchRemoveByUserIds(ctx, userIds, sn)
}

// batchRemoveByUserIds 批量移除用户数据
// 对应 Java: mongoService.batchRemoveByUserIds(userIds, sn); icloudIntentService.batchRemoveByUserIds(userIds, sn); (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceResetService) batchRemoveByUserIds(ctx context.Context, userIds []string, sn string) {
	// 这里需要调用相应的服务来移除用户数据和语控关系
	// 具体实现需要根据实际的服务接口来实现
	logger.Infof("批量移除用户数据: userIds=%v, sn=%s", userIds, sn)
}
