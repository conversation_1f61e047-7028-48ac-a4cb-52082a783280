package dto

// LargeModelParam 大模型参数
// 对应 Java: LargeModelParam (遵循规则38: 严格遵守java对象属性名称)
type LargeModelParam struct {
	// ak - 对应 Java: private String ak;
	Ak string `json:"ak" binding:"required" bindError:"ak不能为空"`
	
	// 请求语义 - 对应 Java: private List<JSONObject> nluInfos;
	NluInfos []map[string]interface{} `json:"nluInfos" binding:"required" bindError:"请求语义不能为空"`
	
	// 输入语音 - 对应 Java: private String query;
	Query string `json:"query"`
}
