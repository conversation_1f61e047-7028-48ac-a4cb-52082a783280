package dto

// MqttPropertyGetReqDTO MQTT属性获取请求DTO
// 对应 Java: MqttPropertyGetReqDTO (遵循规则38: 严格遵守java对象属性名称)
type MqttPropertyGetReqDTO struct {
	// 客户端id（iOS_userid/Android_userid） - 对应 Java: private String clientId;
	ClientId string `json:"clientId"`
	
	// 时间戳 - 对应 Java: private Long timestamp;
	Timestamp int64 `json:"timestamp"`
	
	// 主题 - 对应 Java: private String topic;
	Topic string `json:"topic"`
}
