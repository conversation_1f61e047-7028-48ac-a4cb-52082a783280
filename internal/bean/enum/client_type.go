package enum

// 客户端类型枚举
const (
	// 机器人设备
	CLIENT_TYPE_ROBOT = "ROBOT"

	// APP客户端
	CLIENT_TYPE_APP = "APP"

	// 门锁设备
	CLIENT_TYPE_LOCK = "LOCK"

	// 基站设备
	CLIENT_TYPE_BASE_STATION = "BASE_STATION"

	// 开放平台
	CLIENT_TYPE_OPEN_API = "OPEN_API"
)

// 客户端类型映射
var ClientTypeMap = map[string]string{
	CLIENT_TYPE_ROBOT:        "机器人设备",
	CLIENT_TYPE_APP:          "APP客户端",
	CLIENT_TYPE_LOCK:         "门锁设备",
	CLIENT_TYPE_BASE_STATION: "基站设备",
	CLIENT_TYPE_OPEN_API:     "开放平台",
}

// GetClientTypeName 获取客户端类型名称
func GetClientTypeName(clientType string) string {
	if name, exists := ClientTypeMap[clientType]; exists {
		return name
	}
	return "未知客户端类型"
}
