package enum

// LogEnum 日志类型枚举
type LogEnum struct {
	// 日志标识
	Code string
	// 信息
	Msg string
}

// 日志类型枚举常量定义
var (
	// BIND 绑定
	BIND = &LogEnum{
		Code: "1",
		Msg:  "绑定",
	}

	// UNBIND 解绑
	UNBIND = &LogEnum{
		Code: "-1",
		Msg:  "解绑",
	}

	// SELF 自身
	SELF = &LogEnum{
		Code: "1",
		Msg:  "自身",
	}

	// SELFLESS 管理后台
	SELFLESS = &LogEnum{
		Code: "2",
		Msg:  "管理后台",
	}

	// LOGIN_SUCCESS 登录成功
	LOGIN_SUCCESS = &LogEnum{
		Code: "0",
		Msg:  "登录成功",
	}

	// LOGIN_FAIL 登录失败
	LOGIN_FAIL = &LogEnum{
		Code: "-1",
		Msg:  "登录失败",
	}

	// SHARE_BIND 分享绑定
	SHARE_BIND = &LogEnum{
		Code: "2",
		Msg:  "分享绑定",
	}

	// SHARE_UNBIND 分享解绑
	SHARE_UNBIND = &LogEnum{
		Code: "-2",
		Msg:  "分享解绑",
	}
)
