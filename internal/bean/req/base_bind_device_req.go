package req

// BaseBindDeviceReq 基站绑定设备参数实体
// 对应 Java: BaseBindDeviceReq
type BaseBindDeviceReq struct {
	// 用户ID - 对应 Java: private String userId;
	UserId string `json:"userId" binding:"required" bindError:"userId"`
	
	// 设备ID - 对应 Java: private String deviceId;
	DeviceId string `json:"deviceId" binding:"required" bindError:"deviceId"`
	
	// 基站sn(用户名) - 对应 Java: private String baseSn;
	BaseSn string `json:"baseSn" binding:"required" bindError:"baseSn"`
	
	// 设备sn - 对应 Java: private String sn;
	Sn string `json:"sn" binding:"required" bindError:"sn"`
	
	// 产品ID - 对应 Java: private String productId;
	ProductId string `json:"productId" binding:"required" bindError:"productId"`
}
