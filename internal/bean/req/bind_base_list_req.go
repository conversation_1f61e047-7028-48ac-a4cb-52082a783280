package req

// BindBaseListReq 基站绑定列表请求
// 对应 Java: BindBaseListReq
type BindBaseListReq struct {
	// sn类型,如果是基站sn 为true，否则为false - 对应 Java: private Boolean flag;
	Flag bool `json:"flag" binding:"required" bindError:"flag"`
	
	// sn列表 - 对应 Java: private List<String> snList;
	SnList []string `json:"snList" binding:"required" bindError:"snList"`
	
	// 搜索方式 1：模糊搜索；2：精确搜索 - 对应 Java: private Integer searchType;
	SearchType int `json:"searchType"`
}
