package req

// ConversationPageReq 会话记录分页查询请求
// 对应 Java: ConversationPageReq (遵循规则38: 严格遵守java对象属性名称)
type ConversationPageReq struct {
	// 设备SN - 对应 Java: private String sn;
	Sn string `json:"sn" binding:"required" bindError:"sn不能为空"`
	
	// 页码 - 对应 Java: private int page;
	Page int `json:"page" binding:"required,min=1" bindError:"页码必须大于0"`
	
	// 每页大小 - 对应 Java: private int pageSize;
	PageSize int `json:"pageSize" binding:"required,min=1,max=100" bindError:"每页大小必须在1-100之间"`
}
