package req

// DeviceConfigVerifyPasswordReq 设备配置是否验证密码的参数实体
// 对应 Java: DeviceConfigVerifyPasswordVo (遵循规则38: 严格遵守java对象属性名称)
type DeviceConfigVerifyPasswordReq struct {
	// 设备id - 对应 Java: private String deviceId;
	DeviceId string `json:"deviceId" binding:"required" bindError:"设备id不能为空"`
	
	// 是否开启 - 对应 Java: private Integer verify;
	Verify int `json:"verify" binding:"required,oneof=0 1" bindError:"是否开启必须为0(关闭)或1(开启)"`
}
