package req

// DeviceInfoReq 活跃设备查询请求参数
// 对应 Java: DeviceInfoReq (遵循规则38: 严格按照Java对象属性名称)
type DeviceInfoReq struct {
	// 租户id - 对应 Java: private String tenantId;
	TenantId string `json:"tenantId" binding:"required" bindError:"租户id不能为空"`
	
	// 产品型号代码 - 对应 Java: private String productModeCode;
	ProductModeCode string `json:"productModeCode" binding:"required" bindError:"产品型号代码不能为空"`
	
	// 前percent百分比 个设备 - 对应 Java: private Integer percent;
	Percent int `json:"percent" binding:"required,min=1,max=100" bindError:"前percent百分比 个设备不能为空|ec_612"`
}
