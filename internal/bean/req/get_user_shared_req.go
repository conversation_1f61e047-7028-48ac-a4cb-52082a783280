package req

// GetUserSharedReq 用户获取指定目标的设备分享记录请求
// 对应 Java: GetUserSharedReq extends PageGetReq (遵循规则38: 严格遵守java对象属性名称)
type GetUserSharedReq struct {
	// 页码 - 对应 Java: private Integer page = 1;
	Page int `json:"page" binding:"min=1" bindError:"页码必须大于0"`
	
	// 每页条数 - 对应 Java: private Integer pageSize = DEFAULT_PAGE_SIZE;
	PageSize int `json:"pageSize" binding:"min=1,max=100" bindError:"每页条数必须在1-100之间"`
	
	// 是否是邀请者 - 对应 Java: private boolean inviter;
	Inviter bool `json:"inviter" binding:"required" bindError:"是否是邀请者不能为空"`
	
	// 设备id - 对应 Java: private String targetId;
	TargetId string `json:"targetId" binding:"required" bindError:"设备id不能为空"`
}
