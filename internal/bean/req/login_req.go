package req

// LoginReq 设备的登录通用参数
type LoginReq struct {
	// 厂商ID
	TenantID string `json:"tenantId" binding:"required" bindError:"参数缺失tenantId"`

	// 产品型号代码
	ProductModeCode string `json:"productModeCode" binding:"required" bindError:"参数缺失productModeCode"`

	// 设备唯一序列号
	SN string `json:"sn" binding:"required" bindError:"参数缺失sn"`

	// 设备mac地址
	MAC  string `json:"mac" binding:"required" bindError:"参数缺失mac"`
	Keyt string `json:"keyt" binding:"required" bindError:"参数缺失key"`

	// 当前包信息,所有模块都要发上来
	PackageVersions []RobotVersion `json:"packageVersions" binding:"required" bindError:"版本信息packageVersions缺失"`
}

// RobotVersion 机器人版本信息
type RobotVersion struct {
	// 模块名称
	CtrlVersion string `json:"ctrlVersion"`

	// 版本号
	VersionName string `json:"versionName"`

	// 模块类型
	PackageType string `json:"packageType"`

	Version uint `json:"version"`

	Data map[string]interface{} `json:"data"`
}
