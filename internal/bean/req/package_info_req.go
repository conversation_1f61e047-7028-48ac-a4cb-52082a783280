package req

import "time"

// PackageInfoReq 升级包信息请求
// 对应 Java: PackageInfoVo (遵循规则38: 严格遵守java对象属性名称)
type PackageInfoReq struct {
	// 主键id - 对应 Java: private String id;
	Id string `json:"id" binding:"required" bindError:"主键id不能为空"`
	
	// 租户id - 对应 Java: private String tenantId;
	TenantId string `json:"tenantId"`
	
	// 产品id - 对应 Java: private String productId;
	ProductId string `json:"productId"`
	
	// 产品型号代码（原工程类型） - 对应 Java: private String productModelCode;
	ProductModelCode string `json:"productModelCode" binding:"required" bindError:"产品型号代码不能为空"`
	
	// 包类型 - 对应 Java: private String packageType;
	PackageType string `json:"packageType"`
	
	// 版本名称 - 对应 Java: private String versionName;
	VersionName string `json:"versionName"`
	
	// 版本代码 - 对应 Java: private String versionCode;
	VersionCode string `json:"versionCode"`
	
	// 打包人 - 对应 Java: private String packUser;
	PackUser string `json:"packUser"`
	
	// 内部描述 - 对应 Java: private String innerDesc;
	InnerDesc string `json:"innerDesc"`
	
	// 描述 - 对应 Java: private String description;
	Description string `json:"description"`
	
	// 升级包md5值 - 对应 Java: private String md5;
	Md5 string `json:"md5"`
	
	// 升级包地址 - 对应 Java: private String packageUrl;
	PackageUrl string `json:"packageUrl"`
	
	// 升级包大小 - 对应 Java: private String packageSize;
	PackageSize string `json:"packageSize"`
	
	// 包状态(0:开发包1:测试包2:生产包) - 对应 Java: private Integer packageStatus;
	PackageStatus *int `json:"packageStatus"`
	
	// 发布状态(0:待发布1:已发布) - 对应 Java: private Integer publishStatus;
	PublishStatus *int `json:"publishStatus"`
	
	// 发布描述 - 对应 Java: private String publishDesc;
	PublishDesc string `json:"publishDesc"`
	
	// 测试状态 - 对应 Java: private Integer testStatus;
	TestStatus *int `json:"testStatus"`
	
	// 自测结论 - 对应 Java: private String selfTestConclusion;
	SelfTestConclusion string `json:"selfTestConclusion"`
	
	// 测试结论 - 对应 Java: private String testConclusion;
	TestConclusion string `json:"testConclusion"`
	
	// 测试报告url - 对应 Java: private String testReportUrl;
	TestReportUrl string `json:"testReportUrl"`
	
	// 测试人员 - 对应 Java: private String testBy;
	TestBy string `json:"testBy"`
	
	// 测试时间 - 对应 Java: private Date testTime;
	TestTime *time.Time `json:"testTime"`
	
	// 包配置策略主键id - 对应 Java: private String packageStrategyId;
	PackageStrategyId string `json:"packageStrategyId"`
	
	// 发布时间 - 对应 Java: private Date publishTime;
	PublishTime *time.Time `json:"publishTime"`
	
	// 是否必经版本(0:否1:是) - 对应 Java: private Integer feature;
	Feature *int `json:"feature"`
	
	// 地区 - 对应 Java: private String zone;
	Zone string `json:"zone"`
	
	// 最低兼容版本 - 对应 Java: private Integer minVersion;
	MinVersion *int `json:"minVersion"`
	
	// 是否静默升级(0:否1:是) - 对应 Java: private Integer silence;
	Silence *int `json:"silence"`
	
	// 升级策略 - 对应 Java: private String upgradeStrategy;
	UpgradeStrategy string `json:"upgradeStrategy"`
	
	// 页面类型 - 对应 Java: private Integer searchType;
	SearchType *int `json:"searchType"`
	
	// 主动下发任务Id - 对应 Java: private String pushTaskId;
	PushTaskId string `json:"pushTaskId"`
}
