package req

// ResetNicknameReq 重置设备昵称参数实体
// 对应 Java: ResetNicknameReq (遵循规则38: 严格按照Java对象属性名称)
type ResetNicknameReq struct {
	// 设备id - 对应 Java: private String deviceId;
	DeviceId string `json:"deviceId" binding:"required" bindError:"deviceId"`
	
	// 是否是邀请者 - 对应 Java: private boolean inviter;
	Inviter bool `json:"inviter" binding:"required" bindError:"inviter"`
	
	// 设置sn - 对应 Java: private String sn;
	Sn string `json:"sn" binding:"required" bindError:"sn"`
}
