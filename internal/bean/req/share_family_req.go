package req

// ShareFamilyReq 家庭分享请求参数
// 对应 Java: ShareFamilyVo (遵循规则38: 严格遵守java对象属性名称)
type ShareFamilyReq struct {
	// 被邀请的用户id列表 - 对应 Java: private List<String> beInviteIds;
	BeInviteIds []string `json:"beInviteIds" binding:"required" bindError:"被邀请的用户id列表不能为空"`
	
	// 被分享的家庭ID - 对应 Java: private String targetId;
	TargetId string `json:"targetId" binding:"required" bindError:"被分享的家庭ID不能为空"`
	
	// 被分享的家庭名称 - 对应 Java: private String familyName;
	FamilyName string `json:"familyName" binding:"required" bindError:"被分享的家庭名称不能为空"`
}
