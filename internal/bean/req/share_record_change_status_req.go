package req

// ShareRecordChangeStatusReq 同意或拒绝分享实体封装
// 对应 Java: ShareRecordChangeStatusReq (遵循规则38: 严格遵守java对象属性名称)
type ShareRecordChangeStatusReq struct {
	// 消息分享记录id - 对应 Java: private String shareId;
	ShareId string `json:"shareId" binding:"required" bindError:"消息分享记录id不能为空"`
	
	// 消息记录id - 对应 Java: private String recordId;
	RecordId string `json:"recordId" binding:"required" bindError:"消息记录id不能为空"`
	
	// 状态 - 对应 Java: private Integer status;
	Status int `json:"status" binding:"required,oneof=1 2" bindError:"状态必须为1(同意)或2(拒绝)"`
	
	// 家庭id - 对应 Java: private String familyId;
	FamilyId string `json:"familyId"`
	
	// 家庭名称 - 对应 Java: private String familyName;
	FamilyName string `json:"familyName"`
}
