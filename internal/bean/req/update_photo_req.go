package req

// UpdatePhotoReq 更新产品下对应的设备的产品图片信息
// 对应 Java: UpdatePhotoVo (遵循规则38: 严格按照Java对象属性名称)
type UpdatePhotoReq struct {
	// 产品id - 对应 Java: private String productId;
	ProductId string `json:"productId" binding:"required" bindError:"productId"`
	
	// 产品图片地址 - 对应 Java: private String photoUrl;
	PhotoUrl string `json:"photoUrl" binding:"required" bindError:"photoUrl"`
	
	// 企业id - 对应 Java: private String tenantId;
	TenantId string `json:"tenantId" binding:"required" bindError:"tenantId"`
}
