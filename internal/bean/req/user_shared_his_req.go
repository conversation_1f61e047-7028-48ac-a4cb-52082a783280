package req

// UserSharedHisReq 用户获取分享历史请求
type UserSharedHisReq struct {
	// 家庭id或设备id
	TargetId string `json:"targetId" description:"家庭id或设备id, 当查询家庭分享记录的时候必填"`
	// 共享类型
	Type int `json:"type" description:"共享类型,0-共享设备，1-共享家庭，暂时忽略"`
	// 页码
	Page int `json:"page" binding:"required,min=1" bindError:"页码不能为空且必须大于0"`
	// 每页大小
	PageSize int `json:"pageSize" binding:"required,min=1,max=100" bindError:"每页大小不能为空且必须在1-100之间"`
}
