package res

import (
	"piceacorp.com/device-service/internal/dao/bo"
)

// DeviceShareHisRes 设备分享历史响应
type DeviceShareHisRes struct {
	// 分页数据
	*bo.PageResult[*DeviceShareHisVo] `json:",inline"`
}

// DeviceShareHisVo 设备分享历史VO (响应用)
type DeviceShareHisVo struct {
	// 主键id
	Id string `json:"id"`
	// 邀请者用户id
	InviteId string `json:"inviteId"`
	// 被邀请者用户id
	BeInviteId string `json:"beInviteId"`
	// 设备id或家庭id
	TargetId string `json:"targetId"`
	// 类型,0-共享设备；1-共享家庭
	Type int `json:"type"`
	// 0-正常；1-已同意；2-已拒绝
	Status int `json:"status"`
	// 删除 1-邀请者删除；2-被邀请者删除
	Removed int `json:"removed"`
	// 创建时间
	CreateTime string `json:"createTime"`
	// 设备序列号
	Sn string `json:"sn"`
	// 设备MAC地址
	Mac string `json:"mac"`
	// 设备昵称或家庭名称
	Name string `json:"name"`
	// 产品型号代码
	ProductModeCode string `json:"productModeCode"`
	// 设备虚拟ID
	IotId string `json:"iotId"`
	// 用户名 (加密)
	Username string `json:"username"`
	// 产品型号名称
	ModeType string `json:"modeType"`
	// 产品图片URL
	PhotoUrl string `json:"photoUrl"`
	// 用户头像URL
	AvatarUrl string `json:"avatarUrl"`
}
