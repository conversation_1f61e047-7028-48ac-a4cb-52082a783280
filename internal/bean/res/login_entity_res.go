package res

import "time"

// LoginEntityRes 登录响应实体
type LoginEntityRes struct {
	// ID
	ID string `json:"id"`

	// 客户端类型
	ClientTypeEnum string `json:"clientTypeEnum"`

	// 上下文值
	ContextValues *map[string]interface{} `json:"data"`

	// 重置码 0：不需要重置，1：需要重置
	ResetCode int `json:"resetCode"`

	// 最大升级时间
	MaxUpgradeTime int `json:"maxUpgradeTime"`

	// Token信息
	Token string `json:"token,omitempty"`

	// EMQ Token
	EmqToken string `json:"emqToken,omitempty"`

	// 过期时间
	ExpireTime *time.Time `json:"expireTime,omitempty"`
}
