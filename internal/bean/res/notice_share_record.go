package res

// NoticeShareRecord 设备分享极光推送记录
// 对应 Java: NoticeShareRecordEntity (遵循规则38: 严格遵守java对象属性名称)
type NoticeShareRecord struct {
	// 主键id - 对应 Java: private String id;
	Id string `json:"id"`
	
	// 消息发送者 - 对应 Java: private String from;
	From string `json:"from"`
	
	// 消息接收者 - 对应 Java: private String to;
	To string `json:"to"`
	
	// 标题 - 对应 Java: private String title;
	Title string `json:"title"`
	
	// 消息体 - 对应 Java: private String msg;
	Msg string `json:"msg"`
	
	// 状态 - 对应 Java: private Integer status;
	Status *int `json:"status"`
	
	// 类型 - 对应 Java: private Integer type;
	Type *int `json:"type"`
	
	// 结果 - 对应 Java: private Integer result;
	Result *int `json:"result"`
	
	// 分享记录id - 对应 Java: private String shareId;
	ShareId string `json:"shareId"`
	
	// 设备或家庭id - 对应 Java: private String targetId;
	TargetId string `json:"targetId"`
	
	// 创建者 - 对应 Java: private String createBy;
	CreateBy string `json:"createBy"`
	
	// 创建时间 - 对应 Java: private Long createTime;
	CreateTime *int64 `json:"createTime"`
	
	// 更新者 - 对应 Java: private String updateBy;
	UpdateBy string `json:"updateBy"`
	
	// 更新时间 - 对应 Java: private Long updateTime;
	UpdateTime *int64 `json:"updateTime"`
	
	// 租户id - 对应 Java: private String tenantId;
	TenantId string `json:"tenantId"`
	
	// 地区 - 对应 Java: private String zone;
	Zone string `json:"zone"`
	
	// 设备昵称或家庭名称 - 对应 Java: private String name;
	Name string `json:"name"`
	
	// 用户名 - 对应 Java: private String username;
	Username string `json:"username"`
	
	// 图片地址 - 对应 Java: private String photoUrl;
	PhotoUrl string `json:"photoUrl"`
	
	// 设备sn - 对应 Java: private String sn;
	Sn string `json:"sn"`
}
