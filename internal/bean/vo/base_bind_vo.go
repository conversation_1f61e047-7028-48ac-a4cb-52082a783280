package vo

// BaseBindVo 基站绑定信息
// 对应 Java: BaseBindVo (遵循规则38: 严格遵守java对象属性名称)
type BaseBindVo struct {
	// 基站id - 对应 Java: private String baseId;
	BaseId string `json:"baseId"`
	
	// 设备sn - 对应 Java: private String sn;
	Sn string `json:"sn"`
	
	// 设备ID - 对应 Java: private String deviceId;
	DeviceId string `json:"deviceId"`
	
	// 拥有者ID - 对应 Java: private String owner;
	Owner string `json:"owner"`
	
	// mac - 对应 Java: private String mac;
	Mac string `json:"mac"`
	
	// nickname - 对应 Java: private String nickname;
	Nickname string `json:"nickname"`
}
