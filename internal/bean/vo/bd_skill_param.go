package vo

// BdSkillParam 百度技能参数
// 对应 Java: BdSkillParam (遵循规则38: 严格遵守java对象属性名称)
type BdSkillParam struct {
	// 日志ID - 对应 Java: private String logId;
	LogId string `json:"logId"`
	
	// 百度三元组数据 - 对应 Java: private BdDevice device;
	Device *BdDevice `json:"device"`
	
	// ASR识别结果 - 对应 Java: private String query;
	Query string `json:"query"`
	
	// 请求语义 - 对应 Java: private String nluInfos;
	NluInfos string `json:"nluInfos"`
	
	// 拓展信息 - 对应 Java: private ExtInfo extInfo;
	ExtInfo *ExtInfo `json:"extInfo"`
	
	// 端上自定义信息 - 对应 Java: private String custom;
	Custom string `json:"custom"`
	
	// 会话ID - 对应 Java: private String conversationId;
	ConversationId string `json:"conversationId"`
}

// BdDevice 百度设备信息
// 对应 Java: BdDevice (遵循规则38: 严格遵守java对象属性名称)
type BdDevice struct {
	// 设备AK - 对应 Java: private String ak;
	Ak string `json:"ak"`
}

// ExtInfo 扩展信息
// 对应 Java: ExtInfo (遵循规则38: 严格遵守java对象属性名称)
type ExtInfo struct {
	// 扩展字段，根据实际需要添加
}
