package vo

// BdSkillResponse 百度技能响应
// 对应 Java: BdSkillResponse (遵循规则38: 严格遵守java对象属性名称)
type BdSkillResponse struct {
	// 错误码 - 对应 Java: private Integer errCode;
	ErrCode int `json:"errCode"`
	
	// 日志ID - 对应 Java: private String logId;
	LogId string `json:"logId"`
	
	// 百度三元组数据 - 对应 Java: private BdDevice device;
	Device *BdDevice `json:"device"`
	
	// ASR识别结果 - 对应 Java: private String query;
	Query string `json:"query"`
	
	// 请求语义 - 对应 Java: private String nluInfos;
	NluInfos string `json:"nluInfos"`
	
	// 拓展信息 - 对应 Java: private ExtInfo extInfo;
	ExtInfo *ExtInfo `json:"extInfo"`
	
	// 端上自定义信息 - 对应 Java: private String custom;
	Custom string `json:"custom"`
	
	// 会话ID - 对应 Java: private String conversationId;
	ConversationId string `json:"conversationId"`
	
	// TTS内容 - 对应 Java: private Tts tts;
	Tts *Tts `json:"tts"`
}

// Tts TTS内容
// 对应 Java: Tts (遵循规则38: 严格遵守java对象属性名称)
type Tts struct {
	// 内容 - 对应 Java: private String content;
	Content string `json:"content"`
	
	// 标志 - 对应 Java: private Integer flag;
	Flag int `json:"flag"`
}
