package vo

import (
	"time"
)

// DeviceShareVo 设备分享信息
// 对应 Java: DeviceShareVo (遵循规则38: 严格遵守java对象属性名称)
type DeviceShareVo struct {
	// 分享ID - 对应 Java: private String id;
	Id string `json:"id"`
	
	// 用户ID - 对应 Java: private String userId;
	UserId string `json:"userId"`
	
	// 用户名 - 对应 Java: private String username;
	Username string `json:"username"`
	
	// 设备ID - 对应 Java: private String targetId;
	TargetId string `json:"targetId"`
	
	// 设备sn - 对应 Java: private String sn;
	Sn string `json:"sn"`
	
	// 设备mac - 对应 Java: private String mac;
	Mac string `json:"mac"`
	
	// 设备的虚拟id - 对应 Java: private String iotId;
	IotId string `json:"iotId"`
	
	// 状态 - 对应 Java: private int status;
	Status int `json:"status"`
	
	// 共享类型 - 对应 Java: private int type;
	Type int `json:"type"`
	
	// 删除状态 - 对应 Java: private Integer removed;
	Removed *int `json:"removed"`
	
	// 设备昵称或家庭名称 - 对应 Java: private String name;
	Name string `json:"name"`
	
	// 产品型号代码 - 对应 Java: private String productModeCode;
	ProductModeCode string `json:"productModeCode"`
	
	// 产品型号名称 - 对应 Java: private String modeType;
	ModeType string `json:"modeType"`
	
	// 图片地址 - 对应 Java: private String photoUrl;
	PhotoUrl string `json:"photoUrl"`
	
	// 邀请时间 - 对应 Java: private LocalDateTime createTime;
	CreateTime time.Time `json:"createTime"`
}
