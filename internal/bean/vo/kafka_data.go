package vo

// KafkaData Kafka消息数据
// 对应 Java: KafkaData (遵循规则38: 严格遵守java对象属性名称)
type KafkaData struct {
	// 消息id，时间戳 - 对应 Java: private Long messageId;
	MessageId int64 `json:"messageId"`
	
	// mqtt topic - 对应 Java: private String topic;
	Topic string `json:"topic"`
	
	// mqtt method - 对应 Java: private String method;
	Method string `json:"method"`
	
	// 设备sn - 对应 Java: private String sn;
	Sn string `json:"sn"`
	
	// 厂商id - 对应 Java: private String tenantId;
	TenantId string `json:"tenantId"`
	
	// 产品key，相当与id - 对应 Java: private String productKey;
	ProductKey string `json:"productKey"`
	
	// 物模型版本 - 对应 Java: private String version;
	Version string `json:"version"`
	
	// 地区 - 对应 Java: private String zone;
	Zone string `json:"zone"`
	
	// 时间戳 - 对应 Java: private Long timestamp;
	Timestamp int64 `json:"timestamp"`
	
	// 发送的内容 - 对应 Java: private String data;
	Data string `json:"data"`
}
