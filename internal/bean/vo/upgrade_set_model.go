package vo

// UpgradeSetModel OTA升级消息模型
// 对应 Java: UpgradeSetModel (遵循规则38: 严格遵守java对象属性名称)
type UpgradeSetModel struct {
	// 升级包大小 - 对应 Java: private Integer packageSize;
	PackageSize int `json:"packageSize"`
	
	// 产品型号代码（原工程类型） - 对应 Java: private String productModelCode;
	ProductModelCode string `json:"productModelCode"`
	
	// 固件类型 - 对应 Java: private String packageType;
	PackageType string `json:"packageType"`
	
	// 升级包版本名称 - 对应 Java: private String versionName;
	VersionName string `json:"versionName"`
	
	// 升级包版本 - 对应 Java: private String versionCode;
	VersionCode string `json:"versionCode"`
	
	// 升级包下载地址 - 对应 Java: private String packageUrl;
	PackageUrl string `json:"packageUrl"`
	
	// 升级包MD5值 - 对应 Java: private String md5;
	Md5 string `json:"md5"`
	
	// 签名 - 对应 Java: private boolean signed;
	Signed bool `json:"signed"`
	
	// 是否静默升级 - 对应 Java: private boolean silence;
	Silence bool `json:"silence"`
}
