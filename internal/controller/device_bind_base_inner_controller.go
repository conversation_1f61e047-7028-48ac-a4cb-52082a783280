package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/service"
	"piceacorp.com/device-service/pkg/logger"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// InnerBindListBySn 根据基站sn或者设备sn获取基站绑定列表 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceBindBaseInnerController.bindListBySn(@RequestBody BindBaseListReq req)
// POST /inner/bindBase/bindListBySn
func InnerBindListBySn(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证
	var bindBaseListReq req.BindBaseListReq
	if err := c.ShouldBindJSON(&bindBaseListReq); err != nil {
		c.Error(err)
		return
	}

	// 2. 参数验证 - 对应 Java: if (CollectionUtils.isEmpty(req.getSnList())) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "snList不能为空"); }
	if len(bindBaseListReq.SnList) == 0 {
		logger.Errorf("参数验证失败: snList不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "snList不能为空")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到基站绑定列表查询请求: flag=%v, snList=%v", bindBaseListReq.Flag, bindBaseListReq.SnList)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return deviceBindBaseService.bindListByDeviceSn(req);
	result := service.DeviceBindBaseService.BindListByDeviceSn(c, &bindBaseListReq)

	// 4. 返回响应 - 永远使用 c.JSON(http.StatusOK, response)
	c.JSON(http.StatusOK, result)
}

// InnerBaseBindDevice 基站绑定设备 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceBindBaseInnerController.baseBindDevice(@RequestBody BaseBindDeviceReq req)
// POST /inner/bindBase/baseBindDevice
func InnerBaseBindDevice(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证
	var baseBindDeviceReq req.BaseBindDeviceReq
	if err := c.ShouldBindJSON(&baseBindDeviceReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到基站绑定设备请求: userId=%s, deviceId=%s, baseSn=%s, sn=%s, productId=%s", 
		baseBindDeviceReq.UserId, baseBindDeviceReq.DeviceId, baseBindDeviceReq.BaseSn, 
		baseBindDeviceReq.Sn, baseBindDeviceReq.ProductId)

	// 2. 调用服务层处理业务逻辑 - 对应 Java: Boolean result = deviceBindBaseService.bind(req.getDeviceId(), req.getSn(), req.getUserId(), req.getBaseSn());
	success := service.DeviceBindBaseService.Bind(c, baseBindDeviceReq.DeviceId, baseBindDeviceReq.Sn, 
		baseBindDeviceReq.UserId, baseBindDeviceReq.BaseSn)

	logger.Infof("基站绑定设备结果: userId=%s, deviceId=%s, success=%v", 
		baseBindDeviceReq.UserId, baseBindDeviceReq.DeviceId, success)

	// 3. 返回响应 - 对应 Java: return ResponseMessage.buildSuccess(result);
	result := webRes.Cb(success)
	c.JSON(http.StatusOK, result)
}
