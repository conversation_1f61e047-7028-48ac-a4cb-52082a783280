package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/service"
	"piceacorp.com/device-service/pkg/logger"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
)

// BindConfirm app确认设备发起的绑定app请求
// 对应 Java: DeviceAppController.bindAppConfirm(@RequestBody BindConfirmVo vo, @RequestParam(defaultValue = "false") Boolean fromOldOpenApi)
// POST /app/bind/confirm
func BindConfirm(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证，对应 Java: if (Objects.isNull(vo)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL); }
	var bindReq req.BindConfirmReq
	if err := c.ShouldBindJSON(&bindReq); err != nil {
		c.Error(err)
		return
	}

	// 2. 获取Query参数 - 对应 Java: @RequestParam(defaultValue = "false") Boolean fromOldOpenApi
	fromOldOpenApiStr := c.DefaultQuery("fromOldOpenApi", "false")
	fromOldOpenApi := fromOldOpenApiStr == "true"

	logger.Infof("收到绑定确认请求: bindKey=%s, familyId=%s, roomId=%s, fromOldOpenApi=%v",
		bindReq.BindKey, bindReq.FamilyId, bindReq.RoomId, fromOldOpenApi)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: deviceBindService.bindAppConfirm(userId, key, familyId, roomId)
	result := service.DeviceBindService.BindConfirm(c, &bindReq)

	// 4. 返回响应 - 永远使用 c.JSON(http.StatusOK, response)
	c.JSON(http.StatusOK, result)
}

// ListByUserId 根据用户ID获取绑定设备列表
// 对应 Java: DeviceAppBindController.bindListByUserId
// GET /app/bind/listByUserId
func ListByUserId(c *gin.Context) {
	// 获取上下文信息 - 对应 Java: SystemContextUtils.getId()
	userId := c.Value(ctxKeys.ID).(string)

	logger.Infof("收到获取绑定设备列表请求: userId=%s", userId)

	// 调用服务层处理业务逻辑
	result := service.DeviceBindUserService.GetBindListByUserId(c, userId)

	// 返回响应 - 永远使用 c.JSON(http.StatusOK, response)
	c.JSON(http.StatusOK, result)
}
