package controller

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/service"
	"piceacorp.com/device-service/pkg/logger"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// InnerUntieDevice 根据设备Id解绑设备 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceBindInnerController.untieDevice(@RequestBody UntieDeviceReq req)
// POST /inner/bind/untie
func InnerUntieDevice(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var untieDeviceReq req.UntieDeviceReq
	if err := c.ShouldBindJSON(&untieDeviceReq); err != nil {
		c.Error(err)
		return
	}

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(req.getDeviceId())) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceId不能为空"); } (遵循规则20、38)
	if untieDeviceReq.DeviceId == "" {
		logger.Errorf("参数验证失败: deviceId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "deviceId不能为空")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到解绑设备请求: deviceId=%s", untieDeviceReq.DeviceId)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return deviceBindUserService.untieDevice(req); (遵循规则11)
	result := service.DeviceBindUserService.UntieDevice(c, &untieDeviceReq)

	// 4. 返回响应 - 永远使用 c.JSON(http.StatusOK, response) (遵循规则11)
	c.JSON(http.StatusOK, result)
}

// InnerBindListByUserId 根据userId获取绑定设备列表 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceBindInnerController.bindListByUserId(@RequestParam("userId") String userId)
// GET /inner/bind/bindListByUserId
func InnerBindListByUserId(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam("userId") String userId (遵循规则31)
	userId := c.Query("userId")

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(userId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "userId不能为空"); } (遵循规则20)
	if userId == "" {
		logger.Errorf("参数验证失败: userId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "userId不能为空")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到获取用户绑定设备列表请求: userId=%s", userId)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return deviceBindUserService.getBindListByUserId(userId);
	result := service.DeviceBindUserService.GetBindListByUserId(c, userId)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerSyncBind 同步绑定关系 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceBindInnerController.syncBind(@RequestBody List<DeviceBindUserEntity> list)
// POST /inner/bind/syncBind
func InnerSyncBind(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证
	var bindUserList []*domain.DeviceBindUser
	if err := c.ShouldBindJSON(&bindUserList); err != nil {
		c.Error(err)
		return
	}

	// 2. 参数验证 - 对应 Java: if (CollectionUtil.isEmpty(list)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "list"); } (遵循规则20)
	if len(bindUserList) == 0 {
		logger.Errorf("参数验证失败: list不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "list")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到同步绑定关系请求: count=%d", len(bindUserList))

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return deviceBindUserService.addBatch(list);
	result := service.DeviceBindUserService.AddBatch(c, bindUserList)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerGetBindList 根据userId获取绑定设备列表 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceBindInnerController.getBindList(@RequestParam("userId") String userId,@RequestParam("tenantId") String tenantId)
// GET /inner/bind/getBindList
func InnerGetBindList(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam (遵循规则31)
	userId := c.Query("userId")
	tenantId := c.Query("tenantId")

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(userId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "userId"); } (遵循规则20)
	if userId == "" {
		logger.Errorf("参数验证失败: userId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "userId")
		c.JSON(http.StatusOK, result)
		return
	}

	// 对应 Java: if (StringUtils.isBlank(tenantId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "tenantId"); }
	if tenantId == "" {
		logger.Errorf("参数验证失败: tenantId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到获取绑定关系请求: userId=%s, tenantId=%s", userId, tenantId)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return deviceBindUserService.getBindList(tenantId,userId);
	result := service.DeviceBindUserService.GetBindList(c, tenantId, userId)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerDeleteByIds 根据id删除绑定关系 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceBindInnerController.deleteByIds(@RequestParam("ids") List<String> ids)
// DELETE /inner/bind/deleteByIds
func InnerDeleteByIds(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam("ids") List<String> ids (遵循规则31)
	idsParam := c.Query("ids")

	// 解析逗号分隔的ID列表
	var ids []string
	if idsParam != "" {
		ids = strings.Split(idsParam, ",")
	}

	// 2. 参数验证 - 对应 Java: if (CollectionUtil.isEmpty(ids)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "ids"); } (遵循规则20)
	if len(ids) == 0 {
		logger.Errorf("参数验证失败: ids不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "ids")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到根据id删除绑定关系请求: ids=%v", ids)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return deviceBindUserService.deleteByIds(ids);
	result := service.DeviceBindUserService.DeleteByIds(c, ids)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerGetOwnerListById 根据设备id获取设备拥有者列表 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceBindInnerController.getOwnerListById(@RequestBody OwnerListParam param)
// POST /inner/bind/getOwnerListById
func InnerGetOwnerListById(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证
	var ownerListParam req.OwnerListParam
	if err := c.ShouldBindJSON(&ownerListParam); err != nil {
		c.Error(err)
		return
	}

	// 2. 参数验证 - 对应 Java: if (CollectionUtils.isEmpty(param.getIds())) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "设备id为空"); } (遵循规则20)
	if len(ownerListParam.Ids) == 0 {
		logger.Errorf("参数验证失败: 设备id为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "设备id为空")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到获取设备拥有者列表请求: param=%+v", ownerListParam)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return deviceBindUserService.getOwnerListByDeviceId(param);
	result := service.DeviceBindUserService.GetOwnerListByDeviceId(c, &ownerListParam)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerDeleteByUserIds 根据userId删除绑定关系 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceBindInnerController.deleteByUserIds(@RequestParam("userIds") List<String> userIds,@RequestParam("tenantId") String tenantId)
// DELETE /inner/bind/deleteByUserIds
func InnerDeleteByUserIds(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam (遵循规则31)
	userIdsParam := c.Query("userIds")
	tenantId := c.Query("tenantId")

	// 解析逗号分隔的用户ID列表
	var userIds []string
	if userIdsParam != "" {
		userIds = strings.Split(userIdsParam, ",")
	}

	// 2. 参数验证 - 对应 Java: if (CollectionUtil.isEmpty(userIds)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "userIds"); } (遵循规则20)
	if len(userIds) == 0 {
		logger.Errorf("参数验证失败: userIds不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "userIds")
		c.JSON(http.StatusOK, result)
		return
	}

	// 对应 Java: if (StringUtils.isBlank(tenantId)){ return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "tenantId"); }
	if tenantId == "" {
		logger.Errorf("参数验证失败: tenantId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到根据userId删除绑定关系请求: userIds=%v, tenantId=%s", userIds, tenantId)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return deviceBindUserService.deleteByUserIds(userIds,tenantId);
	result := service.DeviceBindUserService.DeleteByUserIds(c, userIds, tenantId)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerAddOnlineAppSnCacheForTempMap 添加在线app的sn缓存，用于实时地图 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceBindInnerController.addOnlineAppSnCacheForTempMap(@RequestBody AddOnlineAppSnReq req)
// POST /inner/bind/addOnlineAppSnCacheForTempMap
func InnerAddOnlineAppSnCacheForTempMap(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证
	var addOnlineAppSnReq req.AddOnlineAppSnReq
	if err := c.ShouldBindJSON(&addOnlineAppSnReq); err != nil {
		c.Error(err)
		return
	}

	// 2. 参数验证 - 对应 Java: if(ObjectUtils.isEmpty(req) || CollectionUtils.isEmpty(req.getSnList())){ return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "req"); } (遵循规则20)
	if len(addOnlineAppSnReq.SnList) == 0 {
		logger.Errorf("参数验证失败: snList不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "req")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到添加在线app的sn缓存请求: snList=%v", addOnlineAppSnReq.SnList)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: deviceBindUserService.addOnlineAppSnCacheForTempMap(req.getSnList()); return ResponseMessage.buildSuccess(true);
	service.DeviceBindUserService.AddOnlineAppSnCacheForTempMap(c, addOnlineAppSnReq.SnList)

	// 4. 返回响应 - 对应 Java: return ResponseMessage.buildSuccess(true);
	result := webRes.Cb(true)
	c.JSON(http.StatusOK, result)
}
