package controller

import (
	"net/http"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"

	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/service"
	"piceacorp.com/device-service/pkg/logger"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
)

// BindApp 设备发起绑定APP请求
// 对应 Java: DeviceController.bindApp(@RequestBody BindAppReq bind)
// POST /device/bind/app
func BindApp(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证，对应 Java: if (bind == null) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL); }
	var bindAppReq req.BindAppReq
	if err := c.ShouldBindJSON(&bindAppReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到设备绑定APP请求: %+v", bindAppReq)

	// 2. 调用服务层处理业务逻辑 - 对应 Java: deviceBindService.bindApp(deviceId, userId, key);
	result := service.DeviceBindService.BindApp(c, &bindAppReq)

	// 3. 返回响应 - 对应 Java: return ResponseMessage.buildSuccess();
	c.JSON(http.StatusOK, result)
}

// ModifyNickname 修改设备昵称
// 对应 Java: DeviceAppController.modifyNickname
// PUT /app/modify/nickname
func ModifyNickname(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证
	var modifyReq req.ModifyDeviceNicknameReq
	if err := c.ShouldBindJSON(&modifyReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到修改设备昵称请求: deviceId=%s, nickname=%s, sn=%s, mac=%s",
		modifyReq.DeviceId, modifyReq.Nickname, modifyReq.Sn, modifyReq.Mac)

	// 2. 调用服务层处理业务逻辑
	result := service.DeviceInfoService.ModifyNickname(c, &modifyReq)

	// 3. 返回响应 - 永远使用 c.JSON(http.StatusOK, response)
	c.JSON(http.StatusOK, result)
}

// UntieDevice 解绑设备
// 对应 Java: DeviceAppBindController.untieDevice
// POST/DELETE /app/bind/untie
func UntieDevice(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证
	var untieReq req.UntieDeviceReq
	if err := c.ShouldBindJSON(&untieReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到解绑设备请求: deviceId=%s, sn=%s", untieReq.DeviceId, untieReq.Sn)

	// 2. 调用服务层处理业务逻辑
	result := service.DeviceBindUserService.UntieDevice(c, &untieReq)

	// 3. 返回响应 - 永远使用 c.JSON(http.StatusOK, response)
	c.JSON(http.StatusOK, result)
}

// DelShareRobot 删除分享
// 对应 Java: DeviceAppController.delShareRobot(@RequestParam String shareId)
// DELETE /app/shared/del?shareId={shareId}
func DelShareRobot(c *gin.Context) {
	// 1. 获取Query参数 - 对应 Java: @RequestParam String shareId (默认必传)
	shareId, exists := c.GetQuery("shareId")

	// 2. 参数必传检查 - 对应 Java: @RequestParam 默认必传
	if !exists {
		logger.Errorf("参数验证失败: shareId参数未传递")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "shareId参数是必传的")
		c.JSON(http.StatusOK, result)
		return
	}

	// 3. 参数空值检查 - 对应 Java: 控制器中没有显式检查，但业务逻辑需要
	if shareId == "" {
		logger.Errorf("参数验证失败: shareId为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "shareId不能为空")
		c.JSON(http.StatusOK, result)
		return
	}

	// 获取用户ID - 对应 Java: SystemContextUtils.getId()
	userId := c.Value(ctxKeys.ID).(string)

	logger.Infof("收到删除分享请求: shareId=%s, userId=%s", shareId, userId)

	// 4. 调用服务层处理业务逻辑 - 对应 Java: deviceInviteShareService.cancelShare(shareId, userId)
	result := service.DeviceInviteShareService.CancelShare(c, shareId, userId)

	// 5. 返回响应 - 永远使用 c.JSON(http.StatusOK, response)
	c.JSON(http.StatusOK, webRes.Cb(result))
}

// GetBindKey 获取bindKey
// 对应 Java: DeviceAppController.getBindKey(@RequestParam String deviceId)
// GET /app/bindKey?deviceId={deviceId}
func GetBindKey(c *gin.Context) {
	// 1. 获取Query参数 - 对应 Java: @RequestParam String deviceId (默认必传)
	deviceId, exists := c.GetQuery("deviceId")

	// 2. 参数必传检查 - 对应 Java: @RequestParam 默认必传
	if !exists {
		logger.Errorf("参数验证失败: deviceId参数未传递")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "deviceId参数是必传的")
		c.JSON(http.StatusOK, result)
		return
	}

	// 3. 参数空值检查 - 对应 Java: if (StringUtils.isBlank(deviceId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceId"); }
	if deviceId == "" {
		logger.Errorf("参数验证失败: deviceId为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "deviceId")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到获取bindKey请求: deviceId=%s", deviceId)

	// 4. 调用服务层处理业务逻辑 - 对应 Java: return deviceBindService.getBindKey(deviceId, tenantId);
	result := service.DeviceBindService.GetBindKey(c, deviceId)

	// 5. 返回响应 - 永远使用 c.JSON(http.StatusOK, response)
	c.JSON(http.StatusOK, result)
}

// OrCodeBindConfirm 二维码绑定
// 对应 Java: DeviceAppController.orCodeBindConfirm
// POST /app/bind/orCode
func OrCodeBindConfirm(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证
	var orCodeReq req.OrCodeBindReq
	if err := c.ShouldBindJSON(&orCodeReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到二维码绑定请求: deviceId=%s, bindKey=%s, familyId=%s, roomId=%s",
		orCodeReq.DeviceId, orCodeReq.BindKey, orCodeReq.FamilyId, orCodeReq.RoomId)

	// 2. 调用服务层处理业务逻辑
	result := service.DeviceBindService.OrCodeBind(c, &orCodeReq)

	// 3. 返回响应 - 永远使用 c.JSON(http.StatusOK, response)
	c.JSON(http.StatusOK, result)
}

// ConfigVerifyPassword 设备配置是否需要开启验证app密码
// 对应 Java: DeviceAppController.configVerifyPassword(@RequestBody DeviceConfigVerifyPasswordVo vo)
// PUT /app/config/verify
func ConfigVerifyPassword(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var configVerifyReq req.DeviceConfigVerifyPasswordReq
	if err := c.ShouldBindJSON(&configVerifyReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到设备配置验证密码请求: deviceId=%s, verify=%d",
		configVerifyReq.DeviceId, configVerifyReq.Verify)

	// 2. 调用服务层处理业务逻辑 - 对应 Java: return deviceInfoService.configVerify(vo);
	result := service.DeviceInfoService.ConfigVerify(c, &configVerifyReq)

	// 3. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	c.JSON(http.StatusOK, result)
}

// UpdateIotId 更新设备信息的iotId
// 对应 Java: DeviceAppController.updateIotId(@RequestBody IotIdVo vo)
// POST /app/upload/iotId
func UpdateIotId(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var iotIdReq req.IotIdReq
	if err := c.ShouldBindJSON(&iotIdReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到更新设备iotId请求: deviceId=%s, iotId=%s",
		iotIdReq.DeviceId, string(iotIdReq.IotId))

	// 2. 调用服务层处理业务逻辑 - 对应 Java: return deviceInfoService.updateIotId(vo);
	result := service.DeviceInfoService.UpdateIotId(c, &iotIdReq)

	// 3. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	c.JSON(http.StatusOK, result)
}
