package controller

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/service"
	"piceacorp.com/device-service/pkg/logger"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// InnerGetRobotStatus 根据设备id获取设备status (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.getRobotStatus(@RequestParam String tenantId, @RequestParam String deviceId)
// GET /inner/device/status
func InnerGetRobotStatus(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam (遵循规则31)
	tenantId := c.Query("tenantId")
	deviceId := c.Query("deviceId")

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(tenantId)) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, TENANT_ID); } (遵循规则20)
	if tenantId == "" {
		logger.Errorf("参数验证失败: tenantId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
		c.JSON(http.StatusOK, result)
		return
	}

	// 对应 Java: if (StringUtils.isBlank(deviceId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "robotId"); }
	if deviceId == "" {
		logger.Errorf("参数验证失败: deviceId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "robotId")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到获取设备状态请求: tenantId=%s, deviceId=%s", tenantId, deviceId)

	// 3. 调用服务层处理业务逻辑 - 严格按照Java逻辑 (遵循规则42)
	// 对应 Java: DeviceInfoEntity device = deviceInfoCache.getCacheById(tenantId, deviceId);
	// 对应 Java: if (Objects.isNull(device)) { LogUtils.error("设备不存在，tenantId={}, deviceId={}", tenantId, device); return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA, "设备不存在"); }
	// 对应 Java: String sn = device.getSn(); if (!deviceInfoService.ifOnline(sn)) { return ResponseMessage.buildSuccess(null); }
	// 对应 Java: return ResponseMessage.buildSuccess(deviceInfoService.getDeviceStatus(sn));
	result := service.DeviceInfoService.GetRobotStatus(c, tenantId, deviceId)

	// 4. 返回响应 - 永远使用 c.JSON(http.StatusOK, response) (遵循规则11)
	c.JSON(http.StatusOK, result)
}

// InnerGetRobotState 根据SN获取设备state (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.getRobotState(@RequestParam String tenantId, @RequestParam String sn)
// GET /inner/device/state
func InnerGetRobotState(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam (遵循规则31)
	tenantId := c.Query("tenantId")
	sn := c.Query("sn")

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(tenantId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID); } (遵循规则20)
	if tenantId == "" {
		logger.Errorf("参数验证失败: tenantId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到获取设备状态请求: tenantId=%s, sn=%s", tenantId, sn)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(deviceInfoService.getDeviceStatus(sn));
	result := service.DeviceInfoService.GetDeviceStatus(c, sn)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerGetRobotId 根据SN号获取设备id (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.getRobotId(@RequestParam String tenantId, @RequestParam String sn)
// GET /inner/device/id
func InnerGetRobotId(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam (遵循规则31)
	tenantId := c.Query("tenantId")
	sn := c.Query("sn")

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(tenantId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID); } (遵循规则20)
	if tenantId == "" {
		logger.Errorf("参数验证失败: tenantId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
		c.JSON(http.StatusOK, result)
		return
	}

	// 对应 Java: if (StringUtils.isBlank(sn)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "sn"); }
	if sn == "" {
		logger.Errorf("参数验证失败: sn不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "sn")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到根据SN获取设备ID请求: tenantId=%s, sn=%s", tenantId, sn)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: DeviceInfoEntity device = deviceInfoCache.getCacheBySn(tenantId, sn); return ResponseMessage.buildSuccess(Objects.isNull(device) ? null : device.getId());
	result := service.DeviceInfoService.GetRobotId(c, tenantId, sn)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerGetRobotDetail 根据SN号获取设备信息 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.getRobotDetail(@RequestParam String tenantId, @RequestParam String sn)
// GET /inner/device/info
func InnerGetRobotDetail(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam (遵循规则31)
	tenantId := c.Query("tenantId")
	sn := c.Query("sn")

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(tenantId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID); } (遵循规则20)
	if tenantId == "" {
		logger.Errorf("参数验证失败: tenantId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
		c.JSON(http.StatusOK, result)
		return
	}

	// 对应 Java: if (StringUtils.isBlank(sn)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "sn"); }
	if sn == "" {
		logger.Errorf("参数验证失败: sn不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "sn")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到根据SN获取设备信息请求: tenantId=%s, sn=%s", tenantId, sn)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: DeviceInfoEntity device = deviceInfoCache.getCacheBySn(tenantId, sn); return ResponseMessage.buildSuccess(device);
	result := service.DeviceInfoService.GetRobotDetail(c, tenantId, sn)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerIsActivatedRobot 根据设备id查看设备是否激活 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.isActivatedRobot(@RequestParam String tenantId, @RequestParam String deviceId)
// GET /inner/device/active
func InnerIsActivatedRobot(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam (遵循规则31)
	tenantId := c.Query("tenantId")
	deviceId := c.Query("deviceId")

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(tenantId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID); } (遵循规则20)
	if tenantId == "" {
		logger.Errorf("参数验证失败: tenantId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
		c.JSON(http.StatusOK, result)
		return
	}

	// 对应 Java: if (StringUtils.isBlank(deviceId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID); } (注意：Java这里写错了，应该是deviceId)
	if deviceId == "" {
		logger.Errorf("参数验证失败: deviceId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId") // 保持与Java一致的错误信息 (遵循规则20)
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到查看设备是否激活请求: tenantId=%s, deviceId=%s", tenantId, deviceId)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: DeviceInfoEntity device = deviceInfoCache.getCacheById(tenantId, deviceId); return ResponseMessage.buildSuccess(device != null);
	result := service.DeviceInfoService.IsActivatedRobot(c, tenantId, deviceId)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerSelectAllRobotSn 获取所有设备SN列表 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.selectAllRobotSn(@RequestParam String tenantId)
// GET /inner/device/sn
func InnerSelectAllRobotSn(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam (遵循规则31)
	tenantId := c.Query("tenantId")

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(tenantId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID); } (遵循规则20)
	if tenantId == "" {
		logger.Errorf("参数验证失败: tenantId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到获取所有设备SN列表请求: tenantId=%s", tenantId)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(deviceInfoService.selectAllRobotSn());
	result := service.DeviceInfoService.SelectAllRobotSn(c)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerGetDeviceCountByPro 获取产品的设备数量 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.getDeviceCountByPro(@RequestParam List<String> productIds)
// GET /inner/device/getDeviceCountByPro
func InnerGetDeviceCountByPro(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam List<String> productIds (遵循规则31)
	productIdsParam := c.Query("productIds")

	// 解析逗号分隔的产品ID列表
	var productIds []string
	if productIdsParam != "" {
		productIds = strings.Split(productIdsParam, ",")
	}

	// 2. 参数验证 - 对应 Java: if (productIds == null) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL); } (遵循规则20)
	if productIds == nil {
		logger.Errorf("参数验证失败: productIds不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到获取产品设备数量请求: productIds=%v", productIds)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(deviceInfoService.getDeviceCountByPro(productIds));
	result := service.DeviceInfoService.GetDeviceCountByPro(c, productIds)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerControl 设备远程控制 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.control(@RequestBody DeviceControlReq req)
// POST /inner/device/control
func InnerControl(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var deviceControlReq req.DeviceControlReq
	if err := c.ShouldBindJSON(&deviceControlReq); err != nil {
		c.Error(err)
		return
	}

	// 2. 参数验证 - 对应 Java: if (Objects.isNull(req)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL); } (遵循规则20)
	// 对应 Java: if (StringUtils.isBlank(req.getDeviceId())) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "未选中设备"); }
	if deviceControlReq.DeviceId == "" {
		logger.Errorf("参数验证失败: 未选中设备")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "未选中设备")
		c.JSON(http.StatusOK, result)
		return
	}

	// 获取租户ID - 对应 Java: String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID); (遵循规则9)
	tenantId := c.Value(ctxKeys.TENANT_ID).(string)
	if tenantId == "" {
		logger.Errorf("参数验证失败: tenantId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到设备远程控制请求: tenantId=%s, deviceId=%s, content=%s",
		tenantId, deviceControlReq.DeviceId, deviceControlReq.Content)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return deviceControlService.controlDevice(req, tenantId);
	result := service.DeviceControlService.ControlDevice(c, &deviceControlReq, tenantId)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerClassify 根据设备id获取设备分类 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.classify(@RequestParam String tenantId, @RequestParam String deviceId)
// GET /inner/device/classify
func InnerClassify(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam (遵循规则31)
	tenantId := c.Query("tenantId")
	deviceId := c.Query("deviceId")

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(tenantId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID); } (遵循规则20)
	if tenantId == "" {
		logger.Errorf("参数验证失败: tenantId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
		c.JSON(http.StatusOK, result)
		return
	}

	// 对应 Java: if (StringUtils.isBlank(deviceId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceId"); }
	if deviceId == "" {
		logger.Errorf("参数验证失败: deviceId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "deviceId")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到获取设备分类请求: tenantId=%s, deviceId=%s", tenantId, deviceId)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: DeviceInfoEntity device = deviceInfoCache.getCacheById(tenantId, deviceId); return productServiceRemote.getProductClassify(device.getProductId());
	result := service.DeviceInfoService.Classify(c, tenantId, deviceId)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerDelShare 删除分享的设备时，删除分享记录 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.delShare(@RequestBody DelShareReq req)
// DELETE /inner/device/share
func InnerDelShare(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var delShareReq req.DelShareReq
	if err := c.ShouldBindJSON(&delShareReq); err != nil {
		c.Error(err)
		return
	}

	// 2. 参数验证 - 对应 Java: if (Objects.isNull(req)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL); } (遵循规则20)
	if len(delShareReq.DeviceId) == 0 {
		logger.Errorf("参数验证失败: deviceId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "deviceId")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到删除分享记录请求: deviceIds=%v, inviter=%v", delShareReq.DeviceId, delShareReq.Inviter)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return deviceInviteShareService.delShare(req);
	result := service.DeviceInviteShareService.DelShare(c, &delShareReq)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerGetDeviceInfosByIds 根据设备id集合获取设备信息 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.getDeviceInfosByIds(@RequestBody List<String> deviceIds)
// POST /inner/device/getDeviceInfosByIds
func InnerGetDeviceInfosByIds(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var deviceIds []string
	if err := c.ShouldBindJSON(&deviceIds); err != nil {
		c.Error(err)
		return
	}

	// 2. 参数验证 - 对应 Java: if (CollectionUtils.isEmpty(deviceIds)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL); } (遵循规则20)
	if len(deviceIds) == 0 {
		logger.Errorf("参数验证失败: deviceIds不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到根据设备ID集合获取设备信息请求: deviceIds=%v", deviceIds)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(deviceInfoService.getDeviceInfosByIds(deviceIds));
	result := service.DeviceInfoService.GetDeviceInfosByIds(c, deviceIds)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerGetDeviceNickNameByIds 根据设备id集合获取设备昵称 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.getDeviceNickNameByIds(@RequestBody List<String> deviceIds)
// POST /inner/device/getDeviceNickNameByIds
func InnerGetDeviceNickNameByIds(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var deviceIds []string
	if err := c.ShouldBindJSON(&deviceIds); err != nil {
		c.Error(err)
		return
	}

	// 2. 参数验证 - 对应 Java: if (CollectionUtils.isEmpty(deviceIds)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL); } (遵循规则20)
	if len(deviceIds) == 0 {
		logger.Errorf("参数验证失败: deviceIds不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到根据设备ID集合获取设备昵称请求: deviceIds=%v", deviceIds)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(deviceInfoService.getDeviceNickNameByIds(deviceIds));
	result := service.DeviceInfoService.GetDeviceNickNameByIds(c, deviceIds)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerGetProductModeCode 根据设备id获取产品型号代码 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.getProductModeCode(@RequestParam String deviceId)
// GET /inner/device/productModeCode
func InnerGetProductModeCode(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam (遵循规则31)
	deviceId := c.Query("deviceId")

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(deviceId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceId"); } (遵循规则20)
	if deviceId == "" {
		logger.Errorf("参数验证失败: deviceId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "deviceId")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到根据设备ID获取产品型号代码请求: deviceId=%s", deviceId)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(deviceInfoService.getProductModeCode(deviceId));
	result := service.DeviceInfoService.GetProductModeCode(c, deviceId)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerGetDeviceCountByProductModeCode 根据产品型号代码获取设备数量 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.getDeviceCountByProductModeCode(@RequestParam String productModeCode)
// GET /inner/device/count/productModeCode
func InnerGetDeviceCountByProductModeCode(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam (遵循规则31)
	productModeCode := c.Query("productModeCode")

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(productModeCode)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "productModeCode"); } (遵循规则20)
	if productModeCode == "" {
		logger.Errorf("参数验证失败: productModeCode不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "productModeCode")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到根据产品型号代码获取设备数量请求: productModeCode=%s", productModeCode)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(deviceInfoService.getDeviceCountByProductModeCode(productModeCode));
	result := service.DeviceInfoService.GetDeviceCountByProductModeCode(c, productModeCode)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerUpdateVerifyPassword 更新app是否验证密码 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.updateVerifyPassword(@RequestBody DeviceVerifyPasswordVo vo)
// PUT /inner/device/update/verifyPassword
func InnerUpdateVerifyPassword(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var deviceVerifyPasswordReq req.DeviceVerifyPasswordReq
	if err := c.ShouldBindJSON(&deviceVerifyPasswordReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到更新app验证密码请求: verifyPassword=%d, deviceIds=%v",
		deviceVerifyPasswordReq.VerifyPassword, deviceVerifyPasswordReq.DeviceIds)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(deviceInfoService.updateVerifyPassword(vo));
	result := service.DeviceInfoService.UpdateVerifyPassword(c, &deviceVerifyPasswordReq)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerGetDeviceSnByTenantId 根据租户id和设备sn获取设备信息 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.getDeviceSnByTenantId(@RequestBody List<DeviceSnAndTenantReq> reqList)
// PUT /inner/device/list/sns
func InnerGetDeviceSnByTenantId(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var reqList []req.DeviceSnAndTenantReq
	if err := c.ShouldBindJSON(&reqList); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到根据租户ID和设备SN获取设备信息请求: count=%d", len(reqList))

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(deviceInfoService.getDeviceSnByTenantId(reqList));
	result := service.DeviceInfoService.GetDeviceSnByTenantId(c, reqList)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerResetNickname 重置设备昵称 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.resetNickname(@RequestBody ResetNicknameReq req)
// PUT /inner/device/resetNickname
func InnerResetNickname(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var resetNicknameReq req.ResetNicknameReq
	if err := c.ShouldBindJSON(&resetNicknameReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到重置设备昵称请求: deviceId=%s, inviter=%v, sn=%s",
		resetNicknameReq.DeviceId, resetNicknameReq.Inviter, resetNicknameReq.Sn)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(deviceResetService.resetNickname(req));
	result := service.DeviceResetService.ResetNickname(c, &resetNicknameReq)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerUpdatePhoto 更新产品下对应的设备的产品图片信息 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.updatePhoto(@RequestBody UpdatePhotoVo vo)
// PUT /inner/device/update/photo
func InnerUpdatePhoto(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var updatePhotoReq req.UpdatePhotoReq
	if err := c.ShouldBindJSON(&updatePhotoReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到更新产品图片请求: productId=%s, photoUrl=%s, tenantId=%s",
		updatePhotoReq.ProductId, updatePhotoReq.PhotoUrl, updatePhotoReq.TenantId)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(deviceInfoService.updatePhoto(vo));
	result := service.DeviceInfoService.UpdatePhoto(c, &updatePhotoReq)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerGetDeviceInfoById 根据设备id获取设备信息 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.getDeviceInfoById(@PathVariable String id)
// GET /inner/device/getDeviceInfoById/{id}
func InnerGetDeviceInfoById(c *gin.Context) {
	// 1. 获取路径参数 - 对应 Java: @PathVariable String id (遵循规则31)
	id := c.Param("id")

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(id)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "id"); } (遵循规则20)
	if id == "" {
		logger.Errorf("参数验证失败: id不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "id")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到根据设备ID获取设备信息请求: id=%s", id)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(deviceInfoService.getDeviceInfoById(id));
	result := service.DeviceInfoService.GetDeviceInfoById(c, id)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerGetActiveDeviceSNByCode 根据产品型号代码获取活跃设备SN (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.getActiveDeviceSNByCode(@RequestBody DeviceInfoReq req)
// POST /inner/device/getActiveDeviceSNByCode
func InnerGetActiveDeviceSNByCode(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var deviceInfoReq req.DeviceInfoReq
	if err := c.ShouldBindJSON(&deviceInfoReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到根据产品型号代码获取活跃设备SN请求: tenantId=%s, productModeCode=%s, percent=%d",
		deviceInfoReq.TenantId, deviceInfoReq.ProductModeCode, deviceInfoReq.Percent)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(deviceInfoService.getActiveDeviceSNByCode(req));
	result := service.DeviceInfoService.GetActiveDeviceSNByCode(c, &deviceInfoReq)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerResetDevice 重置设备 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.resetDevice(@PathVariable String deviceId)
// POST /inner/device/reset/device/{deviceId}
func InnerResetDevice(c *gin.Context) {
	// 1. 获取路径参数 - 对应 Java: @PathVariable String deviceId (遵循规则31)
	deviceId := c.Param("deviceId")

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(deviceId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceId"); } (遵循规则20)
	if deviceId == "" {
		logger.Errorf("参数验证失败: deviceId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "deviceId")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到重置设备请求: deviceId=%s", deviceId)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return ResponseMessage.buildSuccess(deviceResetService.resetDevice(deviceId));
	result := service.DeviceResetService.ResetDevice(c, deviceId)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}

// InnerGetById 根据设备id获取设备信息 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceInnerController.getById(@RequestParam String tenantId, @RequestParam String deviceId)
// GET /inner/device/getById
func InnerGetById(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam (遵循规则31)
	tenantId := c.Query("tenantId")
	deviceId := c.Query("deviceId")

	// 2. 参数验证 - 对应 Java: if (StringUtils.isBlank(tenantId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID); } (遵循规则20)
	if tenantId == "" {
		logger.Errorf("参数验证失败: tenantId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
		c.JSON(http.StatusOK, result)
		return
	}

	// 对应 Java: if (StringUtils.isBlank(deviceId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceId"); }
	if deviceId == "" {
		logger.Errorf("参数验证失败: deviceId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "deviceId")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到根据设备ID获取设备信息请求: tenantId=%s, deviceId=%s", tenantId, deviceId)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: DeviceInfoEntity device = deviceInfoCache.getCacheById(tenantId, deviceId); return ResponseMessage.buildSuccess(device);
	result := service.DeviceInfoService.GetById(c, tenantId, deviceId)

	// 4. 返回响应
	c.JSON(http.StatusOK, result)
}
