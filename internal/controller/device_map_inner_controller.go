package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/internal/service"
	"piceacorp.com/device-service/pkg/logger"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// InnerGetCurMap 获取设备当前地图 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceMapInnerController.getCurMap(@RequestParam String sn)
// GET /inner/device/map/getCurMap
func InnerGetCurMap(c *gin.Context) {
	// 1. 获取请求参数 - 对应 Java: @RequestParam String sn (遵循规则31: @RequestParam默认必传)
	sn := c.Query("sn")

	// 2. 参数验证 - 对应 Java: 无显式验证，但Service层有验证 (遵循规则31: 严格按照java代码校验)
	if sn == "" {
		logger.Errorf("参数验证失败: sn不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "sn")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到获取设备当前地图请求: sn=%s", sn)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: return deviceMapService.getCurMap(sn);
	result := service.DeviceMapService.GetCurMap(c, sn)

	// 4. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	c.JSON(http.StatusOK, result)
}
