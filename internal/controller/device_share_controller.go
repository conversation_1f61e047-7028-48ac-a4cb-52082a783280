package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/service"
	"piceacorp.com/device-service/pkg/logger"
)

// GetShareHis 获取分享历史
// 对应 Java: DeviceInviteShareController.getShareHis
// POST /app/shared/history
func GetShareHis(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证
	var shareHisReq req.UserSharedHisReq
	if err := c.ShouldBindJSON(&shareHisReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到获取分享历史请求: targetId=%s, type=%d, page=%d, pageSize=%d",
		shareHisReq.TargetId, shareHisReq.Type, shareHisReq.Page, shareHisReq.PageSize)

	// 2. 调用服务层处理业务逻辑
	result := service.DeviceInviteShareService.GetShareHis(c, &shareHisReq)

	// 3. 返回响应 - 永远使用 c.JSON(http.StatusOK, response)
	c.JSON(http.StatusOK, result)
}

// ShareRobot 分享设备给指定用户
// 对应 Java: DeviceAppController.shareRobot
// POST /app/share
func ShareRobot(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证
	var shareReq req.ShareReq
	if err := c.ShouldBindJSON(&shareReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到分享设备请求: beInvited=%s, targetCount=%d", shareReq.BeInvited, len(shareReq.TargetIds))

	// 2. 调用服务层处理业务逻辑
	result := service.DeviceInviteShareService.InviteUserShareRobot(c, &shareReq)

	// 3. 返回响应 - 永远使用 c.JSON(http.StatusOK, response)
	c.JSON(http.StatusOK, result)
}

// SharedRobotReply 回应用户的分享
// 对应 Java: DeviceAppController.sharedRobotReply
// POST /app/shared/reply
func SharedRobotReply(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证
	var replyReq req.ShareReplyReq
	if err := c.ShouldBindJSON(&replyReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到回应分享请求: inviterId=%s, targetId=%s, type=%d, dealType=%d",
		replyReq.InviterId, replyReq.TargetId, replyReq.Type, replyReq.DealType)

	// 2. 调用服务层处理业务逻辑
	result := service.DeviceInviteShareService.ReplyShared(c, &replyReq)

	// 3. 返回响应 - 永远使用 c.JSON(http.StatusOK, response)
	c.JSON(http.StatusOK, result)
}
