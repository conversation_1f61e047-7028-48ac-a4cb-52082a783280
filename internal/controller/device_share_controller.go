package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/service"
	"piceacorp.com/device-service/pkg/logger"
)

// GetShareHis 获取分享历史
// 对应 Java: DeviceInviteShareController.getShareHis
// POST /app/shared/history
func GetShareHis(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证
	var shareHisReq req.UserSharedHisReq
	if err := c.ShouldBindJSON(&shareHisReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到获取分享历史请求: targetId=%s, type=%d, page=%d, pageSize=%d",
		shareHisReq.TargetId, shareHisReq.Type, shareHisReq.Page, shareHisReq.PageSize)

	// 2. 调用服务层处理业务逻辑
	result := service.DeviceInviteShareService.GetShareHis(c, &shareHisReq)

	// 3. 返回响应 - 永远使用 c.JSON(http.StatusOK, response)
	c.JSON(http.StatusOK, result)
}

// ShareRobot 分享设备给指定用户
// 对应 Java: DeviceAppController.shareRobot
// POST /app/share
func ShareRobot(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证
	var shareReq req.ShareReq
	if err := c.ShouldBindJSON(&shareReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到分享设备请求: beInvited=%s, targetCount=%d", shareReq.BeInvited, len(shareReq.TargetIds))

	// 2. 调用服务层处理业务逻辑
	result := service.DeviceInviteShareService.InviteUserShareRobot(c, &shareReq)

	// 3. 返回响应 - 永远使用 c.JSON(http.StatusOK, response)
	c.JSON(http.StatusOK, result)
}

// SharedRobotReply 回应用户的分享
// 对应 Java: DeviceAppController.sharedRobotReply
// POST /app/shared/reply
func SharedRobotReply(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证
	var replyReq req.ShareReplyReq
	if err := c.ShouldBindJSON(&replyReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到回应分享请求: inviterId=%s, targetId=%s, type=%d, dealType=%d",
		replyReq.InviterId, replyReq.TargetId, replyReq.Type, replyReq.DealType)

	// 2. 调用服务层处理业务逻辑
	result := service.DeviceInviteShareService.ReplyShared(c, &replyReq)

	// 3. 返回响应 - 永远使用 c.JSON(http.StatusOK, response)
	c.JSON(http.StatusOK, result)
}

// GetShareDevice 获取用户的分享设备信息
// 对应 Java: DeviceAppController.getShareRobot(@RequestBody GetUserSharedReq req)
// POST /app/shared/device
func GetShareDevice(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var getUserSharedReq req.GetUserSharedReq
	if err := c.ShouldBindJSON(&getUserSharedReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到获取用户分享设备信息请求: inviter=%v, targetId=%s, page=%d, pageSize=%d",
		getUserSharedReq.Inviter, getUserSharedReq.TargetId, getUserSharedReq.Page, getUserSharedReq.PageSize)

	// 2. 调用服务层处理业务逻辑 - 对应 Java: return deviceInviteShareService.getShareRobot(req);
	result := service.DeviceInviteShareService.GetShareRobot(c, &getUserSharedReq)

	// 3. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	c.JSON(http.StatusOK, result)
}

// ChangeShareRecordStatus 通过极光推送的消息拒绝或同意分享
// 对应 Java: DeviceAppController.changeShareRecordStatus(@RequestBody ShareRecordChangeStatusReq req)
// POST /app/share/record/change/status
func ChangeShareRecordStatus(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var shareRecordChangeStatusReq req.ShareRecordChangeStatusReq
	if err := c.ShouldBindJSON(&shareRecordChangeStatusReq); err != nil {
		c.Error(err)
		return
	}

	logger.Infof("收到通过极光推送的消息拒绝或同意分享请求: shareId=%s, recordId=%s, status=%d",
		shareRecordChangeStatusReq.ShareId, shareRecordChangeStatusReq.RecordId, shareRecordChangeStatusReq.Status)

	// 2. 调用服务层处理业务逻辑 - 对应 Java: return deviceInviteShareService.changeShareRecordStatus(req);
	result := service.DeviceInviteShareService.ChangeShareRecordStatus(c, &shareRecordChangeStatusReq)

	// 3. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	c.JSON(http.StatusOK, result)
}
