package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/service"
	"piceacorp.com/device-service/pkg/logger"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// InnerPublishDevices 根据升级包主动下发所有符合条件设备升级 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceUpgradeController.publishDevices(@RequestBody PackageInfoVo info)
// POST /inner/device/publishDevices
func InnerPublishDevices(c *gin.Context) {
	// 1. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var packageInfoReq req.PackageInfoReq
	if err := c.ShouldBindJSON(&packageInfoReq); err != nil {
		c.Error(err)
		return
	}

	// 2. 参数验证 - 对应 Java: if (null == info || StringUtils.isBlank(info.getProductModelCode()) || StringUtils.isBlank(info.getId()))
	if packageInfoReq.ProductModelCode == "" {
		logger.Errorf("参数验证失败: productModelCode不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "")
		c.JSON(http.StatusOK, result)
		return
	}

	if packageInfoReq.Id == "" {
		logger.Errorf("参数验证失败: id不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到发布设备升级请求: productModelCode=%s, id=%s",
		packageInfoReq.ProductModelCode, packageInfoReq.Id)

	// 3. 调用服务层处理业务逻辑 - 对应 Java: deviceUpgradeService.publishDevices(info); return ResponseMessage.buildSuccess();
	// 注意：Java中这是异步方法，Go中也使用异步处理
	go service.DeviceUpgradeService.PublishDevices(c, &packageInfoReq)

	// 4. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	result := webRes.Cbnd() // 对应 Java: ResponseMessage.buildSuccess()
	c.JSON(http.StatusOK, result)
}

// InnerCreatePushUpgradeTask 创建OTA主动下发任务详情信息 (遵循规则36: inner/前缀接口加Inner前缀)
// 对应 Java: DeviceUpgradeController.createPushUpgradeTask(@RequestHeader("tenantId") String tenantId, @RequestBody PackageInfoVo info)
// POST /inner/device/createPushUpgradeTask
func InnerCreatePushUpgradeTask(c *gin.Context) {
	// 1. 获取请求头参数 - 对应 Java: @RequestHeader("tenantId") String tenantId (遵循规则31: @RequestParam默认必传)
	tenantId := c.GetHeader("tenantId")
	if tenantId == "" {
		logger.Errorf("参数验证失败: tenantId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "tenantId")
		c.JSON(http.StatusOK, result)
		return
	}

	// 2. 绑定请求参数 - 使用 validator 框架验证 (遵循规则12)
	var packageInfoReq req.PackageInfoReq
	if err := c.ShouldBindJSON(&packageInfoReq); err != nil {
		c.Error(err)
		return
	}

	// 3. 参数验证 - 对应 Java: if (null == info || StringUtils.isBlank(info.getTenantId()) || StringUtils.isBlank(info.getProductModelCode()) || StringUtils.isBlank(info.getPushTaskId()))
	if packageInfoReq.TenantId == "" {
		logger.Errorf("参数验证失败: tenantId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "")
		c.JSON(http.StatusOK, result)
		return
	}

	if packageInfoReq.ProductModelCode == "" {
		logger.Errorf("参数验证失败: productModelCode不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "")
		c.JSON(http.StatusOK, result)
		return
	}

	if packageInfoReq.PushTaskId == "" {
		logger.Errorf("参数验证失败: pushTaskId不能为空")
		result := webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "")
		c.JSON(http.StatusOK, result)
		return
	}

	logger.Infof("收到创建推送升级任务请求: tenantId=%s, productModelCode=%s, pushTaskId=%s",
		tenantId, packageInfoReq.ProductModelCode, packageInfoReq.PushTaskId)

	// 4. 调用服务层处理业务逻辑 - 对应 Java: deviceUpgradeService.createPushUpgradeTask(info); return ResponseMessage.buildSuccess();
	// 注意：Java中这是异步方法，Go中也使用异步处理
	go service.DeviceUpgradeService.CreatePushUpgradeTask(c, &packageInfoReq)

	// 5. 返回响应 (遵循规则11: 使用c.JSON(http.StatusOK, response)格式)
	result := webRes.Cbnd() // 对应 Java: ResponseMessage.buildSuccess()
	c.JSON(http.StatusOK, result)
}
