package controller

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/service"
)

// RegisterRoutes 注册所有路由
func RegisterRoutes(r *gin.Engine) {
	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		//panic("has custom error")
		c.JSON(200, gin.H{
			"status": "UP",
		})
	})

	r.POST("/auth/login", Login)

	// 设备绑定相关路由
	r.POST("/app/bind/confirm", BindConfirm)
	r.GET("/app/bind/listByUserId", ListByUserId)

	// 设备分享相关路由
	r.POST("/app/shared/history", GetShareHis)
	r.POST("/app/share", ShareRobot)
	r.POST("/app/shared/reply", SharedRobotReply)
	r.POST("/app/shared/device", GetShareDevice)
	r.POST("/app/share/record/change/status", ChangeShareRecordStatus)

	// 设备相关路由
	r.POST("/device/bind/app", BindApp)
	r.PUT("/app/modify/nickname", ModifyNickname)
	r.POST("/app/bind/untie", UntieDevice)
	r.DELETE("/app/bind/untie", UntieDevice)
	r.DELETE("/app/shared/del", DelShareRobot)
	r.POST("/app/bind/orCode", OrCodeBindConfirm)
	r.GET("/app/bindKey", GetBindKey)

	// 内部服务调用路由 - 基站绑定相关 (遵循规则36: inner/前缀接口方法加Inner前缀)
	r.POST("/inner/bindBase/bindListBySn", InnerBindListBySn)
	r.POST("/inner/bindBase/baseBindDevice", InnerBaseBindDevice)

	// 内部服务调用路由 - 设备绑定相关 (遵循规则36: inner/前缀接口方法加Inner前缀)
	r.POST("/inner/bind/untie", InnerUntieDevice)
	r.GET("/inner/bind/bindListByUserId", InnerBindListByUserId)
	r.POST("/inner/bind/syncBind", InnerSyncBind)
	r.GET("/inner/bind/getBindList", InnerGetBindList)
	r.DELETE("/inner/bind/deleteByIds", InnerDeleteByIds)
	r.POST("/inner/bind/getOwnerListById", InnerGetOwnerListById)
	r.DELETE("/inner/bind/deleteByUserIds", InnerDeleteByUserIds)
	r.POST("/inner/bind/addOnlineAppSnCacheForTempMap", InnerAddOnlineAppSnCacheForTempMap)

	// 内部服务调用路由 - 设备相关 (遵循规则36: inner/前缀接口方法加Inner前缀)
	r.GET("/inner/device/status", InnerGetRobotStatus)
	r.GET("/inner/device/state", InnerGetRobotState)
	r.GET("/inner/device/id", InnerGetRobotId)
	r.GET("/inner/device/info", InnerGetRobotDetail)
	r.GET("/inner/device/active", InnerIsActivatedRobot)
	r.GET("/inner/device/sn", InnerSelectAllRobotSn)
	r.GET("/inner/device/getDeviceCountByPro", InnerGetDeviceCountByPro)
	r.POST("/inner/device/control", InnerControl)
	r.GET("/inner/device/classify", InnerClassify)
	r.DELETE("/inner/device/share", InnerDelShare)
	r.POST("/inner/device/getDeviceInfosByIds", InnerGetDeviceInfosByIds)
	r.POST("/inner/device/getDeviceNickNameByIds", InnerGetDeviceNickNameByIds)
	r.GET("/inner/device/productModeCode", InnerGetProductModeCode)
	r.GET("/inner/device/count/productModeCode", InnerGetDeviceCountByProductModeCode)
	r.PUT("/inner/device/update/verifyPassword", InnerUpdateVerifyPassword)
	r.PUT("/inner/device/list/sns", InnerGetDeviceSnByTenantId)
	r.PUT("/inner/device/resetNickname", InnerResetNickname)
	r.PUT("/inner/device/update/photo", InnerUpdatePhoto)
	r.GET("/inner/device/getDeviceInfoById/:id", InnerGetDeviceInfoById)
	r.POST("/inner/device/getActiveDeviceSNByCode", InnerGetActiveDeviceSNByCode)
	r.POST("/inner/device/reset/device/:deviceId", InnerResetDevice)
	r.GET("/inner/device/getById", InnerGetById)

	// 内部服务调用路由 - 设备地图相关 (遵循规则36: inner/前缀接口方法加Inner前缀)
	r.GET("/inner/device/map/getCurMap", InnerGetCurMap)

	// 内部服务调用路由 - 设备分享相关 (遵循规则36: inner/前缀接口方法加Inner前缀)
	r.POST("/inner/share/family", InnerShareFamily)
	r.POST("/inner/share/remove", InnerRemoveShare)
	r.DELETE("/inner/share/del/userId", InnerDelByUserId)

	// 内部服务调用路由 - 设备升级相关 (遵循规则36: inner/前缀接口方法加Inner前缀)
	r.POST("/inner/device/publishDevices", InnerPublishDevices)
	r.POST("/inner/device/createPushUpgradeTask", InnerCreatePushUpgradeTask)

	// 内部服务调用路由 - SN激活相关 (遵循规则36: inner/前缀接口方法加Inner前缀)
	r.POST("/inner/sn/active/insert", InnerSaveActiveSn)
	r.DELETE("/inner/sn/active/sn", InnerDelBySnSingle)
	r.DELETE("/inner/sn/active/sns", InnerDelBySnList)
	r.GET("/inner/sn/active/countByGroupId", InnerCountByGroupId)
	r.GET("/inner/sn/active/getBySn", InnerGetBySn)
	r.GET("/inner/sn/active/snCodes", InnerSnCodes)
	r.GET("/inner/sn/active/getBySnList", InnerGetBySnList)

	// 基站相关路由
	r.GET("/base/baseBindList", BaseBindList)
	r.POST("/base/baseUnAuth", BaseUnAuth)

	// MQTT主题相关路由 (遵循规则36: inner/前缀接口方法加Inner前缀)
	r.POST("/inner/mqtt/propertyGetHandler", InnerPropertyGetHandler)

	// 开放平台API路由
	r.POST("/open/device/batchUnBind", BatchUnBind)
	r.POST("/open/bd/skill", BdSkill)
	r.POST("/open/largemodel/getReply", LargeModelGetReply)

	// RTC设备相关路由
	r.POST("/device/rtc/activateDevice", ActivateDevice)

	// 视频设备相关路由
	r.GET("/app/video/getKey", GetKey)

}

func Login(c *gin.Context) {
	var loginReq req.LoginReq
	if err := c.ShouldBindJSON(&loginReq); err != nil {
		c.Error(err)
		return
	}

	// 处理登录逻辑
	loginRes := service.DeviceAuthService.Login(c, &loginReq)

	// 保存设备版本流水 - 对应 Java: deviceVersionLogService.save(sn, loginReq.getPackageVersions());

	service.DeviceVersionLogService.Save(c, loginReq.SN, loginReq.PackageVersions)

	c.JSON(http.StatusOK, loginRes)
}
