package bo

// BaseBindBo 基站绑定信息BO
// 对应 Java: BaseBindVo
type BaseBindBo struct {
	// 基站ID - 对应 Java: private String baseId;
	BaseId string `gorm:"column:base_id" json:"baseId"`
	
	// 设备ID - 对应 Java: private String deviceId;
	DeviceId string `gorm:"column:device_id" json:"deviceId"`
	
	// 设备SN - 对应 Java: private String sn;
	Sn string `gorm:"column:sn" json:"sn"`
	
	// 拥有者 - 对应 Java: private String owner;
	Owner string `gorm:"column:owner" json:"owner"`
	
	// MAC地址 - 对应 Java: private String mac;
	Mac string `gorm:"column:mac" json:"mac"`
	
	// 设备昵称 - 对应 Java: private String nickname;
	Nickname string `gorm:"column:nickname" json:"nickname"`
}
