package bo

import "math"

// PageResult 分页结果结构
type PageResult[T any] struct {
	// 数据列表
	Records []T `json:"records"`
	// 总记录数
	Total int64 `json:"total"`
	// 每页大小
	Size int `json:"size"`
	// 当前页码
	Current int `json:"current"`
	// 总页数
	Pages int `json:"pages"`
}

// NewPageResult 创建分页结果
func NewPageResult[T any](records []T, total int64, current, size int) *PageResult[T] {
	pages := int(math.Ceil(float64(total) / float64(size)))
	if pages == 0 {
		pages = 1
	}

	return &PageResult[T]{
		Records: records,
		Total:   total,
		Size:    size,
		Current: current,
		Pages:   pages,
	}
}
