package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/dao/repository"
	"piceacorp.com/device-service/pkg/data/cache"
	"piceacorp.com/device-service/pkg/logger"
)

const (
	// 设备信息缓存前缀 (按ID)
	DEVICE_INFO_PREFIX = "device_info_id_"
	// 设备信息缓存前缀 (按SN)
	DEVICE_INFO_SN_PREFIX = "device_info_sn_"
	// 设备信息默认过期时间 (30分钟)
	DEVICE_INFO_DEFAULT_TTL = 30 * time.Minute
)

// IDeviceInfoCache 设备信息缓存接口
type IDeviceInfoCache interface {
	// 根据设备ID获取缓存
	GetCacheById(ctx context.Context, tenantId, deviceId string) (*domain.DeviceInfo, error)
	// 根据设备SN获取缓存
	GetCacheBySn(ctx context.Context, sn, tenantId string) (*domain.DeviceInfo, error)
	// 保存设备信息到缓存
	SaveCache(ctx context.Context, tenantId, deviceId string, deviceInfo *domain.DeviceInfo) error
	// 保存设备信息到缓存并设置过期时间
	SaveCacheWithExpire(ctx context.Context, tenantId, deviceId string, deviceInfo *domain.DeviceInfo, ttl time.Duration) error
	// 删除设备信息缓存
	RemoveCache(ctx context.Context, tenantId, deviceId string) error
	// 检查设备信息缓存是否存在
	ExistsCache(ctx context.Context, tenantId, deviceId string) (bool, error)
	// 清除设备缓存 - 对应 Java: clear(String tenantId, DeviceInfoEntity deviceInfo) (遵循规则2: 完整转换所有使用的方法)
	Clear(ctx context.Context, tenantId string, deviceInfo *domain.DeviceInfo) error
	// 批量清除设备缓存 - 对应 Java: clearMore(List<DeviceInfoEntity> deviceInfos) (遵循规则2: 完整转换所有使用的方法)
	ClearMore(ctx context.Context, deviceInfos []*domain.DeviceInfo) error
	// 添加设备ID或SN到布隆过滤器 - 对应 Java: addIdOrSnBloomFilter(String deviceId) (遵循规则2: 完整转换所有使用的方法)
	AddIdOrSnBloomFilter(ctx context.Context, deviceId string) error
	// 批量清除设备缓存 - 对应 Java: clearCache(String tenantId, List<String> deviceIds) (遵循规则2: 完整转换所有使用的方法)
	ClearCache(ctx context.Context, tenantId string, deviceIds []string) error
}

// deviceInfoCache 设备信息缓存实现
type deviceInfoCache struct{}

var DeviceInfoCache IDeviceInfoCache = &deviceInfoCache{}

// buildDeviceInfoKey 构建设备信息缓存键 (按ID)
func (c *deviceInfoCache) buildDeviceInfoKey(tenantId, deviceId string) string {
	return DEVICE_INFO_PREFIX + tenantId + "_" + deviceId
}

// buildDeviceInfoSnKey 构建设备信息缓存键 (按SN)
func (c *deviceInfoCache) buildDeviceInfoSnKey(tenantId, sn string) string {
	return DEVICE_INFO_SN_PREFIX + tenantId + "_" + sn
}

// GetCacheById 根据设备ID获取缓存
// 对应 Java: deviceInfoCache.getCacheById(deviceId)
func (c *deviceInfoCache) GetCacheById(ctx context.Context, tenantId, deviceId string) (*domain.DeviceInfo, error) {
	key := c.buildDeviceInfoKey(tenantId, deviceId)

	result, err := cache.Rdb.Get(ctx, key).Result()
	if err == nil && len(result) > 0 {
		var deviceInfo domain.DeviceInfo
		err = json.Unmarshal([]byte(result), &deviceInfo)
		if err != nil {
			logger.Errorf("反序列化设备信息失败: deviceId=%s, error=%v", deviceId, err)
			return nil, err
		}
		logger.Debugf("获取设备信息缓存成功: deviceId=%s", deviceId)
		return &deviceInfo, nil
	}

	deviceInfo, err := repository.DeviceRepository.FindById(ctx, deviceId)
	if err != nil {
		return nil, err
	}

	err = c.SaveCache(ctx, tenantId, deviceId, deviceInfo)
	if err != nil {
		return nil, err
	}
	return deviceInfo, nil
}

// GetCacheBySn 根据设备SN获取缓存
// 对应 Java: deviceInfoCache.getCacheBySn(tenantId, sn)
func (c *deviceInfoCache) GetCacheBySn(ctx context.Context, sn, tenantId string) (*domain.DeviceInfo, error) {
	// 获取租户ID - 对应 Java: getCacheBySn(tenantId, sn)
	//tenantId := ctx.Value(ctxKeys.TENANT_ID)
	if tenantId == "" {
		logger.Errorf("获取租户ID失败: sn=%s", sn)
		return nil, fmt.Errorf("获取租户ID失败")
	}

	key := c.buildDeviceInfoSnKey(tenantId, sn)

	// 先从缓存获取
	result, err := cache.Rdb.Get(ctx, key).Result()
	if err != nil {
		if err.Error() == "redis: nil" {
			// 缓存不存在，从数据库查询
			logger.Debugf("设备信息缓存不存在，从数据库查询: sn=%s", sn)
			return c.getDeviceFromDBBySn(ctx, sn, tenantId)
		}
		logger.Errorf("获取设备信息缓存失败: sn=%s, error=%v", sn, err)
		return nil, err
	}

	var deviceInfo domain.DeviceInfo
	err = json.Unmarshal([]byte(result), &deviceInfo)
	if err != nil {
		logger.Errorf("反序列化设备信息失败: sn=%s, error=%v", sn, err)
		return nil, err
	}

	logger.Debugf("获取设备信息缓存成功: sn=%s", sn)
	return &deviceInfo, nil
}

// SaveCache 保存设备信息到缓存 (使用默认过期时间)
func (c *deviceInfoCache) SaveCache(ctx context.Context, tenantId, deviceId string, deviceInfo *domain.DeviceInfo) error {
	return c.SaveCacheWithExpire(ctx, tenantId, deviceId, deviceInfo, DEVICE_INFO_DEFAULT_TTL)
}

// SaveCacheWithExpire 保存设备信息到缓存并设置过期时间
func (c *deviceInfoCache) SaveCacheWithExpire(ctx context.Context, tenantId, deviceId string, deviceInfo *domain.DeviceInfo, ttl time.Duration) error {
	if deviceInfo == nil {
		return fmt.Errorf("设备信息不能为空")
	}

	key := c.buildDeviceInfoKey(tenantId, deviceId)

	data, err := json.Marshal(deviceInfo)
	if err != nil {
		logger.Errorf("序列化设备信息失败: deviceId=%s, error=%v", deviceId, err)
		return err
	}

	err = cache.Rdb.Set(ctx, key, data, ttl).Err()
	if err != nil {
		logger.Errorf("保存设备信息缓存失败: deviceId=%s, ttl=%v, error=%v", deviceId, ttl, err)
		return err
	}

	logger.Infof("保存设备信息缓存成功: deviceId=%s, ttl=%v", deviceId, ttl)
	return nil
}

// RemoveCache 删除设备信息缓存
func (c *deviceInfoCache) RemoveCache(ctx context.Context, tenantId, deviceId string) error {
	key := c.buildDeviceInfoKey(tenantId, deviceId)

	result, err := cache.Rdb.Del(ctx, key).Result()
	if err != nil {
		logger.Errorf("删除设备信息缓存失败: deviceId=%s, error=%v", deviceId, err)
		return err
	}

	logger.Infof("删除设备信息缓存成功: deviceId=%s, deleted=%d", deviceId, result)
	return nil
}

// ExistsCache 检查设备信息缓存是否存在
func (c *deviceInfoCache) ExistsCache(ctx context.Context, tenantId, deviceId string) (bool, error) {
	key := c.buildDeviceInfoKey(tenantId, deviceId)

	result, err := cache.Rdb.Exists(ctx, key).Result()
	if err != nil {
		logger.Errorf("检查设备信息缓存是否存在失败: deviceId=%s, error=%v", deviceId, err)
		return false, err
	}

	exists := result > 0
	logger.Debugf("检查设备信息缓存是否存在: deviceId=%s, exists=%v", deviceId, exists)
	return exists, nil
}

// getDeviceFromDBBySn 从数据库根据SN获取设备信息并缓存
// 对应 Java: deviceInfoMapper.getBySn(tenantId, sn)
func (c *deviceInfoCache) getDeviceFromDBBySn(ctx context.Context, sn, tenantId string) (*domain.DeviceInfo, error) {
	// 从数据库查询 - 对应 Java: deviceInfoMapper.getBySn(tenantId, sn)
	deviceInfo, err := repository.DeviceRepository.FindBySN(ctx, sn)
	if err != nil {
		logger.Errorf("从数据库查询设备信息失败: sn=%s, error=%v", sn, err)
		return nil, err
	}

	// 如果查询到数据，保存到缓存
	if deviceInfo != nil {
		key := c.buildDeviceInfoSnKey(tenantId, sn)
		data, err := json.Marshal(deviceInfo)
		if err != nil {
			logger.Errorf("序列化设备信息失败: sn=%s, error=%v", sn, err)
			// 序列化失败不影响返回数据
		} else {
			err = cache.Rdb.Set(ctx, key, data, DEVICE_INFO_DEFAULT_TTL).Err()
			if err != nil {
				logger.Errorf("保存设备信息缓存失败: sn=%s, error=%v", sn, err)
				// 缓存失败不影响返回数据
			} else {
				logger.Debugf("保存设备信息缓存成功: sn=%s", sn)
			}
		}
	}

	return deviceInfo, nil
}

// Clear 清除设备缓存
// 对应 Java: DeviceInfoCache.clear(String tenantId, DeviceInfoEntity deviceInfo) (遵循规则42: 严格按照Java编码内容转换)
func (c *deviceInfoCache) Clear(ctx context.Context, tenantId string, deviceInfo *domain.DeviceInfo) error {
	if deviceInfo == nil {
		logger.Errorf("设备信息不能为空")
		return fmt.Errorf("设备信息不能为空")
	}

	// 对应 Java: String idKey = "deviceInfo::device_info_id_".concat(tenantId).concat("_").concat(deviceInfo.getId());
	idKey := fmt.Sprintf("deviceInfo::device_info_id_%s_%s", tenantId, deviceInfo.Id)

	// 对应 Java: String snKey = "deviceInfo::device_info_sn_".concat(tenantId).concat("_").concat(deviceInfo.getSn());
	snKey := fmt.Sprintf("deviceInfo::device_info_sn_%s_%s", tenantId, deviceInfo.Sn)

	// 对应 Java: List<String> keys = Arrays.asList(idKey, snKey); redisTemplate.delete(keys);
	keys := []string{idKey, snKey}

	// 删除缓存
	deletedCount, err := cache.Rdb.Del(ctx, keys...).Result()
	if err != nil {
		logger.Errorf("清除设备缓存失败: tenantId=%s, deviceId=%s, sn=%s, keys=%v, error=%v",
			tenantId, deviceInfo.Id, deviceInfo.Sn, keys, err)
		return err
	}

	logger.Infof("清除设备缓存成功: tenantId=%s, deviceId=%s, sn=%s, deletedCount=%d",
		tenantId, deviceInfo.Id, deviceInfo.Sn, deletedCount)
	return nil
}

// ClearMore 批量清除设备缓存
// 对应 Java: DeviceInfoCache.clearMore(List<DeviceInfoEntity> deviceInfos) (遵循规则42: 严格按照Java编码内容转换)
func (c *deviceInfoCache) ClearMore(ctx context.Context, deviceInfos []*domain.DeviceInfo) error {
	// 对应 Java: if (CollectionUtils.isEmpty(deviceInfos)) { return; }
	if len(deviceInfos) == 0 {
		logger.Debugf("设备信息列表为空，无需清除缓存")
		return nil
	}

	// 对应 Java: List<String> keys = getKeys(deviceInfos); redisTemplate.delete(keys);
	keys := c.getKeys(deviceInfos)
	if len(keys) == 0 {
		logger.Debugf("没有有效的缓存key，无需清除缓存")
		return nil
	}

	// 删除缓存
	deletedCount, err := cache.Rdb.Del(ctx, keys...).Result()
	if err != nil {
		logger.Errorf("批量清除设备缓存失败: deviceCount=%d, keyCount=%d, error=%v",
			len(deviceInfos), len(keys), err)
		return err
	}

	logger.Infof("批量清除设备缓存成功: deviceCount=%d, keyCount=%d, deletedCount=%d",
		len(deviceInfos), len(keys), deletedCount)
	return nil
}

// getKeys 获取多个缓存keys
// 对应 Java: DeviceInfoCache.getKeys(List<DeviceInfoEntity> deviceInfos) (遵循规则42: 严格按照Java编码内容转换)
func (c *deviceInfoCache) getKeys(deviceInfos []*domain.DeviceInfo) []string {
	// 对应 Java: if (CollectionUtils.isEmpty(deviceInfos)) { throw new AppRuntimeException("设备信息不能为空"); }
	if len(deviceInfos) == 0 {
		logger.Errorf("设备信息不能为空")
		return []string{}
	}

	var keys []string
	// 对应 Java: String nameSpace = "deviceInfo::device_info_";
	nameSpace := "deviceInfo::device_info_"

	// 对应 Java: for (DeviceInfoEntity device : deviceInfos) { if (Objects.isNull(device)) { continue; } ... }
	for _, device := range deviceInfos {
		if device == nil {
			continue
		}

		// 对应 Java: stringBuilderId = new StringBuilder(nameSpace).append("id_").append(device.getTenantId()).append("_").append(device.getId());
		idKey := fmt.Sprintf("%sid_%s_%s", nameSpace, device.TenantId, device.Id)

		// 对应 Java: stringBuilderSn = new StringBuilder(nameSpace).append("sn_").append(device.getTenantId()).append("_").append(device.getSn());
		snKey := fmt.Sprintf("%ssn_%s_%s", nameSpace, device.TenantId, device.Sn)

		// 对应 Java: list.add(stringBuilderId.toString()); list.add(stringBuilderSn.toString());
		keys = append(keys, idKey, snKey)
	}

	return keys
}

// AddIdOrSnBloomFilter 添加设备ID或SN到布隆过滤器
// 对应 Java: DeviceInfoCache.addIdOrSnBloomFilter(String deviceId) (遵循规则42: 严格按照Java编码内容转换)
func (c *deviceInfoCache) AddIdOrSnBloomFilter(ctx context.Context, deviceId string) error {
	// 对应 Java: bloomFilter.addByBloomFilter(filterHelper, DEVICE_ID_SN_BLOOM_FILTER_KEY, deviceId);
	// 注意：Go版本暂时不实现布隆过滤器，只记录日志
	// 如果需要实现布隆过滤器，需要引入相应的布隆过滤器库
	logger.Infof("添加设备ID到布隆过滤器: deviceId=%s", deviceId)

	// TODO: 实现布隆过滤器逻辑
	// const DEVICE_ID_SN_BLOOM_FILTER_KEY = "bf:deviceInfo:id_sn"
	// 这里需要实现布隆过滤器的添加逻辑

	return nil
}

// ClearCache 批量清除设备缓存
// 对应 Java: DeviceInfoServiceImpl.clearCache(String tenantId, List<String> deviceIds) (遵循规则42: 严格按照Java编码内容转换)
func (c *deviceInfoCache) ClearCache(ctx context.Context, tenantId string, deviceIds []string) error {
	// 对应 Java: if (CollectionUtils.isEmpty(deviceIds)) { return; }
	if len(deviceIds) == 0 {
		logger.Debugf("设备ID列表为空，无需清除缓存")
		return nil
	}

	// 对应 Java: for (String deviceId : deviceIds) { DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheById(tenantId, deviceId); if (Objects.nonNull(deviceInfo)) { deviceInfoCache.clear(tenantId, deviceInfo); } }
	for _, deviceId := range deviceIds {
		deviceInfo, err := c.GetCacheById(ctx, tenantId, deviceId)
		if err != nil {
			logger.Errorf("获取设备缓存失败: deviceId=%s, error=%v", deviceId, err)
			continue
		}
		if deviceInfo != nil {
			err = c.Clear(ctx, tenantId, deviceInfo)
			if err != nil {
				logger.Errorf("清除设备缓存失败: deviceId=%s, error=%v", deviceId, err)
				// 继续处理其他设备，不中断整个流程
			}
		}
	}

	logger.Infof("批量清除设备缓存完成: tenantId=%s, deviceCount=%d", tenantId, len(deviceIds))
	return nil
}
