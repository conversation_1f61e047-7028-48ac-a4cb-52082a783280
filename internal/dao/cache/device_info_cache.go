package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/dao/repository"
	"piceacorp.com/device-service/pkg/data/cache"
	"piceacorp.com/device-service/pkg/logger"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
)

const (
	// 设备信息缓存前缀 (按ID)
	DEVICE_INFO_PREFIX = "device:info:"
	// 设备信息缓存前缀 (按SN)
	DEVICE_INFO_SN_PREFIX = "device:info:sn:"
	// 设备信息默认过期时间 (30分钟)
	DEVICE_INFO_DEFAULT_TTL = 30 * time.Minute
)

// IDeviceInfoCache 设备信息缓存接口
type IDeviceInfoCache interface {
	// 根据设备ID获取缓存
	GetCacheById(ctx context.Context, deviceId string) (*domain.DeviceInfo, error)
	// 根据设备SN获取缓存
	GetCacheBySn(ctx context.Context, sn string) (*domain.DeviceInfo, error)
	// 保存设备信息到缓存
	SaveCache(ctx context.Context, deviceId string, deviceInfo *domain.DeviceInfo) error
	// 保存设备信息到缓存并设置过期时间
	SaveCacheWithExpire(ctx context.Context, deviceId string, deviceInfo *domain.DeviceInfo, ttl time.Duration) error
	// 删除设备信息缓存
	RemoveCache(ctx context.Context, deviceId string) error
	// 检查设备信息缓存是否存在
	ExistsCache(ctx context.Context, deviceId string) (bool, error)
}

// deviceInfoCache 设备信息缓存实现
type deviceInfoCache struct{}

var DeviceInfoCache IDeviceInfoCache = &deviceInfoCache{}

// buildDeviceInfoKey 构建设备信息缓存键 (按ID)
func (c *deviceInfoCache) buildDeviceInfoKey(deviceId string) string {
	return DEVICE_INFO_PREFIX + deviceId
}

// buildDeviceInfoSnKey 构建设备信息缓存键 (按SN)
func (c *deviceInfoCache) buildDeviceInfoSnKey(sn string) string {
	return DEVICE_INFO_SN_PREFIX + sn
}

// GetCacheById 根据设备ID获取缓存
// 对应 Java: deviceInfoCache.getCacheById(deviceId)
func (c *deviceInfoCache) GetCacheById(ctx context.Context, deviceId string) (*domain.DeviceInfo, error) {
	key := c.buildDeviceInfoKey(deviceId)
	
	result, err := cache.Rdb.Get(ctx, key).Result()
	if err != nil {
		if err.Error() == "redis: nil" {
			logger.Debugf("设备信息缓存不存在: deviceId=%s", deviceId)
			return nil, nil
		}
		logger.Errorf("获取设备信息缓存失败: deviceId=%s, error=%v", deviceId, err)
		return nil, err
	}
	
	var deviceInfo domain.DeviceInfo
	err = json.Unmarshal([]byte(result), &deviceInfo)
	if err != nil {
		logger.Errorf("反序列化设备信息失败: deviceId=%s, error=%v", deviceId, err)
		return nil, err
	}
	
	logger.Debugf("获取设备信息缓存成功: deviceId=%s", deviceId)
	return &deviceInfo, nil
}

// GetCacheBySn 根据设备SN获取缓存
// 对应 Java: deviceInfoCache.getCacheBySn(tenantId, sn)
func (c *deviceInfoCache) GetCacheBySn(ctx context.Context, sn string) (*domain.DeviceInfo, error) {
	// 获取租户ID - 对应 Java: getCacheBySn(tenantId, sn)
	tenantId := ctx.Value(ctxKeys.TENANT_ID)
	if tenantId == nil {
		logger.Errorf("获取租户ID失败: sn=%s", sn)
		return nil, fmt.Errorf("获取租户ID失败")
	}

	key := c.buildDeviceInfoSnKey(tenantId.(string) + "_" + sn)

	// 先从缓存获取
	result, err := cache.Rdb.Get(ctx, key).Result()
	if err != nil {
		if err.Error() == "redis: nil" {
			// 缓存不存在，从数据库查询
			logger.Debugf("设备信息缓存不存在，从数据库查询: sn=%s", sn)
			return c.getDeviceFromDBBySn(ctx, sn, tenantId.(string))
		}
		logger.Errorf("获取设备信息缓存失败: sn=%s, error=%v", sn, err)
		return nil, err
	}

	var deviceInfo domain.DeviceInfo
	err = json.Unmarshal([]byte(result), &deviceInfo)
	if err != nil {
		logger.Errorf("反序列化设备信息失败: sn=%s, error=%v", sn, err)
		return nil, err
	}

	logger.Debugf("获取设备信息缓存成功: sn=%s", sn)
	return &deviceInfo, nil
}

// SaveCache 保存设备信息到缓存 (使用默认过期时间)
func (c *deviceInfoCache) SaveCache(ctx context.Context, deviceId string, deviceInfo *domain.DeviceInfo) error {
	return c.SaveCacheWithExpire(ctx, deviceId, deviceInfo, DEVICE_INFO_DEFAULT_TTL)
}

// SaveCacheWithExpire 保存设备信息到缓存并设置过期时间
func (c *deviceInfoCache) SaveCacheWithExpire(ctx context.Context, deviceId string, deviceInfo *domain.DeviceInfo, ttl time.Duration) error {
	if deviceInfo == nil {
		return fmt.Errorf("设备信息不能为空")
	}
	
	key := c.buildDeviceInfoKey(deviceId)
	
	data, err := json.Marshal(deviceInfo)
	if err != nil {
		logger.Errorf("序列化设备信息失败: deviceId=%s, error=%v", deviceId, err)
		return err
	}
	
	err = cache.Rdb.Set(ctx, key, data, ttl).Err()
	if err != nil {
		logger.Errorf("保存设备信息缓存失败: deviceId=%s, ttl=%v, error=%v", deviceId, ttl, err)
		return err
	}
	
	logger.Infof("保存设备信息缓存成功: deviceId=%s, ttl=%v", deviceId, ttl)
	return nil
}

// RemoveCache 删除设备信息缓存
func (c *deviceInfoCache) RemoveCache(ctx context.Context, deviceId string) error {
	key := c.buildDeviceInfoKey(deviceId)
	
	result, err := cache.Rdb.Del(ctx, key).Result()
	if err != nil {
		logger.Errorf("删除设备信息缓存失败: deviceId=%s, error=%v", deviceId, err)
		return err
	}
	
	logger.Infof("删除设备信息缓存成功: deviceId=%s, deleted=%d", deviceId, result)
	return nil
}

// ExistsCache 检查设备信息缓存是否存在
func (c *deviceInfoCache) ExistsCache(ctx context.Context, deviceId string) (bool, error) {
	key := c.buildDeviceInfoKey(deviceId)
	
	result, err := cache.Rdb.Exists(ctx, key).Result()
	if err != nil {
		logger.Errorf("检查设备信息缓存是否存在失败: deviceId=%s, error=%v", deviceId, err)
		return false, err
	}
	
	exists := result > 0
	logger.Debugf("检查设备信息缓存是否存在: deviceId=%s, exists=%v", deviceId, exists)
	return exists, nil
}

// getDeviceFromDBBySn 从数据库根据SN获取设备信息并缓存
// 对应 Java: deviceInfoMapper.getBySn(tenantId, sn)
func (c *deviceInfoCache) getDeviceFromDBBySn(ctx context.Context, sn, tenantId string) (*domain.DeviceInfo, error) {
	// 从数据库查询 - 对应 Java: deviceInfoMapper.getBySn(tenantId, sn)
	deviceInfo, err := repository.DeviceRepository.FindBySN(ctx, sn)
	if err != nil {
		logger.Errorf("从数据库查询设备信息失败: sn=%s, error=%v", sn, err)
		return nil, err
	}

	// 如果查询到数据，保存到缓存
	if deviceInfo != nil {
		key := c.buildDeviceInfoSnKey(tenantId + "_" + sn)
		data, err := json.Marshal(deviceInfo)
		if err != nil {
			logger.Errorf("序列化设备信息失败: sn=%s, error=%v", sn, err)
			// 序列化失败不影响返回数据
		} else {
			err = cache.Rdb.Set(ctx, key, data, DEVICE_INFO_DEFAULT_TTL).Err()
			if err != nil {
				logger.Errorf("保存设备信息缓存失败: sn=%s, error=%v", sn, err)
				// 缓存失败不影响返回数据
			} else {
				logger.Debugf("保存设备信息缓存成功: sn=%s", sn)
			}
		}
	}

	return deviceInfo, nil
}
