package domain

import (
	"time"
)

// Conversation 百度语控会话记录表
// 对应 Java: Conversation (遵循规则42: 严格按照Java编码内容转换)
type Conversation struct {
	// 主键 - 对应 Java: @TableId(value = "id", type = IdType.AUTO) private String id;
	Id string `gorm:"column:id;primaryKey" json:"id"`
	
	// sn - 对应 Java: private String sn;
	Sn string `gorm:"column:sn" json:"sn"`
	
	// 会话 - 对应 Java: private String conversation;
	Conversation string `gorm:"column:conversation" json:"conversation"`
	
	// 创建时间 - 对应 Java: private LocalDateTime createTime;
	CreateTime time.Time `gorm:"column:create_time" json:"createTime"`
}
