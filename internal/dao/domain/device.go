package domain

import "time"

// DeviceInfo 设备信息实体
// 对应 Java: DeviceInfoEntity
type DeviceInfo struct {
	// 主键id（t_sn_detail的主键id）
	Id string `gorm:"column:id;primaryKey" json:"id"`
	// 产品id
	ProductId string `gorm:"column:product_id" json:"productId"`
	// 虚拟设备id，json结构
	IotId string `gorm:"column:iot_id" json:"iotId"`
	// 虚拟设备id，相当与原工程类型
	ProductModeCode string `gorm:"column:product_mode_code" json:"productModeCode"`
	// mac地址
	Mac string `gorm:"column:mac" json:"mac"`
	// 设备sn
	Sn string `gorm:"column:sn" json:"sn"`
	// 密钥
	Key string `gorm:"column:key" json:"key"`
	// 软件版本号
	Versions string `gorm:"column:versions" json:"versions"`
	// 设备昵称
	Nickname string `gorm:"column:nickname" json:"nickname"`
	// 设备默认昵称
	DefaultNickname string `gorm:"column:default_nickname" json:"defaultNickname"`
	// 测试设备是否已重置：0已重置，1未重置
	ResetStatus string `gorm:"column:reset_status" json:"resetStatus"`
	// 最近在线时间
	OnlineTime time.Time `gorm:"column:online_time" json:"onlineTime"`
	// 最近离线时间
	OfflineTime *time.Time `gorm:"column:offline_time" json:"offlineTime"`
	// 登录IP
	Ip string `gorm:"column:ip" json:"ip"`
	// 最近登录的城市
	City string `gorm:"column:city" json:"city"`
	// 产品的图片，冗余字段
	PhotoUrl string `gorm:"column:photo_url" json:"photoUrl"`
	// 创建者
	CreateBy string `gorm:"column:create_by" json:"createBy"`
	// 创建时间
	CreateTime time.Time `gorm:"column:create_time" json:"createTime"`
	// 修改时间
	UpdateTime time.Time `gorm:"column:update_time" json:"updateTime"`
	// 更新者
	UpdateBy string `gorm:"column:update_by" json:"updateBy"`
	// 租户id
	TenantId string `gorm:"column:tenant_id" json:"tenantId"`
	// 区域
	Zone string `gorm:"column:zone" json:"zone"`
	// 设备状态 (不存储在数据库中)
	Status bool `gorm:"-" json:"status"`
	// 设备影子最后一次更新的时间 (不存储在数据库中)
	TimeStamp int64 `gorm:"-" json:"timeStamp"`
	// app是否开启密码验证，默认为否(0:否，1:是)
	VerifyPassword int `gorm:"column:verify_password" json:"verifyPassword"`
	// 当前登录城市是否禁止使用 0 不禁止使用 1 禁止使用
	DisableCity int `gorm:"column:disable_city" json:"disableCity"`
}
