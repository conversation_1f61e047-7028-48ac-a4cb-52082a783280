package domain

import (
	"time"
)

// DeviceBindBase 第三方用户设备绑定关系信息表
// 对应 Java: DeviceBindBaseEntity
type DeviceBindBase struct {
	// 主键id - 对应 Java: @TableId(value = "id", type = IdType.INPUT) private String id;
	Id string `gorm:"column:id;primaryKey" json:"id"`
	
	// 设备id - 对应 Java: private String deviceId;
	DeviceId string `gorm:"column:device_id" json:"deviceId"`
	
	// 设备sn - 对应 Java: private String sn;
	Sn string `gorm:"column:sn" json:"sn"`
	
	// 绑定时间 - 对应 Java: private LocalDateTime bindTime;
	BindTime time.Time `gorm:"column:bind_time" json:"bindTime"`
	
	// 基站id - 对应 Java: private String baseId;
	BaseId string `gorm:"column:base_id" json:"baseId"`
	
	// 创建时间 - 对应 Java: private LocalDateTime createTime;
	CreateTime time.Time `gorm:"column:create_time" json:"createTime"`
	
	// 更新人 - 对应 Java: private String updateId;
	UpdateId string `gorm:"column:update_id" json:"updateId"`
	
	// 更新时间 - 对应 Java: private LocalDateTime updateTime;
	UpdateTime time.Time `gorm:"column:update_time" json:"updateTime"`
	
	// 租户id - 对应 Java: private String tenantId;
	TenantId string `gorm:"column:tenant_id" json:"tenantId"`
	
	// 区域 - 对应 Java: private String zone;
	Zone string `gorm:"column:zone" json:"zone"`
	
	// 基站sn - 对应 Java: private String baseSn;
	BaseSn string `gorm:"column:base_sn" json:"baseSn"`
}
