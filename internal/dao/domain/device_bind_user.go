package domain

import "time"

// DeviceBindUser 关闭智能家居设备绑定关系表
// 对应 Java: DeviceBindUserEntity
type DeviceBindUser struct {
	// 主键id
	Id string `gorm:"column:id;primaryKey" json:"id"`
	// 设备id
	DeviceId string `gorm:"column:device_id" json:"deviceId"`
	// 设备sn
	Sn string `gorm:"column:sn" json:"sn"`
	// 拥有者id
	Owner string `gorm:"column:owner" json:"owner"`
	// 绑定时间
	BindTime time.Time `gorm:"column:bind_time" json:"bindTime"`
	// 创建人
	CreatorId string `gorm:"column:creator_id" json:"creatorId"`
	// 创建时间
	CreateTime time.Time `gorm:"column:create_time" json:"createTime"`
	// 更新人
	UpdateId string `gorm:"column:update_id" json:"updateId"`
	// 更新时间
	UpdateTime time.Time `gorm:"column:update_time" json:"updateTime"`
	// 租户id
	TenantId string `gorm:"column:tenant_id" json:"tenantId"`
	// 区域
	Zone string `gorm:"column:zone" json:"zone"`
	// 最新控制时间
	CtrTime time.Time `gorm:"column:ctr_time" json:"ctrTime"`
	// 排序权重
	DeviceSortWeight int `gorm:"column:device_sort_weight" json:"deviceSortWeight"`
	// 用户端类型（0、非基站（Android、IOS、Andipad、IOSipad、Mini、Web、M），1.基站）
	UserAgent string `gorm:"column:user_agent" json:"userAgent"`
	// 设备分享标识（0、正常，1、分享设备）
	Flag string `gorm:"column:flag" json:"flag"`
	// 产品ID
	ProductId string `gorm:"column:product_id" json:"productId"`
	// 数据添加来源：1：从t_smart_home_room_device同步；2：直接添加
	Source int `gorm:"column:source" json:"source"`
	// 分享状态（0、未分享，1、分享)
	ShareStatus string `gorm:"column:share_status" json:"shareStatus"`
	// 设备昵称 (不存储在数据库中)
	Nickname string `gorm:"-" json:"nickname"`
	// 绑定设备的用户类型,和sso用户类型对应
	BindUserType int `gorm:"column:bind_user_type" json:"bindUserType"`
}
