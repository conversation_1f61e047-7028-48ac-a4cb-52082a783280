package domain

import "time"

// DeviceInviteShare 设备分享邀请信息表
// 对应 Java: DeviceInviteShareEntity
type DeviceInviteShare struct {
	// 主键id，通过雪花算法生产
	Id string `gorm:"column:id;primaryKey" json:"id"`
	// 邀请人的用户id
	InviteId string `gorm:"column:invite_id" json:"inviteId"`
	// 被邀请人的用户id
	BeInviteId string `gorm:"column:be_invite_id" json:"beInviteId"`
	// 设备id或家庭id
	TargetId string `gorm:"column:target_id" json:"targetId"`
	// 类型,0-共享设备；1-共享家庭
	Type int `gorm:"column:type" json:"type"`
	// 0-正常；1-已同意；2-已拒绝
	Status int `gorm:"column:status" json:"status"`
	// 删除 1-邀请者删除；2-被邀请者删除
	Removed int `gorm:"column:removed" json:"removed"`
	// 创建者
	CreateBy string `gorm:"column:create_by" json:"createBy"`
	// 创建时间
	CreateTime time.Time `gorm:"column:create_time" json:"createTime"`
	// 修改人
	UpdateBy string `gorm:"column:update_by" json:"updateBy"`
	// 修改时间
	UpdateTime time.Time `gorm:"column:update_time" json:"updateTime"`
	// 租户id
	TenantId string `gorm:"column:tenant_id" json:"tenantId"`
	// 区域
	Zone string `gorm:"column:zone" json:"zone"`
}
