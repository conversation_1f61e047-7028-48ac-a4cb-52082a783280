package domain

import (
	"time"
)

// DeviceMap 扫地机设备地图表
// 对应 Java: DeviceMapEntity (遵循规则45: 严格按照Java编码内容转换)
type DeviceMap struct {
	// 主键 - 对应 Java: @TableId(value = "id") private String id;
	Id string `gorm:"column:id;primaryKey" json:"id"`
	
	// sn - 对应 Java: private String sn;
	Sn string `gorm:"column:sn" json:"sn"`
	
	// 当前地图id - 对应 Java: private Long curMapId;
	CurMapId *int64 `gorm:"column:cur_map_id" json:"curMapId"`
	
	// 设备当前地图房间 - 对应 Java: @TableField(typeHandler = FastjsonTypeHandler.class) private JSONArray curMapRoom;
	CurMapRoom string `gorm:"column:cur_map_room;type:json" json:"curMapRoom"`
	
	// 企业id - 对应 Java: private String tenantId;
	TenantId string `gorm:"column:tenant_id" json:"tenantId"`
	
	// 创建时间 - 对应 Java: private LocalDateTime createTime;
	CreateTime time.Time `gorm:"column:create_time" json:"createTime"`
	
	// 更新时间 - 对应 Java: private LocalDateTime updateTime;
	UpdateTime time.Time `gorm:"column:update_time" json:"updateTime"`
}
