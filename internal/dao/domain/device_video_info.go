package domain

import "time"

// DeviceVideoInfo 设备视频信息实体
// 对应 Java: DeviceVideoInfoEntity
type DeviceVideoInfo struct {
	// 主键ID
	Id string `gorm:"column:id;primaryKey" json:"id"`
	// 租户ID
	TenantId string `gorm:"column:tenant_id" json:"tenantId"`
	// AIOT云平台产品ID
	ProductId string `gorm:"column:product_id" json:"productId"`
	// 设备SN
	Sn string `gorm:"column:sn" json:"sn"`
	// 提供商 (1-阿里云)
	Provider int `gorm:"column:provider" json:"provider"`
	// 设备名称
	DeviceName string `gorm:"column:device_name" json:"deviceName"`
	// 阿里云产品密钥
	ProductKey string `gorm:"column:product_key" json:"productKey"`
	// 阿里云设备密钥
	DeviceSecret string `gorm:"column:device_secret" json:"deviceSecret"`
	// 创建者
	CreateBy string `gorm:"column:create_by" json:"createBy"`
	// 创建时间
	CreateTime time.Time `gorm:"column:create_time" json:"createTime"`
	// 更新者
	UpdateBy string `gorm:"column:update_by" json:"updateBy"`
	// 更新时间
	UpdateTime time.Time `gorm:"column:update_time" json:"updateTime"`
}
