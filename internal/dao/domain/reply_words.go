package domain

import (
	"time"
)

// ReplyWords 回复词表
// 对应 Java: ReplyWordsEntity (遵循规则46: 严格按照Java编码内容转换)
type ReplyWords struct {
	// 主键id - 对应 Java: @TableId(value = "id", type = IdType.AUTO) private Long id;
	Id int64 `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	
	// 领域 - 对应 Java: private String domain;
	Domain string `gorm:"column:domain" json:"domain"`
	
	// 意图 - 对应 Java: private String intent;
	Intent string `gorm:"column:intent" json:"intent"`
	
	// 条件表达式 - 对应 Java: private String conditionExpression;
	ConditionExpression string `gorm:"column:condition_expression" json:"conditionExpression"`
	
	// 回复词 - 对应 Java: private String replyWord;
	ReplyWord string `gorm:"column:reply_word" json:"replyWord"`
	
	// 创建时间 - 对应 Java: private LocalDateTime createTime;
	CreateTime time.Time `gorm:"column:create_time" json:"createTime"`
	
	// 更新时间 - 对应 Java: private LocalDateTime updateTime;
	UpdateTime time.Time `gorm:"column:update_time" json:"updateTime"`
}
