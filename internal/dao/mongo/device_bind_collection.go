package mongo

import (
	"time"
)

// DeviceBindCollection 设备绑定关系的mongo集合
// 对应 Java: DeviceBindCollection
type DeviceBindCollection struct {
	// ID，格式：userId_sn - 对应 Java: @Id private String id;
	ID string `bson:"_id" json:"id"`
	
	// 创建时间 - 对应 Java: private Long createTime;
	CreateTime int64 `bson:"createTime" json:"createTime"`
}

// NewDeviceBindCollection 创建设备绑定关系对象
// 对应 Java: new DeviceBindCollection()
func NewDeviceBindCollection(userID, sn string) *DeviceBindCollection {
	return &DeviceBindCollection{
		ID:         GetID(userID, sn), // 对应 Java: getId(userId, sn)
		CreateTime: time.Now().UnixMilli(), // 对应 Java: System.currentTimeMillis()
	}
}

// GetID 组成mongo ID
// 对应 Java: private String getId(String userId, String sn) { return userId + "_" + sn; }
func GetID(userID, sn string) string {
	return userID + "_" + sn
}

// CollectionName 获取集合名称
// 对应 Java: @Document(collection = "device_bind_rel")
func (d *DeviceBindCollection) CollectionName() string {
	return "device_bind_rel"
}
