package mongo

import (
	"crypto/md5"
	"fmt"
	"sort"
	"time"

	"piceacorp.com/device-service/internal/bean/req"
)

// DeviceVersionLog 设备版本日志
// 对应 Java: DeviceVersionLog
type DeviceVersionLog struct {
	// ID，格式：SN + hash(packageVersions) - 对应 Java: @Id private String id;
	ID string `bson:"_id" json:"id"`
	
	// 包版本列表 - 对应 Java: private List<DevicePackage> packageVersions;
	PackageVersions []DevicePackage `bson:"packageVersions" json:"packageVersions"`
	
	// 创建时间 - 对应 Java: private LocalDateTime createTime;
	CreateTime int64 `bson:"createTime" json:"createTime"`
	
	// 最后登录时间 - 对应 Java: private LocalDateTime lastLoginTime;
	LastLoginTime int64 `bson:"lastLoginTime" json:"lastLoginTime"`
}

// DevicePackage 设备包信息
// 对应 Java: DeviceVersionLog.DevicePackage
type DevicePackage struct {
	// 包类型 - 对应 Java: private String packageType;
	PackageType string `bson:"packageType" json:"packageType"`
	
	// 版本号 - 对应 Java: private Integer version;
	Version uint `bson:"version" json:"version"`
	
	// 版本名称 - 对应 Java: private String versionName;
	VersionName string `bson:"versionName" json:"versionName"`
	
	// 控制版本 - 对应 Java: private String ctrlVersion;
	CtrlVersion string `bson:"ctrlVersion" json:"ctrlVersion"`
}

// NewDeviceVersionLog 创建设备版本日志
// 对应 Java: new DeviceVersionLog()
func NewDeviceVersionLog(sn string, packageVersions []req.RobotVersion) *DeviceVersionLog {
	now := time.Now().UnixMilli()
	
	// 转换包版本 - 对应 Java: for (RobotVersion version : packageVersions)
	var packages []DevicePackage
	for _, version := range packageVersions {
		pack := DevicePackage{
			PackageType: version.PackageType,
			Version:     version.Version,
			VersionName: version.VersionName,
			CtrlVersion: version.CtrlVersion,
		}
		packages = append(packages, pack)
	}
	
	// 排序 - 对应 Java: Collections.sort(packages);
	sort.Slice(packages, func(i, j int) bool {
		// 对应 Java: DevicePackage.compareTo()
		if packages[i].Version != packages[j].Version {
			return packages[i].Version < packages[j].Version
		}
		if packages[i].VersionName != packages[j].VersionName {
			return packages[i].VersionName < packages[j].VersionName
		}
		if packages[i].CtrlVersion != packages[j].CtrlVersion {
			return packages[i].CtrlVersion < packages[j].CtrlVersion
		}
		return packages[i].PackageType < packages[j].PackageType
	})
	
	// 生成ID - 对应 Java: String id = sn + Arrays.hashCode(packages.toArray());
	id := generateID(sn, packages)
	
	return &DeviceVersionLog{
		ID:              id,
		PackageVersions: packages,
		CreateTime:      now,
		LastLoginTime:   now,
	}
}

// generateID 生成ID
// 对应 Java: sn + Arrays.hashCode(packages.toArray())
func generateID(sn string, packages []DevicePackage) string {
	// 计算包的哈希值
	h := md5.New()
	for _, pkg := range packages {
		h.Write([]byte(fmt.Sprintf("%s%d%s%s", pkg.PackageType, pkg.Version, pkg.VersionName, pkg.CtrlVersion)))
	}
	hash := fmt.Sprintf("%x", h.Sum(nil))
	
	return sn + hash[:8] // 取前8位哈希值
}

// CollectionName 获取集合名称
// 对应 Java: @Document("device_version_log")
func (d *DeviceVersionLog) CollectionName() string {
	return "device_version_log"
}
