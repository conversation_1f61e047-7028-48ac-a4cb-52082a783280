package repository

import (
	"context"

	"piceacorp.com/device-service/internal/dao/bo"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/pkg/data/mysql/base"
	"piceacorp.com/device-service/pkg/logger"
)

// IConversationRepository 会话记录仓库接口
// 对应 Java: ConversationMapper (遵循规则6: 严格按照现有Repository模式转换)
type IConversationRepository interface {
	base.IRepository[domain.Conversation]
	// 根据SN删除会话记录 - 对应 Java: conversationMapper.delete(queryWrapper)
	DeleteBySn(ctx context.Context, sn string) error
	// 插入会话记录 - 对应 Java: conversationMapper.insert(conversation) (遵循规则2: 完整转换所有使用的mapper方法)
	Insert(ctx context.Context, conversation *domain.Conversation) error
	// 分页查询会话记录 - 对应 Java: conversationMapper.selectPage(page, queryWrapper) (遵循规则2: 完整转换所有使用的mapper方法)
	SelectPage(ctx context.Context, sn string, page, pageSize int) (*bo.PageResult[*domain.Conversation], error)
}

// conversationRepository 会话记录仓库实现 (遵循规则15: 使用全局变量而不是构造函数)
type conversationRepository struct {
	base.BaseRepository[domain.Conversation]
}

// ConversationRepository 全局会话记录仓库实例 (遵循规则15: 只对外暴露全局变量)
var ConversationRepository = &conversationRepository{
	BaseRepository: base.NewBaseRepository[domain.Conversation](),
}

// DeleteBySn 根据SN删除会话记录
// 对应 Java: LambdaQueryWrapper<Conversation> queryWrapper = new LambdaQueryWrapper<>(); queryWrapper.eq(Conversation::getSn, sn); conversationMapper.delete(queryWrapper); (遵循规则6: 使用BaseRepository模式)
func (r *conversationRepository) DeleteBySn(ctx context.Context, sn string) error {
	logger.Infof("根据SN删除会话记录: sn=%s", sn)

	// 使用BaseRepository的DB连接 (遵循规则6: 严格按照现有Repository模式)
	result := r.GetDB().WithContext(ctx).Where("sn = ?", sn).Delete(&domain.Conversation{})
	if result.Error != nil {
		logger.Errorf("根据SN删除会话记录失败: sn=%s, error=%v", sn, result.Error)
		return result.Error
	}

	logger.Infof("根据SN删除会话记录成功: sn=%s, rowsAffected=%d", sn, result.RowsAffected)
	return nil
}

// Insert 插入会话记录
// 对应 Java: conversationMapper.insert(conversation) (遵循规则2: 完整转换所有使用的mapper方法)
func (r *conversationRepository) Insert(ctx context.Context, conversation *domain.Conversation) error {
	logger.Infof("插入会话记录: sn=%s, id=%s", conversation.Sn, conversation.Id)

	// 对应 Java: conversationMapper.insert(conversation)
	result := r.GetDB().WithContext(ctx).Create(conversation)
	if result.Error != nil {
		logger.Errorf("插入会话记录失败: sn=%s, id=%s, error=%v", conversation.Sn, conversation.Id, result.Error)
		return result.Error
	}

	logger.Infof("插入会话记录成功: sn=%s, id=%s", conversation.Sn, conversation.Id)
	return nil
}

// SelectPage 分页查询会话记录
// 对应 Java: conversationMapper.selectPage(new Page<>(pageReq.getPage(), pageReq.getPageSize()), queryWrapper) (遵循规则2: 完整转换所有使用的mapper方法)
func (r *conversationRepository) SelectPage(ctx context.Context, sn string, page, pageSize int) (*bo.PageResult[*domain.Conversation], error) {
	logger.Infof("分页查询会话记录: sn=%s, page=%d, pageSize=%d", sn, page, pageSize)

	var conversations []*domain.Conversation
	var total int64

	// 对应 Java: queryWrapper.eq(Conversation::getSn, pageReq.getSn())
	db := r.GetDB().WithContext(ctx).Model(&domain.Conversation{}).Where("sn = ?", sn)

	// 查询总数
	if err := db.Count(&total).Error; err != nil {
		logger.Errorf("查询会话记录总数失败: sn=%s, error=%v", sn, err)
		return nil, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := db.Offset(offset).Limit(pageSize).Find(&conversations).Error; err != nil {
		logger.Errorf("分页查询会话记录失败: sn=%s, page=%d, pageSize=%d, error=%v", sn, page, pageSize, err)
		return nil, err
	}

	// 构造分页结果 (遵循规则6: 非domain的返回放到internal/dao/bo文件夹中)
	pageResult := bo.NewPageResult(conversations, total, page, pageSize)

	logger.Infof("分页查询会话记录成功: sn=%s, page=%d, pageSize=%d, total=%d", sn, page, pageSize, total)
	return pageResult, nil
}
