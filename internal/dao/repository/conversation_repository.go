package repository

import (
	"context"

	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/pkg/data/db"
	"piceacorp.com/device-service/pkg/logger"
)

// IConversationRepository 会话记录仓库接口
// 对应 Java: ConversationMapper (遵循规则42: 严格按照Java编码内容转换)
type IConversationRepository interface {
	// 根据SN删除会话记录 - 对应 Java: conversationMapper.delete(queryWrapper)
	DeleteBySn(ctx context.Context, sn string) error
}

var ConversationRepository conversationRepository

type conversationRepository struct {
	Dbh *db.DatabaseHandler
}

func init() {
	ConversationRepository = conversationRepository{
		Dbh: db.GetDatabaseHandler(),
	}
}

// DeleteBySn 根据SN删除会话记录
// 对应 Java: LambdaQueryWrapper<Conversation> queryWrapper = new LambdaQueryWrapper<>(); queryWrapper.eq(Conversation::getSn, sn); conversationMapper.delete(queryWrapper); (遵循规则42: 严格按照Java编码内容转换)
func (r *conversationRepository) DeleteBySn(ctx context.Context, sn string) error {
	logger.Infof("根据SN删除会话记录: sn=%s", sn)

	result := r.Dbh.DB.WithContext(ctx).Where("sn = ?", sn).Delete(&domain.Conversation{})
	if result.Error != nil {
		logger.Errorf("根据SN删除会话记录失败: sn=%s, error=%v", sn, result.Error)
		return result.Error
	}

	logger.Infof("根据SN删除会话记录成功: sn=%s, rowsAffected=%d", sn, result.RowsAffected)
	return nil
}
