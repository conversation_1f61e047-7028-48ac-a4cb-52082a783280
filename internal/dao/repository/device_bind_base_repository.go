package repository

import (
	"context"
	"errors"

	"gorm.io/gorm"
	"piceacorp.com/device-service/internal/dao/bo"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/pkg/data/mysql"
	"piceacorp.com/device-service/pkg/data/mysql/base"
	"piceacorp.com/device-service/pkg/logger"
)

// IDeviceBindBaseRepository 设备基站绑定关系仓库接口
// 对应 Java: DeviceBindBaseMapper
type IDeviceBindBaseRepository interface {
	base.IRepository[domain.DeviceBindBase]
	// 根据条件查询单个 - 对应 Java: selectOne()
	FindOne(ctx context.Context, deviceId, sn, baseSn, baseId string) (*domain.DeviceBindBase, error)
	// 根据条件删除 - 对应 Java: delete()
	DeleteBySnOrBaseSn(ctx context.Context, sn, baseSn string) error
	// 根据sn列表查询 - 对应 Java: selectList()
	FindBySnList(ctx context.Context, snList []string, isBaseSn bool) ([]*domain.DeviceBindBase, error)
	// 根据多条件查询 - 对应 Java: selectList()
	FindByConditions(ctx context.Context, baseId, baseSn, devId, devSn string) ([]*domain.DeviceBindBase, error)
	// 根据基站ID获取绑定列表 - 对应 Java: getBindListByBaseId()
	GetBindListByBaseId(ctx context.Context, baseId, tenantId string) ([]*bo.BaseBindBo, error)
}

// deviceBindBaseRepository 设备基站绑定关系仓库实现
type deviceBindBaseRepository struct {
	base.BaseRepository[domain.DeviceBindBase]
}

var DeviceBindBaseRepository = &deviceBindBaseRepository{BaseRepository: base.BaseRepository[domain.DeviceBindBase]{Dbh: mysql.DBH}}



// FindOne 根据条件查询单个
// 对应 Java: selectOne()
func (r *deviceBindBaseRepository) FindOne(ctx context.Context, deviceId, sn, baseSn, baseId string) (*domain.DeviceBindBase, error) {
	var entity domain.DeviceBindBase

	query := r.Dbh.DB.WithContext(ctx).Model(&domain.DeviceBindBase{})

	// 对应 Java: query.eq(DeviceBindBaseEntity::getSn,sn)
	if sn != "" {
		query = query.Where("sn = ?", sn)
	}

	// 对应 Java: query.eq(DeviceBindBaseEntity::getDeviceId,deviceId)
	if deviceId != "" {
		query = query.Where("device_id = ?", deviceId)
	}

	// 对应 Java: query.eq(DeviceBindBaseEntity::getBaseSn,baseSn)
	if baseSn != "" {
		query = query.Where("base_sn = ?", baseSn)
	}

	// 对应 Java: query.eq(DeviceBindBaseEntity::getBaseId,baseId)
	if baseId != "" {
		query = query.Where("base_id = ?", baseId)
	}

	// 对应 Java: query.last(" limit 1")
	err := query.Limit(1).First(&entity).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &entity, nil
}

// DeleteBySnOrBaseSn 根据sn或baseSn删除
// 对应 Java: delete()
func (r *deviceBindBaseRepository) DeleteBySnOrBaseSn(ctx context.Context, sn, baseSn string) error {
	logger.Infof("删除设备基站绑定关系: sn=%s, baseSn=%s", sn, baseSn)

	// 对应 Java: deleteQuery.eq(DeviceBindBaseEntity::getSn,sn).or().eq(DeviceBindBaseEntity::getBaseSn,baseSn)
	err := r.Dbh.DB.WithContext(ctx).
		Where("sn = ? OR base_sn = ?", sn, baseSn).
		Delete(&domain.DeviceBindBase{}).Error

	if err != nil {
		logger.Errorf("删除设备基站绑定关系失败: sn=%s, baseSn=%s, error=%v", sn, baseSn, err)
		return err
	}

	logger.Infof("删除设备基站绑定关系成功: sn=%s, baseSn=%s", sn, baseSn)
	return nil
}

// FindBySnList 根据sn列表查询
// 对应 Java: selectList()
func (r *deviceBindBaseRepository) FindBySnList(ctx context.Context, snList []string, isBaseSn bool) ([]*domain.DeviceBindBase, error) {
	var entities []*domain.DeviceBindBase

	query := r.Dbh.DB.WithContext(ctx).Model(&domain.DeviceBindBase{})

	if isBaseSn {
		// 对应 Java: queryWrapper.in(DeviceBindBaseEntity::getBaseSn,req.getSnList())
		query = query.Where("base_sn IN ?", snList)
	} else {
		// 对应 Java: queryWrapper.in(DeviceBindBaseEntity::getSn,req.getSnList())
		query = query.Where("sn IN ?", snList)
	}

	err := query.Find(&entities).Error
	if err != nil {
		logger.Errorf("根据sn列表查询绑定关系失败: snList=%v, isBaseSn=%v, error=%v", snList, isBaseSn, err)
		return nil, err
	}

	logger.Infof("根据sn列表查询绑定关系成功: snList=%v, isBaseSn=%v, count=%d", snList, isBaseSn, len(entities))
	return entities, nil
}

// FindByConditions 根据多条件查询
// 对应 Java: selectList()
func (r *deviceBindBaseRepository) FindByConditions(ctx context.Context, baseId, baseSn, devId, devSn string) ([]*domain.DeviceBindBase, error) {
	var entities []*domain.DeviceBindBase
	
	query := r.Dbh.DB.WithContext(ctx).Model(&domain.DeviceBindBase{})
	
	// 对应 Java: if(StringUtils.isNotBlank(baseId)) queryWrapper.eq(DeviceBindBaseEntity::getBaseId, baseId)
	if baseId != "" {
		query = query.Where("base_id = ?", baseId)
	}
	
	// 对应 Java: if(StringUtils.isNotBlank(baseSn)) queryWrapper.eq(DeviceBindBaseEntity::getBaseSn, baseSn)
	if baseSn != "" {
		query = query.Where("base_sn = ?", baseSn)
	}
	
	// 对应 Java: if(StringUtils.isNotBlank(devId)) queryWrapper.eq(DeviceBindBaseEntity::getDeviceId, devId)
	if devId != "" {
		query = query.Where("device_id = ?", devId)
	}
	
	// 对应 Java: if(StringUtils.isNotBlank(devSn)) queryWrapper.eq(DeviceBindBaseEntity::getSn, devSn)
	if devSn != "" {
		query = query.Where("sn = ?", devSn)
	}
	
	err := query.Find(&entities).Error
	if err != nil {
		logger.Errorf("根据条件查询绑定关系失败: baseId=%s, baseSn=%s, devId=%s, devSn=%s, error=%v", 
			baseId, baseSn, devId, devSn, err)
		return nil, err
	}
	
	logger.Infof("根据条件查询绑定关系成功: baseId=%s, baseSn=%s, devId=%s, devSn=%s, count=%d",
		baseId, baseSn, devId, devSn, len(entities))
	return entities, nil
}

// GetBindListByBaseId 根据基站ID获取绑定列表
// 对应 Java: DeviceBindBaseMapper.getBindListByBaseId(@Param("baseId") String baseId, @Param("tenantId") String tenantId)
// SQL: SELECT tdb.base_id, tdb.device_id,tdb.sn,t.owner,td.mac,td.nickname FROM t_device_bind_base tdb left join t_device_info td on tdb.sn = td.sn left join t_device_bind_user t on tdb.sn = t.sn WHERE tdb.base_id = #{baseId} AND tdb.tenant_id = #{tenantId} ORDER BY tdb.bind_time DESC limit 1
func (r *deviceBindBaseRepository) GetBindListByBaseId(ctx context.Context, baseId, tenantId string) ([]*bo.BaseBindBo, error) {
	var results []*bo.BaseBindBo

	// 对应 Java SQL: 复杂的三表关联查询
	err := r.Dbh.DB.WithContext(ctx).
		Table("t_device_bind_base tdb").
		Select("tdb.base_id, tdb.device_id, tdb.sn, t.owner, td.mac, td.nickname").
		Joins("LEFT JOIN t_device_info td ON tdb.sn = td.sn").
		Joins("LEFT JOIN t_device_bind_user t ON tdb.sn = t.sn").
		Where("tdb.base_id = ? AND tdb.tenant_id = ?", baseId, tenantId).
		Order("tdb.bind_time DESC").
		Limit(1).
		Scan(&results).Error

	if err != nil {
		logger.Errorf("根据基站ID获取绑定列表失败: baseId=%s, tenantId=%s, error=%v", baseId, tenantId, err)
		return nil, err
	}

	logger.Infof("根据基站ID获取绑定列表成功: baseId=%s, tenantId=%s, count=%d", baseId, tenantId, len(results))
	return results, nil
}
