package repository

import (
	"context"
	"errors"
	"gorm.io/gorm"
	"piceacorp.com/device-service/internal/bean/res"
	"piceacorp.com/device-service/internal/dao/bo"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/pkg/data/mysql"
	"piceacorp.com/device-service/pkg/data/mysql/base"
	"piceacorp.com/device-service/pkg/logger"
)

// IDeviceBindUserRepository 设备绑定用户仓库接口
type IDeviceBindUserRepository interface {
	base.IRepository[domain.DeviceBindUser]
	// 根据设备ID查询绑定记录 (对应 list(queryWrapper))
	FindByDeviceId(ctx context.Context, deviceId string) ([]*domain.DeviceBindUser, error)
	// 批量删除绑定记录 (对应 deleteBatchIds)
	DeleteBatchByIds(ctx context.Context, ids []string) error
	// 更新设备拥有者 (对应 updateOwnerByDeviceId)
	UpdateOwnerByDeviceId(ctx context.Context, userId, deviceId string) error
	// 根据用户ID获取绑定设备信息 (对应 getDeviceInfoAndBindByUserId)
	GetDeviceInfoAndBindByUserId(ctx context.Context, userId string) ([]*res.BindDeviceInfoVo, error)
	// 根据用户ID列表获取绑定设备ID列表 (对应 getBindListByUserIdList)
	GetBindListByUserIdList(ctx context.Context, tenantId string, userIdList []string) ([]*bo.BindIdBo, error)
	// 检查分享绑定记录数量 (对应 shareBind 中的 count 查询)
	CountShareBind(ctx context.Context, userId, deviceId string) (int64, error)
	// 保存绑定记录 (对应 save 方法)
	Save(ctx context.Context, deviceBindUser *domain.DeviceBindUser) error
	// 更新分享状态 (对应 update 方法)
	UpdateShareStatus(ctx context.Context, deviceId, owner string, shareStatus string) error
	// 重置基站拥有者 (对应 resetOwner 方法)
	ResetOwner(ctx context.Context, deviceId, newOwner string) error
	// 根据设备ID和用户ID获取绑定记录 (对应 getOne 方法)
	GetByDeviceIdAndUserId(ctx context.Context, deviceId, userId string) (*domain.DeviceBindUser, error)
	// 根据设备ID获取所有绑定记录 (对应 list 方法)
	GetByDeviceId(ctx context.Context, deviceId string) ([]*domain.DeviceBindUser, error)
	// 根据ID删除绑定记录 (对应 removeById 方法)
	DeleteById(ctx context.Context, id string) error
	// 根据设备ID和用户ID删除绑定记录 (对应 remove 方法)
	DeleteByDeviceIdAndUserId(ctx context.Context, deviceId, userId string) error
	// 根据用户ID获取绑定关系 (对应 getListByUserId)
	GetListByUserId(ctx context.Context, tenantId, userId string) ([]*domain.DeviceBindUser, error)
	// 根据id批量删除绑定关系 (对应 deleteBatchIds)
	DeleteBatchIds(ctx context.Context, ids []string) error
	// 根据设备ID列表查询绑定记录 (对应 selectList)
	FindByDeviceIds(ctx context.Context, deviceIds []string) ([]*domain.DeviceBindUser, error)
	// 根据设备ID列表和标志查询绑定记录 (对应 selectList with flag)
	FindByDeviceIdsAndFlag(ctx context.Context, deviceIds []string, flag string) ([]*domain.DeviceBindUser, error)
	// 根据用户ID删除绑定关系 (对应 delete with userIds)
	DeleteByUserIds(ctx context.Context, userIds []string, tenantId string) (int64, error)
	// 根据设备ID删除绑定关系 (对应 reset 方法中的删除绑定) (遵循规则43: 必须全部转换)
	DeleteByDeviceId(ctx context.Context, deviceId string) error
}

// deviceBindUserRepository 设备绑定用户仓库实现
type deviceBindUserRepository struct {
	base.BaseRepository[domain.DeviceBindUser]
}

var DeviceBindUserRepository = &deviceBindUserRepository{
	BaseRepository: base.BaseRepository[domain.DeviceBindUser]{Dbh: mysql.DBH},
}

// FindByDeviceId 根据设备ID查询绑定记录
// 对应 Java: list(queryWrapper.eq("device_id", deviceId).ne("user_client_agent", "1"))
func (r *deviceBindUserRepository) FindByDeviceId(ctx context.Context, deviceId string) ([]*domain.DeviceBindUser, error) {
	var results []*domain.DeviceBindUser

	// 查询条件：device_id = ? AND user_client_agent != '1' (排除基站类型)
	result := r.Dbh.DB.WithContext(ctx).
		Where("device_id = ? AND user_client_agent != ?", deviceId, "1").
		Find(&results)

	if result.Error != nil {
		logger.Errorf("根据设备ID查询绑定记录失败: deviceId=%s, error=%v", deviceId, result.Error)
		return nil, result.Error
	}

	logger.Infof("根据设备ID查询绑定记录成功: deviceId=%s, count=%d", deviceId, len(results))
	return results, nil
}

// DeleteBatchByIds 批量删除绑定记录
// 对应 Java: deleteBatchIds(ids)
func (r *deviceBindUserRepository) DeleteBatchByIds(ctx context.Context, ids []string) error {
	if len(ids) == 0 {
		return nil
	}

	result := r.Dbh.DB.WithContext(ctx).
		Where("id IN ?", ids).
		Delete(&domain.DeviceBindUser{})

	if result.Error != nil {
		logger.Errorf("批量删除绑定记录失败: ids=%v, error=%v", ids, result.Error)
		return result.Error
	}

	logger.Infof("批量删除绑定记录成功: ids=%v, affected=%d", ids, result.RowsAffected)
	return nil
}

// UpdateOwnerByDeviceId 更新设备拥有者
// 对应 Java: updateOwnerByDeviceId(userId, deviceId)
func (r *deviceBindUserRepository) UpdateOwnerByDeviceId(ctx context.Context, userId, deviceId string) error {
	result := r.Dbh.DB.WithContext(ctx).
		Model(&domain.DeviceBindUser{}).
		Where("device_id = ? AND user_client_agent = ?", deviceId, "1"). // 只更新基站类型设备
		Update("owner", userId)

	if result.Error != nil {
		logger.Errorf("更新设备拥有者失败: userId=%s, deviceId=%s, error=%v", userId, deviceId, result.Error)
		return result.Error
	}

	logger.Infof("更新设备拥有者成功: userId=%s, deviceId=%s, affected=%d", userId, deviceId, result.RowsAffected)
	return nil
}

// GetDeviceInfoAndBindByUserId 根据用户ID获取绑定设备信息
// 对应 Java: getDeviceInfoAndBindByUserId(userId)
// SQL: SELECT tdbu.creator_id,tdbu.device_id,tdbu.owner,tdbu.sn,tdbu.create_time,
//
//	tdi.mac,tdi.nickname,tdi.versions,tdi.online_time,tdi.offline_time,
//	tdi.product_id,tdi.photo_url,tdi.product_mode_code,tdi.iot_id
//	FROM t_device_bind_user tdbu INNER JOIN t_device_info tdi ON tdi.id = tdbu.device_id
//	WHERE tdbu.creator_id = #{userId} and tdi.disable_city = 0
//	ORDER BY tdbu.device_sort_weight DESC, tdbu.id DESC
func (r *deviceBindUserRepository) GetDeviceInfoAndBindByUserId(ctx context.Context, userId string) ([]*res.BindDeviceInfoVo, error) {
	var results []*res.BindDeviceInfoVo

	// 复杂关联查询
	err := r.Dbh.DB.WithContext(ctx).
		Table("t_device_bind_user tdbu").
		Select("tdbu.creator_id, tdbu.device_id, tdbu.owner, tdbu.sn, tdbu.create_time, "+
			"tdi.mac, tdi.nickname, tdi.versions, tdi.online_time, tdi.offline_time, "+
			"tdi.product_id, tdi.photo_url, tdi.product_mode_code, tdi.iot_id").
		Joins("INNER JOIN t_device_info tdi ON tdi.id = tdbu.device_id").
		Where("tdbu.creator_id = ? AND tdi.disable_city = 0", userId).
		Order("tdbu.device_sort_weight DESC, tdbu.id DESC").
		Scan(&results).Error

	if err != nil {
		logger.Errorf("根据用户ID获取绑定设备信息失败: userId=%s, error=%v", userId, err)
		return nil, err
	}

	logger.Infof("根据用户ID获取绑定设备信息成功: userId=%s, count=%d", userId, len(results))
	return results, nil
}

// GetBindListByUserIdList 根据用户ID列表获取绑定设备ID列表
// 对应 Java: DeviceBindUserMapper.getBindListByUserIdList(String tenantId, List<String> userIdList)
// SQL: SELECT DISTINCT tdbu.device_id AS device_id, tdbu.creator_id AS user_id FROM t_device_bind_user tdbu WHERE tdbu.tenant_id = ? AND tdbu.creator_id IN (?)
func (r *deviceBindUserRepository) GetBindListByUserIdList(ctx context.Context, tenantId string, userIdList []string) ([]*bo.BindIdBo, error) {
	if len(userIdList) == 0 {
		return []*bo.BindIdBo{}, nil
	}

	var results []*bo.BindIdBo

	err := r.Dbh.DB.WithContext(ctx).
		Table("t_device_bind_user").
		Select("DISTINCT device_id, creator_id as user_id").
		Where("tenant_id = ? AND creator_id IN ?", tenantId, userIdList).
		Scan(&results).Error

	if err != nil {
		logger.Errorf("根据用户ID列表获取绑定设备ID列表失败: tenantId=%s, userIdList=%v, error=%v", tenantId, userIdList, err)
		return nil, err
	}

	logger.Debugf("根据用户ID列表获取绑定设备ID列表成功: tenantId=%s, userCount=%d, bindCount=%d", tenantId, len(userIdList), len(results))
	return results, nil
}

// CountShareBind 检查分享绑定记录数量
// 对应 Java: queryWrapper.eq(DeviceBindUserEntity::getCreatorId, userId).eq(DeviceBindUserEntity::getDeviceId, deviceId).eq(DeviceBindUserEntity::getFlag, Constant.DEVICE_FLAG_1).eq(DeviceBindUserEntity::getUserAgent, UserClientAgentEnum.NON_BASE.getType())
func (r *deviceBindUserRepository) CountShareBind(ctx context.Context, userId, deviceId string) (int64, error) {
	var count int64

	result := r.Dbh.DB.WithContext(ctx).
		Model(&domain.DeviceBindUser{}).
		Where("creator_id = ? AND device_id = ? AND flag = ? AND user_agent = ?",
			userId, deviceId, "1", "1"). // Constant.DEVICE_FLAG_1="1", UserClientAgentEnum.NON_BASE.getType()="1"
		Count(&count)

	if result.Error != nil {
		logger.Errorf("检查分享绑定记录数量失败: userId=%s, deviceId=%s, error=%v", userId, deviceId, result.Error)
		return 0, result.Error
	}

	logger.Debugf("检查分享绑定记录数量成功: userId=%s, deviceId=%s, count=%d", userId, deviceId, count)
	return count, nil
}

// Save 保存绑定记录
// 对应 Java: this.save(deviceBindUser)
func (r *deviceBindUserRepository) Save(ctx context.Context, deviceBindUser *domain.DeviceBindUser) error {
	result := r.Dbh.DB.WithContext(ctx).Create(deviceBindUser)
	if result.Error != nil {
		logger.Errorf("保存绑定记录失败: userId=%s, deviceId=%s, error=%v",
			deviceBindUser.CreatorId, deviceBindUser.DeviceId, result.Error)
		return result.Error
	}

	logger.Infof("保存绑定记录成功: userId=%s, deviceId=%s, id=%s",
		deviceBindUser.CreatorId, deviceBindUser.DeviceId, deviceBindUser.Id)
	return nil
}

// UpdateShareStatus 更新分享状态
// 对应 Java: updateWrapper.set(DeviceBindUserEntity::getShareStatus, DEVICE_SHARE_STATUS_1).eq(DeviceBindUserEntity::getDeviceId, deviceId).eq(DeviceBindUserEntity::getOwner, owner)
func (r *deviceBindUserRepository) UpdateShareStatus(ctx context.Context, deviceId, owner string, shareStatus string) error {
	result := r.Dbh.DB.WithContext(ctx).
		Model(&domain.DeviceBindUser{}).
		Where("device_id = ? AND owner = ?", deviceId, owner).
		Update("share_status", shareStatus)

	if result.Error != nil {
		logger.Errorf("更新分享状态失败: deviceId=%s, owner=%s, shareStatus=%s, error=%v",
			deviceId, owner, shareStatus, result.Error)
		return result.Error
	}

	logger.Infof("更新分享状态成功: deviceId=%s, owner=%s, shareStatus=%s, affected=%d",
		deviceId, owner, shareStatus, result.RowsAffected)
	return nil
}

// ResetOwner 重置基站拥有者
// 对应 Java: updateWrapper.set(DeviceBindUserEntity::getOwner, newOwner).eq(DeviceBindUserEntity::getDeviceId, deviceId)
func (r *deviceBindUserRepository) ResetOwner(ctx context.Context, deviceId, newOwner string) error {
	result := r.Dbh.DB.WithContext(ctx).
		Model(&domain.DeviceBindUser{}).
		Where("device_id = ?", deviceId).
		Update("owner", newOwner)

	if result.Error != nil {
		logger.Errorf("重置基站拥有者失败: deviceId=%s, newOwner=%s, error=%v",
			deviceId, newOwner, result.Error)
		return result.Error
	}

	logger.Infof("重置基站拥有者成功: deviceId=%s, newOwner=%s, affected=%d",
		deviceId, newOwner, result.RowsAffected)
	return nil
}

// GetByDeviceIdAndUserId 根据设备ID和用户ID获取绑定记录
// 对应 Java: queryWrapper.eq(DeviceBindUserEntity::getDeviceId, deviceId).eq(DeviceBindUserEntity::getCreatorId, userId)
func (r *deviceBindUserRepository) GetByDeviceIdAndUserId(ctx context.Context, deviceId, userId string) (*domain.DeviceBindUser, error) {
	var bindUser domain.DeviceBindUser

	result := r.Dbh.DB.WithContext(ctx).
		Where("device_id = ? AND creator_id = ?", deviceId, userId).
		First(&bindUser)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			logger.Debugf("绑定记录不存在: deviceId=%s, userId=%s", deviceId, userId)
			return nil, nil
		}
		logger.Errorf("查询绑定记录失败: deviceId=%s, userId=%s, error=%v", deviceId, userId, result.Error)
		return nil, result.Error
	}

	logger.Debugf("查询绑定记录成功: deviceId=%s, userId=%s, bindId=%s", deviceId, userId, bindUser.Id)
	return &bindUser, nil
}

// GetByDeviceId 根据设备ID获取所有绑定记录
// 对应 Java: queryWrapper.eq(DeviceBindUserEntity::getDeviceId, deviceId)
func (r *deviceBindUserRepository) GetByDeviceId(ctx context.Context, deviceId string) ([]*domain.DeviceBindUser, error) {
	var bindUsers []*domain.DeviceBindUser

	result := r.Dbh.DB.WithContext(ctx).
		Where("device_id = ?", deviceId).
		Find(&bindUsers)

	if result.Error != nil {
		logger.Errorf("查询设备绑定记录失败: deviceId=%s, error=%v", deviceId, result.Error)
		return nil, result.Error
	}

	logger.Debugf("查询设备绑定记录成功: deviceId=%s, count=%d", deviceId, len(bindUsers))
	return bindUsers, nil
}

// DeleteById 根据ID删除绑定记录
// 对应 Java: this.removeById(id)
func (r *deviceBindUserRepository) DeleteById(ctx context.Context, id string) error {
	result := r.Dbh.DB.WithContext(ctx).
		Delete(&domain.DeviceBindUser{}, "id = ?", id)

	if result.Error != nil {
		logger.Errorf("删除绑定记录失败: id=%s, error=%v", id, result.Error)
		return result.Error
	}

	logger.Infof("删除绑定记录成功: id=%s, affected=%d", id, result.RowsAffected)
	return nil
}

// DeleteByDeviceIdAndUserId 根据设备ID和用户ID删除绑定记录
// 对应 Java: queryWrapper.eq(DeviceBindUserEntity::getDeviceId, deviceId).eq(DeviceBindUserEntity::getCreatorId, userId); this.remove(queryWrapper)
func (r *deviceBindUserRepository) DeleteByDeviceIdAndUserId(ctx context.Context, deviceId, userId string) error {
	result := r.Dbh.DB.WithContext(ctx).
		Where("device_id = ? AND creator_id = ?", deviceId, userId).
		Delete(&domain.DeviceBindUser{})

	if result.Error != nil {
		logger.Errorf("删除绑定记录失败: deviceId=%s, userId=%s, error=%v", deviceId, userId, result.Error)
		return result.Error
	}

	logger.Infof("删除绑定记录成功: deviceId=%s, userId=%s, affected=%d", deviceId, userId, result.RowsAffected)
	return nil
}

// GetListByUserId 根据用户ID获取绑定关系
// 对应 Java: DeviceBindUserMapper.getListByUserId(@Param("tenantId") String tenantId,@Param("userId") String userId)
func (r *deviceBindUserRepository) GetListByUserId(ctx context.Context, tenantId, userId string) ([]*domain.DeviceBindUser, error) {
	var entities []*domain.DeviceBindUser

	result := r.Dbh.DB.WithContext(ctx).
		Where("tenant_id = ? AND creator_id = ?", tenantId, userId).
		Find(&entities)

	if result.Error != nil {
		logger.Errorf("根据用户ID获取绑定关系失败: tenantId=%s, userId=%s, error=%v", tenantId, userId, result.Error)
		return nil, result.Error
	}

	logger.Infof("根据用户ID获取绑定关系成功: tenantId=%s, userId=%s, count=%d", tenantId, userId, len(entities))
	return entities, nil
}

// DeleteBatchIds 根据id批量删除绑定关系
// 对应 Java: DeviceBindUserMapper.deleteBatchIds(List<String> ids)
func (r *deviceBindUserRepository) DeleteBatchIds(ctx context.Context, ids []string) error {
	logger.Infof("根据id批量删除绑定关系开始: ids=%v", ids)

	result := r.Dbh.DB.WithContext(ctx).
		Where("id IN ?", ids).
		Delete(&domain.DeviceBindUser{})

	if result.Error != nil {
		logger.Errorf("根据id批量删除绑定关系失败: ids=%v, error=%v", ids, result.Error)
		return result.Error
	}

	logger.Infof("根据id批量删除绑定关系成功: ids=%v, rowsAffected=%d", ids, result.RowsAffected)
	return nil
}

// FindByDeviceIds 根据设备ID列表查询绑定记录
// 对应 Java: selectList(queryWrapper.in(DeviceBindUserEntity::getDeviceId, param.getIds()))
func (r *deviceBindUserRepository) FindByDeviceIds(ctx context.Context, deviceIds []string) ([]*domain.DeviceBindUser, error) {
	var entities []*domain.DeviceBindUser

	result := r.Dbh.DB.WithContext(ctx).
		Where("device_id IN ?", deviceIds).
		Find(&entities)

	if result.Error != nil {
		logger.Errorf("根据设备ID列表查询绑定记录失败: deviceIds=%v, error=%v", deviceIds, result.Error)
		return nil, result.Error
	}

	logger.Infof("根据设备ID列表查询绑定记录成功: deviceIds=%v, count=%d", deviceIds, len(entities))
	return entities, nil
}

// FindByDeviceIdsAndFlag 根据设备ID列表和标志查询绑定记录
// 对应 Java: selectList(queryWrapper.in(DeviceBindUserEntity::getDeviceId, param.getIds()).eq(DeviceBindUserEntity::getFlag, DEVICE_FLAG_0))
func (r *deviceBindUserRepository) FindByDeviceIdsAndFlag(ctx context.Context, deviceIds []string, flag string) ([]*domain.DeviceBindUser, error) {
	var entities []*domain.DeviceBindUser

	result := r.Dbh.DB.WithContext(ctx).
		Where("device_id IN ? AND flag = ?", deviceIds, flag).
		Find(&entities)

	if result.Error != nil {
		logger.Errorf("根据设备ID列表和标志查询绑定记录失败: deviceIds=%v, flag=%s, error=%v", deviceIds, flag, result.Error)
		return nil, result.Error
	}

	logger.Infof("根据设备ID列表和标志查询绑定记录成功: deviceIds=%v, flag=%s, count=%d", deviceIds, flag, len(entities))
	return entities, nil
}

// DeleteByUserIds 根据用户ID删除绑定关系
// 对应 Java: delete(queryWrapper.eq(DeviceBindUserEntity::getTenantId, tenantId).and(e -> e.in(DeviceBindUserEntity::getCreatorId, userIds).or().in(DeviceBindUserEntity::getOwner, userIds)))
func (r *deviceBindUserRepository) DeleteByUserIds(ctx context.Context, userIds []string, tenantId string) (int64, error) {
	logger.Infof("根据用户ID删除绑定关系开始: userIds=%v, tenantId=%s", userIds, tenantId)

	result := r.Dbh.DB.WithContext(ctx).
		Where("tenant_id = ? AND (creator_id IN ? OR owner IN ?)", tenantId, userIds, userIds).
		Delete(&domain.DeviceBindUser{})

	if result.Error != nil {
		logger.Errorf("根据用户ID删除绑定关系失败: userIds=%v, tenantId=%s, error=%v", userIds, tenantId, result.Error)
		return 0, result.Error
	}

	logger.Infof("根据用户ID删除绑定关系成功: userIds=%v, tenantId=%s, rowsAffected=%d", userIds, tenantId, result.RowsAffected)
	return result.RowsAffected, nil
}

// DeleteByDeviceId 根据设备ID删除绑定关系
// 对应 Java: deviceBindUserService.remove(queryWrapper) (遵循规则42: 严格按照Java编码内容转换)
func (r *deviceBindUserRepository) DeleteByDeviceId(ctx context.Context, deviceId string) error {
	logger.Infof("根据设备ID删除绑定关系: deviceId=%s", deviceId)

	result := r.Dbh.DB.WithContext(ctx).Where("device_id = ?", deviceId).Delete(&domain.DeviceBindUser{})
	if result.Error != nil {
		logger.Errorf("根据设备ID删除绑定关系失败: deviceId=%s, error=%v", deviceId, result.Error)
		return result.Error
	}

	logger.Infof("根据设备ID删除绑定关系成功: deviceId=%s, rowsAffected=%d", deviceId, result.RowsAffected)
	return nil
}
