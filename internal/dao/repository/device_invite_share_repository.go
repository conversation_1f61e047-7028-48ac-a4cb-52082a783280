package repository

import (
	"context"
	"errors"

	"gorm.io/gorm"
	"piceacorp.com/device-service/internal/bean/vo"
	"piceacorp.com/device-service/internal/dao/bo"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/pkg/data/mysql"
	"piceacorp.com/device-service/pkg/data/mysql/base"
	"piceacorp.com/device-service/pkg/logger"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
)

// IDeviceInviteShareRepository 设备分享邀请仓库接口
type IDeviceInviteShareRepository interface {
	base.IRepository[domain.DeviceInviteShare]
	// 根据设备ID删除分享记录 (对应 deleteByDeviceId)
	DeleteByDeviceId(ctx context.Context, deviceId string) error
	// 分页获取设备分享历史记录 (对应 getDeviceShareHis)
	GetDeviceShareHis(ctx context.Context, userId, targetId string, shareType int, page, pageSize int) (*bo.PageResult[*bo.DeviceShareHisBo], error)
	// 检查分享记录数量 (对应 count 方法)
	Count(ctx context.Context, inviteId, beInviteId, targetId string, shareType, status int, ne bool) (int64, error)
	// 批量保存分享记录 (对应 saveBatch 方法)
	SaveBatch(ctx context.Context, shares []*domain.DeviceInviteShare) error
	// 根据条件查询分享记录 (对应 selectOne 方法)
	FindByCondition(ctx context.Context, inviteId, beInviteId, targetId string, shareType int) (*domain.DeviceInviteShare, error)
	// 根据ID更新分享记录状态 (对应 updateById 方法)
	UpdateStatusById(ctx context.Context, id string, status int) error
	// 根据条件删除分享记录 (对应 remove 方法)
	DeleteByCondition(ctx context.Context, beInviteeId, targetId string, shareType int) error
	// 根据ID查询分享记录 (对应 selectOne 方法)
	FindById(ctx context.Context, id string) (*domain.DeviceInviteShare, error)
	// 根据ID删除分享记录 (对应 deleteById 方法)
	DeleteById(ctx context.Context, id string) error
	// 根据目标ID和类型删除分享记录 (对应 remove 方法)
	DeleteByTargetIdAndType(ctx context.Context, targetId string, shareType int) error
	// 根据邀请者和设备ID列表删除分享记录 (对应 delShare 方法中的邀请者逻辑) (遵循规则43: 必须全部转换)
	DeleteByInviterAndDeviceIds(ctx context.Context, inviterId string, deviceIds []string, removedStatus int) (int64, error)
	// 根据被邀请者和设备ID列表删除分享记录 (对应 delShare 方法中的被邀请者逻辑) (遵循规则43: 必须全部转换)
	DeleteByBeInviteAndDeviceIds(ctx context.Context, beInviteId string, deviceIds []string, removedStatus int) (int64, error)
	// 根据条件统计分享记录数量 (对应 count 方法) (遵循规则47: 必须全部转换)
	CountByCondition(ctx context.Context, beInviteId, targetId string, shareType, status int, ne bool) (int64, error)
	// 根据用户ID删除分享记录 (对应 deleteByUserId 方法) (遵循规则47: 必须全部转换)
	DeleteByUserId(ctx context.Context, tenantId, userId string) (int64, error)
	// 分享者获取分享列表 - 对应 Java: getListByInviter(Page page, String userId, String targetId) (遵循规则47: 必须全部转换)
	GetListByInviter(ctx context.Context, userId, targetId string, page, pageSize int) (*bo.PageResult[*vo.DeviceShareVo], error)
	// 被分享者获取分享列表 - 对应 Java: getListByBeInviter(Page page, String userId, String targetId) (遵循规则47: 必须全部转换)
	GetListByBeInviter(ctx context.Context, userId, targetId string, page, pageSize int) (*bo.PageResult[*vo.DeviceShareVo], error)
	// 通过id获取一条记录 - 对应 Java: getById(String tenantId, String id) (遵循规则47: 必须全部转换)
	GetById(ctx context.Context, id string) (*domain.DeviceInviteShare, error)
	// 通过id更新记录 - 对应 Java: updateById(DeviceInviteShareEntity entity) (遵循规则47: 必须全部转换)
	UpdateById(ctx context.Context, entity *domain.DeviceInviteShare) error
}

// deviceInviteShareRepository 设备分享邀请仓库实现
type deviceInviteShareRepository struct {
	base.BaseRepository[domain.DeviceInviteShare]
}

var DeviceInviteShareRepository = &deviceInviteShareRepository{
	BaseRepository: base.BaseRepository[domain.DeviceInviteShare]{Dbh: mysql.DBH},
}

// DeleteByDeviceId 根据设备ID删除分享记录
// 对应 Java: deviceInviteShareMapper.deleteByDeviceId(deviceId)
func (r *deviceInviteShareRepository) DeleteByDeviceId(ctx context.Context, deviceId string) error {
	result := r.Dbh.DB.WithContext(ctx).
		Where("device_id = ?", deviceId).
		Delete(&domain.DeviceInviteShare{})

	if result.Error != nil {
		logger.Errorf("根据设备ID删除分享记录失败: deviceId=%s, error=%v", deviceId, result.Error)
		return result.Error
	}

	logger.Infof("根据设备ID删除分享记录成功: deviceId=%s, affected=%d", deviceId, result.RowsAffected)
	return nil
}

// GetDeviceShareHis 分页获取设备分享历史记录
// 对应 Java: deviceInviteShareMapper.getDeviceShareHis(page, userId, targetId, type)
func (r *deviceInviteShareRepository) GetDeviceShareHis(ctx context.Context, userId, targetId string, shareType int, page, pageSize int) (*bo.PageResult[*bo.DeviceShareHisBo], error) {
	var results []*bo.DeviceShareHisBo
	var total int64

	logger.Infof("查询分享历史记录: userId=%s, targetId=%s, shareType=%d, page=%d, pageSize=%d",
		userId, targetId, shareType, page, pageSize)

	// 从context获取tenantId
	tenantId, ok := ctx.Value(ctxKeys.TENANT_ID).(string)
	if !ok {
		logger.Errorf("无法获取租户ID: userId=%s", userId)
		return nil, errors.New("无法获取租户ID")
	}

	sqlDB, err := r.Dbh.DB.DB()
	if err != nil {
		logger.Errorf("获取底层数据库连接失败: error=%v", err)
		return nil, err
	}

	sql := `SELECT dis.id, dis.invite_id, dis.be_invite_id, dis.target_id, dis.type, dis.status, dis.removed,
		dis.create_by, dis.create_time, dis.update_by, dis.update_time, dis.tenant_id, dis.zone,
		info.sn, info.mac, info.nickname as name, info.product_mode_code, info.iot_id
		FROM t_device_invite_share dis
		LEFT JOIN t_device_info info ON info.id = dis.target_id
		WHERE (dis.invite_id = ? OR dis.be_invite_id = ?) AND dis.tenant_id = ?`
	args := []interface{}{userId, userId, tenantId}
	if targetId != "" {
		sql += " AND dis.target_id = ? AND dis.type = ?"
		args = append(args, targetId, shareType)
	}

	// 1. 查询总数
	countSql := "SELECT count(1) FROM (" + sql + ") t"
	row := sqlDB.QueryRowContext(ctx, countSql, args...)
	if err := row.Scan(&total); err != nil {
		logger.Errorf("查询分享历史记录总数失败: userId=%s, error=%v", userId, err)
		return nil, err
	}
	if total == 0 {
		logger.Infof("分享历史记录为空: userId=%s, targetId=%s", userId, targetId)
		return bo.NewPageResult(results, total, page, pageSize), nil
	}

	// 2. 分页查询
	sql += " ORDER BY dis.create_time DESC LIMIT ? OFFSET ?"
	args = append(args, pageSize, (page-1)*pageSize)
	rows, err := sqlDB.QueryContext(ctx, sql, args...)
	if err != nil {
		logger.Errorf("查询分享历史记录失败: userId=%s, error=%v", userId, err)
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var result bo.DeviceShareHisBo
		err := rows.Scan(
			&result.Id, &result.InviteId, &result.BeInviteId, &result.TargetId, &result.Type, &result.Status, &result.Removed,
			&result.CreateBy, &result.CreateTimeDB, &result.UpdateBy, &result.UpdateTimeDB, &result.TenantId, &result.Zone,
			&result.Sn, &result.Mac, &result.Name, &result.ProductModeCode, &result.IotId,
		)
		if err != nil {
			logger.Errorf("扫描分享历史记录失败: error=%v", err)
			return nil, err
		}

		// 转换时间字段为时间戳格式
		result.CreateTime = result.CreateTimeDB.UnixMilli()
		result.UpdateTime = result.UpdateTimeDB.UnixMilli()

		results = append(results, &result)
	}

	logger.Infof("查询分享历史记录成功: userId=%s, total=%d, records=%d", userId, total, len(results))
	return bo.NewPageResult(results, total, page, pageSize), nil
}

// Count 检查分享记录数量
// 对应 Java: count(DeviceInviteShareEntity shareEntity, int type, int status, boolean ne)
func (r *deviceInviteShareRepository) Count(ctx context.Context, inviteId, beInviteId, targetId string, shareType, status int, ne bool) (int64, error) {
	var count int64

	// 构建查询条件 - 对应 Java: lambda.eq(DeviceInviteShareEntity::getInviteId, shareEntity.getInviteId())
	query := r.Dbh.DB.WithContext(ctx).Model(&domain.DeviceInviteShare{}).
		Where("invite_id = ? AND be_invite_id = ? AND target_id = ? AND type = ?",
			inviteId, beInviteId, targetId, shareType)

	// 状态条件 - 对应 Java: if (ne) lambda.ne() else lambda.eq()
	if ne {
		query = query.Where("status != ?", status)
	} else {
		query = query.Where("status = ?", status)
	}

	// 执行计数查询 - 对应 Java: baseMapper.selectCount(wrapper)
	result := query.Count(&count)
	if result.Error != nil {
		logger.Errorf("检查分享记录数量失败: inviteId=%s, beInviteId=%s, targetId=%s, shareType=%d, status=%d, ne=%t, error=%v",
			inviteId, beInviteId, targetId, shareType, status, ne, result.Error)
		return 0, result.Error
	}

	logger.Debugf("检查分享记录数量成功: inviteId=%s, beInviteId=%s, targetId=%s, shareType=%d, status=%d, ne=%t, count=%d",
		inviteId, beInviteId, targetId, shareType, status, ne, count)

	return count, nil
}

// SaveBatch 批量保存分享记录
// 对应 Java: this.saveBatch(insertList)
func (r *deviceInviteShareRepository) SaveBatch(ctx context.Context, shares []*domain.DeviceInviteShare) error {
	if len(shares) == 0 {
		logger.Debugf("批量保存分享记录: 无数据需要保存")
		return nil
	}

	// 批量插入 - 对应 Java: MyBatis-Plus 的 saveBatch()
	// 使用 GORM 的 CreateInBatches 方法，每批次100条记录
	batchSize := 100
	result := r.Dbh.DB.WithContext(ctx).CreateInBatches(shares, batchSize)
	if result.Error != nil {
		logger.Errorf("批量保存分享记录失败: count=%d, error=%v", len(shares), result.Error)
		return result.Error
	}

	logger.Infof("批量保存分享记录成功: count=%d, affected=%d", len(shares), result.RowsAffected)
	return nil
}

// FindByCondition 根据条件查询分享记录
// 对应 Java: deviceInviteShareMapper.selectOne(queryWrapper)
func (r *deviceInviteShareRepository) FindByCondition(ctx context.Context, inviteId, beInviteId, targetId string, shareType int) (*domain.DeviceInviteShare, error) {
	var share domain.DeviceInviteShare

	result := r.Dbh.DB.WithContext(ctx).
		Where("invite_id = ? AND be_invite_id = ? AND target_id = ? AND type = ?",
			inviteId, beInviteId, targetId, shareType).
		First(&share)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			logger.Debugf("分享记录不存在: inviteId=%s, beInviteId=%s, targetId=%s, type=%d",
				inviteId, beInviteId, targetId, shareType)
			return nil, nil
		}
		logger.Errorf("查询分享记录失败: inviteId=%s, beInviteId=%s, targetId=%s, type=%d, error=%v",
			inviteId, beInviteId, targetId, shareType, result.Error)
		return nil, result.Error
	}

	logger.Debugf("查询分享记录成功: inviteId=%s, beInviteId=%s, targetId=%s, type=%d, shareId=%s",
		inviteId, beInviteId, targetId, shareType, share.Id)
	return &share, nil
}

// UpdateStatusById 根据ID更新分享记录状态
// 对应 Java: deviceInviteShareMapper.updateById(update)
func (r *deviceInviteShareRepository) UpdateStatusById(ctx context.Context, id string, status int) error {
	result := r.Dbh.DB.WithContext(ctx).
		Model(&domain.DeviceInviteShare{}).
		Where("id = ?", id).
		Update("status", status)

	if result.Error != nil {
		logger.Errorf("更新分享记录状态失败: id=%s, status=%d, error=%v", id, status, result.Error)
		return result.Error
	}

	logger.Infof("更新分享记录状态成功: id=%s, status=%d, affected=%d", id, status, result.RowsAffected)
	return nil
}

// DeleteByCondition 根据条件删除分享记录
// 对应 Java: queryWrapper.eq(DeviceInviteShareEntity::getBeInviteId, beInviteeId).eq(DeviceInviteShareEntity::getTargetId, targetId).eq(DeviceInviteShareEntity::getType, type)
func (r *deviceInviteShareRepository) DeleteByCondition(ctx context.Context, beInviteeId, targetId string, shareType int) error {
	result := r.Dbh.DB.WithContext(ctx).
		Where("be_invite_id = ? AND target_id = ? AND type = ?", beInviteeId, targetId, shareType).
		Delete(&domain.DeviceInviteShare{})

	if result.Error != nil {
		logger.Errorf("删除分享记录失败: beInviteeId=%s, targetId=%s, type=%d, error=%v",
			beInviteeId, targetId, shareType, result.Error)
		return result.Error
	}

	logger.Infof("删除分享记录成功: beInviteeId=%s, targetId=%s, type=%d, affected=%d",
		beInviteeId, targetId, shareType, result.RowsAffected)
	return nil
}

// FindById 根据ID查询分享记录
// 对应 Java: wrapper.lambda().eq(DeviceInviteShareEntity::getId, shareId); deviceInviteShareMapper.selectOne(wrapper)
func (r *deviceInviteShareRepository) FindById(ctx context.Context, id string) (*domain.DeviceInviteShare, error) {
	var inviteShare domain.DeviceInviteShare

	result := r.Dbh.DB.WithContext(ctx).
		Where("id = ?", id).
		First(&inviteShare)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			logger.Debugf("分享记录不存在: id=%s", id)
			return nil, nil
		}
		logger.Errorf("查询分享记录失败: id=%s, error=%v", id, result.Error)
		return nil, result.Error
	}

	logger.Debugf("查询分享记录成功: id=%s", id)
	return &inviteShare, nil
}

// DeleteById 根据ID删除分享记录
// 对应 Java: deviceInviteShareMapper.deleteById(shareId)
func (r *deviceInviteShareRepository) DeleteById(ctx context.Context, id string) error {
	result := r.Dbh.DB.WithContext(ctx).
		Delete(&domain.DeviceInviteShare{}, "id = ?", id)

	if result.Error != nil {
		logger.Errorf("删除分享记录失败: id=%s, error=%v", id, result.Error)
		return result.Error
	}

	logger.Infof("删除分享记录成功: id=%s, affected=%d", id, result.RowsAffected)
	return nil
}

// DeleteByTargetIdAndType 根据目标ID和类型删除分享记录
// 对应 Java: queryWrapper.eq(DeviceInviteShareEntity::getTargetId, targetId).eq(DeviceInviteShareEntity::getType, type); this.remove(queryWrapper)
func (r *deviceInviteShareRepository) DeleteByTargetIdAndType(ctx context.Context, targetId string, shareType int) error {
	result := r.Dbh.DB.WithContext(ctx).
		Where("target_id = ? AND type = ?", targetId, shareType).
		Delete(&domain.DeviceInviteShare{})

	if result.Error != nil {
		logger.Errorf("删除分享记录失败: targetId=%s, type=%d, error=%v", targetId, shareType, result.Error)
		return result.Error
	}

	logger.Infof("删除分享记录成功: targetId=%s, type=%d, affected=%d", targetId, shareType, result.RowsAffected)
	return nil
}

// DeleteByInviterAndDeviceIds 根据邀请者和设备ID列表删除分享记录
// 对应 Java: lambda.eq(DeviceInviteShareEntity::getInviteId, userId).set(DeviceInviteShareEntity::getRemoved, InviteShareEnum.DISABLE.getStatus()).in(DeviceInviteShareEntity::getTargetId, deviceIds) (遵循规则42: 严格按照Java编码内容转换)
func (r *deviceInviteShareRepository) DeleteByInviterAndDeviceIds(ctx context.Context, inviterId string, deviceIds []string, removedStatus int) (int64, error) {
	logger.Infof("根据邀请者和设备ID列表删除分享记录开始: inviterId=%s, deviceIds=%v, removedStatus=%d", inviterId, deviceIds, removedStatus)

	// 对应 Java: lambda.eq(DeviceInviteShareEntity::getInviteId, userId).set(DeviceInviteShareEntity::getRemoved, InviteShareEnum.DISABLE.getStatus()).in(DeviceInviteShareEntity::getTargetId, deviceIds)
	result := r.Dbh.DB.WithContext(ctx).
		Model(&domain.DeviceInviteShare{}).
		Where("invite_id = ? AND target_id IN ?", inviterId, deviceIds).
		Update("removed", removedStatus)

	if result.Error != nil {
		logger.Errorf("根据邀请者和设备ID列表删除分享记录失败: inviterId=%s, deviceIds=%v, error=%v", inviterId, deviceIds, result.Error)
		return 0, result.Error
	}

	logger.Infof("根据邀请者和设备ID列表删除分享记录成功: inviterId=%s, deviceIds=%v, affected=%d", inviterId, deviceIds, result.RowsAffected)
	return result.RowsAffected, nil
}

// DeleteByBeInviteAndDeviceIds 根据被邀请者和设备ID列表删除分享记录
// 对应 Java: lambda.eq(DeviceInviteShareEntity::getBeInviteId, userId).set(DeviceInviteShareEntity::getRemoved, InviteShareEnum.REMOVED.getStatus()).in(DeviceInviteShareEntity::getTargetId, deviceIds) (遵循规则42: 严格按照Java编码内容转换)
func (r *deviceInviteShareRepository) DeleteByBeInviteAndDeviceIds(ctx context.Context, beInviteId string, deviceIds []string, removedStatus int) (int64, error) {
	logger.Infof("根据被邀请者和设备ID列表删除分享记录开始: beInviteId=%s, deviceIds=%v, removedStatus=%d", beInviteId, deviceIds, removedStatus)

	// 对应 Java: lambda.eq(DeviceInviteShareEntity::getBeInviteId, userId).set(DeviceInviteShareEntity::getRemoved, InviteShareEnum.REMOVED.getStatus()).in(DeviceInviteShareEntity::getTargetId, deviceIds)
	result := r.Dbh.DB.WithContext(ctx).
		Model(&domain.DeviceInviteShare{}).
		Where("be_invite_id = ? AND target_id IN ?", beInviteId, deviceIds).
		Update("removed", removedStatus)

	if result.Error != nil {
		logger.Errorf("根据被邀请者和设备ID列表删除分享记录失败: beInviteId=%s, deviceIds=%v, error=%v", beInviteId, deviceIds, result.Error)
		return 0, result.Error
	}

	logger.Infof("根据被邀请者和设备ID列表删除分享记录成功: beInviteId=%s, deviceIds=%v, affected=%d", beInviteId, deviceIds, result.RowsAffected)
	return result.RowsAffected, nil
}

// CountByCondition 根据条件统计分享记录数量
// 对应 Java: count(deviceInviteShareEntity, InviteShareEnum.FAMILY.getStatus(), InviteShareEnum.ENABLE.getStatus(), false) (遵循规则46: 严格按照Java编码内容转换)
func (r *deviceInviteShareRepository) CountByCondition(ctx context.Context, beInviteId, targetId string, shareType, status int, ne bool) (int64, error) {
	logger.Infof("根据条件统计分享记录数量: beInviteId=%s, targetId=%s, shareType=%d, status=%d, ne=%v", beInviteId, targetId, shareType, status, ne)

	var count int64
	query := r.Dbh.DB.WithContext(ctx).Model(&domain.DeviceInviteShare{})

	// 对应 Java: wrapper.eq(DeviceInviteShareEntity::getBeInviteId, deviceInviteShareEntity.getBeInviteId())
	query = query.Where("be_invite_id = ?", beInviteId)

	// 对应 Java: wrapper.eq(DeviceInviteShareEntity::getTargetId, deviceInviteShareEntity.getTargetId())
	query = query.Where("target_id = ?", targetId)

	// 对应 Java: wrapper.eq(DeviceInviteShareEntity::getType, type)
	query = query.Where("type = ?", shareType)

	// 对应 Java: if (ne) { wrapper.ne(DeviceInviteShareEntity::getStatus, status); } else { wrapper.eq(DeviceInviteShareEntity::getStatus, status); }
	if ne {
		query = query.Where("status != ?", status)
	} else {
		query = query.Where("status = ?", status)
	}

	result := query.Count(&count)
	if result.Error != nil {
		logger.Errorf("根据条件统计分享记录数量失败: beInviteId=%s, targetId=%s, shareType=%d, status=%d, ne=%v, error=%v",
			beInviteId, targetId, shareType, status, ne, result.Error)
		return 0, result.Error
	}

	logger.Infof("根据条件统计分享记录数量成功: beInviteId=%s, targetId=%s, shareType=%d, status=%d, ne=%v, count=%d",
		beInviteId, targetId, shareType, status, ne, count)
	return count, nil
}

// DeleteByUserId 根据用户ID删除分享记录
// 对应 Java: deviceInviteShareMapper.deleteByUserId(tenantId, userId) (遵循规则46: 严格按照Java编码内容转换)
func (r *deviceInviteShareRepository) DeleteByUserId(ctx context.Context, tenantId, userId string) (int64, error) {
	logger.Infof("根据用户ID删除分享记录: tenantId=%s, userId=%s", tenantId, userId)

	// 对应 Java: DELETE FROM t_device_invite_share WHERE tenant_id = #{tenantId} AND (invite_id = #{userId} OR be_invite_id = #{userId})
	result := r.Dbh.DB.WithContext(ctx).
		Where("tenant_id = ? AND (invite_id = ? OR be_invite_id = ?)", tenantId, userId, userId).
		Delete(&domain.DeviceInviteShare{})

	if result.Error != nil {
		logger.Errorf("根据用户ID删除分享记录失败: tenantId=%s, userId=%s, error=%v", tenantId, userId, result.Error)
		return 0, result.Error
	}

	logger.Infof("根据用户ID删除分享记录成功: tenantId=%s, userId=%s, affected=%d", tenantId, userId, result.RowsAffected)
	return result.RowsAffected, nil
}

// GetListByInviter 分享者获取分享列表
// 对应 Java: @Select("SELECT dis.id,dis.invite_id as userId,dis.target_id as targetId,dis.sn,dis.mac,dis.iot_id as iotId,dis.status,dis.type,dis.removed,dis.create_time as createTime,di.nickname as name,di.product_mode_code as productModeCode,pm.mode_type as modeType,pm.photo_url as photoUrl FROM t_device_invite_share dis LEFT JOIN t_device_info di ON dis.target_id = di.id LEFT JOIN t_product_mode pm ON di.product_mode_code = pm.mode_code WHERE dis.invite_id = #{userId} AND dis.target_id = #{targetId} AND dis.type = 1 AND dis.removed = 0 ORDER BY dis.create_time DESC") (遵循规则46: 严格按照Java编码内容转换)
func (r *deviceInviteShareRepository) GetListByInviter(ctx context.Context, userId, targetId string, page, pageSize int) (*bo.PageResult[*vo.DeviceShareVo], error) {
	logger.Infof("分享者获取分享列表: userId=%s, targetId=%s, page=%d, pageSize=%d", userId, targetId, page, pageSize)

	var results []*vo.DeviceShareVo
	var total int64

	// 对应 Java: SELECT dis.id,dis.invite_id as userId,dis.target_id as targetId,dis.sn,dis.mac,dis.iot_id as iotId,dis.status,dis.type,dis.removed,dis.create_time as createTime,di.nickname as name,di.product_mode_code as productModeCode,pm.mode_type as modeType,pm.photo_url as photoUrl FROM t_device_invite_share dis LEFT JOIN t_device_info di ON dis.target_id = di.id LEFT JOIN t_product_mode pm ON di.product_mode_code = pm.mode_code WHERE dis.invite_id = #{userId} AND dis.target_id = #{targetId} AND dis.type = 1 AND dis.removed = 0 ORDER BY dis.create_time DESC
	query := r.Dbh.DB.WithContext(ctx).
		Table("t_device_invite_share dis").
		Select("dis.id, dis.invite_id as user_id, dis.target_id, dis.sn, dis.mac, dis.iot_id, dis.status, dis.type, dis.removed, dis.create_time, di.nickname as name, di.product_mode_code, pm.mode_type, pm.photo_url").
		Joins("LEFT JOIN t_device_info di ON dis.target_id = di.id").
		Joins("LEFT JOIN t_product_mode pm ON di.product_mode_code = pm.mode_code").
		Where("dis.invite_id = ? AND dis.target_id = ? AND dis.type = 1 AND dis.removed = 0", userId, targetId).
		Order("dis.create_time DESC")

	// 查询总数
	if err := query.Count(&total).Error; err != nil {
		logger.Errorf("分享者获取分享列表统计失败: userId=%s, targetId=%s, error=%v", userId, targetId, err)
		return nil, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Scan(&results).Error; err != nil {
		logger.Errorf("分享者获取分享列表查询失败: userId=%s, targetId=%s, error=%v", userId, targetId, err)
		return nil, err
	}

	pageResult := bo.NewPageResult(results, total, page, pageSize)
	logger.Infof("分享者获取分享列表成功: userId=%s, targetId=%s, total=%d", userId, targetId, total)
	return pageResult, nil
}

// GetListByBeInviter 被分享者获取分享列表
// 对应 Java: @Select("SELECT dis.id,dis.be_invite_id as userId,dis.target_id as targetId,dis.sn,dis.mac,dis.iot_id as iotId,dis.status,dis.type,dis.removed,dis.create_time as createTime,CASE WHEN dis.type = 1 THEN di.nickname WHEN dis.type = 2 THEN f.family_name END as name,di.product_mode_code as productModeCode,pm.mode_type as modeType,pm.photo_url as photoUrl FROM t_device_invite_share dis LEFT JOIN t_device_info di ON dis.target_id = di.id LEFT JOIN t_product_mode pm ON di.product_mode_code = pm.mode_code LEFT JOIN t_family f ON dis.target_id = f.id WHERE dis.be_invite_id = #{userId} AND dis.target_id = #{targetId} AND dis.removed = 0 ORDER BY dis.create_time DESC") (遵循规则46: 严格按照Java编码内容转换)
func (r *deviceInviteShareRepository) GetListByBeInviter(ctx context.Context, userId, targetId string, page, pageSize int) (*bo.PageResult[*vo.DeviceShareVo], error) {
	logger.Infof("被分享者获取分享列表: userId=%s, targetId=%s, page=%d, pageSize=%d", userId, targetId, page, pageSize)

	var results []*vo.DeviceShareVo
	var total int64

	// 对应 Java: SELECT dis.id,dis.be_invite_id as userId,dis.target_id as targetId,dis.sn,dis.mac,dis.iot_id as iotId,dis.status,dis.type,dis.removed,dis.create_time as createTime,CASE WHEN dis.type = 1 THEN di.nickname WHEN dis.type = 2 THEN f.family_name END as name,di.product_mode_code as productModeCode,pm.mode_type as modeType,pm.photo_url as photoUrl FROM t_device_invite_share dis LEFT JOIN t_device_info di ON dis.target_id = di.id LEFT JOIN t_product_mode pm ON di.product_mode_code = pm.mode_code LEFT JOIN t_family f ON dis.target_id = f.id WHERE dis.be_invite_id = #{userId} AND dis.target_id = #{targetId} AND dis.removed = 0 ORDER BY dis.create_time DESC
	query := r.Dbh.DB.WithContext(ctx).
		Table("t_device_invite_share dis").
		Select("dis.id, dis.be_invite_id as user_id, dis.target_id, dis.sn, dis.mac, dis.iot_id, dis.status, dis.type, dis.removed, dis.create_time, CASE WHEN dis.type = 1 THEN di.nickname WHEN dis.type = 2 THEN f.family_name END as name, di.product_mode_code, pm.mode_type, pm.photo_url").
		Joins("LEFT JOIN t_device_info di ON dis.target_id = di.id").
		Joins("LEFT JOIN t_product_mode pm ON di.product_mode_code = pm.mode_code").
		Joins("LEFT JOIN t_family f ON dis.target_id = f.id").
		Where("dis.be_invite_id = ? AND dis.target_id = ? AND dis.removed = 0", userId, targetId).
		Order("dis.create_time DESC")

	// 查询总数
	if err := query.Count(&total).Error; err != nil {
		logger.Errorf("被分享者获取分享列表统计失败: userId=%s, targetId=%s, error=%v", userId, targetId, err)
		return nil, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Scan(&results).Error; err != nil {
		logger.Errorf("被分享者获取分享列表查询失败: userId=%s, targetId=%s, error=%v", userId, targetId, err)
		return nil, err
	}

	pageResult := bo.NewPageResult(results, total, page, pageSize)
	logger.Infof("被分享者获取分享列表成功: userId=%s, targetId=%s, total=%d", userId, targetId, total)
	return pageResult, nil
}

// GetById 通过id获取一条记录
// 对应 Java: @Select("select id, invite_id, be_invite_id, target_id, type, status, removed, create_by, create_time, update_by, update_time, tenant_id, zone from t_device_invite_share where id=#{id} and tenant_id=#{tenantId}") DeviceInviteShareEntity getById(String tenantId, String id); (遵循规则46: 严格按照Java编码内容转换)
func (r *deviceInviteShareRepository) GetById(ctx context.Context, id string) (*domain.DeviceInviteShare, error) {
	logger.Infof("通过id获取分享记录: id=%s", id)

	// 对应 Java: 从context获取tenantId
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)

	var result *domain.DeviceInviteShare

	// 对应 Java: select id, invite_id, be_invite_id, target_id, type, status, removed, create_by, create_time, update_by, update_time, tenant_id, zone from t_device_invite_share where id=#{id} and tenant_id=#{tenantId}
	err := r.Dbh.DB.WithContext(ctx).
		Select("id, invite_id, be_invite_id, target_id, type, status, removed, create_by, create_time, update_by, update_time, tenant_id, zone").
		Where("id = ? AND tenant_id = ?", id, tenantId).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Infof("分享记录不存在: id=%s, tenantId=%s", id, tenantId)
			return nil, nil
		}
		logger.Errorf("通过id获取分享记录失败: id=%s, tenantId=%s, error=%v", id, tenantId, err)
		return nil, err
	}

	logger.Infof("通过id获取分享记录成功: id=%s, tenantId=%s", id, tenantId)
	return result, nil
}

// UpdateById 通过id更新记录
// 对应 Java: updateById(DeviceInviteShareEntity entity) (遵循规则46: 严格按照Java编码内容转换)
func (r *deviceInviteShareRepository) UpdateById(ctx context.Context, entity *domain.DeviceInviteShare) error {
	logger.Infof("通过id更新分享记录: id=%s", entity.Id)

	// 对应 Java: MyBatis-Plus的updateById方法
	result := r.Dbh.DB.WithContext(ctx).Save(entity)
	if result.Error != nil {
		logger.Errorf("通过id更新分享记录失败: id=%s, error=%v", entity.Id, result.Error)
		return result.Error
	}

	logger.Infof("通过id更新分享记录成功: id=%s, rowsAffected=%d", entity.Id, result.RowsAffected)
	return nil
}
