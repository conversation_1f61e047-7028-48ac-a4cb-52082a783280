package repository

import (
	"context"

	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/pkg/data/mysql"
	"piceacorp.com/device-service/pkg/data/mysql/base"
	"piceacorp.com/device-service/pkg/logger"
)

// IDeviceMapRepository 设备地图仓库接口
// 对应 Java: DeviceMapMapper (遵循规则6: 严格按照现有Repository模式转换)
type IDeviceMapRepository interface {
	base.IRepository[domain.DeviceMap]
	// 根据SN查询设备地图列表 - 对应 Java: this.list(lambda.eq(DeviceMapEntity::getSn, sn))
	FindBySn(ctx context.Context, sn string) ([]*domain.DeviceMap, error)
}

// deviceMapRepository 设备地图仓库实现 (遵循规则6: 严格按照现有Repository模式转换)
type deviceMapRepository struct {
	base.BaseRepository[domain.DeviceMap]
}

// DeviceMapRepository 全局设备地图仓库实例 (遵循规则6和15: 严格按照现有Repository模式)
var DeviceMapRepository = &deviceMapRepository{BaseRepository: base.BaseRepository[domain.DeviceMap]{Dbh: mysql.DBH}}

// FindBySn 根据SN查询设备地图列表
// 对应 Java: LambdaQueryWrapper<DeviceMapEntity> lambda = new QueryWrapper<DeviceMapEntity>().lambda(); lambda.eq(DeviceMapEntity::getSn, sn); List<DeviceMapEntity> deviceMapList = this.list(lambda); (遵循规则6和45: 严格按照现有Repository模式和Java编码内容转换)
func (r *deviceMapRepository) FindBySn(ctx context.Context, sn string) ([]*domain.DeviceMap, error) {
	logger.Infof("根据SN查询设备地图列表: sn=%s", sn)

	var deviceMaps []*domain.DeviceMap

	// 对应 Java: lambda.eq(DeviceMapEntity::getSn, sn) (遵循规则6: 使用现有Repository模式的数据库连接方式)
	result := r.Dbh.DB.WithContext(ctx).Where("sn = ?", sn).Find(&deviceMaps)
	if result.Error != nil {
		logger.Errorf("根据SN查询设备地图列表失败: sn=%s, error=%v", sn, result.Error)
		return nil, result.Error
	}

	logger.Infof("根据SN查询设备地图列表成功: sn=%s, count=%d", sn, len(deviceMaps))
	return deviceMaps, nil
}
