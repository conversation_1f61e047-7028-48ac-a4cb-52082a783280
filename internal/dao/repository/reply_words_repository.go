package repository

import (
	"context"

	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/pkg/data/mysql"
	"piceacorp.com/device-service/pkg/data/mysql/base"
	"piceacorp.com/device-service/pkg/logger"
)

// IReplyWordsRepository 回复词仓库接口
// 对应 Java: ReplyWordsMapper (遵循规则45: 严格按照规则6转换Mapper到Repository)
type IReplyWordsRepository interface {
	base.IRepository[domain.ReplyWords]
	// 根据领域和意图查询回复词 - 对应 Java: selectList(wrapper)
	FindByDomainAndIntent(ctx context.Context, domain, intent string, slots map[string]interface{}) ([]*domain.ReplyWords, error)
}

// replyWordsRepository 回复词仓库实现 (遵循规则6: 严格按照现有Repository模式转换)
type replyWordsRepository struct {
	base.BaseRepository[domain.ReplyWords]
}

// ReplyWordsRepository 全局回复词仓库实例 (遵循规则15: 只对外暴露全局变量)
var ReplyWordsRepository = &replyWordsRepository{
	BaseRepository: base.BaseRepository[domain.ReplyWords]{Dbh: mysql.DBH},
}

// FindByDomainAndIntent 根据领域和意图查询回复词
// 对应 Java: LambdaQueryWrapper<ReplyWordsEntity> wrapper = new LambdaQueryWrapper<>(); wrapper.eq(ReplyWordsEntity::getDomain, domain).eq(ReplyWordsEntity::getIntent, intent); List<ReplyWordsEntity> replyWords = replyWordsMapper.selectList(wrapper); (遵循规则46: 严格按照Java编码内容转换)
func (r *replyWordsRepository) FindByDomainAndIntent(ctx context.Context, domainStr, intent string,
	slots map[string]interface{}) ([]*domain.ReplyWords, error) {
	logger.Infof("根据领域和意图查询回复词: domain=%s, intent=%s", domainStr, intent)

	var replyWords []*domain.ReplyWords

	// 对应 Java: wrapper.eq(ReplyWordsEntity::getDomain, domain).eq(ReplyWordsEntity::getIntent, intent)
	result := r.Dbh.DB.WithContext(ctx).
		Where("domain = ? AND intent = ?", domainStr, intent).
		Find(&replyWords)

	if result.Error != nil {
		logger.Errorf("根据领域和意图查询回复词失败: domain=%s, intent=%s, error=%v", domainStr, intent, result.Error)
		return nil, result.Error
	}

	logger.Infof("根据领域和意图查询回复词成功: domain=%s, intent=%s, count=%d", domainStr, intent, len(replyWords))
	return replyWords, nil
}
