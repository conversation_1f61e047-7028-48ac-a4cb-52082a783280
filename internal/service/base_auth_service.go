package service

import (
	"context"

	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/service/remote"
	remoteReq "piceacorp.com/device-service/internal/service/remote/req"
	"piceacorp.com/device-service/pkg/logger"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IBaseAuthService 基站认证服务接口
// 对应 Java: IBaseAuthService (遵循规则46: 严格按照Java编码内容转换)
type IBaseAuthService interface {
	// 基站解除认证 - 对应 Java: ResponseMessage<Boolean> baseUnAuth(BaseUnAuthDeviceReq baseUnAuthDeviceReq);
	BaseUnAuth(ctx context.Context, baseUnAuthDeviceReq *req.BaseUnAuthDeviceReq) webRes.IBaseRes
}

var BaseAuthService baseAuthService

type baseAuthService struct{}

// BaseUnAuth 基站解除认证
// 对应 Java: BaseAuthServiceImpl.baseUnAuth(BaseUnAuthDeviceReq baseUnAuthDeviceReq) (遵循规则46: 严格按照Java编码内容转换)
func (s *baseAuthService) BaseUnAuth(ctx context.Context, baseUnAuthDeviceReq *req.BaseUnAuthDeviceReq) webRes.IBaseRes {
	logger.Infof("基站解除认证: baseId=%s, deviceIds=%v", baseUnAuthDeviceReq.BaseId, baseUnAuthDeviceReq.DeviceIds)

	// 对应 Java: if (CollectionUtils.isEmpty(baseUnAuthDeviceReq.getDeviceIds())) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "deviceIds"); }
	if len(baseUnAuthDeviceReq.DeviceIds) == 0 {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "deviceIds")
	}

	// 对应 Java: List<String> snList = deviceInfoService.getSnListByDeviceIds(baseUnAuthDeviceReq.getDeviceIds());
	snList, err := DeviceInfoService.GetSnListByDeviceIds(ctx, baseUnAuthDeviceReq.DeviceIds)
	if err != nil {
		logger.Errorf("根据设备ID列表获取SN列表失败: deviceIds=%v, error=%v", baseUnAuthDeviceReq.DeviceIds, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取设备SN列表失败")
	}

	// 对应 Java: if (CollectionUtils.isNotEmpty(snList)) { ClearFlowParam param = new ClearFlowParam(); param.setTenantId(SystemContextUtils.getTenantId()); param.setSnList(snList); dataStatisticsServiceRemote.flow(param); }
	if len(snList) > 0 {
		tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)
		clearFlowParam := &remoteReq.ClearFlowParam{
			TenantId: tenantId,
			SnList:   snList,
		}
		result := remote.DataStatisticsService.Flow(ctx, clearFlowParam)
		if result.Fail() {
			logger.Errorf("清除设备流水数据失败: snList=%v, error=%v", snList, result.Result)
			// 清除流水数据失败不影响主流程
		}
	}

	// 对应 Java: Boolean result = deviceBindBaseService.baseUnAuth(baseUnAuthDeviceReq); return ResponseMessage.buildSuccess(result);
	result, err := DeviceBindBaseService.BaseUnAuth(ctx, baseUnAuthDeviceReq)
	if err != nil {
		logger.Errorf("基站解除认证失败: baseId=%s, deviceIds=%v, error=%v", baseUnAuthDeviceReq.BaseId, baseUnAuthDeviceReq.DeviceIds, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "基站解除认证失败")
	}

	logger.Infof("基站解除认证成功: baseId=%s, deviceIds=%v, result=%v", baseUnAuthDeviceReq.BaseId, baseUnAuthDeviceReq.DeviceIds, result)
	return webRes.Cb(result)
}
