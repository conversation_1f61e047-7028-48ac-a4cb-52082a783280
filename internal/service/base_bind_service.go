package service

import (
	"context"

	"piceacorp.com/device-service/pkg/logger"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IBaseBindService 基站绑定服务接口
// 对应 Java: IBaseBindService (遵循规则46: 严格按照Java编码内容转换)
type IBaseBindService interface {
	// 查询基站绑定信息 - 对应 Java: ResponseMessage<List<BaseBindVo>> baseBind(String baseId);
	BaseBind(ctx context.Context, baseId string) webRes.IBaseRes
}

var BaseBindService baseBindService

type baseBindService struct{}

// BaseBind 查询基站绑定信息
// 对应 Java: BaseBindServiceImpl.baseBind(String baseId) (遵循规则46: 严格按照Java编码内容转换)
func (s *baseBindService) BaseBind(ctx context.Context, baseId string) webRes.IBaseRes {
	logger.Infof("查询基站绑定信息: baseId=%s", baseId)

	// 对应 Java: List<BaseBindVo> baseBindList = deviceBindBaseService.getBaseBind(baseId);
	baseBindList, err := DeviceBindBaseService.GetBaseBindList(ctx, baseId)
	if err != nil {
		logger.Errorf("查询基站绑定信息失败: baseId=%s, error=%v", baseId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "查询基站绑定信息失败")
	}

	// 对应 Java: return ResponseMessage.buildSuccess(baseBindList);
	logger.Infof("查询基站绑定信息成功: baseId=%s, count=%d", baseId, len(baseBindList))
	return webRes.Cb(baseBindList)
}
