package service

import (
	"context"

	"piceacorp.com/device-service/internal/bean/res"
	"piceacorp.com/device-service/internal/service/remote"
	"piceacorp.com/device-service/pkg/logger"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// ICommonService 通用服务接口
// 对应 Java: CommonService (遵循规则46: 严格按照Java编码内容转换)
type ICommonService interface {
	// 检查分享记录 - 对应 Java: checkShareRecord(String recordId)
	CheckShareRecord(ctx context.Context, recordId string) webRes.IBaseRes
}

var CommonService commonService

type commonService struct{}

// CheckShareRecord 检查分享记录
// 对应 Java: CommonService.checkShareRecord(String recordId) (遵循规则46: 严格按照Java编码内容转换)
func (s *commonService) CheckShareRecord(ctx context.Context, recordId string) webRes.IBaseRes {
	logger.Infof("检查分享记录: recordId=%s", recordId)

	// 对应 Java: return infrastructureThirdRemote.checkShareRecord(recordId);
	result := remote.InfrastructureThirdService.CheckShareRecord(ctx, recordId)
	if result.Fail() {
		logger.Errorf("检查分享记录失败: recordId=%s, error=%s", recordId, result.GetMsg())
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "检查分享记录失败")
	}

	// 对应 Java: NoticeShareRecordEntity one = responseMessage.getResult();
	shareRecord := result.GetData()

	logger.Infof("检查分享记录成功: recordId=%s", recordId)
	return webRes.Cb(shareRecord)
}
