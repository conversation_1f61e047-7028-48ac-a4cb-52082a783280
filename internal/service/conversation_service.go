package service

import (
	"context"

	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/dao/repository"
	"piceacorp.com/device-service/pkg/logger"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IConversationService 会话记录服务接口
// 对应 Java: IConversationService (遵循规则2: 完整转换所有使用的mapper方法)
type IConversationService interface {
	// 根据SN删除会话记录 - 对应 Java: void deleteBySn(String sn);
	DeleteBySn(ctx context.Context, sn string) error
	// 插入会话记录 - 对应 Java: conversationMapper.insert(conversation) (遵循规则2: 完整转换所有使用的mapper方法)
	Insert(ctx context.Context, conversation *domain.Conversation) error
	// 分页查询会话记录 - 对应 Java: ResponseMessage<Page<Conversation>> page(ConversationPageReq pageReq) (遵循规则2: 完整转换所有使用的mapper方法)
	Page(ctx context.Context, pageReq *req.ConversationPageReq) webRes.IBaseRes
}

var ConversationService conversationService

type conversationService struct{}

// DeleteBySn 根据SN删除会话记录
// 对应 Java: ConversationServiceImpl.deleteBySn(String sn) (遵循规则42: 严格按照Java编码内容转换)
func (s *conversationService) DeleteBySn(ctx context.Context, sn string) error {
	logger.Infof("删除语音会话记录: sn=%s", sn)

	// 对应 Java: LambdaQueryWrapper<Conversation> queryWrapper = new LambdaQueryWrapper<>(); queryWrapper.eq(Conversation::getSn, sn); conversationMapper.delete(queryWrapper);
	err := repository.ConversationRepository.DeleteBySn(ctx, sn)
	if err != nil {
		logger.Errorf("删除语音会话记录失败: sn=%s, error=%v", sn, err)
		return err
	}

	logger.Infof("删除语音会话记录成功: sn=%s", sn)
	return nil
}

// Insert 插入会话记录
// 对应 Java: conversationMapper.insert(conversation) (遵循规则2: 完整转换所有使用的mapper方法)
func (s *conversationService) Insert(ctx context.Context, conversation *domain.Conversation) error {
	logger.Infof("插入语音会话记录: sn=%s, id=%s", conversation.Sn, conversation.Id)

	// 对应 Java: conversationMapper.insert(conversation);
	err := repository.ConversationRepository.Insert(ctx, conversation)
	if err != nil {
		logger.Errorf("插入语音会话记录失败: sn=%s, id=%s, error=%v", conversation.Sn, conversation.Id, err)
		return err
	}

	logger.Infof("插入语音会话记录成功: sn=%s, id=%s", conversation.Sn, conversation.Id)
	return nil
}

// Page 分页查询会话记录
// 对应 Java: ConversationServiceImpl.page(ConversationPageReq pageReq) (遵循规则2: 完整转换所有使用的mapper方法)
func (s *conversationService) Page(ctx context.Context, pageReq *req.ConversationPageReq) webRes.IBaseRes {
	logger.Infof("分页查询会话记录: sn=%s, page=%d, pageSize=%d", pageReq.Sn, pageReq.Page, pageReq.PageSize)

	// 对应 Java: if (ObjectUtils.isEmpty(pageReq) || StringUtils.isBlank(pageReq.getSn())) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL); }
	if pageReq == nil || pageReq.Sn == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "")
	}

	// 对应 Java: Page<Conversation> conversationPage = conversationMapper.selectPage(new Page<>(pageReq.getPage(), pageReq.getPageSize()), queryWrapper);
	pageResult, err := repository.ConversationRepository.SelectPage(ctx, pageReq.Sn, pageReq.Page, pageReq.PageSize)
	if err != nil {
		logger.Errorf("分页查询会话记录失败: sn=%s, page=%d, pageSize=%d, error=%v", pageReq.Sn, pageReq.Page, pageReq.PageSize, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "查询会话记录失败")
	}

	// 对应 Java: return ResponseMessage.buildSuccess(conversationPage);
	logger.Infof("分页查询会话记录成功: sn=%s, page=%d, pageSize=%d, total=%d", pageReq.Sn, pageReq.Page, pageReq.PageSize, pageResult.Total)
	return webRes.Cb(pageResult)
}
