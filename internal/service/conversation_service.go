package service

import (
	"context"

	"piceacorp.com/device-service/internal/dao/repository"
	"piceacorp.com/device-service/pkg/logger"
)

// IConversationService 会话记录服务接口
// 对应 Java: IConversationService (遵循规则42: 严格按照Java编码内容转换)
type IConversationService interface {
	// 根据SN删除会话记录 - 对应 Java: void deleteBySn(String sn);
	DeleteBySn(ctx context.Context, sn string) error
}

var ConversationService conversationService

type conversationService struct{}

// DeleteBySn 根据SN删除会话记录
// 对应 Java: ConversationServiceImpl.deleteBySn(String sn) (遵循规则42: 严格按照Java编码内容转换)
func (s *conversationService) DeleteBySn(ctx context.Context, sn string) error {
	logger.Infof("删除语音会话记录: sn=%s", sn)

	// 对应 Java: LambdaQueryWrapper<Conversation> queryWrapper = new LambdaQueryWrapper<>(); queryWrapper.eq(Conversation::getSn, sn); conversationMapper.delete(queryWrapper);
	err := repository.ConversationRepository.DeleteBySn(ctx, sn)
	if err != nil {
		logger.Errorf("删除语音会话记录失败: sn=%s, error=%v", sn, err)
		return err
	}

	logger.Infof("删除语音会话记录成功: sn=%s", sn)
	return nil
}
