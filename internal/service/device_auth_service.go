package service

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/internal/bean/enum"
	"piceacorp.com/device-service/internal/service/remote"
	creq "piceacorp.com/device-service/internal/service/remote/req"
	cres "piceacorp.com/device-service/internal/service/remote/res"
	"piceacorp.com/device-service/pkg/data/cache"
	"piceacorp.com/device-service/pkg/nacos"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
	"strings"
	"time"

	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/bean/res"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/dao/repository"
	"piceacorp.com/device-service/pkg/logger"
	"piceacorp.com/device-service/pkg/utils"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
)

type IDeviceAuthService interface {
	Login(ctx context.Context, req *req.LoginReq) webRes.IBaseRes
	GetCountryCity(ip string) (*cres.CountryCity, error)
}

var DeviceAuthService deviceAuthService

type deviceAuthService struct {
}

func (s *deviceAuthService) Login(ctx context.Context, req *req.LoginReq) webRes.IBaseRes {
	logger.Info(ctx, "设备登录开始", "tenantId", req.TenantID, "mac", req.MAC, "sn", req.SN, "productModeCode", req.ProductModeCode)
	ginCtx := ctx.(*gin.Context)
	// 设备登录请求头没有传递租户ID，需要从请求体中获取，再set进入
	ginCtx.Set(ctxKeys.TENANT_ID, req.TenantID)
	// 获取客户端IP
	clientIP := ctx.Value(ctxKeys.CLIENT_IP).(string)

	// 获取地理位置信息
	countryCity := s.GetCountryCity(ctx, clientIP)

	// 处理错误MAC地址的情况
	mac := req.MAC
	wrongMac := "00:00:00:00:00:00"
	if mac == wrongMac {
		// 设备BUG，服务端补锅
		device, _ := repository.DeviceRepository.FindBySN(ctx, req.SN)
		if device == nil {
			// 还未注册的错误设备，使用sn作为mac继续注册
			mac = req.SN
		} else if mac == device.Mac {
			// 已经注册的错误设备，还没被同样的错误mac顶掉,将mac换成sn
			mac = req.SN
			device.Mac = mac
		} else if strings.HasPrefix(device.Mac, wrongMac) {
			// 已经注册的错误设备，已经被同样的错误mac顶掉,将mac换成sn
			mac = req.SN
			device.Mac = mac
		} else {
			return webRes.Ce(webErr.ILLEGAL_OPERATE, "sn已被另一台设备激活")
		}
	}

	// 通过MAC地址查找设备
	device, _ := repository.DeviceRepository.FindByMAC(ctx, mac)

	// 如果设备存在，执行登录逻辑
	if device != nil {
		return s.doLogin(ctx, req.TenantID, mac, req.SN, req.Keyt, clientIP, device, countryCity, req.ProductModeCode, req.PackageVersions)
	}

	// 设备不存在，执行注册逻辑
	return s.doRegister(ctx, req.TenantID, req.SN, mac, req.Keyt, clientIP, req.ProductModeCode, countryCity, req.PackageVersions)
}

func (s *deviceAuthService) GetCountryCity(ctx context.Context, ip string) *cres.CountryCity {
	// 这里应该调用第三方IP地理位置服务
	response := remote.InfrastructureThirdService.GetIpInfo(ctx, ip)
	if response.Fail() {
		logger.Warn("获取地理位置信息失败", "ip", ip, "error", response)
		return nil
	}
	return response.Result
}

func (s *deviceAuthService) doLogin(ctx context.Context, tenantId, mac, reqSn, key, ip string,
	device *domain.DeviceInfo, countryCity *cres.CountryCity, productModeCode string,
	packageVersions []req.RobotVersion) webRes.IBaseRes {

	logger.Info("设备登录处理", "tenantId", tenantId, "mac", mac, "reqSn", reqSn, "productModeCode", productModeCode)

	// 验证SN是否匹配
	if device.Sn != reqSn {
		return webRes.Ce(webErr.ILLEGAL_OPERATE, fmt.Sprintf("mac已被占用,已注册的设备mac【%s】，reqSn【%s】；当前的mac【%s】,reqSn【%s】",
			device.Mac, device.Sn, mac, reqSn))
	}

	// 验证产品型号代码是否匹配
	if device.ProductModeCode != productModeCode {
		return webRes.Ce(webErr.ILLEGAL_OPERATE, "产品型号代码不匹配")
	}

	if device.Key != key {
		return webRes.Ce(webErr.ILLEGAL_OPERATE, "key错误")
	}
	if device.Key != "" || device.PhotoUrl != "" {
		//  调用产品服务验证SN和产品型号
		productRes := remote.ProductService.GetByCodeAdSn(ctx, reqSn, productModeCode, false)
		if productRes.Fail() {
			return productRes
		}
		productResult := productRes.Result
		snDetailEntity := productResult.SnDetailEntity
		if snDetailEntity == nil {
			return webRes.Ce(webErr.ILLEGAL_OPERATE, "sn不存在")
		}
		if snDetailEntity.Key == "" {
			return webRes.Ce(webErr.ILLEGAL_OPERATE, "服务器中该sn【"+reqSn+"】数据异常，key不存在")
		}
		if snDetailEntity.Key != key {
			return webRes.Ce(webErr.ILLEGAL_OPERATE, "key异常，sn【"+reqSn+"】的key【"+snDetailEntity.Key+"】与参数的key【"+key+"】不一致")
		}
		device.Key = snDetailEntity.Key
		device.PhotoUrl = productResult.ProductModeEntity.PhotoUrl
	}

	// 更新设备信息
	now := time.Now()
	device.OnlineTime = now
	device.UpdateTime = now
	device.Ip = ip
	if countryCity != nil {
		cityJson, _ := json.Marshal(countryCity)
		device.City = string(cityJson)
	}
	// 保存版本信息
	versionsJSON, _ := json.Marshal(packageVersions)
	device.Versions = string(versionsJSON)

	// 检查设备是否需要重置

	var resetCode int
	if device.ResetStatus == "1" {
		device.ResetStatus = "0"
		resetCode = 1
	}

	// 检查设备是否需要强制升级 - 对应 Java: iDeviceUpgradeService.checkDeviceUpgrade(device)
	DeviceUpgradeService.CheckDeviceUpgrade(ctx, device)

	//  判断此设备是否被禁用
	s.setDisableCity(ctx, device, countryCity, reqSn)
	_ = repository.DeviceRepository.Update(ctx, device)

	// 异步记录登录日志
	deviceLoginLog := &creq.DeviceLoginLogEntity{Sn: reqSn, Mac: mac, Key: key,
		ProductModeCode: productModeCode, Version: string(versionsJSON), DeviceId: device.Id,
		Ip: ip, Country: device.City, TenantId: tenantId, Time: now.UnixMilli(), Success: enum.LOGIN_SUCCESS.Code}
	go remote.LogService.LoginLog(ctx, deviceLoginLog)

	// 生成登录响应
	loginEntity := s.newLoginEntity(device, tenantId, countryCity)
	loginEntity.ResetCode = resetCode // 暂时设置为不需要重置

	// 清除设备缓存
	idKey := "deviceInfo::device_info_id_" + tenantId + "_" + device.Id
	snKey := "deviceInfo::device_info_sn_" + tenantId + "_" + device.Sn
	cache.Rdb.Del(ctx, idKey, snKey)
	return webRes.Cb(loginEntity)
}

func (s *deviceAuthService) doRegister(ctx context.Context, tenantId, reqSn, mac, key, ip,
	productModeCode string, countryCity *cres.CountryCity, packageVersions []req.RobotVersion) webRes.IBaseRes {

	logger.Info("设备注册处理", "tenantId", tenantId, "mac", mac, "sn", reqSn, "productModeCode", productModeCode)

	// 调用产品服务验证SN和产品型号
	register := &creq.DeviceRegister{Sn: reqSn, Key: key, ProductModelCode: productModeCode}
	registerRes := remote.ProductService.DeviceRegister(ctx, register)
	if registerRes.Fail() {
		return registerRes
	}

	device := registerRes.Result
	device.Mac = mac
	if countryCity != nil {
		cityJson, _ := json.Marshal(countryCity)
		device.City = string(cityJson)
	}

	s.setDisableCity(ctx, device, countryCity, reqSn)
	_ = repository.DeviceRepository.Create(ctx, device)

	// 将激活设备进行统计 - 对应 Java: kafkaService.deviceRegister(robotInsert);
	KafkaService.DeviceRegister(ctx, device)

	// 检查设备是否需要强制升级 - 对应 Java: iDeviceUpgradeService.checkDeviceUpgrade(robotInsert);
	DeviceUpgradeService.CheckDeviceUpgrade(ctx, device)

	// 异步记录登录日志
	versionsJSON, _ := json.Marshal(packageVersions)
	deviceLoginLog := &creq.DeviceLoginLogEntity{Sn: reqSn, Mac: mac, Key: key,
		ProductModeCode: productModeCode, Version: string(versionsJSON), DeviceId: device.Id,
		Ip: ip, Country: device.City, TenantId: tenantId, Time: time.Now().UnixMilli(), Success: enum.LOGIN_SUCCESS.Code}
	go remote.LogService.LoginLog(ctx, deviceLoginLog)
	loginEntity := s.newLoginEntity(device, tenantId, countryCity)
	// 清除设备缓存
	idKey := "deviceInfo::device_info_id_" + tenantId + "_" + device.Id
	snKey := "deviceInfo::device_info_sn_" + tenantId + "_" + device.Sn
	cache.Rdb.Del(ctx, idKey, snKey)
	return webRes.Cb(loginEntity)
}

func (s *deviceAuthService) newLoginEntity(device *domain.DeviceInfo, tenantID string,
	countryCity *cres.CountryCity) *res.LoginEntityRes {

	contextValues := make(map[string]interface{})
	contextValues["TENANT_ID"] = tenantID
	contextValues["ROBOT_TYPE"] = "device"
	loginEntity := &res.LoginEntityRes{
		ID:             device.Id,
		ClientTypeEnum: enum.CLIENT_TYPE_ROBOT,
		ContextValues:  &contextValues,
	}
	// 创建上下文值
	loginJson, _ := json.Marshal(loginEntity)

	authToken := utils.GenerateJWT(string(loginJson))

	contextValues["CONNECTION_TYPE"] = "device"
	contextValues["SN"] = device.Sn
	contextValues["USERNAME"] = device.Sn
	contextValues["MAC"] = device.Mac
	contextValues["PRODUCT_MODE_CODE"] = device.ProductModeCode
	//生成相关token
	contextValues["AUTH"] = authToken
	//TODO: 服务启动检查配置是否存在，不存在无法启动
	contextValues["EMQ_TOKEN"] = utils.CEmqJWT(device.Sn, nacos.GetString("emq.auth.jwt.secret"))

	if countryCity != nil {
		contextValues["COUNTRY_CITY"] = countryCity
	}

	return loginEntity
}

func (s *deviceAuthService) setDisableCity(ctx context.Context, device *domain.DeviceInfo, countryCity *cres.CountryCity, sn string) {
	disableCity := 1

	// 调用远程服务获取SN详情
	snDetailRes := remote.ProductService.GetSnDetailSellRegin(ctx, sn)
	json, _ := json.Marshal(snDetailRes)
	logger.Infof("setDisableCity-getSnDetail:%s,tc:%s", string(json), ctx.Value(ctxKeys.NONCE))

	if snDetailRes.Fail() {
		device.DisableCity = 0
		return
	}

	snDetail := snDetailRes.Result

	// 检查是否禁用非销售地区
	if snDetail.DisableNotSellRegion != nil && *snDetail.DisableNotSellRegion == 0 {
		device.DisableCity = 0
		return
	}

	// 检查国家城市信息是否为空
	if countryCity == nil {
		device.DisableCity = 0
		return
	}

	// 获取产品销售地区列表
	productModeSellRegions := snDetail.ProductModeSellRegions
	if len(productModeSellRegions) > 0 {
		// 提取地区名称列表
		var cityNames []string
		for _, region := range productModeSellRegions {
			cityNames = append(cityNames, region.RegionNameCh)
		}

		// 检查当前国家是否在销售地区列表中
		for _, cityName := range cityNames {
			// 特殊处理：中国大陆 -> 中国
			if cityName == "中国大陆" {
				cityName = "中国"
			}
			// 如果当前国家在销售地区列表中，则不禁用
			if cityName == countryCity.Country {
				disableCity = 0
				break
			}
		}
	} else {
		// 如果没有销售地区限制，则不禁用
		disableCity = 0
	}

	// 设置禁用状态
	device.DisableCity = disableCity
}
