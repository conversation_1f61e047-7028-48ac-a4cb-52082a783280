package service

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"
	"piceacorp.com/device-service/internal/bean/enum"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/dao/repository"
	"piceacorp.com/device-service/pkg/logger"
	"piceacorp.com/device-service/pkg/utils"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IDeviceBindBaseService 设备基站绑定服务接口
// 对应 Java: IDeviceBindBaseService
type IDeviceBindBaseService interface {
	// 基站和设备的绑定关系 - 对应 Java: Boolean bind(String deviceId,String sn,String baseId,String baseSn)
	Bind(ctx context.Context, deviceId, sn, baseId, baseSn string) bool
	// 根据sn获取基站绑定关系 - 对应 Java: ResponseMessage<List<DeviceBindBaseEntity>> bindListByDeviceSn(BindBaseListReq req)
	BindListByDeviceSn(ctx context.Context, req *req.BindBaseListReq) webRes.IBaseRes
	// 获取基站绑定关系 - 对应 Java: ResponseMessage<List<DeviceBindBaseEntity>> getBaseBind(String baseId, String baseSn, String devId, String devSn)
	GetBaseBind(ctx context.Context, baseId, baseSn, devId, devSn string) webRes.IBaseRes
}

var DeviceBindBaseService deviceBindBaseService

type deviceBindBaseService struct{}

// Bind 基站和设备的绑定关系
// 对应 Java: DeviceBindBaseServiceImpl.bind(String deviceId, String sn,String baseId,String baseSn)
func (s *deviceBindBaseService) Bind(ctx context.Context, deviceId, sn, baseId, baseSn string) bool {
	logger.Infof("基站设备绑定开始: deviceId=%s, sn=%s, baseId=%s, baseSn=%s", deviceId, sn, baseId, baseSn)

	// 查询是否已存在绑定关系 - 对应 Java: this.baseMapper.selectOne(query)
	entity, err := repository.DeviceBindBaseRepository.FindOne(ctx, deviceId, sn, baseSn, baseId)
	
	// 已经存在绑定关系 - 对应 Java: if (Objects.nonNull(entity))
	if err == nil && entity != nil {
		// 原用户重新绑定，直接返回成功 - 对应 Java: logService.bindLog(LogEnum.BIND.getCode(), LogEnum.SELF.getCode(),deviceId); return false;
		LogService.BindLog(ctx, enum.BIND.Code, enum.SELF.Code, deviceId)
		logger.Infof("基站设备绑定关系已存在: deviceId=%s", deviceId)
		return false
	}

	// 如果不是记录不存在的错误，则返回失败
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Errorf("查询绑定关系失败: deviceId=%s, error=%v", deviceId, err)
		return false
	}

	// 删除sn 和 基站原本的绑定关系 - 对应 Java: this.baseMapper.delete(deleteQuery)
	err = repository.DeviceBindBaseRepository.DeleteBySnOrBaseSn(ctx, sn, baseSn)
	if err != nil {
		logger.Errorf("删除原绑定关系失败: sn=%s, baseSn=%s, error=%v", sn, baseSn, err)
		return false
	}

	// 增加绑定关系 - 对应 Java: DeviceBindBaseEntity addEntity = new DeviceBindBaseEntity(...)
	now := time.Now()
	addEntity := &domain.DeviceBindBase{
		Id:         utils.Id(),                    // 对应 Java: SnowflakeUtil.snowflakeId()
		DeviceId:   deviceId,                     // 对应 Java: deviceId
		Sn:         sn,                           // 对应 Java: sn
		BindTime:   now,                          // 对应 Java: LocalDateTime.now()
		BaseId:     baseId,                       // 对应 Java: baseId
		BaseSn:     baseSn,                       // 对应 Java: baseSn
		CreateTime: now,                          // 对应 Java: LocalDateTime.now()
	}

	// 保存绑定关系 - 对应 Java: this.save(addEntity)
	err = repository.DeviceBindBaseRepository.Create(ctx, addEntity)
	if err != nil {
		logger.Errorf("创建绑定关系失败: deviceId=%s, error=%v", deviceId, err)
		return false
	}

	// 记录绑定日志 - 对应 Java: logService.bindLog(LogEnum.BIND.getCode(), LogEnum.SELF.getCode(),deviceId)
	LogService.BindLog(ctx, enum.BIND.Code, enum.SELF.Code, deviceId)

	logger.Infof("基站设备绑定成功: deviceId=%s", deviceId)
	return true
}

// BindListByDeviceSn 根据sn获取基站绑定关系
// 对应 Java: DeviceBindBaseServiceImpl.bindListByDeviceSn(BindBaseListReq req)
func (s *deviceBindBaseService) BindListByDeviceSn(ctx context.Context, req *req.BindBaseListReq) webRes.IBaseRes {
	logger.Infof("查询基站绑定列表: flag=%v, snList=%v", req.Flag, req.SnList)

	// 根据sn列表查询 - 对应 Java: this.baseMapper.selectList(queryWrapper)
	entities, err := repository.DeviceBindBaseRepository.FindBySnList(ctx, req.SnList, req.Flag)
	if err != nil {
		logger.Errorf("查询基站绑定列表失败: flag=%v, snList=%v, error=%v", req.Flag, req.SnList, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "查询基站绑定列表失败")
	}

	logger.Infof("查询基站绑定列表成功: flag=%v, snList=%v, count=%d", req.Flag, req.SnList, len(entities))
	
	// 对应 Java: return ResponseMessage.buildSuccess(list)
	return webRes.Cb(entities)
}

// GetBaseBind 获取基站绑定关系
// 对应 Java: DeviceBindBaseServiceImpl.getBaseBind(String baseId, String baseSn, String devId, String devSn)
func (s *deviceBindBaseService) GetBaseBind(ctx context.Context, baseId, baseSn, devId, devSn string) webRes.IBaseRes {
	logger.Infof("获取基站绑定关系: baseId=%s, baseSn=%s, devId=%s, devSn=%s", baseId, baseSn, devId, devSn)

	// 对应 Java: if(StringUtils.isBlank(baseId) && StringUtils.isBlank(baseSn) && StringUtils.isBlank(devId) &&StringUtils.isBlank(devSn))
	if baseId == "" && baseSn == "" && devId == "" && devSn == "" {
		logger.Infof("所有参数为空，返回空列表")
		return webRes.Cb([]*domain.DeviceBindBase{})
	}

	// 根据条件查询 - 对应 Java: this.list(queryWrapper)
	entities, err := repository.DeviceBindBaseRepository.FindByConditions(ctx, baseId, baseSn, devId, devSn)
	if err != nil {
		logger.Errorf("获取基站绑定关系失败: baseId=%s, baseSn=%s, devId=%s, devSn=%s, error=%v", 
			baseId, baseSn, devId, devSn, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取基站绑定关系失败")
	}

	logger.Infof("获取基站绑定关系成功: baseId=%s, baseSn=%s, devId=%s, devSn=%s, count=%d", 
		baseId, baseSn, devId, devSn, len(entities))
	
	// 对应 Java: return ResponseMessage.buildSuccess(this.list(queryWrapper))
	return webRes.Cb(entities)
}
