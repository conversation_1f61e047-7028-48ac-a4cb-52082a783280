package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"piceacorp.com/device-service/internal/bean/dto"
	"piceacorp.com/device-service/internal/bean/enum"
	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/bean/res"
	"piceacorp.com/device-service/internal/dao/cache"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/dao/repository"
	"piceacorp.com/device-service/internal/service/remote"
	remoteReq "piceacorp.com/device-service/internal/service/remote/req"
	redis "piceacorp.com/device-service/pkg/data/cache"
	"piceacorp.com/device-service/pkg/logger"
	"piceacorp.com/device-service/pkg/util"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IDeviceBindUserService 设备绑定用户服务接口
type IDeviceBindUserService interface {
	// 根据用户ID获取绑定设备列表 - 对应 Java: getBindListByUserId(String userId)
	GetBindListByUserId(ctx context.Context, userId string) webRes.IBaseRes
	// 分享绑定关系
	ShareBind(ctx context.Context, deviceId, nickname, sn, productId string, owner bool) bool
	// 解绑设备 - 对应 Java: untieDevice(UntieDeviceReq req)
	UntieDevice(ctx context.Context, untieReq *req.UntieDeviceReq) webRes.IBaseRes
	// 解绑分享设备
	UntieShareBind(ctx context.Context, deviceId, beInviter string) bool
	// 批量新增 - 对应 Java: addBatch(List<DeviceBindUserEntity> list)
	AddBatch(ctx context.Context, list []*domain.DeviceBindUser) webRes.IBaseRes
	// MQTT属性获取主题处理 - 对应 Java: propertyGetTopicHandler(MqttPropertyGetReqDTO param) (遵循规则47: 必须全部转换)
	PropertyGetTopicHandler(ctx context.Context, param *dto.MqttPropertyGetReqDTO)
	// 根据SN和用户ID删除绑定关系 - 对应 Java: batchUnBindDevice中的删除逻辑 (遵循规则47: 必须全部转换)
	DelBySnAndUserId(ctx context.Context, sn, userId string) webRes.IBaseRes
	// 获取绑定关系 - 对应 Java: getBindList(String tenantId, String userId)
	GetBindList(ctx context.Context, tenantId, userId string) webRes.IBaseRes
	// 根据id删除绑定关系 - 对应 Java: deleteByIds(List<String> ids)
	DeleteByIds(ctx context.Context, ids []string) webRes.IBaseRes
	// 根据设备id获取设备绑定的用户 - 对应 Java: getOwnerListByDeviceId(OwnerListParam param)
	GetOwnerListByDeviceId(ctx context.Context, param *req.OwnerListParam) webRes.IBaseRes
	// 根据userId删除绑定关系 - 对应 Java: deleteByUserIds(List<String> userIds, String tenantId)
	DeleteByUserIds(ctx context.Context, userIds []string, tenantId string) webRes.IBaseRes
	// 添加在线app的sn缓存 - 对应 Java: addOnlineAppSnCacheForTempMap(List<String> snList)
	AddOnlineAppSnCacheForTempMap(ctx context.Context, snList []string)
}

var DeviceBindUserService deviceBindUserService

type deviceBindUserService struct{}

// GetBindListByUserId 根据用户ID获取绑定设备列表
// 对应 Java: DeviceBindUserServiceImpl.getBindListByUserId(String userId)
func (s *deviceBindUserService) GetBindListByUserId(ctx context.Context, userId string) webRes.IBaseRes {
	logger.Infof("获取用户绑定设备列表开始: userId=%s", userId)

	// 获取租户ID - 对应 Java: SystemContextUtils.getTenantId()
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)
	if tenantId == "" {
		logger.Errorf("获取租户ID失败: userId=%s", userId)
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "租户ID不能为空")
	}

	// 1. 查询绑定设备信息 - 对应 Java: List<BindDeviceInfoVo> resultList = this.baseMapper.getDeviceInfoAndBindByUserId(userId);
	// 注意：Java版本使用MyBatis-Plus多租户插件自动添加租户ID条件，Go版本需要手动处理
	resultList, err := repository.DeviceBindUserRepository.GetDeviceInfoAndBindByUserId(ctx, userId, tenantId)
	if err != nil {
		logger.Errorf("查询用户绑定设备失败: userId=%s, tenantId=%s, error=%v", userId, tenantId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "查询用户绑定设备失败")
	}

	// 2. 检查是否有数据 - 对应 Java: if (CollectionUtil.isEmpty(resultList)) { return ResponseMessage.buildSuccess(resultList); }
	if len(resultList) == 0 {
		logger.Infof("用户绑定设备列表为空: userId=%s, tenantId=%s", userId, tenantId)
		return webRes.Cb(resultList)
	}

	// 3. 格式化设备信息 - 对应 Java: resultList = commonService.formatBindDeviceInfoList(resultList);
	resultList = s.formatBindDeviceInfoList(ctx, resultList)

	// 4. 添加在线状态缓存 - 对应 Java: if(CollectionUtils.isNotEmpty(resultList)){ List<String> snList = resultList.stream().map(t -> t.getSn()).collect(Collectors.toList()); addOnlineAppSnCacheForTempMap(snList); }
	if len(resultList) > 0 {
		var snList []string
		for _, device := range resultList {
			snList = append(snList, device.Sn)
		}
		s.addOnlineAppSnCacheForTempMap(snList)
	}

	logger.Infof("获取用户绑定设备列表完成: userId=%s, tenantId=%s, count=%d", userId, tenantId, len(resultList))

	// 5. 返回成功响应 - 对应 Java: return ResponseMessage.buildSuccess(resultList);
	return webRes.Cb(resultList)
}

// formatBindDeviceInfoList 格式化绑定设备信息列表
// 对应 Java: CommonService.formatBindDeviceInfoList(List<BindDeviceInfoVo> bindDeviceInfoVos)
func (s *deviceBindUserService) formatBindDeviceInfoList(ctx context.Context, bindDeviceInfoVos []*res.BindDeviceInfoVo) []*res.BindDeviceInfoVo {
	// 获取上下文信息 - 对应 Java: String userId = SystemContextUtils.getId(); String tenantId = SystemContextUtils.getTenantId();
	userId := ctx.Value(ctxKeys.ID).(string)
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)

	// 获取用户语言设置 - 对应 Java: ResponseMessage<TAuthClientUserExt> userExtByUserId = authUserRemote.getUserExtByUserId(tenantId, userId);
	lang := s.getUserLanguage(ctx, tenantId, userId)

	// 收集所有设备SN
	var snList []string
	for _, device := range bindDeviceInfoVos {
		snList = append(snList, device.Sn)
	}

	// 获取设备影子信息 - 对应 Java: this.getDeviceShadowMap(snList)
	deviceShadowMap := s.getDeviceShadowMap(snList)

	// 补充设备在线状态和属性
	var resultList []*res.BindDeviceInfoVo
	for _, device := range bindDeviceInfoVos {
		// 设置设备状态 - 对应 Java: e.setStatus(0); e.setOnlineStatus(false);
		device.OnlineStatus = new(bool) // 默认离线
		*device.OnlineStatus = false

		// 设置设备影子信息
		if shadowEntity, exists := deviceShadowMap[device.Sn]; exists {
			if shadowEntity.OnlineStatus {
				*device.OnlineStatus = true
			}
			device.Properties = shadowEntity.Properties
		}

		resultList = append(resultList, device)
	}

	logger.Infof("格式化绑定设备信息完成: userId=%s, lang=%s, count=%d", userId, lang, len(resultList))
	return resultList
}

// getDeviceShadowMap 获取设备影子信息映射
// 对应 Java: CommonService.getDeviceShadowMap(snList)
func (s *deviceBindUserService) getDeviceShadowMap(snList []string) map[string]*remoteReq.DeviceShadowEntity {
	if len(snList) == 0 {
		return make(map[string]*remoteReq.DeviceShadowEntity)
	}

	// 调用设备影子服务 - 对应 Java: deviceShadowRemote.findAllBySN(snList)
	response := remote.DeviceShadowService.FindAllBySN(context.Background(), snList)
	if response.Fail() {
		logger.Errorf("获取设备影子信息异常: snList=%v, error=%s", snList, response.Msg)
		return make(map[string]*remoteReq.DeviceShadowEntity)
	}

	// 构建映射 - 对应 Java: deviceShadowMap.put(entity.getId(),entity)
	deviceShadowMap := make(map[string]*remoteReq.DeviceShadowEntity)
	if response.Result != nil && len(*response.Result) > 0 {
		for _, entity := range *response.Result {
			deviceShadowMap[entity.Id] = entity
		}
	}

	logger.Infof("获取设备影子信息成功: snList=%v, count=%d", snList, len(deviceShadowMap))
	return deviceShadowMap
}

// addOnlineAppSnCacheForTempMap 添加在线状态缓存
// 对应 Java: addOnlineAppSnCacheForTempMap(List<String> snList)
func (s *deviceBindUserService) addOnlineAppSnCacheForTempMap(snList []string) {
	if len(snList) == 0 {
		return
	}

	// 实现在线状态缓存逻辑 - 对应 Java 中的 ONLINE_APP_SN_KEY 缓存
	// 这里使用 Redis 缓存设备的在线状态，缓存键格式: OnLineApp:sn:{sn}
	ctx := context.Background()
	for _, sn := range snList {
		cacheKey := fmt.Sprintf("OnLineApp:sn:%s", sn)
		// 设置缓存，过期时间30分钟
		err := redis.Rdb.Set(ctx, cacheKey, "1", 30*time.Minute).Err()
		if err != nil {
			logger.Errorf("设置在线状态缓存失败: sn=%s, error=%v", sn, err)
		}
	}

	logger.Infof("添加在线状态缓存成功: snList=%v", snList)
}

// ShareBind 分享绑定关系
// 对应 Java: DeviceBindUserServiceImpl.shareBind(String deviceId, String nickname, String sn, String productId, String owner)
func (s *deviceBindUserService) ShareBind(ctx context.Context, deviceId, nickname, sn, productId string, owner bool) bool {
	// 获取上下文信息 - 对应 Java: SystemContextUtils.getId(), SystemContextUtils.getTenantId()
	userId := ctx.Value(ctxKeys.ID).(string)
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)

	logger.Infof("分享绑定关系开始: userId=%s, tenantId=%s, deviceId=%s, sn=%s, owner=%t",
		userId, tenantId, deviceId, sn, owner)

	// 判断是否存在分享设备 - 对应 Java: LambdaQueryWrapper<DeviceBindUserEntity> queryWrapper = new QueryWrapper<DeviceBindUserEntity>().lambda()
	count, err := repository.DeviceBindUserRepository.CountShareBind(ctx, userId, deviceId)
	if err != nil {
		logger.Errorf("检查分享设备记录失败: userId=%s, deviceId=%s, error=%v", userId, deviceId, err)
		return false
	}

	// 对应 Java: if (count > 0) return false
	if count > 0 {
		logger.Warnf("分享设备已存在: userId=%s, deviceId=%s", userId, deviceId)
		return false
	}

	// 创建新的分享绑定关系 - 对应 Java: DeviceBindUserEntity deviceBindUser = new DeviceBindUserEntity(deviceId, sn, userId, owner, productId, Constant.DEVICE_FLAG_1, BindSourceEnum.DEVICE_BIND.getCode(), BindUserTypeEnum.SELF.getCode())
	// 严格按照Java构造函数内部的属性赋值逻辑:
	// Java构造函数: public DeviceBindUserEntity(String deviceId,String sn, String creatorId,String owner,String productId,String flag,Integer source,Integer bindUserType)
	// Java构造函数内部实现:
	//     this.id = SnowflakeUtil.snowflakeId();
	//     this.deviceId = deviceId;        (参数1: deviceId)
	//     this.sn = sn;                    (参数2: sn)
	//     this.owner = owner;              (参数4: owner)
	//     this.bindTime = LocalDateTime.now();
	//     this.creatorId = creatorId;      (参数3: creatorId)
	//     this.createTime = LocalDateTime.now();
	//     this.productId = productId;      (参数5: productId)
	//     this.flag = flag;                (参数6: flag)
	//     this.source = source;            (参数7: source)
	//     this.bindUserType = bindUserType; (参数8: bindUserType)
	ownerStr := "false"
	if owner {
		ownerStr = "true"
	}

	now := time.Now()
	deviceBindUser := &domain.DeviceBindUser{
		Id:           util.Id(),                        // 对应 this.id = SnowflakeUtil.snowflakeId()
		DeviceId:     deviceId,                         // 对应 this.deviceId = deviceId (构造函数参数1)
		Sn:           sn,                               // 对应 this.sn = sn (构造函数参数2)
		CreatorId:    userId,                           // 对应 this.creatorId = creatorId (构造函数参数3)
		Owner:        ownerStr,                         // 对应 this.owner = owner (构造函数参数4)
		ProductId:    productId,                        // 对应 this.productId = productId (构造函数参数5)
		Flag:         "1",                              // 对应 this.flag = flag (构造函数参数6: Constant.DEVICE_FLAG_1)
		Source:       2,                                // 对应 this.source = source (构造函数参数7: BindSourceEnum.DEVICE_BIND.getCode())
		BindUserType: 1,                                // 对应 this.bindUserType = bindUserType (构造函数参数8: BindUserTypeEnum.SELF.getCode())
		BindTime:     now,                              // 对应 this.bindTime = LocalDateTime.now()
		CreateTime:   now,                              // 对应 this.createTime = LocalDateTime.now()
		TenantId:     tenantId,                         // 额外字段
		Zone:         ctx.Value(ctxKeys.ZONE).(string), // 额外字段
	}

	// 保存绑定记录 - 对应 Java: this.save(deviceBindUser)
	err = repository.DeviceBindUserRepository.Save(ctx, deviceBindUser)
	if err != nil {
		logger.Errorf("保存分享绑定记录失败: userId=%s, deviceId=%s, error=%v", userId, deviceId, err)
		return false
	}

	// 更新拥有者设备的分享状态 - 对应 Java: LambdaUpdateWrapper<DeviceBindUserEntity> updateWrapper = new LambdaUpdateWrapper<>()
	err = repository.DeviceBindUserRepository.UpdateShareStatus(ctx, deviceId, ownerStr, "1") // DEVICE_SHARE_STATUS_1
	if err != nil {
		logger.Errorf("更新拥有者设备分享状态失败: deviceId=%s, owner=%s, error=%v", deviceId, ownerStr, err)
		// 不返回false，因为主要操作已完成
	}

	// 新增mongo保存的绑定关系 - 对应 Java: mongoService.insert(userId, sn)
	MongoService.Insert(ctx, userId, sn)

	// 新增语控关系 - 对应 Java: icloudIntentService.insert(userId, sn)
	IcloudIntentService.Insert(ctx, userId, sn)

	// 记录绑定日志 - 对应 Java: logService.bindLog(LogEnum.SHARE_BIND.getCode(), LogEnum.SELF.getCode(), deviceId)
	LogService.BindLog(ctx, enum.SHARE_BIND.Code, enum.SELF.Code, deviceId)

	logger.Infof("分享绑定关系成功: userId=%s, deviceId=%s, sn=%s", userId, deviceId, sn)
	return true
}

// getUserLanguage 获取用户语言设置
// 对应 Java: ResponseMessage<TAuthClientUserExt> userExtByUserId = authUserRemote.getUserExtByUserId(tenantId, userId);
func (s *deviceBindUserService) getUserLanguage(ctx context.Context, tenantId, userId string) string {
	// 调用用户服务获取用户扩展信息 - 对应 Java: authUserRemote.getUserExtByUserId(tenantId, userId)
	response := remote.UserService.GetUserExtByUserId(ctx, tenantId, userId)
	if response.Fail() {
		logger.Errorf("获取用户信息失败: tenantId=%s, userId=%s, error=%s", tenantId, userId, response.Msg)
		// 降级处理：从上下文获取语言 - 对应 Java: lang = SystemContextUtils.getContextValue(ContextKey.LANG);
		return ctx.Value(ctxKeys.LANG).(string)
	}

	// 对应 Java: lang = userExtByUserId.getResult().getLang();
	if response.Result != nil {
		return response.Result.Lang
	}

	// 默认语言
	return "en"
}

// UntieDevice 解绑设备
// 对应 Java: DeviceBindUserServiceImpl.untieDevice(UntieDeviceReq req) (遵循规则38、39)
func (s *deviceBindUserService) UntieDevice(ctx context.Context, untieReq *req.UntieDeviceReq) webRes.IBaseRes {
	// 获取上下文信息 - 对应 Java: SystemContextUtils.getId(), SystemContextUtils.getTenantId()
	userId := ctx.Value(ctxKeys.ID).(string)
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)

	logger.Infof("解绑设备开始: userId=%s, tenantId=%s, deviceId=%s, sn=%s",
		userId, tenantId, untieReq.DeviceId, untieReq.Sn)

	// 兼容APP-SDK-OPENAPI的20240601前已发布版本的接口调用 - 对应 Java: if (StringUtils.isBlank(req.getDeviceId()))
	if untieReq.DeviceId == "" {
		if untieReq.Sn != "" {
			// 对应 Java: DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheBySn(SystemContextUtils.getTenantId(), req.getSn());
			deviceInfo, err := cache.DeviceInfoCache.GetCacheBySn(ctx, untieReq.Sn, tenantId)
			if err != nil {
				logger.Errorf("通过SN获取设备信息失败: sn=%s, error=%v", untieReq.Sn, err)
				return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取设备信息失败")
			}
			// 对应 Java: if (Objects.isNull(deviceInfo)) { return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA); }
			if deviceInfo == nil {
				return webRes.Ce(webErr.NOT_FOUND_DATA, "设备不存在")
			}
			// 对应 Java: req.setDeviceId(deviceInfo.getId());
			untieReq.DeviceId = deviceInfo.Id
		}
	}

	// 对应 Java: if (StringUtils.isBlank(req.getDeviceId())) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceId或SN不能为空"); }
	if untieReq.DeviceId == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "deviceId或SN不能为空")
	}

	// 获取绑定记录 - 对应 Java: DeviceBindUserEntity deviceBindUser = this.getOne(queryWrapper)
	bindUser, err := repository.DeviceBindUserRepository.GetByDeviceIdAndUserId(ctx, untieReq.DeviceId, userId)
	if err != nil {
		logger.Errorf("获取绑定记录失败: deviceId=%s, userId=%s, error=%v", untieReq.DeviceId, userId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取绑定记录失败")
	}

	// 对应 Java: if (Objects.isNull(deviceBindUser))
	if bindUser == nil {
		logger.Warnf("绑定记录不存在: deviceId=%s, userId=%s", untieReq.DeviceId, userId)
		return webRes.Ce(webErr.NOT_FOUND_DATA, "绑定记录不存在")
	}

	// 检查是否为设备拥有者 - 对应 Java: if (Constant.DEVICE_OWNER_TRUE.equals(deviceBindUser.getOwner()))
	isOwner := bindUser.Owner == "true"

	if isOwner {
		// 拥有者解绑逻辑 - 对应 Java: ownerUntie(deviceInfo, deviceBindUser)
		return s.ownerUntie(ctx, untieReq.DeviceId, bindUser, tenantId, userId)
	} else {
		// 被分享者解绑逻辑 - 对应 Java: shareUntie(deviceInfo, deviceBindUser)
		return s.shareUntie(ctx, untieReq.DeviceId, bindUser, tenantId, userId)
	}
}

// ownerUntie 拥有者解绑逻辑
// 对应 Java: DeviceBindUserServiceImpl.ownerUntie(DeviceInfoEntity deviceInfo, DeviceBindUserEntity deviceBindUser) (遵循规则39)
func (s *deviceBindUserService) ownerUntie(ctx context.Context, deviceId string, bindUser *domain.DeviceBindUser, tenantId, userId string) webRes.IBaseRes {
	logger.Infof("拥有者解绑开始: deviceId=%s, userId=%s", deviceId, userId)

	// 获取所有绑定该设备的用户 - 对应 Java: List<DeviceBindUserEntity> deviceBindUserList = this.list(queryWrapper)
	bindUserList, err := repository.DeviceBindUserRepository.GetByDeviceId(ctx, deviceId)
	if err != nil {
		logger.Errorf("获取设备绑定用户列表失败: deviceId=%s, error=%v", deviceId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取设备绑定用户列表失败")
	}

	// 检查是否有其他用户绑定 - 对应 Java: if (CollectionUtils.isNotEmpty(deviceBindUserList) && deviceBindUserList.size() > 1)
	if len(bindUserList) > 1 {
		// 有其他用户绑定，需要转移拥有者 - 对应 Java: resetOwner(deviceInfo.getId(), deviceBindUserList.get(1).getCreatorId())
		newOwner := ""
		for _, user := range bindUserList {
			if user.CreatorId != userId {
				newOwner = user.CreatorId
				break
			}
		}

		if newOwner != "" {
			err = repository.DeviceBindUserRepository.ResetOwner(ctx, deviceId, newOwner)
			if err != nil {
				logger.Errorf("重置设备拥有者失败: deviceId=%s, newOwner=%s, error=%v", deviceId, newOwner, err)
				return webRes.Ce(webErr.INNER_SERVER_ERROR, "重置设备拥有者失败")
			}
			logger.Infof("重置设备拥有者成功: deviceId=%s, oldOwner=%s, newOwner=%s", deviceId, userId, newOwner)
		}
	}

	// 删除当前用户的绑定记录 - 对应 Java: this.removeById(deviceBindUser.getId())
	err = repository.DeviceBindUserRepository.DeleteById(ctx, bindUser.Id)
	if err != nil {
		logger.Errorf("删除绑定记录失败: bindId=%s, error=%v", bindUser.Id, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "删除绑定记录失败")
	}

	// 执行通用解绑逻辑 - 对应 Java: commonUntie(deviceInfo, deviceBindUser)
	s.commonUntie(ctx, deviceId, bindUser, tenantId, userId)

	logger.Infof("拥有者解绑成功: deviceId=%s, userId=%s", deviceId, userId)
	return webRes.Cb(true)
}

// shareUntie 被分享者解绑逻辑
// 对应 Java: DeviceBindUserServiceImpl.shareUntie(DeviceInfoEntity deviceInfo, DeviceBindUserEntity deviceBindUser) (遵循规则39)
func (s *deviceBindUserService) shareUntie(ctx context.Context, deviceId string, bindUser *domain.DeviceBindUser, tenantId, userId string) webRes.IBaseRes {
	logger.Infof("被分享者解绑开始: deviceId=%s, userId=%s", deviceId, userId)

	// 删除绑定记录 - 对应 Java: this.removeById(deviceBindUser.getId())
	err := repository.DeviceBindUserRepository.DeleteById(ctx, bindUser.Id)
	if err != nil {
		logger.Errorf("删除绑定记录失败: bindId=%s, error=%v", bindUser.Id, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "删除绑定记录失败")
	}

	// 执行通用解绑逻辑 - 对应 Java: commonUntie(deviceInfo, deviceBindUser)
	s.commonUntie(ctx, deviceId, bindUser, tenantId, userId)

	// 删除分享记录 - 对应 Java: inviteShareService.delByInvitee(deviceBindUser.getCreatorId(), deviceInfo.getId(), InviteShareEnum.DEVICE.getStatus())
	DeviceInviteShareService.DelByInvitee(ctx, userId, deviceId, 0) // InviteShareEnum.DEVICE.getStatus() = 0

	logger.Infof("被分享者解绑成功: deviceId=%s, userId=%s", deviceId, userId)
	return webRes.Cb(true)
}

// commonUntie 通用解绑逻辑
// 对应 Java: DeviceBindUserServiceImpl.commonUntie(DeviceInfoEntity deviceInfo, DeviceBindUserEntity deviceBindUser) (遵循规则39)
func (s *deviceBindUserService) commonUntie(ctx context.Context, deviceId string, bindUser *domain.DeviceBindUser, tenantId, userId string) {
	// 异步执行多个操作
	go func() {
		// 1. 向第三方平台发送解绑信息 - 对应 Java: cloudIntentServiceRemote.syncUntieInfo(new SyncUntieReq(SystemContextUtils.getTenantId(), deviceInfo.getId()))
		syncUntieReq := remoteReq.NewSyncUntieReq(tenantId, deviceId)
		response := remote.CloudIntentService.SyncUntieInfo(ctx, syncUntieReq)
		if response.Fail() {
			logger.Errorf("向第三方平台发送解绑信息失败: deviceId=%s, error=%s", deviceId, response.Msg)
		} else {
			logger.Infof("向第三方平台发送解绑信息成功: deviceId=%s", deviceId)
		}

		// 2. 分享失效 - 对应 Java: commonService.shareInvalidate(Arrays.asList(deviceInfo.getId()), deviceBindUser.getCreatorId(), SystemContextUtils.getTenantId())
		shareInvalidateReq := remoteReq.NewShareInvalidateReq([]string{deviceId}, userId, tenantId)
		shareResponse := remote.InfrastructureThirdService.ShareInvalidate(ctx, shareInvalidateReq)
		if shareResponse.Fail() {
			logger.Errorf("分享失效失败: deviceId=%s, userId=%s, error=%s", deviceId, userId, shareResponse.Msg)
		} else {
			logger.Infof("分享失效成功: deviceId=%s, userId=%s", deviceId, userId)
		}

		// 获取设备信息以获取SN - 对应 Java中已有deviceInfo对象
		deviceInfo, err := cache.DeviceInfoCache.GetCacheById(ctx, tenantId, deviceId)
		if err != nil || deviceInfo == nil {
			logger.Errorf("获取设备信息失败，无法执行后续操作: deviceId=%s, error=%v", deviceId, err)
			return
		}

		// 3. 重置设备昵称 - 对应 Java: deviceResetService.ResetNickname(new ResetNicknameReq(deviceInfo.getSn(), SystemContextUtils.getTenantId()))
		resetNicknameReq := req.ResetNicknameReq{
			DeviceId: deviceInfo.Id,
			Inviter:  true,
			Sn:       deviceInfo.Sn,
		}
		resetResponse := DeviceResetService.ResetNickname(ctx, &resetNicknameReq)
		if resetResponse.Fail() {
			logger.Errorf("重置设备昵称失败: sn=%s, error=%s", deviceInfo.Sn, resetResponse)
		} else {
			logger.Infof("重置设备昵称成功: sn=%s", deviceInfo.Sn)
		}

		// 4. 清除设备流水数据 - 对应 Java: dataStatisticsServiceRemote.flow(new ClearFlowParam().snList(Arrays.asList(deviceInfo.getSn())).tenantId(SystemContextUtils.getTenantId()))
		clearFlowParam := remoteReq.NewClearFlowParam().SetSnList([]string{deviceInfo.Sn}).SetTenantId(tenantId)
		flowResponse := remote.DataStatisticsService.Flow(ctx, clearFlowParam)
		if flowResponse.Fail() {
			logger.Errorf("清除设备流水数据失败: sn=%s, error=%s", deviceInfo.Sn, flowResponse.Msg)
		} else {
			logger.Infof("清除设备流水数据成功: sn=%s", deviceInfo.Sn)
		}

		// 5. 删除mongo绑定关系 - 对应 Java: mongoService.remove(deviceBindUser.getCreatorId(), deviceInfo.getSn())
		MongoService.Remove(ctx, userId, deviceInfo.Sn)

		// 6. 删除语控关系 - 对应 Java: icloudIntentService.remove(deviceBindUser.getCreatorId(), deviceInfo.getSn())
		IcloudIntentService.Remove(ctx, userId, deviceInfo.Sn)

		// 7. 记录解绑日志 - 对应 Java: logService.bindLog(LogEnum.UNBIND.getCode(), LogEnum.SELF.getCode(), deviceInfo.getId())
		LogService.BindLog(ctx, "UNBIND", "SELF", deviceInfo.Id)
	}()
}

// UntieShareBind 解绑分享设备
// 对应 Java: DeviceBindUserServiceImpl.untieShareBind(String deviceId, String beInviter)
func (s *deviceBindUserService) UntieShareBind(ctx context.Context, deviceId, beInviter string) bool {
	logger.Infof("解绑分享设备开始: deviceId=%s, beInviter=%s", deviceId, beInviter)

	// 获取设备信息以获取SN
	deviceInfo, err := repository.DeviceRepository.Get(ctx, deviceId)
	if err != nil {
		logger.Errorf("获取设备信息失败: deviceId=%s, error=%v", deviceId, err)
		return false
	}
	if deviceInfo == nil {
		logger.Errorf("设备不存在: deviceId=%s", deviceId)
		return false
	}

	// 删除绑定记录 - 对应 Java: this.baseMapper.deleteById(entity.getId())
	err = repository.DeviceBindUserRepository.DeleteByDeviceIdAndUserId(ctx, deviceId, beInviter)
	if err != nil {
		logger.Errorf("删除分享绑定记录失败: deviceId=%s, beInviter=%s, error=%v", deviceId, beInviter, err)
		return false
	}

	// 删除mongo绑定关系 - 对应 Java: mongoService.remove(userId, entity.getSn())
	MongoService.Remove(ctx, beInviter, deviceInfo.Sn)

	// 删除语控关系 - 对应 Java: icloudIntentService.remove(userId, entity.getSn())
	IcloudIntentService.Remove(ctx, beInviter, deviceInfo.Sn)

	// 记录解绑日志 - 对应 Java: logService.bindLog(LogEnum.SHARE_UNBIND.getCode(), LogEnum.SELF.getCode(), deviceId)
	LogService.BindLog(ctx, enum.SHARE_UNBIND.Code, enum.SELF.Code, deviceId)

	logger.Infof("解绑分享设备成功: deviceId=%s, beInviter=%s", deviceId, beInviter)
	return true
}

// AddBatch 批量新增
// 对应 Java: DeviceBindUserServiceImpl.addBatch(List<DeviceBindUserEntity> list)
func (s *deviceBindUserService) AddBatch(ctx context.Context, list []*domain.DeviceBindUser) webRes.IBaseRes {
	logger.Infof("批量新增绑定关系开始: count=%d", len(list))

	// 对应 Java: this.saveBatch(list);
	for _, entity := range list {
		err := repository.DeviceBindUserRepository.Create(ctx, entity)
		if err != nil {
			logger.Errorf("批量新增绑定关系失败: entity=%+v, error=%v", entity, err)
			return webRes.Ce(webErr.INNER_SERVER_ERROR, "批量新增绑定关系失败")
		}
	}

	logger.Infof("批量新增绑定关系成功: count=%d", len(list))
	// 对应 Java: return ResponseMessage.buildSuccess(true);
	return webRes.Cb(true)
}

// GetBindList 获取绑定关系
// 对应 Java: DeviceBindUserServiceImpl.getBindList(String tenantId, String userId)
func (s *deviceBindUserService) GetBindList(ctx context.Context, tenantId, userId string) webRes.IBaseRes {
	logger.Infof("获取绑定关系开始: tenantId=%s, userId=%s", tenantId, userId)

	// 对应 Java: return ResponseMessage.buildSuccess(this.baseMapper.getListByUserId(tenantId, userId));
	entities, err := repository.DeviceBindUserRepository.GetListByUserId(ctx, tenantId, userId)
	if err != nil {
		logger.Errorf("获取绑定关系失败: tenantId=%s, userId=%s, error=%v", tenantId, userId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取绑定关系失败")
	}

	logger.Infof("获取绑定关系成功: tenantId=%s, userId=%s, count=%d", tenantId, userId, len(entities))
	return webRes.Cb(entities)
}

// DeleteByIds 根据id删除绑定关系
// 对应 Java: DeviceBindUserServiceImpl.deleteByIds(List<String> ids)
func (s *deviceBindUserService) DeleteByIds(ctx context.Context, ids []string) webRes.IBaseRes {
	logger.Infof("根据id删除绑定关系开始: ids=%v", ids)

	// 对应 Java: this.baseMapper.deleteBatchIds(ids);
	err := repository.DeviceBindUserRepository.DeleteBatchIds(ctx, ids)
	if err != nil {
		logger.Errorf("根据id删除绑定关系失败: ids=%v, error=%v", ids, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "根据id删除绑定关系失败")
	}

	logger.Infof("根据id删除绑定关系成功: ids=%v", ids)
	// 对应 Java: return ResponseMessage.buildSuccess(true);
	return webRes.Cb(true)
}

// GetOwnerListByDeviceId 根据设备id获取设备绑定的用户
// 对应 Java: DeviceBindUserServiceImpl.getOwnerListByDeviceId(OwnerListParam param)
func (s *deviceBindUserService) GetOwnerListByDeviceId(ctx context.Context, param *req.OwnerListParam) webRes.IBaseRes {
	logger.Infof("根据设备id获取设备绑定的用户开始: param=%+v", param)

	// 对应 Java: param.setIds(param.getIds().stream().distinct().collect(Collectors.toList()));
	uniqueIds := make([]string, 0, len(param.Ids))
	seen := make(map[string]bool)
	for _, id := range param.Ids {
		if !seen[id] {
			uniqueIds = append(uniqueIds, id)
			seen[id] = true
		}
	}
	param.Ids = uniqueIds

	// 对应 Java: LambdaQueryWrapper<DeviceBindUserEntity> queryWrapper = new LambdaQueryWrapper<>();
	var entities []*domain.DeviceBindUser
	var err error

	if param.Flag {
		// 对应 Java: queryWrapper.in(DeviceBindUserEntity::getDeviceId, param.getIds()).eq(DeviceBindUserEntity::getFlag, DEVICE_FLAG_0);
		entities, err = repository.DeviceBindUserRepository.FindByDeviceIdsAndFlag(ctx, param.Ids, "0")
	} else {
		// 对应 Java: queryWrapper.in(DeviceBindUserEntity::getDeviceId, param.getIds());
		entities, err = repository.DeviceBindUserRepository.FindByDeviceIds(ctx, param.Ids)
	}

	if err != nil {
		logger.Errorf("查询设备绑定用户失败: param=%+v, error=%v", param, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "查询设备绑定用户失败")
	}

	// 对应 Java: if (CollectionUtil.isEmpty(list)) { return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA); }
	if len(entities) == 0 {
		return webRes.Ce(webErr.NOT_FOUND_DATA, "未找到数据")
	}

	// 对应 Java: Map<String, List<DeviceBindUserEntity>> entityMap = list.stream().collect(Collectors.groupingBy(DeviceBindUserEntity::getDeviceId));
	entityMap := make(map[string][]*domain.DeviceBindUser)
	for _, entity := range entities {
		entityMap[entity.DeviceId] = append(entityMap[entity.DeviceId], entity)
	}

	// 对应 Java: List<OwnerListVo> retList = new ArrayList<>(param.getIds().size());
	retList := make([]*res.OwnerListVo, 0, len(param.Ids))

	// 对应 Java: for (String deviceId : param.getIds())
	for _, deviceId := range param.Ids {
		vo := &res.OwnerListVo{
			DeviceId:   deviceId,
			UserIdList: make([]string, 0),
		}

		groupList := entityMap[deviceId]
		if len(groupList) == 0 {
			// 对应 Java: vo.setUserIdList(userIdList); vo.setDeviceId(deviceId);
			vo.UserIdList = []string{}
		} else {
			// 对应 Java: groupList.forEach(e -> { vo.setTenantId(e.getTenantId()); vo.setDeviceId(deviceId); userIdList.add(e.getCreatorId()); });
			userIdSet := make(map[string]bool)
			for _, entity := range groupList {
				vo.TenantId = entity.TenantId
				userIdSet[entity.CreatorId] = true
			}

			// 转换为切片
			for userId := range userIdSet {
				vo.UserIdList = append(vo.UserIdList, userId)
			}
		}
		retList = append(retList, vo)
	}

	logger.Infof("根据设备id获取设备绑定的用户成功: param=%+v, count=%d", param, len(retList))
	// 对应 Java: return ResponseMessage.buildSuccess(retList);
	return webRes.Cb(retList)
}

// DeleteByUserIds 根据userId删除绑定关系
// 对应 Java: DeviceBindUserServiceImpl.deleteByUserIds(List<String> userIds, String tenantId)
func (s *deviceBindUserService) DeleteByUserIds(ctx context.Context, userIds []string, tenantId string) webRes.IBaseRes {
	logger.Infof("根据userId删除绑定关系开始: userIds=%v, tenantId=%s", userIds, tenantId)

	// 对应 Java: LambdaQueryWrapper<DeviceBindUserEntity> queryWrapper = new LambdaQueryWrapper<>();
	// queryWrapper.eq(DeviceBindUserEntity::getTenantId, tenantId);
	// queryWrapper.and(e -> e.in(DeviceBindUserEntity::getCreatorId, userIds).or().in(DeviceBindUserEntity::getOwner, userIds));
	rowsAffected, err := repository.DeviceBindUserRepository.DeleteByUserIds(ctx, userIds, tenantId)
	if err != nil {
		logger.Errorf("根据userId删除绑定关系失败: userIds=%v, tenantId=%s, error=%v", userIds, tenantId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "根据userId删除绑定关系失败")
	}

	logger.Infof("根据userId删除绑定关系成功: userIds=%v, tenantId=%s, rowsAffected=%d", userIds, tenantId, rowsAffected)
	// 对应 Java: return ResponseMessage.buildSuccess(row > 0);
	return webRes.Cb(rowsAffected > 0)
}

// AddOnlineAppSnCacheForTempMap 添加在线app的sn缓存，用于实时地图
// 对应 Java: DeviceBindUserServiceImpl.addOnlineAppSnCacheForTempMap(List<String> snList)
func (s *deviceBindUserService) AddOnlineAppSnCacheForTempMap(ctx context.Context, snList []string) {
	// 对应 Java: @Async
	go func() {
		// 对应 Java: if(CollectionUtils.isEmpty(snList)){ return; }
		if len(snList) == 0 {
			logger.Debugf("SN列表为空，跳过添加在线app的sn缓存")
			return
		}

		logger.Infof("添加在线app的sn缓存开始: snList=%v", snList)

		// 对应 Java: private static final String ONLINE_APP_SN_KEY = "OnLineApp:sn:";
		const onlineAppSnKey = "OnLineApp:sn:"

		// 对应 Java: snList.forEach(sn -> { redisTemplate.opsForValue().set(ONLINE_APP_SN_KEY+sn, 1, 5, TimeUnit.MINUTES); });
		for _, sn := range snList {
			key := onlineAppSnKey + sn
			err := redis.Rdb.Set(ctx, key, "1", 5*time.Minute).Err()
			if err != nil {
				logger.Errorf("添加在线app的sn缓存失败: sn=%s, error=%v", sn, err)
				continue
			}

			// 对应 Java: Object o = redisTemplate.opsForValue().get(ONLINE_APP_SN_KEY + sn); LogUtils.info("添加在线app的sn缓存成功,sn:{},value:{}",sn,o);
			value, err := redis.Rdb.Get(ctx, key).Result()
			if err != nil {
				logger.Errorf("验证在线app的sn缓存失败: sn=%s, error=%v", sn, err)
			} else {
				logger.Infof("添加在线app的sn缓存成功: sn=%s, value=%s", sn, value)
			}
		}

		logger.Infof("添加在线app的sn缓存完成: snList=%v", snList)
	}()
}

// PropertyGetTopicHandler MQTT属性获取主题处理
// 对应 Java: DeviceBindUserServiceImpl.propertyGetTopicHandler(MqttPropertyGetReqDTO param) (遵循规则46: 严格按照Java编码内容转换)
func (s *deviceBindUserService) PropertyGetTopicHandler(ctx context.Context, param *dto.MqttPropertyGetReqDTO) {
	logger.Infof("MQTT属性获取主题处理: clientId=%s, topic=%s", param.ClientId, param.Topic)

	// 对应 Java: if (StringUtils.isNotBlank(param.getClientId())) { String[] split = param.getClientId().split("_"); if (split.length >= 2) { String userId = split[1]; redisTemplate.opsForValue().set("onlineAppSn:" + userId, param.getTopic(), 30, TimeUnit.MINUTES); } }
	if param.ClientId != "" {
		// 对应 Java: String[] split = param.getClientId().split("_");
		parts := strings.Split(param.ClientId, "_")
		if len(parts) >= 2 {
			// 对应 Java: String userId = split[1];
			userId := parts[1]

			// 对应 Java: redisTemplate.opsForValue().set("onlineAppSn:" + userId, param.getTopic(), 30, TimeUnit.MINUTES);
			key := fmt.Sprintf("onlineAppSn:%s", userId)
			err := redis.Rdb.Set(ctx, key, param.Topic, 30*time.Minute).Err()
			if err != nil {
				logger.Errorf("设置在线APP SN缓存失败: userId=%s, topic=%s, error=%v", userId, param.Topic, err)
			} else {
				logger.Infof("设置在线APP SN缓存成功: userId=%s, topic=%s", userId, param.Topic)
			}
		}
	}
}

// DelBySnAndUserId 根据SN和用户ID删除绑定关系
// 对应 Java: OpenDeviceBindServiceImpl.batchUnBindDevice中的删除逻辑 (遵循规则46: 严格按照Java编码内容转换)
func (s *deviceBindUserService) DelBySnAndUserId(ctx context.Context, sn, userId string) webRes.IBaseRes {
	logger.Infof("根据SN和用户ID删除绑定关系: sn=%s, userId=%s", sn, userId)

	// 对应 Java: if (StringUtils.isBlank(sn)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "sn 不能为空"); }
	if sn == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "sn 不能为空")
	}

	// 对应 Java: if (StringUtils.isEmpty(userId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "userId 不能为空"); }
	if userId == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "userId 不能为空")
	}

	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)

	// 对应 Java: DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheBySn(tenantId, sn); if (Objects.isNull(deviceInfo)) { return ResponseMessage.buildFail(NOT_FOUND_DATA, Constant.MES_DEVICE_NOTEXIST); }
	deviceInfo, err := cache.DeviceInfoCache.GetCacheBySn(ctx, sn, tenantId)
	if err != nil || deviceInfo == nil {
		logger.Errorf("设备不存在: sn=%s, tenantId=%s, error=%v", sn, tenantId, err)
		return webRes.Ce(webErr.NOT_FOUND_DATA, "设备不存在")
	}

	// 对应 Java: 查询这里的userId是否为开发平台的openid (简化处理，实际需要调用SSO服务)
	// SsoResponseMessage<ThirdUserinfoDTO> thirdUserByOpenIdMessage = ssoRemote.getThirdUserByOpenId(userId,SystemContextUtils.getTenantId());
	// 这里简化处理，直接使用传入的userId

	// 对应 Java: LambdaQueryWrapper<DeviceBindUserEntity> wrapper = new LambdaQueryWrapper<>(); wrapper.eq(DeviceBindUserEntity::getOwner, userId); wrapper.in(DeviceBindUserEntity::getSn, snList);
	bindEntities, err := repository.DeviceBindUserRepository.GetByOwnerAndSn(ctx, userId, sn)
	if err != nil {
		logger.Errorf("查询绑定关系失败: sn=%s, userId=%s, error=%v", sn, userId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "查询绑定关系失败")
	}

	// 对应 Java: if(ObjectUtils.isEmpty(bindSnList)){ return ResponseMessage.buildFail(NOT_FOUND_DATA, executeResult, Constant.MES_DEVICE_UNBIND_FAIL); }
	if len(bindEntities) == 0 {
		logger.Infof("绑定关系不存在或设备的管理员不是当前用户: sn=%s, userId=%s", sn, userId)
		return webRes.Cb(false)
	}

	// 对应 Java: int delete = this.baseMapper.delete(wrapper);
	deleteCount, err := repository.DeviceBindUserRepository.DeleteByOwnerAndSn(ctx, userId, sn)
	if err != nil {
		logger.Errorf("删除绑定关系失败: sn=%s, userId=%s, error=%v", sn, userId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "删除绑定关系失败")
	}

	result := deleteCount > 0
	logger.Infof("根据SN和用户ID删除绑定关系完成: sn=%s, userId=%s, deleteCount=%d, result=%v", sn, userId, deleteCount, result)
	return webRes.Cb(result)
}
