package service

import (
	"context"
	"encoding/json"
	"time"

	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/dao/cache"
	"piceacorp.com/device-service/internal/service/remote"
	"piceacorp.com/device-service/pkg/logger"
	"piceacorp.com/device-service/pkg/message/kafka"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IDeviceControlService 设备控制服务接口
// 对应 Java: DeviceControlService (遵循规则43: 必须全部转换)
type IDeviceControlService interface {
	// 控制设备 - 对应 Java: controlDevice(DeviceControlReq req, String tenantId)
	ControlDevice(ctx context.Context, req *req.DeviceControlReq, tenantId string) webRes.IBaseRes
}

var DeviceControlService deviceControlService

type deviceControlService struct{}

// 常量定义 - 对应 Java: private static final int
const (
	// 成功 - 对应 Java: private static final int SUCCESS = 0;
	SUCCESS = 0
	// 设备离线 - 对应 Java: private static final int ROBOT_OFFLINE = 1;
	ROBOT_OFFLINE = 1
	// 获取设备影子失败 - 对应 Java: private static final int GET_SHADOW_FAIL = 2;
	GET_SHADOW_FAIL = 2
)

// ControlDevice 控制设备
// 对应 Java: DeviceControlService.controlDevice(DeviceControlReq req, String tenantId) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceControlService) ControlDevice(ctx context.Context, req *req.DeviceControlReq, tenantId string) webRes.IBaseRes {
	logger.Infof("设备远程控制开始: deviceId=%s, tenantId=%s, content=%s", req.DeviceId, tenantId, req.Content)

	// 对应 Java: final DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheById(tenantId, req.getDeviceId());
	deviceInfo, err := cache.DeviceInfoCache.GetCacheById(ctx, tenantId, req.DeviceId)
	if err != nil {
		logger.Errorf("获取设备缓存失败: deviceId=%s, error=%v", req.DeviceId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "获取设备信息失败")
	}

	if deviceInfo == nil {
		logger.Errorf("设备不存在: deviceId=%s", req.DeviceId)
		return webRes.Ce(webErr.NOT_FOUND_DATA, "设备不存在")
	}

	// 对应 Java: //通过设备sn获取设备影子
	// 对应 Java: final ResponseMessage<DeviceShadowEntity> response = deviceShadowRemote.findBySN(deviceInfo.getSn());
	// 对应 Java: final DeviceShadowEntity deviceShadow = response.getResult();
	deviceShadow, err := remote.DeviceShadowService.FindBySN(ctx, deviceInfo.Sn)
	if err != nil {
		logger.Errorf("调用设备影子服务失败: sn=%s, error=%v", deviceInfo.Sn, err)
		return webRes.Cb(GET_SHADOW_FAIL)
	}

	// 对应 Java: if (Objects.isNull(deviceShadow)) { LogUtils.error("设备远程控制，远程调用获取设备影子失败，req={}", JsonUtils.toJSON(req)); return ResponseMessage.buildSuccess(GET_SHADOW_FAIL); }
	if deviceShadow == nil {
		logger.Errorf("设备远程控制，远程调用获取设备影子失败，req=%+v", req)
		return webRes.Cb(GET_SHADOW_FAIL)
	}

	// 对应 Java: //设备离线
	// 对应 Java: if (!deviceShadow.getOnlineStatus()) { return ResponseMessage.buildSuccess(ROBOT_OFFLINE); }
	if !deviceShadow.OnlineStatus {
		return webRes.Cb(ROBOT_OFFLINE)
	}

	// 对应 Java: //发送控制命令
	// 对应 Java: kafkaService.senServiceInvoke(req.getDeviceId(), tenantId, MqttContentUtil.joinContent(req.getContent()), DevicePushTag.CONTROL, Constant.SERVICE_INVOKE_KAFKA_TOPIC);
	content := s.joinContent(req.Content)
	devicePushTag := "CONTROL" // 对应 Java: DevicePushTag.CONTROL
	topic := "service_invoke"  // 对应 Java: Constant.SERVICE_INVOKE_KAFKA_TOPIC

	err = s.senServiceInvoke(ctx, req.DeviceId, tenantId, content, devicePushTag, topic)
	if err != nil {
		logger.Errorf("发送控制命令失败: deviceId=%s, error=%v", req.DeviceId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "发送控制命令失败")
	}

	// 对应 Java: return ResponseMessage.buildSuccess(SUCCESS);
	return webRes.Cb(SUCCESS)
}

// joinContent 拼接内容
// 对应 Java: MqttContentUtil.joinContent(req.getContent()) (遵循规则42: 严格按照Java编码内容转换)
func (s *deviceControlService) joinContent(content string) string {
	// 对应 Java: MqttContentUtil.joinContent 的具体实现
	// 这里需要根据Java的具体实现来转换，暂时直接返回原内容
	return content
}

// senServiceInvoke 发送服务调用
// 对应 Java: kafkaService.senServiceInvoke(req.getDeviceId(), tenantId, MqttContentUtil.joinContent(req.getContent()), DevicePushTag.CONTROL, Constant.SERVICE_INVOKE_KAFKA_TOPIC) (遵循规则34)
func (s *deviceControlService) senServiceInvoke(ctx context.Context, deviceId, tenantId, content, devicePushTag, topic string) error {
	// 构造消息内容
	message := map[string]interface{}{
		"deviceId":      deviceId,
		"tenantId":      tenantId,
		"content":       content,
		"devicePushTag": devicePushTag,
		"timestamp":     time.Now().Unix(), // 遵循规则43: 使用time.Now().Unix()而不是utils.CurrentTimeMillis()
	}

	// 转换为JSON字符串 (遵循规则42: 使用json.Marshal而不是utils.ToJSON)
	messageJSON, err := json.Marshal(message)
	if err != nil {
		return err
	}

	// 对应 Java: kafkaService.senServiceInvoke (遵循规则34: 使用kafka.Producer发送消息)
	return kafka.Producer.SendMessage(ctx, topic, string(messageJSON))
}
