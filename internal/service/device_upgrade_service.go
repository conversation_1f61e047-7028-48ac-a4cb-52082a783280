package service

import (
	"context"
	"encoding/json"

	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/pkg/logger"
	"piceacorp.com/device-service/pkg/message/kafka"
)

// IDeviceUpgradeService 设备升级服务接口
// 对应 Java: IDeviceUpgradeService
type IDeviceUpgradeService interface {
	// 检查设备是否需要升级 - 对应 Java: void checkDeviceUpgrade(DeviceInfoEntity device)
	CheckDeviceUpgrade(ctx context.Context, device *domain.DeviceInfo)
}

var DeviceUpgradeService deviceUpgradeService

type deviceUpgradeService struct{}

// CheckDeviceUpgrade 检查设备是否需要升级
// 对应 Java: DeviceUpgradeServiceImpl.checkDeviceUpgrade(DeviceInfoEntity device)
func (s *deviceUpgradeService) CheckDeviceUpgrade(ctx context.Context, device *domain.DeviceInfo) {
	// 对应 Java: if (null == device || StringUtils.isBlank(device.getId()) || StringUtils.isBlank(device.getVersions()))
	if device == nil || device.Id == "" || device.Versions == "" {
		logger.Errorf("当前设备没有version信息，不检查是否需OTA升级: device=%+v", device)
		return
	}

	// 异步执行检查
	go func() {
		logger.Infof("开始发送kafka检测设备是否需要OTA主动升级消息 设备: %+v", device)

		// 对应 Java: kafkaTemplate.send(Constant.UPGRADE_DEVICE_CHECK, JSONObject.toJSONString(device));
		deviceJSON, err := json.Marshal(device)
		if err != nil {
			logger.Errorf("序列化设备信息失败: deviceId=%s, error=%v", device.Id, err)
			return
		}

		// 发送到Kafka - 对应 Java: kafkaTemplate.send(Constant.UPGRADE_DEVICE_CHECK, JSONObject.toJSONString(device));
		err = kafka.Producer.SendMessage(ctx, "upgradeDeviceCheck", string(deviceJSON))
		if err != nil {
			logger.Errorf("发送kafka检测设备是否需要OTA主动升级消息失败: deviceId=%s, error=%v", device.Id, err)
			return
		}

		logger.Infof("发送kafka检测设备是否需要OTA主动升级消息成功: deviceId=%s", device.Id)
	}()
}
