package service

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/spf13/viper"
	"strconv"
	"sync"
	"time"

	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/bean/vo"
	"piceacorp.com/device-service/internal/dao/domain"
	redis "piceacorp.com/device-service/pkg/data/cache"
	"piceacorp.com/device-service/pkg/logger"
	"piceacorp.com/device-service/pkg/message/kafka"
)

// IDeviceUpgradeService 设备升级服务接口
// 对应 Java: IDeviceUpgradeService
type IDeviceUpgradeService interface {
	// 检查设备是否需要升级 - 对应 Java: void checkDeviceUpgrade(DeviceInfoEntity device)
	CheckDeviceUpgrade(ctx context.Context, device *domain.DeviceInfo)
	// 发布设备升级 - 对应 Java: void publishDevices(PackageInfoVo info) (遵循规则47: 必须全部转换)
	PublishDevices(ctx context.Context, info *req.PackageInfoReq)
	// 创建推送升级任务 - 对应 Java: void createPushUpgradeTask(PackageInfoVo info) (遵循规则47: 必须全部转换)
	CreatePushUpgradeTask(ctx context.Context, info *req.PackageInfoReq)
}

var DeviceUpgradeService deviceUpgradeService

type deviceUpgradeService struct{}

// CheckDeviceUpgrade 检查设备是否需要升级
// 对应 Java: DeviceUpgradeServiceImpl.checkDeviceUpgrade(DeviceInfoEntity device)
func (s *deviceUpgradeService) CheckDeviceUpgrade(ctx context.Context, device *domain.DeviceInfo) {
	// 对应 Java: if (null == device || StringUtils.isBlank(device.getId()) || StringUtils.isBlank(device.getVersions()))
	if device == nil || device.Id == "" || device.Versions == "" {
		logger.Errorf("当前设备没有version信息，不检查是否需OTA升级: device=%+v", device)
		return
	}

	// 异步执行检查
	go func() {
		logger.Infof("开始发送kafka检测设备是否需要OTA主动升级消息 设备: %+v", device)

		// 对应 Java: kafkaTemplate.send(Constant.UPGRADE_DEVICE_CHECK, JSONObject.toJSONString(device));
		deviceJSON, err := json.Marshal(device)
		if err != nil {
			logger.Errorf("序列化设备信息失败: deviceId=%s, error=%v", device.Id, err)
			return
		}

		// 发送到Kafka - 对应 Java: kafkaTemplate.send(Constant.UPGRADE_DEVICE_CHECK, JSONObject.toJSONString(device));
		err = kafka.Producer.SendMessage(ctx, "upgradeDeviceCheck", string(deviceJSON))
		if err != nil {
			logger.Errorf("发送kafka检测设备是否需要OTA主动升级消息失败: deviceId=%s, error=%v", device.Id, err)
			return
		}

		logger.Infof("发送kafka检测设备是否需要OTA主动升级消息成功: deviceId=%s", device.Id)
	}()
}

// PublishDevices 发布设备升级
// 对应 Java: DeviceUpgradeServiceImpl.publishDevices(PackageInfoVo info) (遵循规则46: 严格按照Java编码内容转换)
func (s *deviceUpgradeService) PublishDevices(ctx context.Context, info *req.PackageInfoReq) {
	logger.Infof("发布设备升级开始: productModelCode=%s, id=%s", info.ProductModelCode, info.Id)

	// 对应 Java: String productModelCode = info.getProductModelCode();
	productModelCode := info.ProductModelCode

	// 对应 Java: Integer count = deviceInfoService.getCountByCode(productModelCode);
	count, err := DeviceInfoService.GetCountByCode(ctx, productModelCode)
	if err != nil {
		logger.Errorf("获取设备数量失败: productModelCode=%s, error=%v", productModelCode, err)
		return
	}

	// 对应 Java: if (null != count && count > 0)
	if count > 0 {
		// 对应 Java: @Value("${upgrade.publishNum:2000}") private int publishNum;
		publishNum := viper.GetInt("upgrade.publishNum")
		// 对应 Java: @Value("${upgrade.threadNum:4}") private int threadNum;
		threadNum := viper.GetInt("upgrade.threadNum")
		if publishNum <= 0 {
			publishNum = 2000
		}
		if threadNum <= 0 {
			threadNum = 4
		}
		// 对应 Java: int pageCount = (count + publishNum - 1) / publishNum;
		pageCount := (count + publishNum - 1) / publishNum
		logger.Infof("%d 条设备 分 %d 每次 %d 推送kafka升级消息！", count, pageCount, threadNum)

		// 对应 Java: ThreadPoolExecutor threadPool = ThreadPoolUtil.getThreadPool(threadNum);
		var wg sync.WaitGroup

		// 对应 Java: for (int i = 1; i <= pageCount; i++)
		for i := 1; i <= pageCount; i++ {
			wg.Add(1)
			go func(pageNum int) {
				defer wg.Done()
				s.processDevicePage(ctx, info, productModelCode, pageNum, publishNum)
			}(i)
		}

		wg.Wait()
		logger.Infof("发布设备升级完成: productModelCode=%s, id=%s", info.ProductModelCode, info.Id)
	} else {
		logger.Infof("没有找到需要升级的设备: productModelCode=%s", productModelCode)
	}
}

// processDevicePage 处理设备分页升级
// 对应 Java: threadPool.execute(() -> { ... }) (遵循规则46: 严格按照Java编码内容转换)
func (s *deviceUpgradeService) processDevicePage(ctx context.Context, info *req.PackageInfoReq, productModelCode string, pageNum, publishNum int) {
	// 对应 Java: Page<DeviceInfoEntity> device = deviceInfoService.getPageByCode(productModelCode, finalI, publishNum);
	devicePage, err := DeviceInfoService.GetPageByCode(ctx, productModelCode, pageNum, publishNum)
	if err != nil {
		logger.Errorf("获取设备分页数据失败: productModelCode=%s, page=%d, error=%v", productModelCode, pageNum, err)
		return
	}

	// 对应 Java: if (null == device || device.getRecords().isEmpty())
	if devicePage == nil || len(devicePage.Records) == 0 {
		logger.Errorf("%s 固件升级包没有找到对应工程类型为 %s 的设备", info.Id, info.ProductModelCode)
		return
	}

	// 对应 Java: UpgradeSetModel model = new UpgradeSetModel(...)
	packageSizeInt, _ := strconv.Atoi(info.PackageSize)
	model := &vo.UpgradeSetModel{
		PackageSize:      packageSizeInt,
		ProductModelCode: info.ProductModelCode,
		PackageType:      info.PackageType,
		VersionName:      info.VersionName,
		VersionCode:      info.VersionCode,
		PackageUrl:       info.PackageUrl,
		Md5:              info.Md5,
		Signed:           false,                                     // 对应 Java: false
		Silence:          info.Silence != nil && *info.Silence == 1, // 对应 Java: info.getSilence().equals(1)
	}

	// 对应 Java: String body = JSONObject.toJSONString(model);
	body, err := json.Marshal(model)
	if err != nil {
		logger.Errorf("序列化升级模型失败: model=%+v, error=%v", model, err)
		return
	}

	// 对应 Java: List<DeviceInfoEntity> deviceInfos = Collections.synchronizedList(new ArrayList<DeviceInfoEntity>()); deviceInfos = device.getRecords();
	deviceInfos := devicePage.Records

	// 对应 Java: for (DeviceInfoEntity deviceInfo : deviceInfos)
	for _, deviceInfo := range deviceInfos {
		s.sendUpgradeMessage(ctx, deviceInfo, info, string(body))
	}
}

// sendUpgradeMessage 发送升级消息
// 对应 Java: for循环内的消息发送逻辑 (遵循规则46: 严格按照Java编码内容转换)
func (s *deviceUpgradeService) sendUpgradeMessage(ctx context.Context, deviceInfo *domain.DeviceInfo, info *req.PackageInfoReq, body string) {
	// 对应 Java: try { ... } catch (Exception e) { ... }
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("%s 设备推送kafka升级消息失败: %v", deviceInfo.Sn, r)
			// 对应 Java: redisTemplate.opsForList().rightPushAll("errorUpgrade:" + productModelCode + ":" + info.getVersionCode(), deviceInfo.getSn());
			errorKey := fmt.Sprintf("errorUpgrade:%s:%s", info.ProductModelCode, info.VersionCode)
			redis.Rdb.RPush(ctx, errorKey, deviceInfo.Sn)
		}
	}()

	// 对应 Java: KafkaData data = new KafkaData();
	timestamp := time.Now().Unix() * 1000 // 转换为毫秒
	data := &vo.KafkaData{
		MessageId:  timestamp,
		Timestamp:  timestamp,
		ProductKey: deviceInfo.ProductModeCode,
		TenantId:   deviceInfo.TenantId,
		Topic:      fmt.Sprintf("/mqtt/%s/%s/ota.upgrade.set", deviceInfo.ProductModeCode, deviceInfo.Sn), // 对应 Java: Constant.MQTT_BASE_TOPIC + ... + Constant.MOTHOD_UPGRADE_SET
		Sn:         deviceInfo.Sn,
		Version:    info.VersionCode,
		Data:       body,
	}

	// 对应 Java: String kafkaData = JSONObject.toJSONString(data);
	kafkaData, err := json.Marshal(data)
	if err != nil {
		logger.Errorf("序列化Kafka数据失败: data=%+v, error=%v", data, err)
		return
	}

	// 对应 Java: LogUtils.info("kafka推送设备OTA升级消息,topic为:{},内容为:{}", Constant.UPGRADE_SET_TOPIC, kafkaData);
	logger.Infof("kafka推送设备OTA升级消息,topic为:upgradeSet,内容为:%s", string(kafkaData))

	// 对应 Java: kafkaTemplate.send(Constant.UPGRADE_SET_TOPIC, kafkaData);
	err = kafka.Producer.SendMessage(ctx, "upgradeSet", string(kafkaData))
	if err != nil {
		logger.Errorf("%s 设备推送kafka升级消息失败: %v", deviceInfo.Sn, err)
		// 对应 Java: redisTemplate.opsForList().rightPushAll("errorUpgrade:" + productModelCode + ":" + info.getVersionCode(), deviceInfo.getSn());
		errorKey := fmt.Sprintf("errorUpgrade:%s:%s", info.ProductModelCode, info.VersionCode)
		redis.Rdb.RPush(ctx, errorKey, deviceInfo.Sn)
		return
	}

	// 对应 Java: redisTemplate.opsForList().rightPushAll("upgrade:" + productModelCode + ":" + info.getVersionCode(), sn);
	upgradeKey := fmt.Sprintf("upgrade:%s:%s", info.ProductModelCode, info.VersionCode)
	redis.Rdb.RPush(ctx, upgradeKey, deviceInfo.Sn)
}

// CreatePushUpgradeTask 创建推送升级任务
// 对应 Java: DeviceUpgradeServiceImpl.createPushUpgradeTask(PackageInfoVo info) (遵循规则46: 严格按照Java编码内容转换)
func (s *deviceUpgradeService) CreatePushUpgradeTask(ctx context.Context, info *req.PackageInfoReq) {
	logger.Infof("创建推送升级任务开始: productModelCode=%s, pushTaskId=%s", info.ProductModelCode, info.PushTaskId)

	// 对应 Java: int pageSize = 500;
	pageSize := 500

	// 对应 Java: Page<DeviceInfoEntity> pageResult = deviceInfoService.getPageByCode(info.getProductModelCode(), 1, pageSize);
	pageResult, err := DeviceInfoService.GetPageByCode(ctx, info.ProductModelCode, 1, pageSize)
	if err != nil {
		logger.Errorf("获取设备分页数据失败: productModelCode=%s, error=%v", info.ProductModelCode, err)
		return
	}

	// 对应 Java: List<DeviceInfoEntity> deviceList = pageResult.getRecords(); long total = pageResult.getTotal(); long pages = pageResult.getPages();
	deviceList := pageResult.Records
	total := pageResult.Total
	pages := pageResult.Pages

	// 对应 Java: if (total > 0 && CollectionUtils.isNotEmpty(deviceList))
	if total > 0 && len(deviceList) > 0 {
		// 对应 Java: JSONObject object = new JSONObject(); object.put("taskId",info.getPushTaskId()); object.put("device",deviceList);
		taskData := map[string]interface{}{
			"taskId": info.PushTaskId,
			"device": deviceList,
		}

		// 对应 Java: kafkaTemplate.send(Constant.UPGRADE_DEVICE_LIST,object.toJSONString());
		taskJSON, err := json.Marshal(taskData)
		if err != nil {
			logger.Errorf("序列化任务数据失败: taskData=%+v, error=%v", taskData, err)
			return
		}

		err = kafka.Producer.SendMessage(ctx, "upgradeDeviceList", string(taskJSON))
		if err != nil {
			logger.Errorf("发送升级设备列表消息失败: taskId=%s, error=%v", info.PushTaskId, err)
			return
		}

		// 对应 Java: if (pages > 1) { for (long i = 2; i <= pages; i++) { ... } }
		if pages > 1 {
			for i := 2; i <= pages; i++ {
				// 对应 Java: Page<DeviceInfoEntity> result = deviceInfoService.getPageByCode(info.getProductModelCode(), (int) i, pageSize);
				result, err := DeviceInfoService.GetPageByCode(ctx, info.ProductModelCode, int(i), pageSize)
				if err != nil {
					logger.Errorf("获取设备分页数据失败: productModelCode=%s, page=%d, error=%v", info.ProductModelCode, i, err)
					continue
				}

				// 对应 Java: JSONObject json = new JSONObject(); json.put("taskId",info.getPushTaskId()); json.put("device",result.getRecords());
				pageTaskData := map[string]interface{}{
					"taskId": info.PushTaskId,
					"device": result.Records,
				}

				// 对应 Java: kafkaTemplate.send(Constant.UPGRADE_DEVICE_LIST,json.toJSONString());
				pageTaskJSON, err := json.Marshal(pageTaskData)
				if err != nil {
					logger.Errorf("序列化分页任务数据失败: pageTaskData=%+v, error=%v", pageTaskData, err)
					continue
				}

				err = kafka.Producer.SendMessage(ctx, "upgradeDeviceList", string(pageTaskJSON))
				if err != nil {
					logger.Errorf("发送分页升级设备列表消息失败: taskId=%s, page=%d, error=%v", info.PushTaskId, i, err)
					continue
				}
			}
		}

		logger.Infof("创建推送升级任务成功: productModelCode=%s, pushTaskId=%s, total=%d, pages=%d",
			info.ProductModelCode, info.PushTaskId, total, pages)
	} else {
		// 对应 Java: LogUtils.error("OTA 主动下发升级任务详情新增失败，productModelCode: {} 下没有找到设备", info.getProductModelCode());
		logger.Errorf("OTA 主动下发升级任务详情新增失败，productModelCode: %s 下没有找到设备", info.ProductModelCode)
	}
}
