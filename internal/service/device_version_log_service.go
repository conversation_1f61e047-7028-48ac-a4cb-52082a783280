package service

import (
	"context"

	"go.mongodb.org/mongo-driver/v2/bson"
	"piceacorp.com/device-service/internal/bean/req"
	mongoEntity "piceacorp.com/device-service/internal/dao/mongo"
	mongoDb "piceacorp.com/device-service/pkg/data/mongo"
	"piceacorp.com/device-service/pkg/logger"
)

// IDeviceVersionLogService 设备版本流水服务接口
// 对应 Java: IDeviceVersionLogService
type IDeviceVersionLogService interface {
	// 保存设备版本流水 - 对应 Java: void save(String sn, List<RobotVersion> packageVersions)
	Save(ctx context.Context, sn string, packageVersions []req.RobotVersion)
}

var DeviceVersionLogService deviceVersionLogService

type deviceVersionLogService struct{}

// Save 保存设备版本流水
// 对应 Java: DeviceVersionLogServiceImpl.save(String sn, List<RobotVersion> packageVersions)
func (s *deviceVersionLogService) Save(ctx context.Context, sn string, packageVersions []req.RobotVersion) {
	// 异步执行 - 对应 Java: @Async
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("保存设备版本流水出错: %v", r)
			}
		}()
		// 对应 Java: if (CollectionUtil.isEmpty(packageVersions)) { return; }
		if len(packageVersions) == 0 {
			logger.Debugf("设备版本信息为空，跳过保存: sn=%s", sn)
			return
		}

		logger.Infof("保存设备版本流水开始: sn=%s, packageVersions=%d", sn, len(packageVersions))

		// 创建设备版本日志对象 - 对应 Java: DeviceVersionLog log = new DeviceVersionLog()
		deviceVersionLog := mongoEntity.NewDeviceVersionLog(sn, packageVersions)

		collection := mongoDb.DB.Collection(deviceVersionLog.CollectionName())

		// 检查是否已存在 - 对应 Java: boolean isExist = mongoTemplate.exists(query, DeviceVersionLog.class);
		filter := bson.M{"_id": deviceVersionLog.ID}
		count, err := collection.CountDocuments(context.Background(), filter)
		if err != nil {
			logger.Errorf("检查设备版本流水是否存在失败: sn=%s, id=%s, error=%v", sn, deviceVersionLog.ID, err)
			return
		}

		if count > 0 {
			// 如果存在就修改最近登录时间 - 对应 Java: Update update = new Update().set("lastLoginTime", now); mongoTemplate.updateFirst(query, update, DeviceVersionLog.class);
			update := bson.M{
				"$set": bson.M{
					"lastLoginTime": deviceVersionLog.LastLoginTime,
				},
			}
			_, err := collection.UpdateOne(context.Background(), filter, update)
			if err != nil {
				logger.Errorf("更新设备版本流水最后登录时间失败: sn=%s, id=%s, error=%v", sn, deviceVersionLog.ID, err)
				return
			}
			logger.Infof("更新设备版本流水最后登录时间成功: sn=%s, id=%s", sn, deviceVersionLog.ID)
			return
		}

		// 不存在就新增一条 - 对应 Java: mongoTemplate.save(log);
		_, err = collection.InsertOne(context.Background(), deviceVersionLog)
		if err != nil {
			logger.Errorf("保存设备版本流水失败: sn=%s, id=%s, error=%v", sn, deviceVersionLog.ID, err)
			return
		}

		logger.Infof("保存设备版本流水成功: sn=%s, id=%s", sn, deviceVersionLog.ID)
	}()
}
