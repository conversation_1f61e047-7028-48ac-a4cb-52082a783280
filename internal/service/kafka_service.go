package service

import (
	"context"
	"encoding/json"
	"github.com/spf13/viper"

	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/pkg/logger"
	"piceacorp.com/device-service/pkg/message/kafka"
)

// IKafkaService Kafka服务接口
// 对应 Java: KafkaService
type IKafkaService interface {
	// 设备注册统计 - 对应 Java: void deviceRegister(DeviceInfoEntity infoEntity)
	DeviceRegister(ctx context.Context, infoEntity *domain.DeviceInfo)
}

var KafkaService kafkaService

type kafkaService struct{}

// DeviceRegister 设备注册统计
// 对应 Java: KafkaServiceImpl.deviceRegister(DeviceInfoEntity infoEntity)
func (s *kafkaService) DeviceRegister(ctx context.Context, infoEntity *domain.DeviceInfo) {
	// 异步执行 - 对应 Java: @Async
	go func() {
		// 获取设备注册主题 - 对应 Java: @Value("${kafka.bigDataService.device.register:deviceRegister}")
		deviceRegisterTopic := viper.GetString("kafka.bigDataService.device.register")
		if deviceRegisterTopic == "" {
			deviceRegisterTopic = "deviceRegister"
		}

		logger.Infof("统计激活设备开始: deviceId=%s, sn=%s, topic=%s", infoEntity.Id, infoEntity.Sn, deviceRegisterTopic)

		// 对应 Java: kafkaTemplate.send(deviceRegister, JsonUtils.toJSON(infoEntity));
		deviceJSON, err := json.Marshal(infoEntity)
		if err != nil {
			logger.Errorf("设备统计，序列化设备信息失败: deviceId=%s, error=%v", infoEntity.Id, err)
			return
		}

		// 发送到Kafka
		err = kafka.Producer.SendMessage(ctx, deviceRegisterTopic, string(deviceJSON))
		if err != nil {
			logger.Errorf("设备统计，写入 kafka 异常: deviceId=%s, topic=%s, error=%v", infoEntity.Id, deviceRegisterTopic, err)
			return
		}

		logger.Infof("统计激活设备成功: deviceId=%s, sn=%s, topic=%s", infoEntity.Id, infoEntity.Sn, deviceRegisterTopic)
	}()
}
