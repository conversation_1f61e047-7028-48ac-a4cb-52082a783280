package service

import (
	"context"

	"piceacorp.com/device-service/internal/bean/vo"
	"piceacorp.com/device-service/pkg/logger"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IOpenDeviceBindService 开放平台设备绑定服务接口
// 对应 Java: IOpenDeviceBindService (遵循规则46: 严格按照Java编码内容转换)
type IOpenDeviceBindService interface {
	// 批量解绑设备 - 对应 Java: ResponseMessage<BatchExecuteResult> batchUnBindDevice(UnBindDeviceVo unBindDeviceVo);
	BatchUnBindDevice(ctx context.Context, unBindDeviceVo *vo.UnBindDeviceVo) webRes.IBaseRes
}

var OpenDeviceBindService openDeviceBindService

type openDeviceBindService struct{}

// BatchUnBindDevice 批量解绑设备
// 对应 Java: OpenDeviceBindServiceImpl.batchUnBindDevice(UnBindDeviceVo unBindDeviceVo) (遵循规则46: 严格按照Java编码内容转换)
func (s *openDeviceBindService) BatchUnBindDevice(ctx context.Context, unBindDeviceVo *vo.UnBindDeviceVo) webRes.IBaseRes {
	logger.Infof("批量解绑设备: userId=%s, snList=%v", unBindDeviceVo.UserId, unBindDeviceVo.SnList)

	// 对应 Java: if (StringUtils.isBlank(unBindDeviceVo.getUserId())) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "userId"); }
	if unBindDeviceVo.UserId == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "userId")
	}

	// 对应 Java: if (CollectionUtils.isEmpty(unBindDeviceVo.getSnList())) { return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "snList"); }
	if len(unBindDeviceVo.SnList) == 0 {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "snList")
	}

	// 对应 Java: BatchExecuteResult batchExecuteResult = new BatchExecuteResult(); List<String> successResult = new ArrayList<>(); Map<String, String> failResult = new HashMap<>();
	batchExecuteResult := &vo.BatchExecuteResult{
		SuccessResult: []string{},
		FailResult:    make(map[string]string),
	}

	// 对应 Java: for (String sn : unBindDeviceVo.getSnList()) { try { ResponseMessage<Boolean> responseMessage = deviceBindUserService.delBySnAndUserId(sn, unBindDeviceVo.getUserId()); if (responseMessage.isSuccess() && responseMessage.getResult()) { successResult.add(sn); } else { failResult.put(sn, "解绑失败"); } } catch (Exception e) { failResult.put(sn, "解绑异常"); } }
	for _, sn := range unBindDeviceVo.SnList {
		result := DeviceBindUserService.DelBySnAndUserId(ctx, sn, unBindDeviceVo.UserId)
		if !result.Fail() {
			// 检查返回结果是否为true
			if resultData, ok := result.GetData().(bool); ok && resultData {
				batchExecuteResult.SuccessResult = append(batchExecuteResult.SuccessResult, sn)
			} else {
				batchExecuteResult.FailResult[sn] = "解绑失败"
			}
		} else {
			batchExecuteResult.FailResult[sn] = "解绑异常"
		}
	}

	// 对应 Java: batchExecuteResult.setSuccessResult(successResult); batchExecuteResult.setFailResult(failResult); return ResponseMessage.buildSuccess(batchExecuteResult);
	logger.Infof("批量解绑设备完成: userId=%s, successCount=%d, failCount=%d", 
		unBindDeviceVo.UserId, len(batchExecuteResult.SuccessResult), len(batchExecuteResult.FailResult))
	return webRes.Cb(batchExecuteResult)
}
