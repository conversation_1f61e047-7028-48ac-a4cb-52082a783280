package service

import (
	"context"
	"github.com/spf13/viper"
	"time"

	"piceacorp.com/device-service/internal/bean/vo"
	"piceacorp.com/device-service/internal/dao/cache"
	"piceacorp.com/device-service/internal/dao/domain"
	"piceacorp.com/device-service/internal/dao/repository"
	"piceacorp.com/device-service/internal/service/remote"
	"piceacorp.com/device-service/pkg/logger"
	"piceacorp.com/device-service/pkg/util"
	ctxKeys "piceacorp.com/device-service/pkg/web/common/context"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IOpenDeviceBindService 开放平台设备绑定服务接口
// 对应 Java: IOpenDeviceBindService (遵循规则46: 严格按照Java编码内容转换)
type IOpenDeviceBindService interface {
	// 批量解绑设备 - 对应 Java: ResponseMessage<BatchExecuteResult> batchUnBindDevice(UnBindDeviceVo unBindDeviceVo);
	BatchUnBindDevice(ctx context.Context, unBindDeviceVo *vo.UnBindDeviceVo) webRes.IBaseRes
}

var OpenDeviceBindService openDeviceBindService

type openDeviceBindService struct{}

// BatchUnBindDevice 批量解绑设备
// 对应 Java: OpenDeviceBindServiceImpl.batchUnBindDevice(UnBindDeviceVo unBindDeviceVo) (遵循规则46: 严格按照Java编码内容转换)
func (s *openDeviceBindService) BatchUnBindDevice(ctx context.Context, unBindDeviceVo *vo.UnBindDeviceVo) webRes.IBaseRes {
	logger.Infof("批量解绑设备: userId=%s, snList=%v", unBindDeviceVo.UserId, unBindDeviceVo.SnList)

	// 对应 Java: BatchExecuteResult executeResult = new BatchExecuteResult(); Map<String, String> failResult = new HashMap<>(); List<String> bindSnList = new ArrayList<>(); executeResult.setFailResult(failResult);
	executeResult := &vo.BatchExecuteResult{
		SuccessResult: []string{},
		FailResult:    make(map[string]string),
	}
	var bindSnList []string

	// 对应 Java: try { ... } catch (Exception ex) { ... }
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("批量解绑设备出现异常: %v", r)
			executeResult.FailResult = make(map[string]string)
			for _, sn := range unBindDeviceVo.SnList {
				executeResult.FailResult[sn] = "设备解绑失败"
			}
		}
	}()

	// 对应 Java: String tenantId = SystemContextUtils.getTenantId(); List<String> snList = unBindDeviceVo.getSnList(); String userId = unBindDeviceVo.getUserId();
	tenantId := ctx.Value(ctxKeys.TENANT_ID).(string)
	snList := make([]string, len(unBindDeviceVo.SnList))
	copy(snList, unBindDeviceVo.SnList)
	userId := unBindDeviceVo.UserId

	// 对应 Java: if (StringUtils.isEmpty(userId)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "userId 不能为空"); }
	if userId == "" {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "userId 不能为空")
	}

	// 对应 Java: if (ObjectUtils.isEmpty(snList)) { return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "snList 不能为空"); }
	if len(snList) == 0 {
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "snList 不能为空")
	}

	// 对应 Java: snList.forEach(sn -> { DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheBySn(tenantId, sn); if (Objects.isNull(deviceInfo)) { failResult.put(sn, Constant.MES_DEVICE_NOTEXIST); } });
	for _, sn := range snList {
		deviceInfo, err := cache.DeviceInfoCache.GetCacheBySn(ctx, sn, tenantId)
		if err != nil || deviceInfo == nil {
			executeResult.FailResult[sn] = "设备不存在"
		}
	}

	// 对应 Java: snList.removeAll(failResult.keySet());
	var validSnList []string
	for _, sn := range snList {
		if _, exists := executeResult.FailResult[sn]; !exists {
			validSnList = append(validSnList, sn)
		}
	}
	snList = validSnList

	// 对应 Java: if (ObjectUtils.isEmpty(snList)) { return ResponseMessage.buildFail(NOT_FOUND_DATA, executeResult, Constant.MES_DEVICE_UNBIND_FAIL); }
	if len(snList) == 0 {
		return webRes.Ce(webErr.NOT_FOUND_DATA, "设备解绑失败")
	}

	// 对应 Java: SsoResponseMessage<ThirdUserinfoDTO> thirdUserByOpenIdMessage = ssoRemote.getThirdUserByOpenId(userId,SystemContextUtils.getTenantId());
	thirdUserResult := remote.SsoService.GetThirdUserByOpenId(ctx, userId, tenantId)
	uinfo := thirdUserResult.Result
	if !thirdUserResult.Fail() && uinfo != nil {
		if uinfo.SsoId != "" {
			userId = uinfo.SsoId
		}
	}

	// 对应 Java: LambdaQueryWrapper<DeviceBindUserEntity> wrapper = new LambdaQueryWrapper<>(); wrapper.eq(DeviceBindUserEntity::getOwner, userId); wrapper.in(DeviceBindUserEntity::getSn, snList); List<DeviceBindUserEntity> deviceBindEntities = this.baseMapper.selectList(wrapper);
	deviceBindEntities, err := repository.DeviceBindUserRepository.GetByOwnerAndSnList(ctx, userId, snList)
	if err != nil {
		logger.Errorf("查询绑定关系失败: userId=%s, snList=%v, error=%v", userId, snList, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "设备解绑失败")
	}

	// 对应 Java: bindSnList = deviceBindEntities.stream().map(DeviceBindUserEntity::getSn).collect(Collectors.toList());
	for _, entity := range deviceBindEntities {
		bindSnList = append(bindSnList, entity.Sn)
	}

	// 对应 Java: snList.removeAll(bindSnList); snList.forEach(sn -> failResult.put(sn, "绑定关系不存在或设备的管理员不是当前用户，无法解绑"));
	for _, sn := range snList {
		found := false
		for _, bindSn := range bindSnList {
			if sn == bindSn {
				found = true
				break
			}
		}
		if !found {
			executeResult.FailResult[sn] = "绑定关系不存在或设备的管理员不是当前用户，无法解绑"
		}
	}

	// 对应 Java: if(ObjectUtils.isEmpty(bindSnList)){ return ResponseMessage.buildFail(NOT_FOUND_DATA, executeResult, Constant.MES_DEVICE_UNBIND_FAIL); }
	if len(bindSnList) == 0 {
		return webRes.Ce(webErr.NOT_FOUND_DATA, "设备解绑失败")
	}

	// 对应 Java: int delete = this.baseMapper.delete(wrapper);
	deleteCount, err := repository.DeviceBindUserRepository.DeleteByOwnerAndSnList(ctx, userId, bindSnList)
	if err != nil {
		logger.Errorf("删除绑定关系失败: userId=%s, bindSnList=%v, error=%v", userId, bindSnList, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "设备解绑失败")
	}

	// 对应 Java: if (deviceBindConfig.getRelation()) { long remove = deviceBindRepository.remove(userId, bindSnList); LogUtils.info("mongo删除绑定关系, unBindDeviceVo={}, remove={}", unBindDeviceVo, remove); }
	deviceBindRelation := viper.GetBool("device.bind.relation")
	if deviceBindRelation {
		removeCount, err := repository.DeviceBindMongoRepository.Remove(ctx, userId, bindSnList)
		if err != nil {
			logger.Errorf("mongo删除绑定关系失败: userId=%s, bindSnList=%v, error=%v", userId, bindSnList, err)
		} else {
			logger.Infof("mongo删除绑定关系: unBindDeviceVo=%+v, remove=%d", unBindDeviceVo, removeCount)
		}
	}

	// 对应 Java: List<BindLogEntity> bindLogEntityList = new ArrayList<>(); for (DeviceBindUserEntity t : deviceBindEntities) { bindLogEntityList.add(new BindLogEntity(t.getDeviceId(), userId, LogEnum.UNBIND.getCode(), LogEnum.SELF.getCode(), tenantId)); } logService.bindLogList(bindLogEntityList);
	var bindLogEntityList []*domain.BindLog
	for _, entity := range deviceBindEntities {
		bindLog := &domain.BindLog{
			Id:         util.Id(),
			DeviceId:   entity.DeviceId,
			UserId:     userId,
			LogType:    2, // 对应 Java: LogEnum.UNBIND.getCode() = 2
			OperateBy:  1, // 对应 Java: LogEnum.SELF.getCode() = 1
			TenantId:   tenantId,
			CreateTime: time.Now(),
		}
		bindLogEntityList = append(bindLogEntityList, bindLog)
	}
	LogService.BindLogListDomain(ctx, bindLogEntityList)

	// 对应 Java: executeResult.setSuccessResult(bindSnList);
	executeResult.SuccessResult = bindSnList

	// 对应 Java: return delete > 0 ? ResponseMessage.buildSuccess(executeResult) : ResponseMessage.buildFail(NOT_FOUND_DATA, executeResult, Constant.MES_DEVICE_UNBIND_FAIL);
	if deleteCount > 0 {
		logger.Infof("批量解绑设备成功: userId=%s, successCount=%d, failCount=%d",
			userId, len(executeResult.SuccessResult), len(executeResult.FailResult))
		return webRes.Cb(executeResult)
	} else {
		return webRes.Ce(webErr.NOT_FOUND_DATA, "设备解绑失败")
	}
}
