package remote

import (
	"context"
	"piceacorp.com/device-service/internal/service/remote/req"
	"piceacorp.com/device-service/pkg/remote"
	"piceacorp.com/device-service/pkg/remote/res"
)

// cloudIntentService 语控服务远程调用
type cloudIntentService struct{}

// SName 返回服务名称
func (s *cloudIntentService) Name() string {
	return "icloud-intent-service"
}

// CloudIntentService 语控服务实例
var CloudIntentService = &cloudIntentService{}

// SyncBindInfo 向第三方平台发送绑定信息
// 对应 Java: @PostMapping("/inner/icloud/syncBindInfo")
// ResponseMessage<Boolean> syncBindInfo(@RequestBody SyncBindReq req);
func (s *cloudIntentService) SyncBindInfo(ctx context.Context, req *req.SyncBindReq) *res.CSRes[*bool] {
	url := "/inner/icloud/syncBindInfo"
	return remote.Post[*res.CSRes[*bool]](ctx, url, req, s)
}

// SyncDevice 同步设备
// 对应 Java: IcloudIntentServiceRemote.syncDevice(@RequestBody SyncDeviceReportReq req)
func (s *cloudIntentService) SyncDevice(ctx context.Context, syncReq *req.SyncDeviceReportReq) *res.CSRes[interface{}] {
	url := "/sync/device"
	return remote.Post[*res.CSRes[interface{}]](ctx, url, syncReq, s)
}

// SyncUntieInfo 向第三方平台发送解绑信息
// 对应 Java: IcloudIntentServiceRemote.syncUntieInfo(@RequestBody SyncUntieReq req)
func (s *cloudIntentService) SyncUntieInfo(ctx context.Context, syncUntieReq *req.SyncUntieReq) *res.CSRes[*bool] {
	url := "/inner/icloud/syncUntieInfo"
	return remote.Post[*res.CSRes[*bool]](ctx, url, syncUntieReq, s)
}
