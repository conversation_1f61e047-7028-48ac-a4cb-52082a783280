package remote

import (
	"context"
	"piceacorp.com/device-service/internal/service/remote/req"
	"piceacorp.com/device-service/pkg/remote"
	"piceacorp.com/device-service/pkg/remote/res"
)

// dataStatisticsService 数据统计服务远程调用
type dataStatisticsService struct{}

// Name 返回服务名称
func (s *dataStatisticsService) Name() string {
	return "data-statistics-service"
}

// DataStatisticsService 数据统计服务实例
var DataStatisticsService = &dataStatisticsService{}

// Flow 清除设备流水数据
// 对应 Java: @PostMapping("/inner/flow/clear")
// ResponseMessage<Boolean> flow(@RequestBody ClearFlowParam param);
func (s *dataStatisticsService) Flow(ctx context.Context, clearFlowParam *req.ClearFlowParam) *res.CSRes[*bool] {
	url := "/inner/flow/clear"
	return remote.Post[*res.CSRes[*bool]](ctx, url, clearFlowParam, s)
}
