package remote

import (
	"context"
	"piceacorp.com/device-service/internal/service/remote/req"
	"piceacorp.com/device-service/pkg/remote"
	"piceacorp.com/device-service/pkg/remote/res"
)

// deviceResetService 设备重置服务远程调用
type deviceResetService struct{}

// Name 返回服务名称
func (s *deviceResetService) Name() string {
	return "device-reset-service"
}

// DeviceResetService 设备重置服务实例
var DeviceResetService = &deviceResetService{}

// ResetNickname 重置设备昵称
// 对应 Java: @PostMapping("/inner/reset/nickname")
// ResponseMessage<Boolean> ResetNickname(@RequestBody ResetNicknameReq req);
func (s *deviceResetService) ResetNickname(ctx context.Context, resetNicknameReq *req.ResetNicknameReq) *res.CSRes[*bool] {
	url := "/inner/reset/nickname"
	getRes, err := remote.Post[*res.CSRes[*bool]](ctx, url, resetNicknameReq, s)
	return remote.HandleReqError(getRes, err)
}
