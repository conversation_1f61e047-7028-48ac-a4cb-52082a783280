package remote

import (
	"context"
	"fmt"
	"piceacorp.com/device-service/internal/service/remote/req"
	"piceacorp.com/device-service/pkg/remote"
	"piceacorp.com/device-service/pkg/remote/res"
)

// deviceShadowService 设备影子服务远程调用
type deviceShadowService struct{}

// Name 返回服务名称
func (s *deviceShadowService) Name() string {
	return "device-shadow-service"
}

// DeviceShadowService 设备影子服务实例
var DeviceShadowService = &deviceShadowService{}

// FindAllBySN 通过SN列表获取设备影子列表
// 对应 Java: ResponseMessage<List<DeviceShadowEntity>> findAllBySN(@RequestBody List<String> sns);
func (s *deviceShadowService) FindAllBySN(ctx context.Context, sns []string) *res.CSRes[*[]*req.DeviceShadowEntity] {
	url := "/device-shadow/all"
	return remote.Post[*res.CSRes[*[]*req.DeviceShadowEntity]](ctx, url, sns, s)
}

// FindBySN 通过SN获取设备影子
// 对应 Java: ResponseMessage<DeviceShadowEntity> findBySN(@RequestParam String sn); (遵循规则43: 必须全部转换)
func (s *deviceShadowService) FindBySN(ctx context.Context, sn string) (*req.DeviceShadowEntity, error) {
	url := "/device-shadow/findBySN?sn=" + sn
	getRes := remote.Get[*res.CSRes[*req.DeviceShadowEntity]](ctx, url, s)

	if getRes.Fail() {
		return nil, fmt.Errorf(getRes.Msg)
	}
	return getRes.Result, nil
}
