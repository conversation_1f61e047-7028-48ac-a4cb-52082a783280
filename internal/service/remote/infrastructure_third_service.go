package remote

import (
	"context"
	remoteReq "piceacorp.com/device-service/internal/service/remote/req"
	cres "piceacorp.com/device-service/internal/service/remote/res"
	"piceacorp.com/device-service/pkg/remote"
	"piceacorp.com/device-service/pkg/remote/res"
)

type infrastructureThirdService struct{}

func (s *infrastructureThirdService) Name() string {
	return "infrastructure-third"
}

var InfrastructureThirdService = &infrastructureThirdService{}

func (s *infrastructureThirdService) GetIpInfo(ctx context.Context, host string) *res.CSRes[*cres.CountryCity] {
	url := "/geo/ip/info?host=" + host
	getRes, err := remote.Get[*res.CSRes[*cres.CountryCity], *cres.CountryCity](ctx, url, s)
	return remote.HandleReqError(getRes, err)
}

// PushShare 发送分享推送消息
// 对应 Java: void pushShare(@RequestBody JPushShareReq req);
func (s *infrastructureThirdService) PushShare(ctx context.Context, pushReq *remoteReq.JPushShareReq) *res.CSRes[interface{}] {
	url := "/jpush/share"
	getRes, err := remote.Post[*res.CSRes[interface{}]](ctx, url, pushReq, s)
	return remote.HandleReqError(getRes, err)
}

// ShareInvalidate 分享失效
// 对应 Java: InfrastructureThirdServiceRemote.shareInvalidate(@RequestBody ShareInvalidateReq req)
func (s *infrastructureThirdService) ShareInvalidate(ctx context.Context, shareInvalidateReq *remoteReq.ShareInvalidateReq) *res.CSRes[*bool] {
	url := "/inner/share/invalidate"
	getRes, err := remote.Post[*res.CSRes[*bool]](ctx, url, shareInvalidateReq, s)
	return remote.HandleReqError(getRes, err)
}

// InvalidateMsgByTargetIdAndUid 设置分享消息为无效状态
// 对应 Java: CommonService.shareInvalidate(String targetId,String uid,boolean pushMsg)
func (s *infrastructureThirdService) InvalidateMsgByTargetIdAndUid(ctx context.Context, targetId, uid string) *res.CSRes[*bool] {
	url := "/inner/msg/invalidate"
	req := map[string]string{
		"targetId": targetId,
		"uid":      uid,
	}
	getRes, err := remote.Post[*res.CSRes[*bool]](ctx, url, req, s)
	return remote.HandleReqError(getRes, err)
}

// PushCancelShareMsg 推送用户取消解绑消息
// 对应 Java: CommonService.shareInvalidate 中的推送逻辑
func (s *infrastructureThirdService) PushCancelShareMsg(ctx context.Context, targetId, uid string) *res.CSRes[*bool] {
	url := "/inner/push/cancel/share"
	req := map[string]string{
		"targetId": targetId,
		"uid":      uid,
	}
	getRes, err := remote.Post[*res.CSRes[*bool]](ctx, url, req, s)
	return remote.HandleReqError(getRes, err)
}

// PushFamilyShare 推送家庭分享消息
// 对应 Java: @PostMapping("/inner/common/pushFamilyShare")
// ResponseMessage<Boolean> pushFamilyShare(@RequestBody JPushFamilyShareReq req); (遵循规则47: 必须全部转换)
func (s *infrastructureThirdService) PushFamilyShare(ctx context.Context, req *remoteReq.JPushFamilyShareReq) *res.CSRes[*bool] {
	url := "/inner/common/pushFamilyShare"
	getRes, err := remote.Post[*res.CSRes[*bool]](ctx, url, req, s)
	return remote.HandleReqError(getRes, err)
}
