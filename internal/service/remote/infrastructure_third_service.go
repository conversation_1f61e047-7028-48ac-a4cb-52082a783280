package remote

import (
	"context"

	"piceacorp.com/device-service/internal/bean/res"
	remoteReq "piceacorp.com/device-service/internal/service/remote/req"
	cres "piceacorp.com/device-service/internal/service/remote/res"
	"piceacorp.com/device-service/pkg/remote"
	remoteRes "piceacorp.com/device-service/pkg/remote/res"
)

type infrastructureThirdService struct{}

func (s *infrastructureThirdService) Name() string {
	return "infrastructure-third"
}

var InfrastructureThirdService = &infrastructureThirdService{}

func (s *infrastructureThirdService) GetIpInfo(ctx context.Context, host string) *remoteRes.CSRes[*cres.CountryCity] {
	url := "/geo/ip/info?host=" + host
	return remote.Get[*remoteRes.CSRes[*cres.CountryCity]](ctx, url, s)
}

// CheckShareRecord 检查分享记录
// 对应 Java: CommonService.checkShareRecord(String recordId) (遵循规则47: 必须全部转换)
func (s *infrastructureThirdService) CheckShareRecord(ctx context.Context, recordId string) *remoteRes.CSRes[*res.NoticeShareRecord] {
	url := "/inner/common/checkShareRecord?recordId=" + recordId
	return remote.Get[*remoteRes.CSRes[*res.NoticeShareRecord]](ctx, url, s)
}

// PushShare 发送分享推送消息
// 对应 Java: void pushShare(@RequestBody JPushShareReq req);
func (s *infrastructureThirdService) PushShare(ctx context.Context, pushReq *remoteReq.JPushShareReq) *remoteRes.CSRes[interface{}] {
	url := "/jpush/share"
	return remote.Post[*remoteRes.CSRes[interface{}]](ctx, url, pushReq, s)
}

// InvalidateMsgByTargetIdAndUid 设置分享消息为无效状态
// 对应 Java: CommonService.shareInvalidate(String targetId,String uid,boolean pushMsg)
func (s *infrastructureThirdService) InvalidateMsgByTargetIdAndUid(ctx context.Context, targetId, uid string) *remoteRes.CSRes[*bool] {
	url := "/inner/notice/share/records/invalid/target/" + targetId + "/" + uid
	return remote.Patch[*remoteRes.CSRes[*bool]](ctx, url, nil, s)
}

// PushCancelShareMsg 推送用户取消解绑消息
// 对应 Java: CommonService.shareInvalidate 中的推送逻辑
func (s *infrastructureThirdService) PushCancelShareMsg(ctx context.Context, targetId, uid string) *remoteRes.CSRes[*bool] {
	url := "/inner/push/cancel-share/" + targetId + "/" + uid
	return remote.Post[*remoteRes.CSRes[*bool]](ctx, url, nil, s)
}

// PushFamilyShare 推送家庭分享消息
// 对应 Java: @PostMapping("/inner/common/pushFamilyShare")
// ResponseMessage<Boolean> pushFamilyShare(@RequestBody JPushFamilyShareReq req); (遵循规则47: 必须全部转换)
func (s *infrastructureThirdService) PushFamilyShare(ctx context.Context, req *remoteReq.JPushFamilyShareReq) *remoteRes.CSRes[*bool] {
	url := "/inner/push/share/family"
	return remote.Post[*remoteRes.CSRes[*bool]](ctx, url, req, s)
}
