package remote

import (
	"context"
	"piceacorp.com/device-service/internal/service/remote/req"
	"piceacorp.com/device-service/pkg/remote"
	"piceacorp.com/device-service/pkg/remote/res"
)

type logService struct{}

func (s *logService) Name() string {
	return "log-service"
}

var LogService = &logService{}

// LoginLog 插入一条登录日志
// 对应 Java: @PostMapping(value = "/inner/log/device/login", consumes = MediaType.APPLICATION_JSON_VALUE)
// ResponseMessage<String> loginLog(@RequestBody DeviceLoginLogEntity deviceLogEntity);
func (s *logService) LoginLog(ctx context.Context, log *req.DeviceLoginLogEntity) *res.CSRes[*string] {
	url := "/inner/log/device/login"
	return remote.Post[*res.CSRes[*string]](ctx, url, log, s)
}

// Report 插入一条绑定或解绑日志
// 对应 Java: @PostMapping(value = "/inner/log/bind/report", consumes = MediaType.APPLICATION_JSON_VALUE)
// ResponseMessage<String> report(@RequestBody BindLogEntity bindLogEntity);
func (s *logService) Report(ctx context.Context, bindLog *req.BindLogEntity) *res.CSRes[*string] {
	url := "/inner/log/bind/report"
	return remote.Post[*res.CSRes[*string]](ctx, url, bindLog, s)
}

// ReportList 插入多条绑定日志
// 对应 Java: @PostMapping(value = "/inner/log/bind/report/list", consumes = MediaType.APPLICATION_JSON_VALUE)
// ResponseMessage<Void> reportList(@RequestBody List<BindLogEntity> bindLogEntities);
func (s *logService) ReportList(ctx context.Context, bindLogs []*req.BindLogEntity) *res.CSRes[interface{}] {
	url := "/inner/log/bind/report/list"
	return remote.Post[*res.CSRes[interface{}]](ctx, url, bindLogs, s)
}
