package req

// BindLogEntity 绑定或解绑日志
// 对应 Java: BindLogEntity
type BindLogEntity struct {
	// 日志id, 雪花算法生成
	Id string `json:"id"`
	// 设备id
	DeviceId string `json:"deviceId"`
	// 用户id
	UserId string `json:"userId"`
	// 绑定类型，1=绑定;2=解绑
	Type string `json:"type"`
	// 操作类型，1=自身；2=管理后台
	OperateType string `json:"operateType"`
	// 设备日志发生时间
	Time int64 `json:"time"`
	// 创建时间
	CreateTime int64 `json:"createTime"`
	// 地区
	Zone string `json:"zone"`
	// 租户id
	TenantId string `json:"tenantId"`
}
