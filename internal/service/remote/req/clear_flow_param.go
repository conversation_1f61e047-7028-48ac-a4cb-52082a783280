package req

// ClearFlowParam 清除流水相关参数
// 对应 Java: ClearFlowParam
type ClearFlowParam struct {
	// sn列表
	SnList []string `json:"snList"`
	// 用户ID列表
	UserIdList []string `json:"userIdList"`
	// 删除开始时间,yyyy-MM-dd
	BeginTime string `json:"beginTime"`
	// 删除结束时间,yyyy-MM-dd
	EndTime string `json:"endTime"`
	// 租户ID
	TenantId string `json:"tenantId"`
}

// NewClearFlowParam 创建清除流水参数
func NewClearFlowParam() *ClearFlowParam {
	return &ClearFlowParam{}
}

// SnList 设置sn列表 - 对应 Java: public ClearFlowParam snList(List<String> snList)
func (c *ClearFlowParam) SetSnList(snList []string) *ClearFlowParam {
	c.SnList = snList
	return c
}

// TenantId 设置租户ID - 对应 Java: public ClearFlowParam tenantId(String tenantId)
func (c *ClearFlowParam) SetTenantId(tenantId string) *ClearFlowParam {
	c.TenantId = tenantId
	return c
}
