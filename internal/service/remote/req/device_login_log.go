package req

// DeviceLoginLogEntity 设备登录或登出日志
type DeviceLoginLogEntity struct {
	// 日志id, 雪花算法生成
	Id string `json:"id" description:"日志id, 雪花算法生成"`
	// 设备sn
	Sn string `json:"sn" description:"设备sn"`
	// 设备mac
	Mac string `json:"mac" description:"设备mac"`
	// 设备sn对应的key
	Key string `json:"key" description:"设备sn对应的key"`
	// 产品型号代码（由 企业英文缩写.产品型号名称 组成）(功能相当于原工程类型）
	ProductModeCode string `json:"productModeCode" description:"产品型号代码（由 企业英文缩写.产品型号名称 组成）(功能相当于原工程类型）"`
	// 软件版本
	Version string `json:"version" description:"软件版本"`
	// 设备id
	DeviceId string `json:"deviceId" description:"设备id"`
	// 类型，登录或登出（1-登录，2-登出）
	Type string `json:"type" description:"类型，登录或登出"`
	// 登录结果，0为成功，1为失败。登出时忽略
	Success string `json:"success" description:"登录结果，0为成功，1为失败。登出时忽略"`
	// ip地址
	Ip string `json:"ip" description:"ip地址"`
	// 城市
	Country string `json:"country" description:"城市"`
	// 设备日志发生时间
	Time int64 `json:"time" description:"设备日志发生时间"`
	// 创建时间
	CreateTime int64 `json:"createTime" description:"创建时间"`
	// 地区
	Zone string `json:"zone" description:"地区"`
	// 租户id
	TenantId string `json:"tenantId" description:"租户id"`
}
