package req

import "time"

// FamilyInfoEntity 家庭信息实体
type FamilyInfoEntity struct {
	// 家庭ID
	Id string `json:"id"`
	// 家庭名称
	Name string `json:"name"`
	// 家庭描述
	Description string `json:"description"`
	// 创建者ID
	CreateBy string `json:"createBy"`
	// 创建时间
	CreateTime time.Time `json:"createTime"`
	// 更新者ID
	UpdateBy string `json:"updateBy"`
	// 更新时间
	UpdateTime time.Time `json:"updateTime"`
	// 租户ID
	TenantId string `json:"tenantId"`
	// 区域
	Zone string `json:"zone"`
}
