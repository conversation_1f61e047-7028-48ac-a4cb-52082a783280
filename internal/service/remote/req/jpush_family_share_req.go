package req

// JPushFamilyShareReq 家庭分享推送请求
// 对应 Java: JPushFamilyShareReq (遵循规则38: 严格遵守java对象属性名称)
type JPushFamilyShareReq struct {
	// 推送标题 - 对应 Java: private String title;
	Title string `json:"title"`
	
	// 分享类型 - 对应 Java: private Integer type;
	Type int `json:"type"`
	
	// 分享状态 - 对应 Java: private Integer status;
	Status int `json:"status"`
	
	// 目标ID - 对应 Java: private String targetId;
	TargetId string `json:"targetId"`
	
	// 家庭名称 - 对应 Java: private String familyName;
	FamilyName string `json:"familyName"`
	
	// 推送列表 - 对应 Java: private List<Map<String, String>> list;
	List []map[string]string `json:"list"`
}
