package req

import "time"

// ProductModeEntity 产品型号实体
type ProductModeEntity struct {
	// 产品型号ID
	Id string `json:"id"`
	// 产品型号代码
	Code string `json:"code"`
	// 产品型号名称
	Label string `json:"label"`
	// 产品型号描述
	Description string `json:"description"`
	// 产品图片URL
	PhotoUrl string `json:"photoUrl"`
	// 产品ID
	ProductId string `json:"productId"`
	// 创建者ID
	CreateBy string `json:"createBy"`
	// 创建时间
	CreateTime time.Time `json:"createTime"`
	// 更新者ID
	UpdateBy string `json:"updateBy"`
	// 更新时间
	UpdateTime time.Time `json:"updateTime"`
	// 租户ID
	TenantId string `json:"tenantId"`
	// 区域
	Zone string `json:"zone"`
}
