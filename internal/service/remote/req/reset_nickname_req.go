package req

// ResetNicknameReq 重置昵称请求
// 对应 Java: ResetNicknameReq
type ResetNicknameReq struct {
	// 设备SN
	Sn string `json:"sn"`
	// 租户ID
	TenantId string `json:"tenantId"`
}

// NewResetNicknameReq 创建重置昵称请求
// 对应 Java: public ResetNicknameReq(String sn, String tenantId)
func NewResetNicknameReq(sn, tenantId string) *ResetNicknameReq {
	return &ResetNicknameReq{
		Sn:       sn,
		TenantId: tenantId,
	}
}
