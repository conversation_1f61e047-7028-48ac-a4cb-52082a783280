package req

// RevokeDto 管理员撤销实体类
// 对应 Java: RevokeDto
type RevokeDto struct {
	// 被邀请人的用户id
	BeInviteId string `json:"beInviteId"`
	// 家庭id
	FamilyId string `json:"familyId"`
	// 共享状态
	InviteId string `json:"inviteId"`
}

// NewRevokeDto 创建管理员撤销请求
// 对应 Java: public RevokeDto(String beInviteId, String familyId, String inviteId)
func NewRevokeDto(beInviteId, familyId, inviteId string) *RevokeDto {
	return &RevokeDto{
		BeInviteId: beInviteId,
		FamilyId:   familyId,
		InviteId:   inviteId,
	}
}
