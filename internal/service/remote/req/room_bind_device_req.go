package req

// RoomBindDeviceReq 家庭房间绑定设备参数实体
type RoomBindDeviceReq struct {
	// 家庭Id
	FamilyId string `json:"familyId" description:"家庭Id"`
	// 房间Id
	RoomId string `json:"roomId" description:"房间Id"`
	// 设备Id
	DeviceId string `json:"deviceId" description:"设备Id"`
	// 设备昵称
	Nickname string `json:"nickname" description:"设备昵称"`
	// 设备sn
	Sn string `json:"sn" description:"设备sn"`
	// 产品信息id
	ProductId string `json:"productId" description:"产品信息id"`
}

// NewRoomBindDeviceReq 创建房间绑定设备请求
func NewRoomBindDeviceReq(familyId, roomId, deviceId, nickname, sn, productId string) *RoomBindDeviceReq {
	return &RoomBindDeviceReq{
		FamilyId:  familyId,
		RoomId:    roomId,
		DeviceId:  deviceId,
		Nickname:  nickname,
		Sn:        sn,
		ProductId: productId,
	}
}
