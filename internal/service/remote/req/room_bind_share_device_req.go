package req

// RoomBindShareDeviceReq 家庭房间绑定分享设备参数实体
type RoomBindShareDeviceReq struct {
	// 家庭Id
	FamilyId string `json:"familyId" description:"家庭Id"`
	// 设备Id
	DeviceId string `json:"deviceId" description:"设备Id"`
	// 设备昵称
	Nickname string `json:"nickname" description:"设备昵称"`
	// 设备sn
	Sn string `json:"sn" description:"设备sn"`
	// 被邀请者用户id
	UserId string `json:"userId" description:"被邀请者用户id"`
	// 设备拥有者用户id
	Owner string `json:"owner" description:"设备拥有者用户id"`
	// 产品信息id
	ProductId string `json:"productId" description:"产品信息id"`
}

// NewRoomBindShareDeviceReq 创建房间绑定分享设备请求
func NewRoomBindShareDeviceReq(userId, familyId, deviceId, nickname, sn, owner, productId string) *RoomBindShareDeviceReq {
	return &RoomBindShareDeviceReq{
		UserId:    userId,
		FamilyId:  familyId,
		DeviceId:  deviceId,
		Nickname:  nickname,
		Sn:        sn,
		Owner:     owner,
		ProductId: productId,
	}
}
