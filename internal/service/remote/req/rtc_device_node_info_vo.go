package req

// RtcDeviceNodeInfoVo RTC设备节点信息VO
// 对应 Java: RtcDeviceNodeInfoVo
type RtcDeviceNodeInfoVo struct {
	// RTC应用ID
	RtcAppId string `json:"rtcAppId"`
	// 节点ID
	NodeId string `json:"nodeId"`
	// 节点密钥
	NodeSecret string `json:"nodeSecret"`
	// 客户端密钥
	ClientKey string `json:"clientKey"`
	// 客户端密码
	ClientSecret string `json:"clientSecret"`
	// RTC产品密钥
	RtcProductKey string `json:"rtcProductKey"`
}
