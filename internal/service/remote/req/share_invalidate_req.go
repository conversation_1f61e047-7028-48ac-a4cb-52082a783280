package req

// ShareInvalidateReq 分享失效请求
// 对应 Java: ShareInvalidateReq
type ShareInvalidateReq struct {
	// 设备ID列表
	DeviceIds []string `json:"deviceIds"`
	// 用户ID
	UserId string `json:"userId"`
	// 租户ID
	TenantId string `json:"tenantId"`
}

// NewShareInvalidateReq 创建分享失效请求
func NewShareInvalidateReq(deviceIds []string, userId, tenantId string) *ShareInvalidateReq {
	return &ShareInvalidateReq{
		DeviceIds: deviceIds,
		UserId:    userId,
		TenantId:  tenantId,
	}
}
