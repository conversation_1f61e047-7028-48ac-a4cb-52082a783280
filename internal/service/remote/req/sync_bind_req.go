package req

// SyncBindReq 同步绑定信息请求
type SyncBindReq struct {
	// 租户Id
	TenantId string `json:"tenantId" description:"租户Id"`
	// 设备Id
	DeviceId string `json:"deviceId" description:"设备Id"`
	// 用户Id
	UserId string `json:"userId" description:"用户Id"`
}

// NewSyncBindReq 创建同步绑定信息请求
func NewSyncBindReq(tenantId, deviceId, userId string) *SyncBindReq {
	return &SyncBindReq{
		TenantId: tenantId,
		DeviceId: deviceId,
		UserId:   userId,
	}
}
