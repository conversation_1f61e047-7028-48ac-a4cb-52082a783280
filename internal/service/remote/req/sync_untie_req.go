package req

// SyncUntieReq 同步解绑请求
// 对应 Java: SyncUntieReq
type SyncUntieReq struct {
	// 租户ID
	TenantId string `json:"tenantId"`
	// 设备ID
	DeviceId string `json:"deviceId"`
}

// NewSyncUntieReq 创建同步解绑请求
// 对应 Java: public SyncUntieReq(String tenantId, String deviceId)
func NewSyncUntieReq(tenantId, deviceId string) *SyncUntieReq {
	return &SyncUntieReq{
		TenantId: tenantId,
		DeviceId: deviceId,
	}
}
