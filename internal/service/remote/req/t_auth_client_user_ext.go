package req

import "time"

// TAuthClientUserExt 用户扩展信息实体
type TAuthClientUserExt struct {
	// 用户ID
	Id string `json:"id"`
	// SSO ID
	SsoId string `json:"ssoId"`
	// IP地址
	Ip string `json:"ip"`
	// 国家，IP解析得出
	Country string `json:"country"`
	// 城市，IP解析得出
	City string `json:"city"`
	// 评分记录
	RatingRecord string `json:"ratingRecord"`
	// token的版本号，用于控制TOKEN过期
	TokenVersion int64 `json:"tokenVersion"`
	// 最后在线时间
	OnlineTime time.Time `json:"onlineTime"`
	// 最后离线时间
	OfflineTime time.Time `json:"offlineTime"`
	// -1 ：永久，0 ：解封； 其他具体时间值：解封时间
	DisableTime int64 `json:"disableTime"`
	// 创建时间
	CreateTime time.Time `json:"createTime"`
	// 创建者
	CreateBy string `json:"createBy"`
	// 更新时间
	UpdateTime time.Time `json:"updateTime"`
	// 更新者
	UpdateBy string `json:"updateBy"`
	// 租户ID
	TenantId string `json:"tenantId"`
	// 区域
	Zone string `json:"zone"`
	// 手机系统 (1：Android , 2：IOS，3：其他)
	PhoneSys int `json:"phoneSys"`
	// 手机品牌
	PhoneBrand string `json:"phoneBrand"`
	// 产品类型，对应旧业务的project_type(工程类型)
	ProjectType string `json:"projectType"`
	// 语言
	Lang string `json:"lang"`
	// 通知设置
	NoticeSetting string `json:"noticeSetting"`
	// 内测用户标识（1:是，0:否）
	BetaFlag int `json:"betaFlag"`
	// app密码
	AppPassword string `json:"appPassword"`
	// 当前包版本号
	VersionCode string `json:"versionCode"`
	// 当前包版本名称
	VersionName string `json:"versionName"`
	// 是否开启智能家居(1：开启，2：关闭)
	EnableSmartHome int `json:"enableSmartHome"`
}
