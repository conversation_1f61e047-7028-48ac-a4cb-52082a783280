package req

import "time"

// TAuthClientUserInfo 用户信息实体
type TAuthClientUserInfo struct {
	// 用户ID
	Id string `json:"id"`
	// 用户名
	Username string `json:"username"`
	// 加密用户名
	UsernameEnc string `json:"usernameEnc"`
	// 用户昵称
	Nickname string `json:"nickname"`
	// 头像URL
	AvatarUrl string `json:"avatarUrl"`
	// 手机号
	Phone string `json:"phone"`
	// 邮箱
	Email string `json:"email"`
	// 性别 0-未知 1-男 2-女
	Gender int `json:"gender"`
	// 生日
	Birthday *time.Time `json:"birthday"`
	// 状态 0-正常 1-禁用
	Status int `json:"status"`
	// 创建时间
	CreateTime time.Time `json:"createTime"`
	// 更新时间
	UpdateTime time.Time `json:"updateTime"`
	// 租户ID
	TenantId string `json:"tenantId"`
	// 区域
	Zone string `json:"zone"`
}
