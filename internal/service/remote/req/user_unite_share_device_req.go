package req

// UserUniteShareDeviceReq 家庭房间解绑分享设备参数实体
type UserUniteShareDeviceReq struct {
	// 设备id
	DeviceId string `json:"deviceId" description:"设备id"`
	// 邀请者
	Inviter string `json:"inviter" description:"邀请者"`
	// 被邀请者
	BeInviter string `json:"beInviter" description:"被邀请者"`
	// 是否是拥有者
	Owner bool `json:"owner" description:"是否是拥有者"`
}

// NewUserUniteShareDeviceReq 创建用户解绑分享设备请求
func NewUserUniteShareDeviceReq(deviceId, inviter, beInviter string, owner bool) *UserUniteShareDeviceReq {
	return &UserUniteShareDeviceReq{
		DeviceId:  deviceId,
		Inviter:   inviter,
		BeInviter: beInviter,
		Owner:     owner,
	}
}
