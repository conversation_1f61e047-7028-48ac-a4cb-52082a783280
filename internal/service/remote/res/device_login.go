package res

import "piceacorp.com/device-service/pkg/web/common/time"

// InnerDeviceLoginVo 设备登录查询信息实体
type InnerDeviceLoginCRes struct {
	// 产品信息
	ProductModeEntity *ProductModeEntity `json:"productModeEntity" description:"产品信息"`
	// sn信息
	SnDetailEntity *ProductSnDetailEntity `json:"snDetailEntity" description:"sn信息"`
	// 已激活的sn信息
	ProductSnDetailActiveZoneEntity *ProductSnDetailActiveZoneEntity `json:"productSnDetailActiveZoneEntity" description:"已激活的sn信息"`
}

// ProductModeEntity 产品型号（设备型号）表
type ProductModeEntity struct {
	// 主键id
	Id string `json:"id" description:"主键id"`
	// 企业id
	TenantId string `json:"tenantId" description:"企业id"`
	// 区域
	Zone string `json:"zone" description:"区域"`
	// 产品型号代码
	Code string `json:"code" description:"产品型号代码（由 企业英文缩写.产品型号名称 组成）(功能相当于原工程类型）"`
	// 产品型号名称
	Label string `json:"label" description:"产品型号名称（由 英文字母 和 数字 组成）"`
	// 产品信息id
	ProductInfoId string `json:"productInfoId" description:"产品信息id"`
	// 别名前缀
	AliasPrefix string `json:"aliasPrefix" description:"别名前缀（如：LETGO）"`
	// sn号后缀位数
	SnSuffixBit *int `json:"snSuffixBit" description:"sn号后缀位数"`
	// 标签
	Tag string `json:"tag" description:"标签"`
	// 创建时间
	CreateTime *time.Ptime `json:"createTime" description:"创建时间"`
	// 创建者
	CreateBy string `json:"createBy" description:"创建者"`
	// 更新时间
	UpdateTime *time.Ptime `json:"updateTime" description:"更新时间"`
	// 更新者
	UpdateBy string `json:"updateBy" description:"更新者"`
	// 是否删除
	DeleteFlag *int `json:"deleteFlag" description:"是否删除（0：正常；低于0的数字：已删除）"`
	// 产品名称
	ProductName string `json:"productName" description:"产品名称"`
	// 图片地址
	PhotoUrl string `json:"photoUrl" description:"图片地址"`
	// 是否开启防串货功能  0 不开启  1  开启
	PreventCrossSelling *int `json:"preventCrossSelling" description:"是否开启防串货功能  0 不开启  1  开启"`
	// 销售地区是否全球 0 全球  1  具体地区  详见 t_product_mode_sell_region
	SellRegion *int `json:"sellRegion" description:"销售地区是否全球 0 全球  1  具体地区  详见 t_product_mode_sell_region"`
	// sellRegion=1时-销售地区-具体地区
	ProductModeSellRegions []ProductModeSellRegionEntity `json:"productModeSellRegions" description:"sellRegion=1时-销售地区-具体地区"`
}

// ProductSnDetailEntity SN详情实体
type ProductSnDetailEntity struct {
	// 主键id
	Id string `json:"id" description:"主键id"`
	// sn号
	Sn string `json:"sn" description:"sn号"`
	// sn组id
	GroupId string `json:"groupId" description:"sn组id"`
	// 密钥
	Key string `json:"key" description:"密钥"`
	// 激活时间
	ActiveTime *time.Ptime `json:"activeTime" description:"激活时间"`
	// 产品型号
	ProductModeId string `json:"productModeId" description:"产品型号"`
	// 区域
	Zone string `json:"zone" description:"区域"`
	// 创建者
	CreateBy string `json:"createBy" description:"创建者 用户Id"`
	// 创建时间
	CreateTime *time.Ptime `json:"createTime" description:"创建时间"`
	// 修改者
	UpdateBy string `json:"updateBy" description:"修改者 用户Id"`
	// 修改时间
	UpdateTime *time.Ptime `json:"updateTime" description:"修改时间"`
	// 租户id
	TenantId string `json:"tenantId" description:"租户id"`
	// 删除标志
	DeleteFlag *int `json:"deleteFlag" description:"0：正常；低于0的数字：已删除"`
	// 是否禁用非销售地区 0 不禁用   1  禁用
	DisableNotSellRegion *int `json:"disableNotSellRegion" description:"是否禁用非销售地区 0 不禁用   1  禁用"`
	// sellRegion=1时-销售地区-具体地区
	ProductModeSellRegions []ProductModeSellRegionEntity `json:"productModeSellRegions" description:"sellRegion=1时-销售地区-具体地区"`
}

// ProductSnDetailActiveZoneEntity 地区SN激活表
type ProductSnDetailActiveZoneEntity struct {
	// 主键id
	Id string `json:"id" description:"主键id"`
	// sn号
	Sn string `json:"sn" description:"sn号"`
	// sn组id
	GroupId string `json:"groupId" description:"sn组id"`
	// 密钥
	Key string `json:"key" description:"密钥"`
	// 激活时间
	ActiveTime *time.Ptime `json:"activeTime" description:"激活时间"`
	// 产品型号
	ProductModeId string `json:"productModeId" description:"产品型号"`
	// 区域
	Zone string `json:"zone" description:"区域"`
	// 创建者
	CreateBy string `json:"createBy" description:"创建者 用户Id"`
	// 创建时间
	CreateTime *time.Ptime `json:"createTime" description:"创建时间"`
	// 租户id
	TenantId string `json:"tenantId" description:"租户id"`
}

// ProductModeSellRegionEntity 产品型号售卖地区表
type ProductModeSellRegionEntity struct {
	// 主键id
	Id string `json:"id" description:"主键id"`
	// 产品型号id
	ProductModeId string `json:"productModeId" description:"产品型号id"`
	// 地区id
	RegionId string `json:"regionId" description:"地区id"`
	// 地区中文名称
	RegionNameCh string `json:"regionNameCh" description:"地区中文名称"`
}
