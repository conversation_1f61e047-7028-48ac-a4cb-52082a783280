package remote

import (
	"context"
	"piceacorp.com/device-service/internal/service/remote/req"
	"piceacorp.com/device-service/pkg/remote"
	"piceacorp.com/device-service/pkg/remote/res"
)

// smartHomeService 智能家居服务远程调用
type smartHomeService struct{}

// SName 返回服务名称
func (s *smartHomeService) Name() string {
	return "smart-home-service"
}

// SmartHomeService 智能家居服务实例
var SmartHomeService = &smartHomeService{}

// BindDevice 用户家庭绑定设备
// 对应 Java: @PostMapping("/smartHome/device/bind")
// ResponseMessage<Boolean> bindDevice(@RequestBody RoomBindDeviceReq param);
func (s *smartHomeService) BindDevice(ctx context.Context, param *req.RoomBindDeviceReq) *res.CSRes[*bool] {
	url := "/smartHome/device/bind"
	return remote.Post[*res.CSRes[*bool]](ctx, url, param, s)
}

// BindShareDevice 用户家庭绑定分享设备
// 对应 Java: @PostMapping("/smartHome/device/shareBind")
// ResponseMessage<Boolean> bindShareDevice(@RequestBody RoomBindShareDeviceReq param);
func (s *smartHomeService) BindShareDevice(ctx context.Context, param *req.RoomBindShareDeviceReq) *res.CSRes[*bool] {
	url := "/smartHome/device/shareBind"
	return remote.Post[*res.CSRes[*bool]](ctx, url, param, s)
}

// UserUntieDevice 根据用户Id解绑分享设备
// 对应 Java: @PostMapping("/smartHome/device/userUntieDevice")
// ResponseMessage<Boolean> userUntieDevice(@RequestBody UserUniteShareDeviceReq param);
func (s *smartHomeService) UserUntieDevice(ctx context.Context, param *req.UserUniteShareDeviceReq) *res.CSRes[*bool] {
	url := "/smartHome/device/userUntieDevice"
	return remote.Post[*res.CSRes[*bool]](ctx, url, param, s)
}

// GetFamilyInfoByIds 根据家庭ID列表获取家庭信息列表
// 对应 Java: @PostMapping("/smartHome/family/getFamilyInfoByIds")
// ResponseMessage<List<FamilyInfoEntity>> getFamilyInfoByIds(@RequestBody List<String> familyIds);
func (s *smartHomeService) GetFamilyInfoByIds(ctx context.Context, familyIds []string) *res.CSRes[*[]*req.FamilyInfoEntity] {
	url := "/smartHome/family/getFamilyInfoByIds"
	return remote.Post[*res.CSRes[*[]*req.FamilyInfoEntity]](ctx, url, familyIds, s)
}

// Confirm 确认家庭分享
// 对应 Java: SmartHomeServiceRemote.confirm(@RequestBody ShareFamilyReplyReq req)
func (s *smartHomeService) Confirm(ctx context.Context, confirmReq *req.ShareFamilyReplyReq) *res.CSRes[*bool] {
	url := "/inner/family/confirm"
	return remote.Post[*res.CSRes[*bool]](ctx, url, confirmReq, s)
}

// Revoke 管理员撤销家庭分享
// 对应 Java: @PostMapping("/smartHome/userShare/revoke")
// ResponseMessage<Boolean> revoke(@RequestBody RevokeDto revokeDto);
func (s *smartHomeService) Revoke(ctx context.Context, revokeDto *req.RevokeDto) *res.CSRes[*bool] {
	url := "/smartHome/userShare/revoke"
	return remote.Post[*res.CSRes[*bool]](ctx, url, revokeDto, s)
}
