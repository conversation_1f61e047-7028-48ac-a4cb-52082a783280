package remote

import (
	"context"
	"fmt"

	"piceacorp.com/device-service/internal/service/remote/req"
	"piceacorp.com/device-service/pkg/remote"
	"piceacorp.com/device-service/pkg/remote/res"
)

// userService 用户服务远程调用
type userService struct{}

// Name 返回服务名称
func (s *userService) Name() string {
	return "user-center"
}

// UserService 用户服务实例
var UserService = &userService{}

// GetListByIds 根据用户ID列表获取用户信息列表
// 对应 Java: ResponseMessage<List<TAuthClientUserInfo>> getListByIds(@RequestParam String tenantId, @RequestBody List<String> userIds);
func (s *userService) GetListByIds(ctx context.Context, tenantId string, userIds []string) *res.CSRes[*[]*req.TAuthClientUserInfo] {
	url := fmt.Sprintf("/inner/user/list/ids?tenantId=%s", tenantId)
	return remote.Post[*res.CSRes[*[]*req.TAuthClientUserInfo]](ctx, url, userIds, s)
}

// GetUserExtByUserId 根据用户ID获取用户扩展信息
// 对应 Java: ResponseMessage<TAuthClientUserExt> getUserExtByUserId(@RequestParam String tenantId, @RequestParam String userId);
func (s *userService) GetUserExtByUserId(ctx context.Context, tenantId, userId string) *res.CSRes[*req.TAuthClientUserExt] {
	url := fmt.Sprintf("/inner/user/userExt/userId?tenantId=%s&userId=%s", tenantId, userId)
	return remote.Post[*res.CSRes[*req.TAuthClientUserExt]](ctx, url, nil, s)
}

// GetByUsername 通过用户名获取用户信息
// 对应 Java: ResponseMessage<TAuthClientUserInfo> getByUsername(@RequestParam String tenantId, @RequestParam String username);
func (s *userService) GetByUsername(ctx context.Context, tenantId, username string) *res.CSRes[*req.TAuthClientUserInfo] {
	url := fmt.Sprintf("/inner/user/getByUsername?tenantId=%s&username=%s", tenantId, username)
	return remote.Get[*res.CSRes[*req.TAuthClientUserInfo]](ctx, url, s)
}
