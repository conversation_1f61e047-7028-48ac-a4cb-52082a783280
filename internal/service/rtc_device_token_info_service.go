package service

import (
	"context"

	"piceacorp.com/device-service/internal/bean/req"
	"piceacorp.com/device-service/internal/bean/res"
	"piceacorp.com/device-service/internal/service/remote"
	remoteReq "piceacorp.com/device-service/internal/service/remote/req"
	"piceacorp.com/device-service/internal/utils"
	"piceacorp.com/device-service/pkg/logger"
	webRes "piceacorp.com/device-service/pkg/web/common/res"
	webErr "piceacorp.com/device-service/pkg/web/common/res/code"
)

// IRtcDeviceTokenInfoService RTC设备令牌信息服务接口
// 对应 Java: RtcDeviceTokenInfoService
type IRtcDeviceTokenInfoService interface {
	// 激活设备，获取访问授权令牌
	ActivateDevice(ctx context.Context, param *req.RtcDeviceNodeInfoReq) webRes.IBaseRes
}

var RtcDeviceTokenInfoService rtcDeviceTokenInfoService

type rtcDeviceTokenInfoService struct{}

// ActivateDevice 激活设备，获取访问授权令牌
// 对应 Java: RtcDeviceTokenInfoServiceImpl.activateDevice(RtcDeviceNodeInfoReq param)
func (s *rtcDeviceTokenInfoService) ActivateDevice(ctx context.Context, param *req.RtcDeviceNodeInfoReq) webRes.IBaseRes {
	logger.Infof("RTC设备激活开始: sn=%s, mac=%s", param.Sn, param.Mac)

	// 1. 获取设备信息 - 对应 Java: deviceInfoService.getBySnAndMac(param.getSn(), param.getMac())
	deviceInfo, err := DeviceInfoService.GetBySnAndMac(ctx, param.Sn, param.Mac)
	if err != nil {
		logger.Errorf("查询设备信息失败: sn=%s, mac=%s, error=%v", param.Sn, param.Mac, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "查询设备信息失败")
	}

	// 对应 Java: if (Objects.isNull(deviceInfo))
	if deviceInfo == nil {
		logger.Warnf("设备不存在: sn=%s, mac=%s", param.Sn, param.Mac)
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, "设备不存在")
	}

	deviceId := deviceInfo.Id
	sn := deviceInfo.Sn
	productId := deviceInfo.ProductId

	logger.Infof("获取设备信息成功: deviceId=%s, sn=%s, productId=%s", deviceId, sn, productId)

	// 2. 获取设备绑定的密钥信息 - 对应 Java: productRemote.getNodeInfoFromDevice(getNodeInfoParam)
	getNodeInfoParam := &remoteReq.NodeInfoFromDeviceParam{
		Sn:        sn,
		ProductId: productId,
	}

	nodeInfoResponse := remote.ProductService.GetNodeInfoFromDevice(ctx, getNodeInfoParam)
	logger.Infof("获取到的设备密钥信息: success=%t, msg=%s", !nodeInfoResponse.Fail(), nodeInfoResponse.Msg)

	// 对应 Java: if (!nodeInfoMessage.isSuccess() || Objects.isNull(nodeInfoMessage.getResult()))
	if nodeInfoResponse.Fail() || nodeInfoResponse.Result == nil {
		logger.Errorf("获取不到密钥信息，设备激活音视频失败: sn=%s, productId=%s, error=%s", sn, productId, nodeInfoResponse.Msg)
		return webRes.Ce(webErr.NOT_FOUND_DATA, nodeInfoResponse.Msg)
	}

	nodeInfoVo := nodeInfoResponse.Result

	// 3. 构建响应对象 - 对应 Java: RtcActivateDeviceTokenVo vo = new RtcActivateDeviceTokenVo()
	responseVo := &res.RtcActivateDeviceTokenRes{
		AppId:      nodeInfoVo.RtcAppId,
		NodeId:     nodeInfoVo.NodeId,
		NodeSecret: nodeInfoVo.NodeSecret,
	}

	// 4. 激活获取新token - 对应 Java: rtcRequestConfig.activateDeviceRequest(nodeInfoVo)
	rtcActivateDeviceVo, err := utils.RtcRequestConfigInstance.ActivateDeviceRequest(ctx, nodeInfoVo)
	if err != nil {
		logger.Errorf("调用声网API失败: deviceId=%s, error=%v", deviceId, err)
		return webRes.Ce(webErr.INNER_SERVER_ERROR, "调用声网API失败")
	}

	// 对应 Java: if (!rtcActivateDeviceVo.getSuccess() || StringUtils.isBlank(rtcActivateDeviceVo.getData()))
	if !rtcActivateDeviceVo.Success || rtcActivateDeviceVo.Data == "" {
		logger.Errorf("激活设备授权失败: deviceId=%s, success=%t, msg=%s, data=%s",
			deviceId, rtcActivateDeviceVo.Success, rtcActivateDeviceVo.Msg, rtcActivateDeviceVo.Data)
		return webRes.Ce(webErr.PARAM_VALIDATE_FAIL, rtcActivateDeviceVo.Msg)
	}

	// 5. 设置token并返回 - 对应 Java: vo.setToken(rtcActivateDeviceVo.getData())
	responseVo.Token = rtcActivateDeviceVo.Data

	logger.Infof("RTC设备激活成功: deviceId=%s, sn=%s, appId=%s, nodeId=%s",
		deviceId, sn, responseVo.AppId, responseVo.NodeId)

	// 对应 Java: return ResponseMessage.buildSuccess(vo)
	return webRes.Cb(responseVo)
}
