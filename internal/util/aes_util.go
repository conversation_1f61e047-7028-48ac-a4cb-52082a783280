package util

import (
	"crypto/aes"
	"crypto/md5"
	"encoding/base64"
	"fmt"
)

// AESUtil AES加密工具类
// 对应 Java: AESUtil
type AESUtil struct{}

// Encrypt AES加密
// 对应 Java: AESUtil.Encrypt(String plaintext, String key)
func Encrypt(plaintext, key string) (string, error) {
	// 生成密钥 - 对应 Java: 使用MD5生成16字节密钥
	keyBytes := generateKey(key)

	// 创建AES加密器
	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		return "", fmt.Errorf("创建AES加密器失败: %w", err)
	}

	// 使用ECB模式 - 对应 Java: AES/ECB/PKCS5Padding
	plainBytes := []byte(plaintext)

	// PKCS5Padding填充
	paddedData := pkcs5Padding(plainBytes, aes.BlockSize)

	// ECB模式加密
	encrypted := make([]byte, len(paddedData))
	for i := 0; i < len(paddedData); i += aes.BlockSize {
		block.Encrypt(encrypted[i:i+aes.BlockSize], paddedData[i:i+aes.BlockSize])
	}

	// Base64编码 - 对应 Java: Base64.getEncoder().encodeToString()
	return base64.StdEncoding.EncodeToString(encrypted), nil
}

// Decrypt AES解密
// 对应 Java: AESUtil.Decrypt(String ciphertext, String key)
func Decrypt(ciphertext, key string) (string, error) {
	// 生成密钥 - 对应 Java: 使用MD5生成16字节密钥
	keyBytes := generateKey(key)

	// Base64解码
	encrypted, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("Base64解码失败: %w", err)
	}

	// 创建AES解密器
	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		return "", fmt.Errorf("创建AES解密器失败: %w", err)
	}

	// ECB模式解密
	decrypted := make([]byte, len(encrypted))
	for i := 0; i < len(encrypted); i += aes.BlockSize {
		block.Decrypt(decrypted[i:i+aes.BlockSize], encrypted[i:i+aes.BlockSize])
	}

	// 去除PKCS5Padding填充
	plaintext, err := pkcs5UnPadding(decrypted)
	if err != nil {
		return "", fmt.Errorf("去除填充失败: %w", err)
	}

	return string(plaintext), nil
}

// generateKey 生成密钥
// 对应 Java: 使用MD5哈希生成16字节密钥
func generateKey(key string) []byte {
	hash := md5.Sum([]byte(key))
	return hash[:]
}

// pkcs5Padding PKCS5填充
func pkcs5Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padtext := make([]byte, padding)
	for i := range padtext {
		padtext[i] = byte(padding)
	}
	return append(data, padtext...)
}

// pkcs5UnPadding 去除PKCS5填充
func pkcs5UnPadding(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("数据为空")
	}

	padding := int(data[len(data)-1])
	if padding > len(data) || padding == 0 {
		return nil, fmt.Errorf("无效的填充")
	}

	// 验证填充
	for i := len(data) - padding; i < len(data); i++ {
		if data[i] != byte(padding) {
			return nil, fmt.Errorf("无效的填充")
		}
	}

	return data[:len(data)-padding], nil
}
