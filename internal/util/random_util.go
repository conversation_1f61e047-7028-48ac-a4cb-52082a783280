package util

import (
	"crypto/rand"
	"encoding/hex"
)

// RandomUtil 随机工具类
// 对应 Java: RandomUtil
type RandomUtil struct{}

// GetRandomBindKey 生成随机绑定密钥
// 对应 Java: RandomUtil.getRandomBindKey(BIND_KEY_LENGTH)
func GetRandomBindKey(length int) string {
	// 生成指定长度的随机字节
	bytes := make([]byte, length/2)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机数生成失败，使用当前时间戳作为种子生成
		return generateFallbackKey(length)
	}
	
	// 转换为十六进制字符串
	return hex.EncodeToString(bytes)
}

// generateFallbackKey 生成备用密钥
func generateFallbackKey(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	
	// 使用简单的伪随机算法
	seed := int64(1234567890) // 固定种子，实际应用中可以使用时间戳
	for i := 0; i < length; i++ {
		seed = (seed*1103515245 + 12345) & 0x7fffffff
		result[i] = charset[seed%int64(len(charset))]
	}
	
	return string(result)
}
