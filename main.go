package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"piceacorp.com/device-service/pkg/common/config"
	"piceacorp.com/device-service/pkg/data/cache"
	"piceacorp.com/device-service/pkg/data/mongo"
	"piceacorp.com/device-service/pkg/data/mysql"
	"piceacorp.com/device-service/pkg/message/kafka"
	"piceacorp.com/device-service/pkg/web/middleware"
	"piceacorp.com/device-service/pkg/web/validator"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/internal/controller"
	"piceacorp.com/device-service/pkg/logger"
	"piceacorp.com/device-service/pkg/nacos"
)

func main() {
	// 1. 加载配置
	cfg, err := config.LoadConfig("./configs", true)
	if err != nil {
		fmt.Printf("load config failed: %v", err)
		os.Exit(1)
	}
	release := cfg.Server.Mode == gin.ReleaseMode
	cfg.Log.Production = release
	// 2. 初始化日志
	logger.Init(cfg.Server.Name, cfg.Log)
	defer logger.Sync()

	logger.Info("Starting device-service...")

	// 4. 初始化Nacos配置
	nacosConfig := &nacos.Config{
		ServerAddr:  cfg.Nacos.Host,
		ServerPort:  cfg.Nacos.Port,
		Namespace:   cfg.Nacos.Namespace,
		DataID:      cfg.Nacos.DataID,
		Group:       cfg.Nacos.Group,
		LogMode:     cfg.Nacos.LogMode,
		ServiceName: cfg.Server.Name,
		ServicePort: cfg.Server.Port,
	}

	// 定义依赖的服务列表
	// 通过扫描包来加载
	dependentServices := []string{
		"product-service",
		"infrastructure-third",
		"log-service",
		// 添加其他依赖的服务
	}

	// 启动Nacos
	deregisterNacosService, err := nacos.Bootstrap(nacosConfig, dependentServices)
	checkErr(err)

	// 初始化redis
	closeRedis, err := cache.InitRedis()
	checkErr(err)

	//初始化mongo
	mongoClose, err := mongo.InitMongoDB()
	checkErr(err)

	// 5. 初始化数据库
	err = mysql.InitMySQL(&cfg.Database)
	checkErr(err)

	//Kafka消费者
	producerClose, err := kafka.InitProducer(cfg.Server.Name)
	checkErr(err)

	// 6. 设置Gin模式
	if release {
		gin.SetMode(gin.ReleaseMode)
	}

	// 7. 创建Gin引擎
	r := gin.New()
	gin.Default()
	r.Use(middleware.LoggingAndRecovery(), middleware.ContextKeys())

	// 8. 注册路由
	controller.RegisterRoutes(r)

	//初始化 请求绑定校验异常返回
	validator.InitValidator()

	// 9. 创建HTTP服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
		Handler: r,
	}

	// 10. 启动HTTP服务器
	go func() {
		logger.Info(fmt.Sprintf("Server is running on port %d", cfg.Server.Port))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal(fmt.Sprintf("Failed to start server: %v", err))
		}
	}()

	// 11. 优雅关闭
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// 手动注销Nacos服务，
	// 服务注册时已配置为临时的，即使不主动注销当服务停止也会自动注销，
	deregisterNacosService()
	// 关闭redis连接
	closeRedis()
	// 关闭数据库连接
	mysql.Close()
	//关闭mongo
	mongoClose()
	//关闭Kafka
	producerClose()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logger.Fatal(fmt.Sprintf("Server forced to shutdown: %v", err))
	}

	logger.Info("Server exited")
}

func checkErr(err error) {
	if err != nil {
		logger.Error("server start failed", err)
		os.Exit(1)
	}
}
