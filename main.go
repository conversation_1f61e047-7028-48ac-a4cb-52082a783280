package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"piceacorp.com/device-service/pkg/common/config"
	"piceacorp.com/device-service/pkg/data/cache"
	"piceacorp.com/device-service/pkg/data/mongo"
	"piceacorp.com/device-service/pkg/data/mysql"
	"piceacorp.com/device-service/pkg/message/kafka"
	"piceacorp.com/device-service/pkg/web/middleware"
	"piceacorp.com/device-service/pkg/web/validator"
	"sync"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/internal/controller"
	"piceacorp.com/device-service/pkg/logger"
	"piceacorp.com/device-service/pkg/nacos"
)

func main() {
	// 1. 定义当前服务
	config.SC.Server.Name = "device-service-go"
	config.SC.Server.Port = 8304
	if config.SC.Server.Mode == "" {
		config.SC.Server.Mode = os.Getenv("GIN_MODE")
		if config.SC.Server.Mode == "" {
			config.SC.Server.Mode = os.Getenv("DEPLOY_ENV")
		}
	}
	// 定义依赖的服务列表
	// 通过扫描包来加载
	dependentServices := []string{
		"product-service",
		"infrastructure-third",
		"log-service",
		// 添加其他依赖的服务
	}
	// 启动Nacos
	subscribeService, registerCurrentService, deregisterNacosService, err := nacos.Bootstrap()
	checkErr(err)

	// 2. 初始化日志
	logger.Init()
	defer logger.Sync()

	logger.Info("Starting device-service...")

	// 初始化redis
	redisClose, err := cache.InitRedis()
	checkErr(err)

	//初始化mongo
	mongoClose, err := mongo.InitMongoDB()
	checkErr(err)

	// 5. 初始化数据库
	mysqlCLose, err := mysql.InitMySQL()
	checkErr(err)

	//Kafka消费者
	kafkaProducerClose, err := kafka.InitProducer()
	checkErr(err)

	// 6. 设置Gin模式
	gin.SetMode(config.SC.Server.Mode)

	// 7. 创建Gin引擎
	r := gin.New()
	r.Use(middleware.LoggingAndRecovery(), middleware.ContextKeys())

	// 8. 注册路由
	controller.RegisterRoutes(r)

	//初始化 请求绑定校验异常返回
	validator.InitValidator()

	// 9. 创建HTTP服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", config.SC.Server.Port),
		Handler: r,
	}

	// 订阅nacos服务
	subscribeService(dependentServices)
	// 10. 启动HTTP服务器
	go func() {
		logger.Info(fmt.Sprintf("Server is running on port %d", config.SC.Server.Port))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("failed to start server: %v", err)
		}
	}()
	//注册nacos服务
	registerCurrentService()
	// 11. 优雅关闭
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info("shutting down server...")
	// 手动注销Nacos服务，
	// 服务注册时已配置为临时的，即使不主动注销当服务停止也会自动注销，
	deregisterNacosService()
	// 停止http监听
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		logger.Fatal(fmt.Sprintf("server forced to shutdown: %v", err))
	}

	// 关闭redis连接、数据库连接、mongo、Kafka
	wg := closeMiddleware(redisClose, mysqlCLose, mongoClose, kafkaProducerClose)
	//等待关闭中间件
	wg.Wait()
	logger.Info("server exited")
}

func checkErr(err error) {
	if err != nil {
		fmt.Println("server start failed", err)
		os.Exit(1)
	}
}

func closeMiddleware(closeFns ...func()) *sync.WaitGroup {
	var wg sync.WaitGroup
	for _, fn := range closeFns {
		wg.Add(1)
		go func() {
			defer wg.Done()
			fn()
		}()
	}
	return &wg
}
