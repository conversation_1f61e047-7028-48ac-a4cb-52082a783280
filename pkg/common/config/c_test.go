package config

import (
	"strings"
	"testing"

	"github.com/nacos-group/nacos-sdk-go/v2/common/logger"
	"github.com/spf13/viper"
)

func TestEnvJson(t *testing.T) {
	var config Config
	jsonEnv := "{\"nacos.TestVal\":\"nacos-TestVal\",\"nacos.port\":\"8848\",\"nacos.namespace\":\"aiot\",\"nacos\":{\"host\":\"jsondata\"},\"sentinel.host\":\"sentinel-dashboard\",\"sentinel.port\":\"8316\",\"tidb.host\":\"************\",\"tidb.port\":\"3306\",\"tidb.user\":\"aiot\",\"tidb.passwd\":\"p+6oVA3hZfhapw6z\",\"kafka.hostlist\":\"************:9092,************:9092,************:9092\",\"zookeeper.hostlist\":\"************:2181,************:2181,************:2181\",\"redis.host1\":\"************:7000\",\"redis.host2\":\"************:7001\",\"redis.host3\":\"************:7002\",\"redis.host4\":\"************:7003\",\"redis.host5\":\"************:7004\",\"redis.host6\":\"************:7005\",\"redis.passwd\":\"iJz5LNKn4BV8j7DO\",\"mongodb.hostlist\":\"************:27017,************:27017,************:27017\",\"mongodb.user\":\"3i\",\"mongodb.passwd\":\"MXB0gfvHamR45YPLbdhkr6DAZUltO1V3\",\"mqtt.host\":\"testk8s-mqtt.3irobotix.net\",\"mqtt.port\":\"1883\",\"tdengine.host\":\"test-tdengine.3irobotix.net\",\"tdengine.port\":\"6030\",\"tdengine.user\":\"aiot\",\"tdengine.passwd\":\"jf@WaV@9JY\",\"influxdb.host\":\"************\",\"influxdb.port\":\"8086\",\"influxdb.user\":\"aiot\",\"influxdb.passwd\":\"tTsKSiacXy1a\",\"es.uris\":\"es-cluster:9200\",\"es.username\":\"elastic\",\"es.password\":\"elatic2021\",\"emq.secret\":\"o7T2sFwdgniDDw\",\"oss.endpoint1\":\"oss-cn-shenzhen.aliyuncs.com\",\"oss.accessKeyId\":\"LTAI5tK1V8UNKWQ9WoNxzrgK\",\"oss.accessKeySecret\":\"******************************\",\"oss.bucketName\":\"cvss\",\"alicloudHost\":\"api.link.aliyun.com\"}"
	if jsonEnv != "" {
		// viper.AutomaticEnv()
		viper.AddConfigPath("../../configs")
		viper.SetConfigName("config")
		viper.SetConfigType("yaml")
		viper.ReadInConfig()

		// if err := viper.ReadInConfig(); err != nil {

		// }
		logger.Info("viper config: ", viper.AllSettings())

		// if err := viper.Unmarshal(&config); err != nil {

		// }
		logger.Info("合并JSON后的配置: ", config)

		// viper.SetConfigType("json")
		viper.MergeConfig(strings.NewReader(jsonEnv))

		logger.Info("viper config: ", viper.AllSettings())
		if err := viper.Unmarshal(&config); err != nil {

		}

		logger.Info("合并JSON后的配置: ", viper.AllSettings())
		logger.Info("合并JSON后的配置: ", config)
	}

	// viper.Read(strings.NewReader(jsonEnv))
}
