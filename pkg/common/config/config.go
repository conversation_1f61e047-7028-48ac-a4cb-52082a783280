package config

import (
	"encoding/json"
	"fmt"
	"github.com/spf13/viper"
	"os"
)

// Config 应用配置
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Log      LogConfig      `mapstructure:"log"`
	Nacos    NacosConfig    `mapstructure:"nacos"`
	Redis    RedisConfig    `mapstructure:"redis"`
	Database DatabaseConfig `mapstructure:"database"`
	Kafka    KafkaConfig    `mapstructure:"kafka"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port uint64 `mapstructure:"port"`
	Mode string `mapstructure:"mode"`
	Name string `mapstructure:"name"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host               string   `mapstructure:"host"`
	Port               int      `mapstructure:"port"`
	Username           string   `mapstructure:"username"`
	Password           string   `mapstructure:"password"`
	DatabaseName       string   `mapstructure:"databaseName"`
	MaxIdleConns       int      `mapstructure:"maxIdleConns"`
	MaxOpenConns       int      `mapstructure:"maxOpenConns"`
	ConnMaxLifetime    int      `mapstructure:"connMaxLifetime"`    // 单位：秒
	LogLevel           string   `mapstructure:"logLevel"`           // silent, error, warn, info
	SlowThreshold      int      `mapstructure:"slowThreshold"`      // 慢SQL阈值，单位：毫秒
	IgnoreTenantTables []string `mapstructure:"ignoreTenantTables"` // 忽略多租户的表
}

// RedisConfig Redis配置
type RedisConfig struct {
	Cluster    bool     `mapstructure:"cluster"`
	Addresses  []string `mapstructure:"addresses"`
	Password   string   `mapstructure:"password"`
	MaxRetries int      `mapstructure:"maxRetries"`
	PoolSize   int      `mapstructure:"poolSize"`
}

type MongoDBConfig struct {
	Uri string `mapstructure:"uri"`
}

// KafkaConfig Kafka配置
type KafkaConfig struct {
	Brokers string `mapstructure:"brokers"`
}

// NacosConfig Nacos配置
type NacosConfig struct {
	Host      string `mapstructure:"host"`
	Port      int    `mapstructure:"port"`
	Namespace string `mapstructure:"namespace"`
	DataID    string `mapstructure:"dataId"`
	Group     string `mapstructure:"group"`
	LogMode   string `mapstructure:"LogMode"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `mapstructure:"level"`
	Production bool   `mapstructure:"production"`
	Path       string `mapstructure:"path"`
	MaxSize    int    `mapstructure:"maxSize"`
	MaxBackups int    `mapstructure:"maxBackups"`
	MaxAge     int    `mapstructure:"maxAge"`
	Compress   bool   `mapstructure:"compress"`
}

var ServiceConfig *Config

// Viper uses the following precedence order. Each item takes precedence over the item below it:
// explicit call to Set
// flag
// env
// config
// key/value store
// default
func LoadConfig(path string, printConfig bool) (*Config, error) {
	// 兼容之前java服务变量读取
	jsonEnv := os.Getenv("SPRING_APPLICATION_JSON")
	fmt.Println("jsonEnv: ", jsonEnv)
	appEnvs := make(map[string]string)
	json.Unmarshal([]byte(jsonEnv), &appEnvs)
	for k, v := range appEnvs {
		os.Setenv(k, v)
	}
	fmt.Println(appEnvs)
	// 读取环境变量
	viper.AutomaticEnv()
	viper.AddConfigPath(path)
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	// 读取本地config
	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	//if jsonEnv != "" {
	//	viper.MergeConfig(strings.NewReader(jsonEnv))
	//}

	config := &Config{}
	if err := viper.Unmarshal(config); err != nil {
		return nil, err
	}

	// handle nacos default data
	// the namespaceId of Nacos.When namespace is public, fill in the blank string here.
	if config.Nacos.Namespace == "public" {
		config.Nacos.Namespace = ""
	}
	if config.Nacos.Group == "" {
		config.Nacos.Group = "DEFAULT_GROUP"
	}
	if config.Nacos.DataID == "" {
		config.Nacos.DataID = config.Server.Name
	}
	//if config.Nacos.LogMode == "" {
	//	config.Nacos.LogMode = config.Server.Mode
	//}
	data, _ := json.MarshalIndent(config, "", "  ")
	if printConfig {
		fmt.Print(string(data))
	}
	ServiceConfig = config
	return config, nil
}
