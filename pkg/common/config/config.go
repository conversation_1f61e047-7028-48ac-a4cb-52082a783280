package config

import (
	"encoding/json"
	"fmt"
	"github.com/spf13/viper"
	"os"
)

// Config 应用配置
type config struct {
	Server   serverConfig   `mapstructure:"server"`
	Log      logConfig      `mapstructure:"log"`
	Nacos    nacosConfig    `mapstructure:"nacos"`
	Redis    redisConfig    `mapstructure:"redis"`
	MongoDB  mongoDBConfig  `mapstructure:"mongodb"`
	Database databaseConfig `mapstructure:"database"`
	Kafka    kafkaConfig    `mapstructure:"kafka"`
}

// ServerConfig 服务器配置
type serverConfig struct {
	Port uint64 `mapstructure:"port"`
	Mode string `mapstructure:"mode"`
	Name string `mapstructure:"name"`
	Pcf  bool   `mapstructure:"pcf"` // 是否输出配置信息,默认不输出。先通过nacos配置为true。然后再修改nacos即会输出
}

// DatabaseConfig 数据库配置
type databaseConfig struct {
	Host               string   `mapstructure:"host"`
	Port               int      `mapstructure:"port"`
	Username           string   `mapstructure:"username"`
	Password           string   `mapstructure:"password"`
	DatabaseName       string   `mapstructure:"databaseName"`
	MaxIdleConns       int      `mapstructure:"maxIdleConns"`
	MaxOpenConns       int      `mapstructure:"maxOpenConns"`
	ConnMaxLifetime    int      `mapstructure:"connMaxLifetime"`    // 单位：秒
	LogLevel           string   `mapstructure:"logLevel"`           // silent, error, warn, info
	SlowThreshold      int      `mapstructure:"slowThreshold"`      // 慢SQL阈值，单位：毫秒
	IgnoreTenantTables []string `mapstructure:"ignoreTenantTables"` // 忽略多租户的表
}

// RedisConfig Redis配置
type redisConfig struct {
	Cluster    bool     `mapstructure:"cluster"`
	Addresses  []string `mapstructure:"addresses"`
	Password   string   `mapstructure:"password"`
	MaxRetries int      `mapstructure:"maxRetries"`
	PoolSize   int      `mapstructure:"poolSize"`
}

type mongoDBConfig struct {
	Uri string `mapstructure:"uri"`
}

// KafkaConfig Kafka配置
type kafkaConfig struct {
	Brokers string `mapstructure:"brokers"`
}

// NacosConfig Nacos配置
type nacosConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	Namespace    string `mapstructure:"namespace"`
	DataId       string `mapstructure:"dataId"`
	CommonDataId string `mapstructure:"commonDataId"` //公共配置文件
	Group        string `mapstructure:"group"`
	LogMode      string `mapstructure:"logMode"`
	LogDir       string `mapstructure:"logDir"`   // 日志目录
	CacheDir     string `mapstructure:"cacheDir"` // 缓存目录
}

// LogConfig 日志配置
type logConfig struct {
	Level      string `mapstructure:"level"`
	Production bool   `mapstructure:"production"`
	Path       string `mapstructure:"path"`
	MaxSize    int    `mapstructure:"maxSize"`
	MaxBackups int    `mapstructure:"maxBackups"`
	MaxAge     int    `mapstructure:"maxAge"`
	Compress   bool   `mapstructure:"compress"`
}

var SC *config

// Viper uses the following precedence order. Each item takes precedence over the item below it:
// explicit call to Set
// flag
// env
// config
// key/value store
// default
func init() {
	// 兼容之前java服务变量读取
	jsonEnv := os.Getenv("SPRING_APPLICATION_JSON")
	fmt.Println("jsonEnv: ", jsonEnv)
	appEnvs := make(map[string]string)
	json.Unmarshal([]byte(jsonEnv), &appEnvs)
	for k, v := range appEnvs {
		// 最高优先级
		viper.Set(k, v)
	}
	viper.AddConfigPath("./configs")
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	// 读取配置文件时，允许从环境变量加载
	viper.AutomaticEnv()
	// 读取本地config
	if err := viper.ReadInConfig(); err != nil {
		fmt.Printf("WARNING: viper read config failed: %v", err)
		//os.Exit(1)
	}

	//if jsonEnv != "" {
	//	viper.MergeConfig(strings.NewReader(jsonEnv))
	//}

	c := &config{}
	if err := viper.Unmarshal(c); err != nil {
		fmt.Printf("viper unmarshal config failed: %v", err)
		os.Exit(1)
	}
	SC = c
}
