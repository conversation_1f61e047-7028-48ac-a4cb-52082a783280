package cache

import (
	"context"
	"piceacorp.com/device-service/pkg/common/config"
	"time"

	"github.com/redis/go-redis/v9"
	"piceacorp.com/device-service/pkg/logger"
)

var Rdb *redis.ClusterClient

func InitRedis() (func(), error) {
	logger.Info("start init redis client")
	Rdb = redis.NewClusterClient(&redis.ClusterOptions{
		Addrs:        config.SC.Redis.Addresses,
		Password:     config.SC.Redis.Password,
		MaxRetries:   config.SC.Redis.MaxRetries,
		PoolSize:     config.SC.Redis.PoolSize,
		MinIdleConns: 2,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolTimeout:  4 * time.Second,
	})
	res, err := Rdb.ClusterNodes(context.Background()).Result()
	if err != nil {
		logger.Error("redis init fail", err)
		return nil, err
	}
	logger.Info("redis cluster nodes result: \n", res)
	return func() {
		Rdb.Close()
		logger.Info("redis connection closed")
	}, err
}
