package cache

import (
	"context"
	"piceacorp.com/device-service/pkg/common/config"
	"time"

	"github.com/redis/go-redis/v9"
	"piceacorp.com/device-service/pkg/common"
	"piceacorp.com/device-service/pkg/logger"
	"piceacorp.com/device-service/pkg/nacos"
)

var Rdb *redis.ClusterClient

func InitRedis() (func(), error) {
	logger.Info("start init redis client")
	redisConfig := &config.RedisConfig{}
	nacos.UnmarshalConfigKey(common.REDIS, redisConfig)
	Rdb = redis.NewClusterClient(&redis.ClusterOptions{
		Addrs:        redisConfig.Addresses,
		Password:     redisConfig.Password,
		MaxRetries:   redisConfig.MaxRetries,
		PoolSize:     redisConfig.PoolSize,
		MinIdleConns: 2,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolTimeout:  4 * time.Second,
	})
	res, err := Rdb.ClusterNodes(context.Background()).Result()
	if err != nil {
		logger.Error("redis init fail", err)
		return nil, err
	}
	logger.Info("redis cluster nodes result: \n", res)
	return func() { Rdb.Close() }, err
}
