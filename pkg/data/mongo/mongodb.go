package mongo

import (
	"context"
	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
	"go.mongodb.org/mongo-driver/v2/x/mongo/driver/connstring"
	"piceacorp.com/device-service/pkg/common/config"
	"piceacorp.com/device-service/pkg/logger"
)

var DB *mongo.Database

func InitMongoDB() (func(), error) {
	var uri = config.SC.MongoDB.Uri
	connString, err := connstring.ParseAndValidate(uri)
	if err != nil {
		return nil, err
	}
	serverAPI := options.ServerAPI(options.ServerAPIVersion1)
	mongoLogger := options.Logger()
	mongoLogger.SetSink(&mongoLoggerWrapper{})
	mongoLogger.SetComponentLevel(options.LogComponentCommand, options.LogLevelDebug)

	opts := options.Client().ApplyURI(uri).SetServerAPIOptions(serverAPI).SetLoggerOptions(mongoLogger)
	// Create a new client and connect to the server
	client, err := mongo.Connect(opts)
	if err != nil {
		return nil, err
	}
	// Send a ping to confirm a successful connection
	var result bson.M
	DB = client.Database(connString.Database)
	//client.Database("admin").(context.Background(), bson.D{{"ping", 1}})
	if err := DB.RunCommand(context.TODO(), bson.D{{"ping", 1}}).Decode(&result); err != nil {
		panic(err)
	}
	logger.Info("Pinged your deployment. You successfully connected to MongoDB!")

	return func() {
		_ = client.Disconnect(context.TODO())
		logger.Info(" mongodb connection closed")
	}, nil
}

type mongoLoggerWrapper struct{}

func (m *mongoLoggerWrapper) Info(level int, msg string, keysAndValues ...interface{}) {
	logger.Info(msg, keysAndValues)
}

func (m *mongoLoggerWrapper) Error(err error, msg string, keysAndValues ...interface{}) {
	logger.Error(msg, keysAndValues, err)
}
