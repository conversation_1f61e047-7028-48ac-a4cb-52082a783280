package mongo

import (
	"context"
	"fmt"
	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
	"go.mongodb.org/mongo-driver/v2/x/mongo/driver/connstring"
	"piceacorp.com/device-service/pkg/common"
	"piceacorp.com/device-service/pkg/common/config"
	"piceacorp.com/device-service/pkg/nacos"
)

var DB *mongo.Database

func InitMongoDB() (func(), error) {
	mongoConfig := &config.MongoDBConfig{}
	nacos.UnmarshalConfigKey(common.MONGO, mongoConfig)
	var uri = mongoConfig.Uri
	connString, err := connstring.ParseAndValidate(uri)
	if err != nil {
		return nil, err
	}
	serverAPI := options.ServerAPI(options.ServerAPIVersion1)
	logger := options.Logger()
	logger.SetComponentLevel(options.LogComponentCommand, options.LogLevelDebug)

	opts := options.Client().ApplyURI(uri).SetServerAPIOptions(serverAPI).SetLoggerOptions(logger)
	// Create a new client and connect to the server
	client, err := mongo.Connect(opts)
	if err != nil {
		return nil, err
	}
	// Send a ping to confirm a successful connection
	var result bson.M
	DB = client.Database(connString.Database)
	//client.Database("admin").(context.Background(), bson.D{{"ping", 1}})
	if err := DB.RunCommand(context.TODO(), bson.D{{"ping", 1}}).Decode(&result); err != nil {
		panic(err)
	}
	fmt.Println("Pinged your deployment. You successfully connected to MongoDB!")

	return func() {
		_ = client.Disconnect(context.TODO())
	}, nil
}
