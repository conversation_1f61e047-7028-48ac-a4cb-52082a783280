package base

import (
	"context"
	"errors"
	"gorm.io/gorm"
	"piceacorp.com/device-service/pkg/data/mysql"
	"piceacorp.com/device-service/pkg/logger"
)

type IRepository[T any] interface {
	Create(ctx context.Context, obj *T) error
	Update(ctx context.Context, obj *T) error
	Del(ctx context.Context, id string) error
	Get(ctx context.Context, id string) (*T, error)
	List(ctx context.Context, conds ...interface{}) (*[]T, error)
}

type BaseRepository[T any] struct {
	Dbh *mysql.DBHolder
}

// NewRepository 创建仓库
func NewRepository[T any](db *gorm.DB) IRepository[T] {
	if db == nil {
		db = mysql.DBH.DB
	}
	return &BaseRepository[T]{Dbh: &mysql.DBHolder{DB: db}}
}

// Create 创建
func (r *BaseRepository[T]) Create(ctx context.Context, obj *T) error {
	result := r.Dbh.DB.WithContext(ctx).Create(obj)
	if result.Error != nil {
		logger.Error("create fail", result.Error)
		return result.Error
	}
	return nil
}

// Update 更新
func (r *BaseRepository[T]) Update(ctx context.Context, obj *T) error {
	result := r.Dbh.DB.WithContext(ctx).Save(obj)
	r.Dbh.DB.WithContext(ctx)
	if result.Error != nil {
		logger.Error("update fail", result.Error)
		return result.Error
	}
	return nil
}

// Del 根据ID删除
func (r *BaseRepository[T]) Del(ctx context.Context, id string) error {
	var obj T
	result := r.Dbh.DB.WithContext(ctx).Delete(&obj, "id = ?", id)
	if result.Error != nil {
		logger.Error("del fail", result.Error)
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("设备不存在")
	}
	return nil
}

// Get 根据ID查找
func (r *BaseRepository[T]) Get(ctx context.Context, id string) (*T, error) {
	var obj T
	result := r.Dbh.DB.WithContext(ctx).Where("id = ?", id).First(&obj)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		logger.Error("find fail", result.Error)
		return nil, result.Error
	}
	return &obj, nil
}

// List查找集合
func (r *BaseRepository[T]) List(ctx context.Context, conds ...interface{}) (*[]T, error) {
	var objs []T
	result := r.Dbh.DB.WithContext(ctx).Find(&objs, conds...)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		logger.Error("list fail", result.Error)
		return nil, result.Error
	}
	return &objs, nil
}
