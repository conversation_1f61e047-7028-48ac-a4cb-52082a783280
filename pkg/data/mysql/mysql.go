package mysql

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm/utils"
	"piceacorp.com/device-service/pkg/common"
	"piceacorp.com/device-service/pkg/common/config"
	"piceacorp.com/device-service/pkg/nacos"
	webcontext "piceacorp.com/device-service/pkg/web/common/context"
	"reflect"
	"strings"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	sqlLogger "gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
	"piceacorp.com/device-service/pkg/logger"
)

var (
	// 全局数据库连接持有
	DBH *DBHolder
)

type DBHolder struct {
	DB *gorm.DB
}

func init() {
	DBH = &DBHolder{}
}

// Config 数据库配置
type Config struct {
	Driver          string
	Host            string
	Port            int
	Username        string
	Password        string
	Database        string
	MaxIdleConns    int
	MaxOpenConns    int
	ConnMaxLifetime int // 秒
	LogLevel        string
	SlowThreshold   int // 毫秒
}

// gormLogAdapter GORM日志适配器
type gormLogAdapter struct{}

// Printf 实现gorm logger.Writer接口
func (g *gormLogAdapter) Printf(format string, args ...interface{}) {
	logger.Debugf(format, args...)
}

// InitMySQL 初始化MySQL连接
func InitMySQL(cfg *config.DatabaseConfig) error {
	nacos.UnmarshalConfigKey(common.DATABASE, cfg)
	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.Username,
		cfg.Password,
		cfg.Host,
		cfg.Port,
		cfg.DatabaseName,
	)

	// 设置日志级别
	var logLevel sqlLogger.LogLevel
	switch cfg.LogLevel {
	case "silent":
		logLevel = sqlLogger.Silent
	case "error":
		logLevel = sqlLogger.Error
	case "warn":
		logLevel = sqlLogger.Warn
	case "info":
		logLevel = sqlLogger.Info
	default:
		logLevel = sqlLogger.Silent
	}

	// 配置GORM
	gormConfig := &gorm.Config{
		Logger: sqlLogger.New(
			&gormLogAdapter{}, // 自定义日志适配器
			sqlLogger.Config{
				SlowThreshold:             time.Duration(cfg.SlowThreshold) * time.Millisecond,
				LogLevel:                  logLevel,
				IgnoreRecordNotFoundError: true,
				Colorful:                  false,
			},
		),
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   "t_",
			SingularTable: true, // 使用单数表名
		},
		DisableForeignKeyConstraintWhenMigrating: true, // 禁用外键约束
	}

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), gormConfig)
	if err != nil {
		logger.Error("connect data fail", err)
		return err
	}

	// 获取底层SQL DB
	sqlDB, err := db.DB()
	if err != nil {
		logger.Error("failed to get the underlying data connection", err)
		return err
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime) * time.Second)

	//注册回调增强
	queryCallback := db.Callback().Query()
	updateCallBack := db.Callback().Update()
	deleteCallback := db.Callback().Delete()
	rawCallback := db.Callback().Raw()
	rowCallback := db.Callback().Row()
	//打印结果回调
	if cfg.LogLevel == gin.DebugMode {
		printRes := func(db *gorm.DB) {
			sql, vars := db.Statement.SQL.String(), db.Statement.Vars
			count := db.RowsAffected
			var res any
			if count > 0 {
				res = db.Statement.Dest
			}
			// 数组处理
			if isArrayOrSlice(res) {
				logger.Debugf("%s\nSQL: %s\nCount:%d\nRes: \n%+v", utils.FileWithLineNum(), db.Dialector.Explain(sql, vars...), count, iterateAnySlice(res))
				return
			}
			logger.Debugf("%s\nSQL: %s\nCount:%d\nRes: %+v", utils.FileWithLineNum(), db.Dialector.Explain(sql, vars...), count, res)
		}
		_ = queryCallback.Register("print_res", printRes)
		_ = updateCallBack.Register("print_res", printRes)
		_ = deleteCallback.Register("print_res", printRes)
		_ = rawCallback.Register("print_res", printRes)
		_ = rowCallback.Register("print_res", printRes)
	}
	//多租户增强
	// 装配不启用多租户的表
	var ignoreTenantTablesMap = make(map[string]int)
	for i, v := range cfg.IgnoreTenantTables {
		ignoreTenantTablesMap[v] = i
	}
	mt := func(db *gorm.DB) {
		it, exist := db.InstanceGet(common.IGNORE_TID)
		if exist {
			//手动忽略多租户
			if v, ok := it.(bool); ok && v {
				return
			}
		}
		// 配置忽略多租户
		_, ok := ignoreTenantTablesMap[db.Statement.Table]
		if ok {
			return
		}
		tid := db.Statement.Context.Value(webcontext.TENANT_ID)
		if tid == nil || tid == "" {
			logger.Error("TID not found")
		}
		db.Where("tenant_id = ?", tid)
	}
	_ = queryCallback.Before("gorm:query").Register("multi_tenant", mt)
	_ = updateCallBack.Before("gorm:update").Register("multi_tenant", mt)
	_ = deleteCallback.Before("gorm:delete").Register("multi_tenant", mt)
	_ = rawCallback.Before("gorm:raw").Register("multi_tenant", mt)
	_ = rowCallback.Before("gorm:row").Register("multi_tenant", mt)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		logger.Error("data connection test failed", err)
		return err
	}

	logger.Info("data connection successful")
	DBH.DB = db
	return nil
}

func IgnoreTenant() {
	DBH.DB.InstanceSet(common.IGNORE_TID, true)
}

// Close 关闭数据库连接
func Close() error {
	if DBH.DB == nil {
		return nil
	}

	sqlDB, err := DBH.DB.DB()
	if err != nil {
		logger.Error("获取底层数据库连接失败", err)
		return err
	}

	err = sqlDB.Close()
	if err != nil {
		logger.Error("failed to close database connection", err)
		return err
	}

	logger.Info("database connection closed")
	return nil
}

func isArrayOrSlice(v any) bool {
	if v == nil {
		return false
	}
	val := reflect.TypeOf(v)
	// 如果是指针，解引用直到非指针
	for val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	// 如果解引用后是接口，获取接口的具体值
	if val.Kind() == reflect.Interface {
		val = reflect.TypeOf(val)
	}

	// 判断最终类型是否为数组或切片
	return val.Kind() == reflect.Slice || val.Kind() == reflect.Array
}

func iterateAnySlice(arr any) string {
	val := reflect.ValueOf(arr)
	// 如果是指针，解引用直到非指针
	for val.Kind() == reflect.Ptr {
		val = val.Elem()
	}
	builder := strings.Builder{}
	// 遍历切片
	for i := 0; i < val.Len(); i++ {
		elem := val.Index(i)

		// 如果是指针，取值
		if elem.Kind() == reflect.Ptr {
			elem = elem.Elem()
		}
		builder.WriteString(fmt.Sprintf("%d - %+v\n", i, elem))
	}

	return builder.String()
}
