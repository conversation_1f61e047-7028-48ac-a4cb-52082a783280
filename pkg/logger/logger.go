package logger

import (
	"github.com/gin-gonic/gin"
	"io"
	"os"
	"path/filepath"
	"piceacorp.com/device-service/pkg/common/config"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

var sugar *zap.SugaredLogger

// Init 初始化日志
func Init() {
	serviceName := config.SC.Server.Name
	// handle log default config
	//#log:
	//#  level: debug
	//#  path: ./logs
	//#  maxSize: 100
	//#  maxBackups: 7
	//#  maxAge: 30
	//#  compress: true
	logCfg := config.SC.Log
	if logCfg.Level == "" {
		logCfg.Level = "debug"
	}
	if logCfg.Path == "" {
		logCfg.Path = "./logs"
	}
	if logCfg.MaxAge == 0 {
		logCfg.MaxAge = 3
	}
	// 设置日志级别
	var logLevel zapcore.Level
	switch logCfg.Level {
	case "debug":
		logLevel = zapcore.DebugLevel
	case "info", "release":
		logLevel = zapcore.InfoLevel
	case "warn":
		logLevel = zapcore.WarnLevel
	case "error":
		logLevel = zapcore.ErrorLevel
	default:
		logLevel = zapcore.InfoLevel
	}

	// 创建Core
	var core zapcore.Core
	if logCfg.Production {
		// 日志轮转
		lumberJackLogger := &lumberjack.Logger{
			Filename:   filepath.Join(logCfg.Path, "info.log"),
			MaxSize:    logCfg.MaxSize, // MB
			MaxBackups: logCfg.MaxBackups,
			MaxAge:     logCfg.MaxAge, // days
			Compress:   true,
		}

		// 编码器配置
		encoderConfig := zapcore.EncoderConfig{
			TimeKey:        "time",
			LevelKey:       "level",
			NameKey:        "logger",
			CallerKey:      "caller",
			MessageKey:     "msg",
			StacktraceKey:  "stacktrace",
			LineEnding:     zapcore.DefaultLineEnding,
			EncodeLevel:    zapcore.LowercaseLevelEncoder,
			EncodeTime:     zapcore.ISO8601TimeEncoder,
			EncodeDuration: zapcore.SecondsDurationEncoder,
			EncodeCaller:   zapcore.ShortCallerEncoder,
		}

		core = zapcore.NewCore(
			zapcore.NewJSONEncoder(encoderConfig),
			zapcore.AddSync(lumberJackLogger),
			zap.NewAtomicLevelAt(logLevel),
		)
	} else {
		syncWriter := zapcore.AddSync(os.Stdout)
		if config.SC.Server.Mode == gin.TestMode {
			// 日志轮转
			lumberJackLogger := &lumberjack.Logger{
				Filename:   filepath.Join(logCfg.Path, "info.log"),
				MaxSize:    logCfg.MaxSize, // MB
				MaxBackups: logCfg.MaxBackups,
				MaxAge:     logCfg.MaxAge, // days
				Compress:   true,
			}
			// 创建写入器：同时写入到文件和终端
			syncWriter = zapcore.AddSync(io.MultiWriter(os.Stdout, lumberJackLogger))
		}
		// 开发环境: 日志输出到控制台
		encoderConfig := zap.NewDevelopmentEncoderConfig()
		encoderConfig.EncodeTime = zapcore.TimeEncoderOfLayout(time.DateTime)
		encoderConfig.ConsoleSeparator = " "
		core = zapcore.NewCore(
			zapcore.NewConsoleEncoder(encoderConfig),
			syncWriter,
			zap.NewAtomicLevelAt(logLevel),
		)
	}

	// 创建Logger
	log := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))
	if logCfg.Production {
		log = log.Named(serviceName)
	}
	zap.L().With()
	sugar = log.Sugar()
}

// TODO: 补充traceId 跟踪业务流
// Debug 输出debug级别日志
//
//	func Debugw(msg string, args ...interface{}) {
//		sugar.Debugw(msg, args...)
//	}
func Debug(args ...interface{}) {
	sugar.Debug(args...)
}
func Debugf(template string, args ...interface{}) {
	sugar.Debugf(template, args...)
}

// Info 输出info级别日志
//func Infow(msg string, args ...interface{}) {
//	sugar.Infow(msg, args...)
//}

//func Info(args ...interface{}) {
//	sugar.Info(args...)
//}

func Info(args ...interface{}) {
	sugar.Info(args...)
}
func Infof(template string, args ...interface{}) {
	sugar.Infof(template, args...)
}

// Warn 输出warn级别日志
//
//	func Warnw(msg string, args ...interface{}) {
//		sugar.Warnw(msg, args...)
//	}
func Warn(args ...interface{}) {
	sugar.Warn(args...)
}
func Warnf(template string, args ...interface{}) {
	sugar.Warnf(template, args...)
}

// Error 输出error级别日志
//
//	func Errorw(msg string, args ...interface{}) {
//		sugar.Errorw(msg, args...)
//	}
func Error(args ...interface{}) {
	sugar.Error(args...)
}
func Errorf(template string, args ...interface{}) {
	sugar.Errorf(template, args...)
}

// Fatal 输出fatal级别日志
//
//	func Fatalw(msg string, args ...interface{}) {
//		sugar.Fatalw(msg, args...)
//	}
func Fatal(args ...interface{}) {
	sugar.Fatal(args...)
}
func Fatalf(template string, args ...interface{}) {
	sugar.Fatalf(template, args...)
}

// Sync 同步日志缓冲区
func Sync() {
	sugar.Sync()
}
