package kafka

import (
	"context"
	"encoding/json"
	"fmt"
	"piceacorp.com/device-service/pkg/common"
	"piceacorp.com/device-service/pkg/common/config"
	"piceacorp.com/device-service/pkg/logger"
	"piceacorp.com/device-service/pkg/message"
	"piceacorp.com/device-service/pkg/nacos"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/IBM/sarama"
)

// kafkaProducer Kafka生产者实现
type kafkaProducer struct {
	asyncProducer sarama.AsyncProducer
	closed        bool
	mutex         sync.RWMutex
	errorChan     chan error
	successChan   chan *sarama.ProducerMessage
}

// 全局生产者实例 - 模拟Redis的全局变量方式
var (
	Producer message.Producer
	once     sync.Once
)

// InitProducer 初始化Kafka生产者 - 模拟Redis的初始化方式
func InitProducer(clientId string) (func(), error) {
	logger.Info("start init kafka producer")
	kafkaConfig := &config.KafkaConfig{}
	nacos.UnmarshalConfigKey(common.KAFKA, kafkaConfig)
	var err error
	var resFn func()
	once.Do(func() {
		saramaConfig := sarama.NewConfig()
		// 生产者配置
		saramaConfig.Producer.RequiredAcks = sarama.WaitForLocal
		//config.Producer.Retry.Max = c.RetryMax
		//config.Producer.Retry.Backoff = c.RetryBackoff
		//config.Producer.Return.Successes = true
		//config.Producer.Return.Errors = true
		//config.Producer.Timeout = c.Timeout
		//config.Producer.MaxMessageBytes = c.MaxMessageBytes

		// 批处理配置
		//config.Producer.Flush.Frequency = c.FlushFrequency
		//config.Producer.Flush.Messages = c.FlushMessages
		//config.Producer.Flush.Bytes = c.FlushBytes

		// 客户端配置
		saramaConfig.ClientID = clientId + strconv.Itoa(int(time.Now().UnixMilli()))
		//config.Version = sarama.V2_8_0_0 // 使用Kafka 2.8.0版本
		// 创建异步生产者
		asyncProducer, createErr := sarama.NewAsyncProducer(strings.Split(kafkaConfig.Brokers, ","), saramaConfig)
		if createErr != nil {
			err = fmt.Errorf("创建Kafka生产者失败: %w", createErr)
			return
		}

		p := &kafkaProducer{
			asyncProducer: asyncProducer,
			closed:        false,
			errorChan:     make(chan error, 100),
			successChan:   make(chan *sarama.ProducerMessage, 100),
		}

		// 启动错误和成功处理协程
		go p.handleErrors()
		go p.handleSuccesses()

		Producer = p
		resFn = func() {
			_ = p.Close()
		}
	})
	return resFn, err
}

// SendMessage 发送消息
func (p *kafkaProducer) SendMessage(ctx context.Context, topic string, value interface{}) error {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	if p.closed {
		return fmt.Errorf("kafkaProducer closed")
	}

	// 序列化消息值
	valueBytes, err := p.serializeValue(value)
	if err != nil {
		return fmt.Errorf("serializeValue fail: %w", err)
	}

	// 创建消息
	msg := &sarama.ProducerMessage{
		Topic: topic,
		Value: sarama.StringEncoder(valueBytes),
		//Headers: []sarama.RecordHeader{
		//	{
		//		Key:   []byte("timestamp"),
		//		Value: []byte(fmt.Sprintf("%d", time.Now().Unix())),
		//	},
		//},
	}
	//if key != "" {
	//	msg.Key = sarama.StringEncoder(key)
	//}

	//logger.Infof("send kafka msg: topic=%s, key=%s, value=%s", topic, key, string(valueBytes))
	logger.Infof("send kafka msg: topic=%s,  value=%s", topic, string(valueBytes))
	// 发送消息
	p.asyncProducer.Input() <- msg
	select {
	case <-p.asyncProducer.Successes():
		return nil
	case err := <-p.asyncProducer.Errors():
		return fmt.Errorf("send kafka msg fail, %w", err)
	case <-ctx.Done():
		return ctx.Err()
		//case <-time.After(p.config.Timeout):
		//	return fmt.Errorf("发送消息超时")
	}
}

// SendMessageAsync 异步发送消息
func (p *kafkaProducer) SendMessageAsync(ctx context.Context, topic string, key string, value interface{}) error {
	return p.SendMessage(ctx, topic, value)
}

// SendMessageWithHeaders 发送带头部的消息
func (p *kafkaProducer) SendMessageWithHeaders(ctx context.Context, topic string, key string, value interface{}, headers map[string]string) error {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	if p.closed {
		return fmt.Errorf("生产者已关闭")
	}

	// 序列化消息值
	valueBytes, err := p.serializeValue(value)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	// 创建消息头部
	var recordHeaders []sarama.RecordHeader
	//recordHeaders := []sarama.RecordHeader{
	//	{
	//		Key:   []byte("timestamp"),
	//		Value: []byte(fmt.Sprintf("%d", time.Now().Unix())),
	//	},
	//}

	// 添加自定义头部
	for k, v := range headers {
		recordHeaders = append(recordHeaders, sarama.RecordHeader{
			Key:   []byte(k),
			Value: []byte(v),
		})
	}

	// 创建消息
	msg := &sarama.ProducerMessage{
		Topic:   topic,
		Key:     sarama.StringEncoder(key),
		Value:   sarama.ByteEncoder(valueBytes),
		Headers: recordHeaders,
	}

	// 发送消息
	p.asyncProducer.Input() <- msg

	select {
	case p.asyncProducer.Input() <- msg:
		return nil
	case <-ctx.Done():
		return ctx.Err()
		//case <-time.After(p.config.Timeout):
		//	return fmt.Errorf("发送消息超时")
	}
}

// Close 关闭生产者
func (p *kafkaProducer) Close() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.closed {
		return nil
	}

	p.closed = true

	// 关闭输入通道
	p.asyncProducer.AsyncClose()

	// 等待所有消息发送完成
	for {
		select {
		case <-p.asyncProducer.Successes():
			// 继续等待
		case <-p.asyncProducer.Errors():
			// 继续等待
		case <-time.After(5 * time.Second):
			// 超时退出
			return nil
		default:
			// 没有更多消息，退出
			return nil
		}
	}
}

// serializeValue 序列化消息值
func (p *kafkaProducer) serializeValue(value interface{}) ([]byte, error) {
	switch v := value.(type) {
	case string:
		return []byte(v), nil
	case []byte:
		return v, nil
	default:
		return json.Marshal(v)
	}
}

// handleErrors 处理错误
func (p *kafkaProducer) handleErrors() {
	for err := range p.asyncProducer.Errors() {
		select {
		case p.errorChan <- err.Err:
		default:
			// 错误通道满了，丢弃错误
		}
	}
}

// handleSuccesses 处理成功
func (p *kafkaProducer) handleSuccesses() {
	for msg := range p.asyncProducer.Successes() {
		select {
		case p.successChan <- msg:
		default:
			// 成功通道满了，丢弃消息
		}
	}
}
