package message

import "context"

// Producer 消息生产者接口
type Producer interface {
	// SendMessage 发送消息
	SendMessage(ctx context.Context, topic string, value interface{}) error
	// SendMessageAsync 异步发送消息
	//SendMessageAsync(ctx context.Context, topic string, key string, value interface{}) error
	// SendMessageWithHeaders 发送带头部的消息
	//SendMessageWithHeaders(ctx context.Context, topic string, key string, value interface{}, headers map[string]string) error
	//Close() error
}
