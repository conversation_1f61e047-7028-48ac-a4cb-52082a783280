package nacos

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/nacos-group/nacos-sdk-go/v2/clients"
	"github.com/nacos-group/nacos-sdk-go/v2/clients/config_client"
	"github.com/nacos-group/nacos-sdk-go/v2/clients/naming_client"
	"github.com/nacos-group/nacos-sdk-go/v2/common/constant"
	"github.com/nacos-group/nacos-sdk-go/v2/util"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
	"piceacorp.com/device-service/pkg/common/config"
)

var client *Client

// Client Nacos客户端
type Client struct {
	configClient config_client.IConfigClient
	namingClient naming_client.INamingClient
	localIP      string
}

// NewClient 创建Nacos客户端
func initClient() error {
	nacosConfig := config.SC.Nacos
	// 设置默认值
	if nacosConfig.LogDir == "" {
		nacosConfig.LogDir = "logs/nacos"
	}
	if nacosConfig.CacheDir == "" {
		nacosConfig.CacheDir = "cache/nacos"
	}

	// 创建ServerConfig
	serverConfigs := []constant.ServerConfig{
		{
			IpAddr: nacosConfig.Host,
			Port:   uint64(nacosConfig.Port),
		},
	}

	// 创建ClientConfig
	clientConfig := constant.ClientConfig{
		NamespaceId:         nacosConfig.Namespace,
		TimeoutMs:           5000,
		NotLoadCacheAtStart: true,
		LogDir:              nacosConfig.LogDir,
		CacheDir:            nacosConfig.CacheDir,
	}

	if nacosConfig.LogMode == gin.DebugMode {
		clientConfig.LogLevel = gin.DebugMode
		clientConfig.AppendToStdout = true
	}

	// 如果提供了用户名和密码，设置认证信息
	//if config.Username != "" && config.Password != "" {
	//	clientConfig.Username = config.Username
	//	clientConfig.Password = config.Password
	//}

	// 创建配置客户端

	configClient, err := clients.NewConfigClient(
		vo.NacosClientParam{
			ClientConfig:  &clientConfig,
			ServerConfigs: serverConfigs,
		},
	)
	if err != nil {
		fmt.Println("Failed to create Nacos configuration client")
		return err
	}

	// 创建命名客户端
	namingClient, err := clients.NewNamingClient(
		vo.NacosClientParam{
			ClientConfig:  &clientConfig,
			ServerConfigs: serverConfigs,
		},
	)
	if err != nil {
		fmt.Println("Failed to create Nacos named client", err)
		return err
	}

	client = &Client{
		configClient: configClient,
		namingClient: namingClient,
		localIP:      util.LocalIP(),
	}

	return nil
}
