package nacos

import (
	"github.com/gin-gonic/gin"
	"github.com/nacos-group/nacos-sdk-go/v2/clients"
	"github.com/nacos-group/nacos-sdk-go/v2/clients/config_client"
	"github.com/nacos-group/nacos-sdk-go/v2/clients/naming_client"
	"github.com/nacos-group/nacos-sdk-go/v2/common/constant"
	"github.com/nacos-group/nacos-sdk-go/v2/util"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
	"piceacorp.com/device-service/pkg/logger"
)

var client *Client

// Config Nacos配置
type Config struct {
	ServerAddr  string
	ServerPort  int
	Namespace   string
	DataID      string
	Group       string
	Username    string // 可选，用于认证
	Password    string // 可选，用于认证
	LogDir      string // 日志目录
	CacheDir    string // 缓存目录
	ServiceName string //当前服务名称
	ServicePort uint64 //当前服务开发的端口
	LogMode     string //如果是debug模式则打开相关日志
}

// Client Nacos客户端
type Client struct {
	config       Config
	configClient config_client.IConfigClient
	namingClient naming_client.INamingClient
	localIP      string
}

// NewClient 创建Nacos客户端
func initClient(config *Config) error {
	// 设置默认值
	if config.LogDir == "" {
		config.LogDir = "logs/nacos"
	}
	if config.CacheDir == "" {
		config.CacheDir = "cache/nacos"
	}

	// 创建ServerConfig
	serverConfigs := []constant.ServerConfig{
		{
			IpAddr: config.ServerAddr,
			Port:   uint64(config.ServerPort),
		},
	}

	// 创建ClientConfig
	clientConfig := constant.ClientConfig{
		NamespaceId:         config.Namespace,
		TimeoutMs:           5000,
		NotLoadCacheAtStart: true,
		LogDir:              config.LogDir,
		CacheDir:            config.CacheDir,
	}

	if config.LogMode == gin.DebugMode {
		clientConfig.LogLevel = gin.DebugMode
		clientConfig.AppendToStdout = true
	}

	// 如果提供了用户名和密码，设置认证信息
	if config.Username != "" && config.Password != "" {
		clientConfig.Username = config.Username
		clientConfig.Password = config.Password
	}

	// 创建配置客户端

	configClient, err := clients.NewConfigClient(
		vo.NacosClientParam{
			ClientConfig:  &clientConfig,
			ServerConfigs: serverConfigs,
		},
	)
	if err != nil {
		logger.Error("创建Nacos配置客户端失败")
		return err
	}

	// 创建命名客户端
	namingClient, err := clients.NewNamingClient(
		vo.NacosClientParam{
			ClientConfig:  &clientConfig,
			ServerConfigs: serverConfigs,
		},
	)
	if err != nil {
		logger.Error("创建Nacos命名客户端失败", err)
		return err
	}

	client = &Client{
		config:       *config,
		configClient: configClient,
		namingClient: namingClient,
		localIP:      util.LocalIP(),
	}

	return nil
}
