package nacos

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
	"github.com/spf13/viper"
	"piceacorp.com/device-service/pkg/common/config"
	"reflect"
	"regexp"
	"strings"
	"sync"
)

var configMutex sync.RWMutex

// InitConfigManager 创建配置管理器
func initConfig() error {
	// 初始化时
	//获取公共配置
	content, err := getConfig(config.SC.Nacos.CommonDataId)
	if err == nil {
		parseConfig(content)
	} else {
		fmt.Printf("get common config: [%s] fail", config.SC.Nacos.CommonDataId)
	}
	// 监听公共配置
	err = listenConfigChange(config.SC.Nacos.CommonDataId)
	if err != nil {
		fmt.Println("listen nacos common config change fail", err)
		return err
	}
	//获取当前服务配置
	content, err = getConfig(config.SC.Nacos.DataId)
	if err == nil {
		parseConfig(content)
	} else {
		return err
	}

	// 监听配置
	err = listenConfigChange(config.SC.Nacos.DataId)
	if err != nil {
		fmt.Println("listen nacos config change fail", err)
		return err
	}

	return nil

}

// GetConfig 获取配置
func getConfig(dataId string) (string, error) {
	content, err := client.configClient.GetConfig(vo.ConfigParam{
		DataId: dataId,
		Group:  config.SC.Nacos.Group,
	})
	if err != nil {
		fmt.Println("failed to get nacos configuration", err)
		return "", err
	}
	return content, nil
}

// 监听配置变化
func listenConfigChange(dataId string) error {
	return client.configClient.ListenConfig(vo.ConfigParam{
		DataId: dataId,
		Group:  config.SC.Nacos.Group,
		OnChange: func(namespace, group, dataId, data string) {
			if config.SC.Server.Pcf {
				fmt.Printf(data)
			}
			// 更新缓存
			configMutex.Lock()
			defer configMutex.Unlock()
			parseConfig(data)
		},
	})
}

// 支持读取变量格式 ${xxx:default}，xxx为变量名，default为默认值
// 读取文件内容时一次性解析所有变量值，反序列就无需该Hook了
var envDecodeHook = viper.DecodeHook(
	func(f reflect.Type, t reflect.Type, data interface{}) (interface{}, error) {
		if dataStr, ok := data.(string); ok {
			return parsingEnvVariables(dataStr), nil
		}
		return data, nil
	})

// 解析配置到Viper
func parseConfig(content string) error {
	if content == "" {
		return nil
	}
	//解析变量配置
	content = parsingEnvVariables(content)
	if config.SC.Server.Pcf && config.SC.Server.Mode != gin.ReleaseMode {
		fmt.Printf(content)
	}
	// 配置是YAML格式
	err := viper.MergeConfig(strings.NewReader(content))
	if err != nil {
		fmt.Println("parse nacos config fail", err)
		return err
	}
	//err = viper.Unmarshal(config.SC, envDecodeHook)
	err = viper.Unmarshal(config.SC)
	if err != nil {
		fmt.Println("unmarshal nacos config fail", err)
		return err
	}
	return nil
}

// 支持读取变量 ${xxx:default}，xxx为变量名，default为默认值
// 读取顺序：
// Viper uses the following precedence order. Each item takes precedence over the item below it:
// explicit call to Set
// flag
// env
// config
// key/value store
// default
func parsingEnvVariables(dataStr string) string {
	// 检查字符串中是否包含${...}格式的占位符
	if strings.Contains(dataStr, "${") && strings.Contains(dataStr, "}") {
		// 使用正则表达式查找所有${...}格式的占位符
		re := regexp.MustCompile(`\${([^}]*)}`)
		result := dataStr

		// 替换所有匹配的占位符
		matches := re.FindAllStringSubmatch(dataStr, -1)
		for _, match := range matches {
			placeholder := match[0] // 完整的占位符，如${mongo.user}
			envVarName := match[1]  // 占位符内的内容，如mongodb.user

			// 检查是否有默认值（使用 :分隔）
			var defaultValue string
			if idx := strings.Index(envVarName, ":"); idx >= 0 {
				defaultValue = envVarName[idx+1:]
				envVarName = envVarName[:idx]
			}

			envValue := viper.GetString(envVarName)
			if envValue != "" {
				result = strings.Replace(result, placeholder, envValue, -1)
				continue
			}

			// 如果环境变量与当前配置中都不存在但有默认值，使用默认值
			if defaultValue != "" {
				result = strings.Replace(result, placeholder, defaultValue, -1)
			}
		}
		return result
	}
	return dataStr
}
