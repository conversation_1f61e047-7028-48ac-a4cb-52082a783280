package nacos

import (
	"encoding/json"
	"fmt"
	"os"
	"reflect"
	"regexp"
	"strings"
	"sync"

	// "github.com/go-viper/mapstructure/v2"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
	"github.com/spf13/viper"
	"piceacorp.com/device-service/pkg/logger"
)

var (
	configViper     *viper.Viper
	listeners       []func(string)
	structListeners map[string][]interface{}
	configMutex     sync.RWMutex
)

// InitConfigManager 创建配置管理器
func initConfig() error {
	// 初始化时获取一次配置
	content, err := getConfig()
	if err == nil {
		parseConfig(content)
	} else {
		return err
	}

	// 监听配置
	err = listenConfigChange()
	if err != nil {
		logger.Error("listen nacos config change fail", err)
		return err
	}

	return nil

}

// GetConfig 获取配置
func getConfig() (string, error) {
	content, err := client.configClient.GetConfig(vo.ConfigParam{
		DataId: client.config.DataID,
		Group:  client.config.Group,
	})
	if err != nil {
		logger.Error("获取Nacos配置失败", err)
		return "", err
	}
	return content, nil
}

// AddListener 添加配置变化监听器
func AddListener(listener func(string)) {
	configMutex.Lock()
	defer configMutex.Unlock()
	listeners = append(listeners, listener)
}

// AddStructListener 添加结构体配置监听器
// key: 配置键路径，如 "server.port"
// target: 指向结构体字段的指针
func AddStructListener(key string, target interface{}) {
	configMutex.Lock()
	defer configMutex.Unlock()

	if _, ok := structListeners[key]; !ok {
		structListeners[key] = make([]interface{}, 0)
	}
	structListeners[key] = append(structListeners[key], target)

	// 立即更新一次
	if configViper != nil {
		value := configViper.Get(key)
		if value != nil {
			updateTarget(target, value)
		}
	}
}

// 更新目标值
func updateTarget(target interface{}, value interface{}) {
	switch ptr := target.(type) {
	case *string:
		*ptr = fmt.Sprintf("%v", value)
	case *int:
		if v, ok := value.(int); ok {
			*ptr = v
		}
	case *int64:
		if v, ok := value.(int64); ok {
			*ptr = v
		}
	case *float64:
		if v, ok := value.(float64); ok {
			*ptr = v
		}
	case *bool:
		if v, ok := value.(bool); ok {
			*ptr = v
		}
	default:
		// 尝试JSON转换
		jsonBytes, err := json.Marshal(value)
		if err == nil {
			err = json.Unmarshal(jsonBytes, target)
			if err != nil {
				logger.Error("更新结构体配置失败", err)
			}
		}
	}
}

// 监听配置变化
func listenConfigChange() error {
	return client.configClient.ListenConfig(vo.ConfigParam{
		DataId: client.config.DataID,
		Group:  client.config.Group,
		OnChange: func(namespace, group, dataId, data string) {
			logger.Infof("nacos config content:\n%s", data)
			// 更新缓存
			configMutex.Lock()
			defer configMutex.Unlock()
			parseConfig(data)

			// 通知所有其余自定义监听器
			for _, listener := range listeners {
				listener(data)
			}

			// 更新结构体监听器
			updateStructListeners()
		},
	})
}

// 解析配置到Viper
func parseConfig(content string) {
	v := viper.New()
	// 配置是YAML格式
	v.SetConfigType("yaml")
	err := v.ReadConfig(strings.NewReader(content))
	if err != nil {
		logger.Error("parse nacos config fail", err)
		os.Exit(1)
	}
	configViper = v
}

// 更新所有结构体监听器
func updateStructListeners() {

	if configViper == nil {
		return
	}

	for key, targets := range structListeners {
		value := configViper.Get(key)
		if value == nil {
			continue
		}

		for _, target := range targets {
			updateTarget(target, value)
		}
	}
}

// 支持读取 ${xxx:default}，xxx为环境变量名，default为默认值
var envDecodeHook = viper.DecodeHook(
	func(f reflect.Type, t reflect.Type, data interface{}) (interface{}, error) {
		if datastr, ok := data.(string); ok {
			// 检查字符串中是否包含${...}格式的占位符
			if strings.Contains(datastr, "${") && strings.Contains(datastr, "}") {
				// 使用正则表达式查找所有${...}格式的占位符
				re := regexp.MustCompile(`\${([^}]*)}`)
				result := datastr

				// 替换所有匹配的占位符
				matches := re.FindAllStringSubmatch(datastr, -1)
				for _, match := range matches {
					placeholder := match[0] // 完整的占位符，如${mongo.user}
					envVarName := match[1]  // 占位符内的内容，如mongodb.user

					// 检查是否有默认值（使用 :分隔）
					var defaultValue string
					if idx := strings.Index(envVarName, ":"); idx >= 0 {
						defaultValue = envVarName[idx+1:]
						envVarName = envVarName[:idx]
					}

					// 获取环境变量值
					envValue := os.Getenv(envVarName)
					// 如果环境变量存在，使用其值
					if envValue != "" {
						result = strings.Replace(result, placeholder, envValue, -1)
						continue
					}

					// 从当前配置中读取
					if configViper != nil {
						envValue = configViper.GetString(envVarName)
						if envValue != "" {
							result = strings.Replace(result, placeholder, envValue, -1)
							continue
						}
					}

					// 如果环境变量与当前配置中都不存在但有默认值，使用默认值
					if defaultValue != "" {
						result = strings.Replace(result, placeholder, defaultValue, -1)
					}
				}
				return result, nil
			}
		}
		return data, nil
	})

func UnmarshalConfigKey(key string, rawVal any) {
	configMutex.RLock()
	defer configMutex.RUnlock()
	err := configViper.UnmarshalKey(key, rawVal, envDecodeHook)
	if err != nil {
		logger.Errorf("unmarshal nacos config key: %s, error: %w", key, err)
	}
}

// GetString 获取字符串配置
func GetString(key string) string {
	configMutex.RLock()
	defer configMutex.RUnlock()
	if configViper == nil {
		return ""
	}
	return configViper.GetString(key)
}

func GetStringOrDefault(key, defaultVal string) string {
	configMutex.RLock()
	defer configMutex.RUnlock()
	if configViper == nil {
		return ""
	}
	nv := configViper.GetString(key)
	if nv == "" {
		return defaultVal
	}
	return nv
}

// GetInt 获取整数配置
func GetInt(key string) int {
	configMutex.RLock()
	defer configMutex.RUnlock()
	if configViper == nil {
		return 0
	}
	return configViper.GetInt(key)
}

// GetBool 获取布尔配置
func GetBool(key string) bool {
	configMutex.RLock()
	defer configMutex.RUnlock()
	if configViper == nil {
		return false
	}
	return configViper.GetBool(key)
}

// UnmarshalKey 将配置解析到结构体
// func UnmarshalKey(key string, rawVal interface{}) error {
// 	configMutex.RLock()
// 	defer configMutex.RUnlock()
// 	if configViper == nil {
// 		return fmt.Errorf("配置未初始化")
// 	}
// 	return configViper.UnmarshalKey(key, rawVal)
// }
