package nacos

import (
	"fmt"

	"github.com/nacos-group/nacos-sdk-go/v2/model"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
	"piceacorp.com/device-service/pkg/logger"
)

// RegisterService 注册服务
func registerService(serviceName string, port uint64, metadata map[string]string) (bool, error) {
	param := vo.RegisterInstanceParam{
		Ip:          client.localIP,
		Port:        port,
		ServiceName: serviceName,
		Weight:      10,
		Enable:      true,
		Healthy:     true,
		Ephemeral:   true, // 临时实例，断开连接后自动注销
		Metadata:    metadata,
		GroupName:   client.config.Group,
	}
	success, err := client.namingClient.RegisterInstance(param)
	if err != nil {
		logger.Error(fmt.Sprintf("service: %s, register fail", serviceName), err)
		return false, err
	}
	logger.Info(fmt.Sprintf("service: %s, register success，IP=%s, Port=%d", serviceName, client.localIP, port))
	return success, nil
}

// DeregisterService 注销服务
func deregisterService(serviceName string, port uint64) (bool, error) {

	param := vo.DeregisterInstanceParam{
		Ip:          client.localIP,
		Port:        port,
		ServiceName: serviceName,
		GroupName:   client.config.Group,
	}

	success, err := client.namingClient.DeregisterInstance(param)
	if err != nil {
		logger.Errorf("deregisterService:: %s fail, error: %w", serviceName, err)
		return false, err
	}

	logger.Infof(fmt.Sprintf("deregisterService: %s success", serviceName))
	return success, nil
}

// GetService 获取服务信息
func getService(serviceName string) (model.Service, error) {
	param := vo.GetServiceParam{
		ServiceName: serviceName,
		GroupName:   client.config.Group,
	}

	service, err := client.namingClient.GetService(param)
	if err != nil {
		logger.Error(fmt.Sprintf("获取服务[%s]信息失败", serviceName), err)
		return model.Service{}, err
	}
	return service, nil
}

// Subscribe 订阅服务变化
func subscribe(serviceName string, callback func([]model.Instance)) {
	param := vo.SubscribeParam{
		ServiceName: serviceName,
		GroupName:   client.config.Group,
		SubscribeCallback: func(services []model.Instance, err error) {
			if err != nil {
				logger.Error("服务变化回调错误", err)
				return
			}
			callback(services)
		},
	}

	err := client.namingClient.Subscribe(&param)
	if err != nil {
		logger.Errorf("Subscribe Service Fail: %s, error: %w", serviceName, err)
	}

}
