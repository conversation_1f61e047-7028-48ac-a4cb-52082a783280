package nacos

import (
	"errors"
	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/pkg/common/config"
	"resty.dev/v3"

	"piceacorp.com/device-service/pkg/logger"
)

// InitServiceDiscovery 初始化服务发现
// 注册本服务并订阅依赖的服务
func Bootstrap(nacosConfig *Config, dependentServices []string) (func(), error) {

	// 创建Nacos客户端
	err := initClient(nacosConfig)
	if err != nil {
		logger.Fatal("init nacos client fail", err)
		return nil, err
	}

	// 初始化Nacs远程配置
	err = initConfig()
	if err != nil {
		logger.Fatal("init nacos config fail", err)
		return nil, err
	}

	// 初始化Nacos服务注册与发现
	// 当前服务元数据
	metadata := map[string]string{
		"preserved.register.source": "GO",
	}
	// 注册当前服务
	success, err := registerService(nacosConfig.ServiceName, nacosConfig.ServicePort, metadata)
	if err != nil {
		logger.Fatal("register nacos service fail", err)
		return nil, err
	}
	if !success {
		return nil, errors.New("subscribe service faile: " + nacosConfig.ServiceName)
	}

	// 订阅依赖的服务
	for _, depServiceName := range dependentServices {
		err := subscribeService(depServiceName)
		if err != nil {
			logger.Errorf("subscribe service faile: %s, error: %w", depServiceName, err)
			// 继续订阅其他服务，不中断流程
		}
	}

	// 初始化http客户端调用远程服务时补充相关请求头
	httpClient.AddRequestMiddleware(func(client *resty.Client, req *resty.Request) error {
		gctx, ok := req.Context().(*gin.Context)
		if ok {
			for k, v := range gctx.Keys {
				vs, ok := v.(string)
				if ok {
					req.SetHeader(k, vs)
				}
			}
		}
		req.SetHeader("pre-application", config.ServiceConfig.Server.Name)
		return nil
	})

	return func() {
		// 注销服务
		deregisterService(nacosConfig.ServiceName, nacosConfig.ServicePort)
	}, nil
}
