package nacos

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"sync/atomic"

	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/pkg/common/config"
	"piceacorp.com/device-service/pkg/logger"

	"github.com/nacos-group/nacos-sdk-go/v2/model"
	"resty.dev/v3"
)

var (
	httpClient   = resty.New()
	serviceCache = make(map[string][]serviceEndpoint)
	counterMap   = make(map[string]*atomic.Uint64)
	mutex        sync.RWMutex
)

// ServiceEndpoint 服务端点
type serviceEndpoint struct {
	IP       string
	Port     uint64
	Metadata map[string]string
	Weight   float64
	Healthy  bool
}

func init() {
	// 初始化http客户端调用远程服务时补充相关请求头
	httpClient.AddRequestMiddleware(func(client *resty.Client, req *resty.Request) error {
		gctx, ok := req.Context().(*gin.Context)
		if ok {
			for k, v := range gctx.Keys {
				vs, ok := v.(string)
				if ok {
					req.SetHeader(k, vs)
				}
			}
		}
		req.SetHeader("pre-application", config.SC.Server.Name)
		return nil
	})
}

// CallService 调用服务
func CallService[T any](ctx context.Context, serviceName, path string, method string, reqBody any, failCall func() (failRes T)) (T, error) {
	response := new(T)
	endpoint, err := getServiceEndpoint(serviceName)
	if err != nil {
		return *response, err
	}
	path = strings.TrimPrefix(path, "/")
	url := fmt.Sprintf("http://%s:%d/%s", endpoint.IP, endpoint.Port, path)
	logger.Infof("start call %s api, path: %s", serviceName, url)
	req := httpClient.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		//SetResult(responseJson).
		SetAllowMethodDeletePayload(true).
		SetAllowMethodGetPayload(true)
	if reqBody != nil {
		req.SetBody(reqBody)
	}
	var resp *resty.Response
	switch method {
	case http.MethodGet:
		resp, err = req.Get(url)
	case http.MethodPost:
		resp, err = req.Post(url)
	case http.MethodPut:
		resp, err = req.Put(url)
	case http.MethodDelete:
		resp, err = req.Delete(url)
	default:
		return *response, fmt.Errorf("not allow http method: %s", method)
	}

	if err != nil {
		return *response, fmt.Errorf("call service: %s fail,: %w", serviceName, err)
	}

	if !resp.IsSuccess() {
		logger.Errorf("call service: %s fail, http status: %d, res: %s", serviceName, resp.StatusCode(), resp.String())
		if failCall != nil {
			return failCall(), nil
		}
		return *response, nil
	}
	responseBodyBytes := resp.Bytes()
	logger.Infof("end call %s api, path: %s, res: %v", serviceName, url, string(responseBodyBytes))
	err = json.Unmarshal(responseBodyBytes, response)
	if err != nil {
		return *response, err
	}
	return *response, nil
}

// SubscribeService 订阅服务
func subscribeService(serviceName string) {
	// 初始化计数器
	mutex.Lock()
	if _, exists := counterMap[serviceName]; !exists {
		counterMap[serviceName] = &atomic.Uint64{}
	}
	mutex.Unlock()

	// 订阅服务变化
	subscribe(serviceName, func(instances []model.Instance) {
		updateServiceCache(serviceName, instances)
	})

	// 立即获取一次服务实例
	refreshService(serviceName)
}

// RefreshService 刷新服务缓存
func refreshService(serviceName string) {
	service, err := getService(serviceName)
	if err != nil {
		logger.Errorf("subscribe service faile: %s, error: %w", serviceName, err)
		return
	}
	updateServiceCache(serviceName, service.Hosts)
}

// 更新服务缓存
func updateServiceCache(serviceName string, instances []model.Instance) {
	endpoints := make([]serviceEndpoint, 0, len(instances))
	for _, instance := range instances {
		if instance.Healthy {
			endpoints = append(endpoints, serviceEndpoint{
				IP:       instance.Ip,
				Port:     instance.Port,
				Metadata: instance.Metadata,
				Weight:   instance.Weight,
				Healthy:  instance.Healthy,
			})
		}
	}

	mutex.Lock()
	defer mutex.Unlock()

	if len(endpoints) > 0 {
		serviceCache[serviceName] = endpoints
		logger.Infof("service cache refreshed. name: %s endpotions: %v", serviceName, endpoints)
	} else {
		logger.Warnf("service: %s unavailable", serviceName)
	}
}

// GetServiceEndpoint 获取服务端点（轮询方式）
func getServiceEndpoint(serviceName string) (*serviceEndpoint, error) {
	mutex.RLock()
	endpoints, exists := serviceCache[serviceName]
	counter := counterMap[serviceName]
	mutex.RUnlock()

	// 如果计数器不存在，需要初始化
	if counter == nil {
		mutex.Lock()
		if counterMap[serviceName] == nil {
			counterMap[serviceName] = &atomic.Uint64{}
		}
		counter = counterMap[serviceName]
		mutex.Unlock()
	}

	if !exists || len(endpoints) == 0 {
		// 尝试刷新服务
		refreshService(serviceName)

		// 重新获取刷新后的endpoints
		mutex.RLock()
		endpoints = serviceCache[serviceName]
		mutex.RUnlock()

		if len(endpoints) == 0 {
			return nil, fmt.Errorf("service [%s] has no healthy instances", serviceName)
		}
	}

	// 轮询选择实例
	idx := int(counter.Load() % uint64(len(endpoints)))
	counter.Add(1)

	// 重置计数器避免溢出
	if counter.Load() >= uint64(len(endpoints)*1000) {
		counter.Store(0)
	}

	return &endpoints[idx], nil
}
