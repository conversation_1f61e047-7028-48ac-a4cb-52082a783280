package remote

import (
	"context"
	"net/http"

	"piceacorp.com/device-service/pkg/nacos"
	"piceacorp.com/device-service/pkg/remote/res"
	ecode "piceacorp.com/device-service/pkg/web/common/res/code"
)

type baseService interface {
	Name() string
}

func Get[R *res.CSRes[D], D any](ctx context.Context, url string, s baseService) R {
	response, err := nacos.CallService[R](ctx, s.Name(), url, http.MethodGet, nil, nil)
	return handleResError(response, err)
}

func Post[R *res.CSRes[D], D any](ctx context.Context, url string, reqBody any, s baseService) R {
	response, err := nacos.CallService[R](ctx, s.Name(), url, http.MethodPost, reqBody, nil)
	return handleResError(response, err)
}

func Delete[R *res.CSRes[D], D any](ctx context.Context, url string, s baseService) R {
	response, err := nacos.CallService[R](ctx, s.Name(), url, http.MethodDelete, nil, nil)
	return handleResError(response, err)
}

func Put[R *res.CSRes[D], D any](ctx context.Context, url string, reqBody any, s baseService) R {
	response, err := nacos.CallService[R](ctx, s.Name(), url, http.MethodPut, reqBody, nil)
	return handleResError(response, err)
}

func Patch[R *res.CSRes[D], D any](ctx context.Context, url string, reqBody any, s baseService) R {
	response, err := nacos.CallService[R](ctx, s.Name(), url, http.MethodPatch, reqBody, nil)
	return handleResError(response, err)
}

func handleResError[D any](response *res.CSRes[D], err error) *res.CSRes[D] {
	if response == nil {
		response = &res.CSRes[D]{Code: ecode.INNER_SERVER_ERROR.Code}
	}
	if err != nil {
		response.Code = ecode.INNER_SERVER_ERROR.Code
		response.Msg = ecode.INNER_SERVER_ERROR.Msg + "-" + err.Error()
	}
	return response
}
