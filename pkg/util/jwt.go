package util

import "github.com/golang-jwt/jwt/v5"

var defaultSecret = []byte("device.token.key")

func GenerateJWT(val string) string {
	claims := jwt.MapClaims{}
	claims["value"] = val
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	s, _ := token.SignedString(defaultSecret)
	return s
}

func CEmqJWT(sn, secret string) string {
	claims := jwt.MapClaims{}
	claims["username"] = sn
	claims["clientId"] = sn
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	s, _ := token.SignedString([]byte(secret))
	return s
}

func JWT(payload map[string]interface{}, secret string) string {
	claims := jwt.MapClaims(payload)
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	s, _ := token.SignedString([]byte(secret))
	return s
}
