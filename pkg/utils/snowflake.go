package utils

import (
	"errors"
	"fmt"
	"math/rand"
	"net"
	"os"
	"sync"
	"time"

	"github.com/google/uuid"
)

const (
	// 开始时间戳 (2010-11-04 09:42:54 UTC) - 对应Java中的DEFAULT_EPOCH
	epoch = int64(1288834974657)

	// 机器ID所占位数
	workerIDBits = uint(5)

	// 数据中心ID所占位数
	datacenterIDBits = uint(5)

	// 序列所占位数
	sequenceBits = uint(12)

	// 最大机器ID
	maxWorkerID = int64(-1) ^ (int64(-1) << workerIDBits)

	// 最大数据中心ID
	maxDatacenterID = int64(-1) ^ (int64(-1) << datacenterIDBits)

	// 机器ID左移位数
	workerIDShift = sequenceBits

	// 数据中心ID左移位数
	datacenterIDShift = sequenceBits + workerIDBits

	// 时间戳左移位数
	timestampShift = sequenceBits + workerIDBits + datacenterIDBits

	// 序列掩码
	sequenceMask = int64(-1) ^ (int64(-1) << sequenceBits)
)

// 定义错误
var (
	ErrInvalidWorkerID     = errors.New("worker ID超出范围")
	ErrInvalidDatacenterID = errors.New("datacenter ID超出范围")
	ErrClockMovedBackwards = errors.New("系统时钟回退")
)

// Snowflake 雪花算法结构体
type Snowflake struct {
	mutex         sync.Mutex
	lastTimestamp int64
	workerID      int64
	datacenterID  int64
	sequence      int64
}

// 全局雪花算法实例
var (
	globalSnowflake *Snowflake
	once            sync.Once
)

// NewSnowflake 创建雪花算法实例
func NewSnowflake(workerID, datacenterID int64) (*Snowflake, error) {
	if workerID < 0 || workerID > maxWorkerID {
		return nil, ErrInvalidWorkerID
	}
	if datacenterID < 0 || datacenterID > maxDatacenterID {
		return nil, ErrInvalidDatacenterID
	}
	return &Snowflake{
		lastTimestamp: -1,
		workerID:      workerID,
		datacenterID:  datacenterID,
		sequence:      0,
	}, nil
}

// getWorkerID 自动生成workerID
func getWorkerID() int64 {
	var workerID int64

	// 尝试从主机IP地址生成
	hostname, err := os.Hostname()
	if err == nil {
		addrs, err := net.LookupIP(hostname)
		if err == nil && len(addrs) > 0 {
			// 使用IP地址和随机UUID生成字符串
			workerIDStr := addrs[0].String() + uuid.New().String()

			// 计算字符串中所有字符的ASCII码总和
			sum := 0
			for _, r := range workerIDStr {
				sum += int(r)
			}

			// 取模确保在范围内
			workerID = int64(sum) % (maxWorkerID + 1)
		}
	}

	// 如果上述方法失败，使用随机数
	if workerID == 0 {
		// 生成0到maxWorkerID之间的随机数
		rand.Seed(time.Now().UnixNano())
		workerID = rand.Int63n(maxWorkerID + 1)
	}

	return workerID
}

// getDatacenterID 自动生成datacenterID
func getDatacenterID() int64 {
	// 使用主机名和随机UUID生成字符串
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "unknown"
	}

	datacenterStr := hostname + uuid.New().String()

	// 计算字符串中所有字符的ASCII码总和
	sum := 0
	for _, r := range datacenterStr {
		sum += int(r)
	}

	// 取模确保在范围内
	datacenterID := int64(sum) % (maxDatacenterID + 1)

	return datacenterID
}

// NextID 生成下一个ID
func (s *Snowflake) NextID() int64 {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	timestamp := time.Now().UnixMilli() // 当前时间戳，精确到毫秒

	// 如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过
	if timestamp < s.lastTimestamp {
		// 重新初始化 数据中心ID与机器ID
		s.workerID = getWorkerID()
		s.datacenterID = getDatacenterID()
		s.lastTimestamp = -1
		s.sequence = 0
		return 0
	}

	// 如果是同一时间生成的，则进行毫秒内序列
	if timestamp == s.lastTimestamp {
		s.sequence = (s.sequence + 1) & sequenceMask
		// 毫秒内序列溢出
		if s.sequence == 0 {
			// 阻塞到下一个毫秒，获得新的时间戳
			for timestamp <= s.lastTimestamp {
				timestamp = time.Now().UnixMilli()
			}
		}
	} else {
		// 时间戳改变，毫秒内序列重置
		s.sequence = 0
	}

	// 更新上次生成ID的时间戳
	s.lastTimestamp = timestamp

	// 移位并通过或运算拼到一起组成64位的ID
	return ((timestamp - epoch) << timestampShift) |
		(s.datacenterID << datacenterIDShift) |
		(s.workerID << workerIDShift) |
		s.sequence
}

// 初始化全局雪花算法实例
func init() {
	workerID := getWorkerID()
	datacenterID := getDatacenterID()
	once.Do(func() {
		var err error
		globalSnowflake, err = NewSnowflake(workerID, datacenterID)
		if err != nil {
			panic(fmt.Sprintf("初始化雪花算法失败: %v", err))
		}
	})
}

// NextStrID 生成字符串格式的ID
func NextStrID() string {
	return fmt.Sprintf("%d", globalSnowflake.NextID())
}

// GenerateID 使用全局雪花算法实例生成ID
func GenerateID() (int64, error) {
	if globalSnowflake == nil {
		return 0, errors.New("snowflake not initialized")
	}
	id := globalSnowflake.NextID()
	if id == 0 {
		return 0, ErrClockMovedBackwards
	}
	return id, nil
}

// Id 简便方法，生成字符串ID
func Id() string {
	id, _ := GenerateID()
	return fmt.Sprintf("%d", id)
}
