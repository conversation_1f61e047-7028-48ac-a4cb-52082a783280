package context

const (
	AUTH              = "Authorization"
	FAMILY            = "Family"
	EMQ_TOKEN         = "Emqtoken"
	TRACE_ID          = "Traceid"
	NONCE             = "Nonce"
	ID                = "Id"
	TENANT_ID         = "Tenantid"
	USERNAME          = "Username"
	NICKNAME          = "Nickname"
	EMAIL             = "Email"
	TCP_ADDR          = "Tcpaddr"
	CONNECTION_TYPE   = "Connectiontype"
	PROJECT_TYPE      = "Projecttype"
	PRODUCT_MODE_CODE = "Productmodecode"
	ROBOT_TYPE        = "Robottype"
	ZONE              = "Zone"
	CLIENT_TYPE       = "Clienttype"
	LANG              = "Lang"
	SN                = "Sn"
	MAC               = "Mac"
	PHONE             = "Phone"
	USER_AGENT        = "User-Agent"
	INDEX             = "Index"
	VERSION_INFO      = "Versioninfo"
	BIND_LIST         = "Bindlist"
	CLIENT_IP         = "Clientip"
	CONTROL_DEVICE    = "Controldevice"
	COUNTRY_CITY      = "Countrycity"
	BETA_FLAG         = "Betaflag"
	FLAG              = "Flag"
	ROLE_ID           = "Roleid"
	ENABLE_SMART_HOME = "Enablesmarthome"
	CLIENT_ID         = "Clientid"
	OPEN_USER_ID      = "Openuserid"
	OPEN_USER_NAME    = "Openusername"
	OPEN_CLIENT_TYPE  = "Openclienttype"
	APP_ID            = "Appid"
)

// 定义所有上下文键的信息
var Keys = map[string]string{
	AUTH:              "授权token",
	FAMILY:            "家庭信息",
	EMQ_TOKEN:         "emqToken",
	TRACE_ID:          "调用链追踪唯一ID",
	NONCE:             "调用链追踪唯一ID(请求侧生成)",
	ID:                "当前用户ID",
	TENANT_ID:         "租户id（厂商id）",
	USERNAME:          "用户名",
	NICKNAME:          "昵称",
	EMAIL:             "当前用户邮箱",
	TCP_ADDR:          "TCP",
	CONNECTION_TYPE:   "连接类型",
	PROJECT_TYPE:      "产品类型",
	PRODUCT_MODE_CODE: "产品型号代码",
	ROBOT_TYPE:        "机器类型",
	ZONE:              "地区",
	CLIENT_TYPE:       "终端类型",
	LANG:              "语言",
	SN:                "设备序列号",
	MAC:               "设备mac地址",
	PHONE:             "手机号",
	USER_AGENT:        "表示来源",
	INDEX:             "步长",
	VERSION_INFO:      "版本信息",
	BIND_LIST:         "绑定列表",
	CLIENT_IP:         "客户端IP",
	CONTROL_DEVICE:    "控制的设备",
	COUNTRY_CITY:      "地区",
	BETA_FLAG:         "app内测用户标识",
	FLAG:              "角色是否拥有全部产品，0:否，1:是",
	ROLE_ID:           "角色ID",
	ENABLE_SMART_HOME: "是否开启智能家居(1：开启，2：关闭)",
	CLIENT_ID:         "客户端id",
	OPEN_USER_ID:      "开放平台用户ID",
	OPEN_USER_NAME:    "开放平台用户ID",
	OPEN_CLIENT_TYPE:  "开放平台客户端类型",
	APP_ID:            "app产品ID",
}
