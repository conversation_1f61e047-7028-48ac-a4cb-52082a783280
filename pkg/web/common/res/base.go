package res

const SUCCESS_CODE = 0

type IBaseRes interface {
	Fail() bool
}

//type IBaseError interface {
//	IBaseRes
//	C() int
//	M() string
//}

// BRes 成功响应
type SRes[T any] struct {
	Code   int `json:"code"`
	Result T   `json:"result"`
}

func (r *SRes[T]) Fail() bool {
	return SUCCESS_CODE != r.Code
}

// Cb 创建响应create baseRes
func Cb[T any](res T) IBaseRes {
	return &SRes[T]{
		Code:   SUCCESS_CODE,
		Result: res,
	}
}

// Cbnd 创建响应create baseRes no data
func Cbnd() IBaseRes {
	return &SRes[any]{
		Code: SUCCESS_CODE,
	}
}

// ERes 异常响应
type ERes struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

func (e *ERes) Fail() bool {
	return true
}

func (e *ERes) C() int {
	return e.Code
}

func (e *ERes) M() string {
	return e.Msg
}

// Ce 创建基于通用错误code的自定义消息 错误响应
func Ce(constError *ERes, message string) *ERes {
	return &ERes{
		Code: constError.C(),
		Msg:  constError.M() + "-" + message,
	}
}

// ERes 创建错误响应
func CERes(code int, msg string) *ERes {
	return &ERes{
		Code: code,
		Msg:  msg,
	}
}
