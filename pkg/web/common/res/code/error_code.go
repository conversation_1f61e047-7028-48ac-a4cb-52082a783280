package code

import "piceacorp.com/device-service/pkg/web/common/res"

var ILLEGAL_PROTOCOL = &res.ERes{Code: 601, Msg: "无法解析的数据格式"}
var HTTP_ERROR = &res.ERes{Code: 602, Msg: "http错误码"}
var TO_LOGIN = &res.ERes{Code: 603, Msg: "请先登录"}
var NO_SERVICE = &res.ERes{Code: 604, Msg: "找不到可用服务，服务繁忙"}
var LOGIN_STATE = &res.ERes{Code: 605, Msg: "已经登录"}
var REJECT = &res.ERes{Code: 606, Msg: "错误次数太多，稍后尝试"}
var NO_TRACE_ID = &res.ERes{Code: 607, Msg: "缺少traceId"}
var FORBID_URI = &res.ERes{Code: 608, Msg: "禁止访问的URI"}
var NO_AUTH = &res.ERes{Code: 609, Msg: "未授权或者授权过期，请重新登录"}
var SECURITY_SIGN_ERROR = &res.ERes{Code: 610, Msg: "签名错误"}
var NOT_FOUND_DATA = &res.ERes{Code: 611, Msg: "数据异常"}
var PARAM_VALIDATE_FAIL = &res.ERes{Code: 612, Msg: "参数合法性校验失败"}
var INNER_SERVER_ERROR = &res.ERes{Code: 613, Msg: "服务内部异常"}
var CACHE_ERROR = &res.ERes{Code: 614, Msg: "缓存异常"}
var ILLEGAL_STATE = &res.ERes{Code: 615, Msg: "非法状态"}
var SERVICE_BUSY = &res.ERes{Code: 616, Msg: "服务器正在开小差，请稍后再试"}
var ILLEGAL_OPERATE = &res.ERes{Code: 617, Msg: "非法操作"}
var DUPLICATE_DATA = &res.ERes{Code: 618, Msg: "重复数据"}
var THIRD_PARTY_ERROR = &res.ERes{Code: 619, Msg: "第三方异常"}
var USERNAME_PWD_ERROR = &res.ERes{Code: 620, Msg: "用户名或者密码错误"}
var FORBID_USE = &res.ERes{Code: 621, Msg: "禁止使用"}
var USER_EXISTS = &res.ERes{Code: 622, Msg: "该用户已存在"}
var USER_NOT_EXISTS = &res.ERes{Code: 623, Msg: "该用户不存在"}
var USE_TOO_OFEN = &res.ERes{Code: 624, Msg: "操作过于频繁，请稍后再试"}
var NO_PERMISSION = &res.ERes{Code: 625, Msg: "没有相应的权限"}
var CAPTCHA_WRONG = &res.ERes{Code: 626, Msg: "验证码错误"}
var USERNAME_PWD_ERROR_FIVE = &res.ERes{Code: 627, Msg: "用户名或者密码错误过多，5分钟后再试"}
var USERNAME_PWD_ERROR_TEN = &res.ERes{Code: 628, Msg: "用户名或者密码错误过多，10分钟后再试"}
var USERNAME_PWD_ERROR_FIFTEEN = &res.ERes{Code: 629, Msg: "用户名或者密码错误过多，24小时后再试或联系客服解决"}
var DEVICE_NOT_REGISTERED = &res.ERes{Code: 1001, Msg: "设备未注册"}
var MAC_ALREADY_OCCUPIED = &res.ERes{Code: 1002, Msg: "MAC地址已被占用"}
var PRODUCT_MODE_CODE_MISMATCH = &res.ERes{Code: 1003, Msg: "产品型号代码不匹配"}
var DEVICE_KEY_VERIFY_FAILED = &res.ERes{Code: 1004, Msg: "设备密钥验证失败"}
var DEVICE_FORCE_UPGRADE = &res.ERes{Code: 1005, Msg: "设备需要强制升级"}
var DEVICE_DISABLED = &res.ERes{Code: 1006, Msg: "设备被禁用"}
