package time

import (
	"database/sql/driver"
	"fmt"
	"strconv"
	"time"
)

type Ptime time.Time

func (t Ptime) MarshalJSON() ([]byte, error) {
	// 转成时间戳并输出为 JSON 数字
	return []byte(fmt.Sprintf("%d", time.Time(t).UnixMilli())), nil
}

func (t *Ptime) UnmarshalJSON(b []byte) error {
	// 支持时间戳反序列化
	ts, err := strconv.ParseInt(string(b), 10, 64)
	if err != nil {
		return err
	}
	*t = Ptime(time.UnixMilli(ts))
	return nil
}

// Scan 实现 sql.Scanner 接口，用于 GORM 从数据库读取
func (pt *Ptime) Scan(value interface{}) error {
	if value == nil {
		*pt = Ptime(time.Time{})
		return nil
	}
	if t, ok := value.(time.Time); ok {
		*pt = Ptime(t)
		return nil
	}
	return nil
}

// Value 实现 driver.Valuer 接口，用于 GORM 写入数据库
func (pt Ptime) Value() (driver.Value, error) {
	t := time.Time(pt)
	if t.<PERSON><PERSON>() {
		return nil, nil
	}
	return t, nil
}

func (t *Ptime) Time() time.Time {
	return time.Time(*t)
}

// Now 创建当前时间的 Ptime
func Now() Ptime {
	return Ptime(time.Now())
}
