package time

import (
	"fmt"
	"strconv"
	"time"
)

type Ptime time.Time

func (t Ptime) MarshalJSON() ([]byte, error) {
	// 转成时间戳并输出为 JSON 数字
	return []byte(fmt.Sprintf("%d", time.Time(t).UnixMilli())), nil
}

func (t *Ptime) UnmarshalJSON(b []byte) error {
	// 支持时间戳反序列化
	ts, err := strconv.ParseInt(string(b), 10, 64)
	if err != nil {
		return err
	}
	*t = Ptime(time.UnixMilli(ts))
	return nil
}

func (t *Ptime) Time() time.Time {
	return time.Time(*t)
}
