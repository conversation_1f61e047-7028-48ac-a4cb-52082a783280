package middleware

import (
	"bytes"
	"errors"
	"github.com/go-playground/validator/v10"
	"io"
	"net"
	"net/http"
	"net/http/httputil"
	"os"
	"piceacorp.com/device-service/pkg/web/common/res/code"
	"runtime/debug"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/pkg/logger"
)

// 响应码常量
const (
	//参数绑定校验返回异常码与消息分隔符
	VALID_EINFO_ECODE_SEPARATOR = "|ec_"
)

var (
	default404Body  = "404 not found"
	default405Body  = "405 method not allowed"
	defaultHttpBody = map[int]string{404: default404Body, 405: default405Body}
)

// Logger 日志中间件
func LoggingAndRecovery() gin.HandlerFunc {
	return func(c *gin.Context) {
		// req log
		// 替换默认的 ResponseWriter
		blw := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = blw
		start := time.Now()
		path := c.Request.URL.Path
		var reqBody string
		reqBodyBytes, _ := c.GetRawData()
		reqBody = string(reqBodyBytes)
		// 重新设置请求体，供下游使用
		c.Request.Body = io.NopCloser(bytes.NewBuffer(reqBodyBytes))
		header := c.Request.Header
		proto := c.Request.Proto
		method := c.Request.Method
		httpStatus := c.Writer.Status()

		cost := time.Since(start)
		//logger.Infof("%d %s %s %s %s %d %v %s %s", cost, c.ClientIP(), proto, method, path, c.Writer.Status(), header, reqBody, string(blw.body.Bytes()))
		//if len(c.Errors) == 0 {
		//	log(cost, c, proto, method, path, httpStatus, header, reqBody, blw.body.Bytes())
		//}

		// error recover
		defer func() {
			// 处理验证客户端错误
			if len(c.Errors) > 0 {
				// 获取第一个错误
				err := c.Errors.Last().Err
				// 入参绑定认证异常
				if validationErrors, ok := err.(validator.ValidationErrors); ok {
					msg := validationErrors[0].Field()
					ecode := code.PARAM_VALIDATE_FAIL.Code
					// 返回参数验证失败响应
					msgAndCode := strings.Split(msg, VALID_EINFO_ECODE_SEPARATOR)
					if len(msgAndCode) == 2 {
						ecode, _ = strconv.Atoi(msgAndCode[1])
						msg = msgAndCode[0]
					}
					c.AbortWithStatusJSON(http.StatusOK, gin.H{
						"code": ecode,
						"msg":  msg,
					})
					//logger.Info("request end: ", cost, c.ClientIP(), proto, method, path, c.Writer.Status(), header, reqBody, string(blw.body.Bytes()))
				} else {
					// 其他客户端异常
					c.AbortWithStatusJSON(http.StatusOK, gin.H{
						"code": http.StatusBadRequest,
						"msg":  err.Error(),
					})
				}
				log(cost, c, proto, method, path, httpStatus, header, reqBody, blw.body.Bytes())
				return
			}
			// 处理服务器异常
			if err := recover(); err != nil {
				// 检查连接是否已断开
				var brokenPipe bool
				if ne, ok := err.(*net.OpError); ok {
					var se *os.SyscallError
					if errors.As(ne.Err, &se) {
						if strings.Contains(strings.ToLower(se.Error()), "broken pipe") ||
							strings.Contains(strings.ToLower(se.Error()), "connection reset by peer") {
							brokenPipe = true
						}
					}
				}

				httpRequest, _ := httputil.DumpRequest(c.Request, false)

				logger.Errorf("[Recovery from panic] %v\n%s%s", err, string(httpRequest), string(debug.Stack()))
				if brokenPipe {
					//logger.Error(c.Request.URL.Path,
					//	zap.Any("error", err),
					//	zap.String("request", string(httpRequest)),
					//)
					// 如果连接已断开，无法写入状态码
					c.Abort()
					return
				}
				c.AbortWithStatusJSON(http.StatusOK, gin.H{
					"code": 500,
					"msg":  "Internal Server Error",
				})
			}
			// 400异常处理
			if httpStatus > 399 {
				c.AbortWithStatusJSON(http.StatusOK, gin.H{
					"code": httpStatus,
					"msg":  defaultHttpBody[httpStatus],
				})
			}
			log(cost, c, proto, method, path, httpStatus, header, reqBody, blw.body.Bytes())
		}()

		// 继续后续请求
		c.Next()
	}
}

func log(cost time.Duration, c *gin.Context, proto, method, path string, httpStatus int, header http.Header, reqBody string, resBody []byte) {
	reqBody = strings.ReplaceAll(reqBody, "\r\n", "")
	logger.Infof("request end: %d - %s - %s - %s - %s - %d - %v - %s - %s", cost.Milliseconds(), c.ClientIP(), proto, method, path, httpStatus, header, reqBody, string(resBody))
}

type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w bodyLogWriter) Write(b []byte) (int, error) {
	// 记录响应数据
	w.body.Write(b)
	// 写入原始响应流
	return w.ResponseWriter.Write(b)
}
