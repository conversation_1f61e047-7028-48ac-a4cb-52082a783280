package validator

import (
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
	"reflect"
)

// InitValidator 设置验证器
func InitValidator() {
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		// 注册自定义错误信息处理
		v.RegisterTagNameFunc(func(fld reflect.StructField) string {
			// 提取异常信息
			einfo := fld.Tag.Get("bindError")
			//if name == "" {
			//	// 其次使用json标签
			//	name = strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
			//}
			if einfo == "" {
				return einfo
			}
			return einfo
		})

		// 这里可以注册自定义验证函数
		// 例如: v.RegisterValidation("customtag", customValidationFunc)
	}
}
