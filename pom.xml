<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.irobotics.aiot</groupId>
        <artifactId>parent-aiot</artifactId>
        <version>2023.0-SNAPSHOT</version>
    </parent>

    <groupId>com.irobotics.aiot</groupId>
    <artifactId>device-service</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>device-service</name>
    <description>device-service</description>


    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <mybatis-plus.version>*******</mybatis-plus.version>
        <bravo.version>2023.0.0-SNAPSHOT</bravo.version>
        <knife4j.version>4.0.0</knife4j.version>
        <jjwt.version>0.9.1</jjwt.version>
        <operate-record-service.version>2023.0.0-SNAPSHOT</operate-record-service.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.irobotics.sso</groupId>
            <artifactId>irobotics-sso-user</artifactId>
            <version>2023.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <!-- 引入加密服务 -->
        <dependency>
            <groupId>com.irobotics.aiot</groupId>
            <artifactId>encryption-service</artifactId>
            <version>2023.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.64</version>
        </dependency>
        <!--<dependency>
            <groupId>at.favre.lib</groupId>
            <artifactId>hkdf</artifactId>
            <version>1.1.0</version>
        </dependency>-->
        <dependency>
            <groupId>com.github.netricecake</groupId>
            <artifactId>hkdf</artifactId>
            <version>1.1</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/commons-codec/commons-codec -->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.15</version>
        </dependency>



        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <!--kafka-->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <!--knife4j swagger 聚合文档-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
            <version>${knife4j.version}</version>
        </dependency>
        <!--redis-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.irobotics.aiot.bravo</groupId>
            <artifactId>bravo-token-aiot</artifactId>
            <version>${bravo.version}</version>
        </dependency>
        <dependency>
            <groupId>com.irobotics.aiot.bravo</groupId>
            <artifactId>bravo-basic-aiot</artifactId>
            <version>${bravo.version}</version>
        </dependency>
        <dependency>
            <groupId>com.irobotics.aiot.bravo</groupId>
            <artifactId>bravo-web-aiot</artifactId>
            <version>${bravo.version}</version>
        </dependency>
        <dependency>
            <groupId>com.irobotics.aiot.bravo</groupId>
            <artifactId>bravo-context-aiot</artifactId>
            <version>${bravo.version}</version>
        </dependency>
        <dependency>
            <groupId>com.irobotics.aiot.bravo</groupId>
            <artifactId>bravo-feign-aiot</artifactId>
            <version>${bravo.version}</version>
        </dependency>
        <!-- 雪花算法 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.11</version>
        </dependency>
        <!-- 引入规范日志 -->
        <dependency>
            <groupId>com.irobotics.aiot.bravo</groupId>
            <artifactId>bravo-log-aiot</artifactId>
            <version>${bravo.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.28</version>
        </dependency>
        <!-- mysql-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!-- mybatis plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <!--健康检查-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!--引入nacos client依赖-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!--引入nacos config-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--引入sentinel-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!--引入openfeign-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
            <scope>compile</scope>
        </dependency>

        <!--引入sentinel 持久化到nacos-->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-nacos</artifactId>
        </dependency>

        <!--SpringBoot-test-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>jakarta.xml.bind</groupId>
                    <artifactId>jakarta.xml.bind-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
            <version>4.0.1</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.2.1</version>
        </dependency>

        <!--
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-mongodb</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-mongodb</artifactId>
            <version>3.3.5</version>
        </dependency>-->

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>${jjwt.version}</version>
        </dependency>

        <!--读取c库-->
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>5.10.0</version>
        </dependency>

        <dependency>
            <groupId>com.irobotics.aiot</groupId>
            <artifactId>operate-record-service</artifactId>
            <version>${operate-record-service.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>13.0</version>
            <scope>compile</scope>
        </dependency>

        <!--多数据源切换 https://baomidou.com/pages/a61e1b/#dynamic-datasource-->
        <!--<dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
            <version>4.1.0</version>
        </dependency>-->

        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-core</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>1.1.1</version>
        </dependency>

    </dependencies>


    <build>
        <!-- 资源打包 -->
        <resources>
            <resource>
                <directory>src/main/resources/config</directory>
                <excludes>
                    <exclude>bootstrap-*</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources/config</directory>
                <filtering>true</filtering>
                <includes>
                    <include>bootstrap.yml</include>
                    <include>bootstrap-${profiles.active}.yml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.dll</include>
                    <include>**/*.so</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <!-- https://gitee.com/roseboy/classfinal -->
                <groupId>net.roseboy</groupId>
                <artifactId>classfinal-maven-plugin</artifactId>
                <version>${classfinal.version}</version>
                <configuration>
                    <!--                    无密码模式-->
                    <password>#</password><!--加密打包之后pom.xml会被删除，不用担心在jar包里找到此密码-->
                    <packages>com.irobotics</packages>
                    <cfgfiles>*.yml,/mapper/*.xml</cfgfiles>
                    <excludes>org.spring</excludes>
                    <libjars>
                        bravo-*.jar,
                        encryption-service-*.jar,
                        irobotics-sso-*.jar,
                        sync-dependency-*.jar,
                        aiot-*.jar
                    </libjars>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>classFinal</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
