package com.irobotics.aiot.device.cache;

import com.irobotics.aiot.device.common.DeviceKey;
import com.irobotics.aiot.device.vo.BindKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 设备bindKey缓存类
 */
@Component
public class BindKeyCache {

    /**
     * 数字7
     */
    private static final Integer SEVEN = 7;

    /**
     * redis
     */
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 获取bindKey
     *
     * @param id
     * @param tenantId
     * @param deviceKey
     * @return
     */
    public BindKey getBindKey(String id, String tenantId, DeviceKey deviceKey) {
        return (BindKey) redisTemplate.opsForValue().get(redisKeyOfBindKey(id, tenantId, deviceKey));
    }

    /**
     * 生成缓存中的key格式为：
     * 1、正常配网的bindKey的格式：BIND_KEY:USER:BIND_KEY_用户id，表示key中有userId作为组成
     * 2、扫码绑定时的bindKey格式：BIND_KEY:DEVICE:BIND_KEY_设备id，表示key中有deviceId作为组成，因为扫码时，需要把设备id与bindKey放在一起，以便，扫码后绑定时，手机号与对应的设备进行绑定
     *
     * @param id        用户id或设备id
     * @param tenantId  企业id
     * @param deviceKey key的类型
     * @return
     */
    public String redisKeyOfBindKey(String id, String tenantId, DeviceKey deviceKey) {
        String name = deviceKey.name();
        String nameSpace = DeviceKey.BIND_KEY + ":" + name + ":";
        return nameSpace + DeviceKey.BIND_KEY + "_" + id + "_" + tenantId;
    }

    /**
     * 保存bindKey
     *
     * @param bindKey   需要保存的内容
     * @param id        设备id或用户id，deviceKey为user，则id为用户id，bindKey为device,则id为设备id
     * @param tenantId
     * @param deviceKey bindKey的类型，USER或DEVICE
     */
    public void saveBindKey(BindKey bindKey, String id, String tenantId, DeviceKey deviceKey) {
        redisTemplate.opsForValue().set(redisKeyOfBindKey(id, tenantId, deviceKey), bindKey, SEVEN, TimeUnit.DAYS);
    }

    /**
     * 指定过期时间保存bindKey
     *
     * @param bindKey
     * @param id
     * @param tenantId
     * @param deviceKey
     * @param ttl
     * @param timeUnit
     */
    public void saveBindKeyWithExpire(BindKey bindKey, String id, String tenantId, DeviceKey deviceKey, Long ttl, TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(redisKeyOfBindKey(id, tenantId, deviceKey), bindKey, ttl, timeUnit);
    }

    /**
     * 移除key
     * 缓存中的key格式为：
     * 1、正常配网的bindKey的格式：BIND_KEY:USER:BIND_KEY_用户id，表示key中有userId作为组成
     * 2、扫码绑定时的bindKey格式：BIND_KEY:DEVICE:BIND_KEY_设备id，表示key中有deviceId作为组成，因为扫码时，需要把设备id与bindKey放在一起，以便，扫码后绑定时，手机号与对应的设备进行绑定
     *
     * @param id        用户id或设备id
     * @param tenantId  企业id
     * @param deviceKey key的类型
     */
    public void removeBindKey(String id, String tenantId, DeviceKey deviceKey) {
        String key = redisKeyOfBindKey(id, tenantId, deviceKey);
        redisTemplate.delete(key);
    }
}
