package com.irobotics.aiot.device.cache;

import com.irobotics.aiot.device.common.DeleteRecordKey;
import com.irobotics.aiot.device.vo.DeleteResp;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @since 2022/1/12
 */
@Component
public class DeleteRecordCache {

    /**
     * 过期时间
     */
    private static final Integer EXPIRE_TIME = 60;

    /**
     * redis
     */
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 保存出厂初始化完成进度
     *
     * @param percent
     * @param taskId
     */
    public void putFinishPercent(Integer percent, String taskId) {
        redisTemplate.opsForValue().set(DeleteRecordKey.DELETE_RECORD_KEY + taskId + "-percent", percent, EXPIRE_TIME, TimeUnit.MINUTES);
    }

    /**
     * 获取出厂初始化进度
     *
     * @param taskId
     * @return
     */
    public Integer getFinishPercent(String taskId) {
        return (Integer) redisTemplate.opsForValue().get(DeleteRecordKey.DELETE_RECORD_KEY + taskId + "-percent");
    }

    /**
     * 保存设备出厂初始化结果
     *
     * @param taskId
     * @param result
     */
    public void putResult(String taskId, List<DeleteResp> result) {
        redisTemplate.opsForValue().set(DeleteRecordKey.DELETE_RECORD_KEY + taskId + "-result", result, EXPIRE_TIME, TimeUnit.MINUTES);
    }

    /**
     * 获取设备出厂初始化结果
     *
     * @param taskId
     * @return
     */
    public List<DeleteResp> getResult(String taskId) {
        return (List<DeleteResp>) redisTemplate.opsForValue().get(DeleteRecordKey.DELETE_RECORD_KEY + taskId + "-result");
    }
}
