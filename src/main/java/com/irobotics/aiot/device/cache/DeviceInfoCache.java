package com.irobotics.aiot.device.cache;

import com.irobotics.aiot.bravo.basic.exception.AppRuntimeException;
import com.irobotics.aiot.device.config.BloomFilterConfig;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.mapper.DeviceInfoMapper;
import com.irobotics.aiot.device.utils.BloomFilterHelper;
import com.irobotics.aiot.device.utils.RedisBloomFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2022/3/11 17:16
 */
@Component
public class DeviceInfoCache {

    public static final String DEVICE_ID_SN_BLOOM_FILTER_KEY = "bf:deviceInfo:id_sn";

    /**
     * 设备信息mapper
     */
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;

    /**
     * 布隆过滤器
     */
    @Autowired
    private RedisBloomFilter bloomFilter;

    /**
     * 布隆过滤器
     */
    @Autowired
    private BloomFilterHelper<String> filterHelper;

    /**
     * redis
     */
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 布隆配置
     */
    @Autowired
    private BloomFilterConfig bloomFilterConfig;


    /**
     * 通过设备id获取设备信息
     * 1、有缓存则获取缓存的数据，尽管缓存的是空值
     * 2、没有缓存，则查询布隆过滤器中有没有，没有则直接返回（代表没有该数据），布隆过滤器中记录的是设备id对应的位置
     * 3、布隆过滤器中存在，那么去查数据库
     *
     * @param tenantId 企业id
     * @param deviceId 设备id
     * @return
     */
    @Cacheable(value = "deviceInfo", key = "'device_info_id_'+#tenantId+'_'+#deviceId")
    public DeviceInfoEntity getCacheById(String tenantId, String deviceId) {
        if (bloomFilterConfig.getUsed()) {
            boolean exists = bloomFilter.includeByBloomFilter(filterHelper, DEVICE_ID_SN_BLOOM_FILTER_KEY, deviceId);
            if (!exists) {
                return null;
            }
        }
        return deviceInfoMapper.getById(tenantId, deviceId);
    }

    /**
     * 通过设备sn获取设备信息
     * 1、有缓存则获取缓存的数据
     * 2、没有缓存，则查询布隆过滤器有没有，没有则直接返回（代表没有数据），布隆过滤器中记录的是设备的sn
     * 3、布隆过滤器中存在，那么去查数据库
     *
     * @param tenantId
     * @param sn
     * @return
     */
    @Cacheable(value = "deviceInfo", key = "'device_info_sn_'+#tenantId+'_'+#sn")
    public DeviceInfoEntity getCacheBySn(String tenantId, String sn) {
        if (bloomFilterConfig.getUsed()) {
            boolean exists = bloomFilter.includeByBloomFilter(filterHelper, DEVICE_ID_SN_BLOOM_FILTER_KEY, sn);
            if (!exists) {
                return null;
            }
        }
        return deviceInfoMapper.getBySn(tenantId, sn);
    }

    /**
     * 清除设备缓存
     *
     * @param tenantId
     * @param deviceInfo
     */
    public void clear(String tenantId, DeviceInfoEntity deviceInfo) {
        String idKey = "deviceInfo::device_info_id_".concat(tenantId).concat("_").concat(deviceInfo.getId());
        String snKey = "deviceInfo::device_info_sn_".concat(tenantId).concat("_").concat(deviceInfo.getSn());
        List<String> keys = Arrays.asList(idKey, snKey);
        redisTemplate.delete(keys);
    }

    /**
     * 把设备id写进布隆过滤器中
     *
     * @param deviceId 设备id
     */
    public void addIdOrSnBloomFilter(String deviceId) {
        bloomFilter.addByBloomFilter(filterHelper, DEVICE_ID_SN_BLOOM_FILTER_KEY, deviceId);
    }

    /**
     * 删除设备的缓存
     *
     * @param deviceInfos
     */
    public void clearMore(List<DeviceInfoEntity> deviceInfos) {
        if (CollectionUtils.isEmpty(deviceInfos)) {
            return;
        }
        List<String> keys = getKeys(deviceInfos);
        redisTemplate.delete(keys);
    }

    /**
     * 获取多个缓存keys
     *
     * @param deviceInfos
     * @return
     */
    public static List<String> getKeys(List<DeviceInfoEntity> deviceInfos) {
        if (CollectionUtils.isEmpty(deviceInfos)) {
            throw new AppRuntimeException("设备信息不能为空");
        }
        List<String> list = new ArrayList<>();
        StringBuilder stringBuilderId = null;
        StringBuilder stringBuilderSn = null;
        String nameSpace = "deviceInfo::device_info_";
        for (DeviceInfoEntity device : deviceInfos) {
            if (Objects.isNull(device)) {
                continue;
            }
            stringBuilderId = new StringBuilder(nameSpace).append("id_").append(device.getTenantId()).append("_").append(device.getId());
            stringBuilderSn = new StringBuilder(nameSpace).append("sn_").append(device.getTenantId()).append("_").append(device.getSn());
            list.add(stringBuilderId.toString());
            list.add(stringBuilderSn.toString());
        }
        return list;
    }
}
