package com.irobotics.aiot.device.cache;

import com.irobotics.aiot.bravo.basic.exception.AppRuntimeException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class ProductSnDetailActiveCache {

    /**
     * 激活sn缓存前缀
     */
    private static final String nameSpace = "productSnDetailActive::";

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 去除sn缓存
     * @param sns
     */
    @Async
    public void removeCacheBySns(List<String> sns) {
        if(CollectionUtils.isEmpty(sns)){
            return;
        }
        List<String> keys = keys(sns);
        redisTemplate.delete(keys);
    }

    /**
     * 获取sn缓存key
     * @param sns
     * @return
     */
    public static List<String> keys(List<String> sns) {
        if (CollectionUtils.isEmpty(sns)) {
            throw new AppRuntimeException("sn列表不能为空");
        }
        List<String> keys = new ArrayList<>();
        StringBuilder builder = null;
        for (String sn : sns) {
            builder = new StringBuilder(nameSpace).append("productSnDetailActive_sn_").append(sn);
            keys.add(builder.toString());
        }
        return keys;
    }

    /**
     * 删除指定的key的缓存
     * @param key
     * @return
     */
    public boolean removeKey(String key){
        return redisTemplate.delete(key);
    }
}
