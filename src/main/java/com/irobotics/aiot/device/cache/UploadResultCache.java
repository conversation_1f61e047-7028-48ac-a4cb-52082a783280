package com.irobotics.aiot.device.cache;

import com.irobotics.aiot.device.common.UploadResultKey;
import com.irobotics.aiot.device.vo.IdSnUsernameResp;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @since 2022/1/13
 */
@Component
public class UploadResultCache {

    private static final Integer SIXTEEN = 60;

    /**
     * redis
     */
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 保存完成进度
     * @param percent
     * @param taskId
     */
    public void putFinishPercent(Integer percent, String taskId) {
        String taskPercent = taskId + "-percent";
        String key = UploadResultKey.UPLOAD_RESULT_KEY + taskPercent;
        redisTemplate.opsForValue().set(key, percent, SIXTEEN, TimeUnit.MINUTES);
    }

    /**
     * 获取完成进度
     * @param taskId
     * @return
     */
    public Integer getFinishPercent(String taskId) {
        String taskPercent = taskId + "-percent";
        String key = UploadResultKey.UPLOAD_RESULT_KEY + taskPercent;
        return (Integer) redisTemplate.opsForValue().get(key);
    }

    /**
     * 保存结果
     * @param taskId
     * @param result
     */
    public void putResult(String taskId, List<IdSnUsernameResp> result) {
        String taskResult = taskId + "-result";
        String key = UploadResultKey.UPLOAD_RESULT_KEY + taskResult;
        redisTemplate.opsForValue().set(key, result, SIXTEEN, TimeUnit.MINUTES);
    }

    /**
     * 获取结果
     * @param taskId
     * @return
     */
    public List<IdSnUsernameResp> getResult(String taskId) {
        String taskResult = taskId + "-result";
        String key = UploadResultKey.UPLOAD_RESULT_KEY + taskResult;
        return (List<IdSnUsernameResp>) redisTemplate.opsForValue().get(key);
    }
}
