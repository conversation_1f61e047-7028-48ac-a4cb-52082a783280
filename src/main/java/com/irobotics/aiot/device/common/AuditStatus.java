package com.irobotics.aiot.device.common;

public enum AuditStatus {

    NotAudit(0,"未审核"),
    Audited(1,"审核通过"),
    AuditFailed(2,"审核不通过"),
    AuditPartly(3,"部分审核通过");

    /**
     * 审核状态标识
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String desc;

    AuditStatus(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
