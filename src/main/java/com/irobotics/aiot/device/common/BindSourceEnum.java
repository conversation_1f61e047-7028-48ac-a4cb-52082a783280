package com.irobotics.aiot.device.common;



/**
 * @author:tlh
 * @create: 2023-07-14 17:24:57
 * @Description:
 */

public enum BindSourceEnum {

    SMART_HOME_BIND(1,"智能家居同步"),
    DEVICE_BIND(2,"直接绑定");

    private final Integer code;
    private final String msg;

    BindSourceEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
