package com.irobotics.aiot.device.common;

/**
 * @author:tlh
 * @create: 2023-09-15 10:45:30
 * @Description:
 */
public enum BindUserTypeEnum {

     SELF(1, "自建账号绑定关系"),
     OPEN_API(2, "第三方授权账号,绑定关系");

    /**
     * 标识码
     */
    private final Integer code;

    /**
     * 信息
     */
    private final String msg;

    BindUserTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
