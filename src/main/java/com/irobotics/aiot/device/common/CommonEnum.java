package com.irobotics.aiot.device.common;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/9/28 16:14
 */
@SuppressWarnings("ALL")
public enum CommonEnum {

    DELETE(-1, "已删除"),
    NORMAL(0, "正常，未删除"),
    LIKE_SEARCH(1,"模糊搜索"),
    EXACT_SEARCH(2,"精准搜索");

    /**
     * 标识码
     */
    private final Integer code;

    /**
     * 信息
     */
    private final String msg;

    CommonEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
