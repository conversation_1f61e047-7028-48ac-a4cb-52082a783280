package com.irobotics.aiot.device.common;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2021/10/9 16:42
 * @desc Constant
 */
@SuppressWarnings("ALL")
public class Constant {
    /**
     * 属性设置 MQTT Topic正则匹配
     * /mqtt/producetKey001/DF0102030405/thing/service/property/set
     */
    public static final Pattern propertySetPattern = Pattern.compile("(?<=/mqtt/).*?(?<=/).*?(?<=/thing/service/property/set)");
    /**
     * 属性上报 MQTT Topic正则匹配
     * /mqtt/producetKey001/DF0102030405/thing/event/property/post
     */
    public static final Pattern propertyPostPattern = Pattern.compile("(?<=/mqtt/).*?(?<=/).*?(?<=/thing/event/property/post)");
    /**
     * 事件上报 MQTT Topic正则匹配
     */
    public static final Pattern eventPostPattern = Pattern.compile("(?<=/mqtt/).*?(?<=/).*?(?<=/thing/event/).*?(?=/post)");
    /**
     * 服务调用 MQTT Topic正则匹配
     * /mqtt/producetKey001/DF0102030405/thing/service/SetWeight
     */
    public static final Pattern serviceInvokePattern = Pattern.compile("(?<=/mqtt/).*?(?<=/).*?(?<=/thing/service/).*");
    /**
     * 上下线状态 MQTT Topic正则匹配
     * $SYS/brokers/emqx@127.0.0.1/clients/45928c38-ebe6-4bce-a232-1c5d5e970e781633768535137/connected
     */
    public static final Pattern deviceStatusPattern = Pattern.compile("(?<=SYS/brokers).*?(?<=/).*?(?<=/clients/).*");


    /**
     * propertyPost
     */
    public static final String PROPERTY_POST_KAFKA_TOPIC = "propertyPost";
    /**
     * propertySet
     */
    public static final String PROPERTY_SET_KAFKA_TOPIC = "propertySet";
    /**
     * eventPost
     */
    public static final String EVENT_POST_KAFKA_TOPIC = "eventPost";
    /**
     * serviceInvoke
     */
    public static final String SERVICE_INVOKE_KAFKA_TOPIC = "serviceInvoke";
    /**
     * statusPost
     */
    public static final String STATUS_POST_KAFKA_TOPIC = "statusPost";

    /**
     * connectedFlag
     */
    public static final String connectedFlag = "connected_at";

    /**
     * disconnectedFlag
     */
    public static final String disconnectedFlag = "disconnected_at";

    /**
     * OTA升级消息推送
     */
    public static final String MQTT_BASE_TOPIC = "/mqtt/";

    /**
     * 正斜杠分隔符
     */
    public static final String BASE_PREF = "/";

    /**
     * 发送 OTA升级消息 MQTT Topic
     */
    public static final String UPGRADE_SET_TOPIC = "upgradeSet";
    /**
     * 发送 OTA升级消息 方法名称
     */
    public static final String MOTHOD_UPGRADE_SET = "ota.upgrade.set";

    /**
     * 发送 OTA升级消息 MQTT Topic
     */
    public static final String UPGRADE_GET_TOPIC = "upgradeGet";
    /**
     * 发送 OTA升级消息 方法名称
     */
    public static final String MOTHOD_UPGRADE_GET = "ota.upgrade.get";

    /**
     * OTA 升级进度上报
     */
    public static final String UPGRADE_POST_TOPIC = "upgradePost";

    /**
     * 发送OTA需升级的设备集合
     */
    public static final String UPGRADE_DEVICE_LIST = "upgradeDeviceList";

    /**
     * 发送检测设备是否需升级
     */
    public static final String UPGRADE_DEVICE_CHECK = "upgradeDeviceCheck";

    /**
     * 设备分享标识（0、正常）
     */
    public static final String DEVICE_FLAG_0 = "0";

    /**
     * 设备分享标识（1、分享设备）
     */
    public static final String DEVICE_FLAG_1 = "1";

    /**
     * 分享状态（0、未分享）
     */
    public static final String DEVICE_SHARE_STATUS_0 = "0";

    /**
     * 分享状态（1、分享）
     */
    public static final String DEVICE_SHARE_STATUS_1 = "1";

    public static final String MES_DEVICE_UNBIND_FAIL = "批量解绑设备失败";

    public static final String MES_DEVICE_NOTEXIST = "设备不存在或尚未注册到服务器";

    /**
     * 杉川企业id
     */
    public static final String ADMIN_TENANT_ID = "0";
}
