package com.irobotics.aiot.device.common;

/**
 * 设备推送标记
 */
@SuppressWarnings("ALL")
public enum DevicePushTag {

    ON_LINE, //在线
    OFF_LINE, //离线
    SHARE_ROBOT, //分享设备
    UPGRADE, //升级
    UNBIND, //解除绑定
    CONTROL, //控制设备
    RESET; //重置

    /**
     * 标记
     */
    private final String tag;

    DevicePushTag() {
        this.tag = this.name().toLowerCase();
    }

    public String getTag() {
        return tag;
    }
}
