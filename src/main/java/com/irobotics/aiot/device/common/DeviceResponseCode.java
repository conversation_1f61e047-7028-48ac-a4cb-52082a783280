package com.irobotics.aiot.device.common;

import com.irobotics.aiot.bravo.basic.constant.IResponseCode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description
 * @since 2022/1/13
 */
@AllArgsConstructor
public enum DeviceResponseCode implements IResponseCode {
    EXCEL_ERROR(700, "excel解析失败"),

    DEVICE_SAME_NICKNAME_ERROR(701, "设备昵称不能相同"),

    DEVICE_ALREADY_SHARED(705, "不可重复共享给同一用户"),

    SHARE_SELF(685, "自己不可共享自己");
    /**
     * 错误码
     */
    @Getter
    @Setter
    private final int code;
    /**
     * 错误信息
     */
    @Getter
    @Setter
    private final String msg;

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
