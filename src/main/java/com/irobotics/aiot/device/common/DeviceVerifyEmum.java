package com.irobotics.aiot.device.common;

import java.util.HashMap;
import java.util.Map;

public enum DeviceVerifyEmum {

    OPEN(1, "开启"),
    CLOSE(0, "关闭"),
    ;

    private final Integer code;

    private final String msg;

    DeviceVerifyEmum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static Map<Integer, String> map() {
        DeviceVerifyEmum[] values = DeviceVerifyEmum.values();
        Map<Integer, String> map = new HashMap<>(values.length);
        for (DeviceVerifyEmum value : values) {
            map.put(value.code, value.msg);
        }
        return map;
    }

    public static boolean check(Integer verify) {
        return map().containsKey(verify);
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
