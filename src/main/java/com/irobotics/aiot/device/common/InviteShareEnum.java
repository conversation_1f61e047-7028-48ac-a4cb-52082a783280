package com.irobotics.aiot.device.common;

/**
 * 邀请共享枚举类
 */
@SuppressWarnings("ALL")
public enum InviteShareEnum {
    ENABLE(0, "正常的分享状态"),

    AGREED(1, "同意分享"),
    REJECTED(2, "拒绝分享"),

    DISABLE(1, "邀请方删除"), // 邀请方删除
    REMOVED(2, "被邀请方删除"), // 被邀请方删除

    DEVICE(0, "共享设备"),
    FAMILY(1, "共享家庭"),
    ;

    /**
     * 状态
     */
    private final int status;
    /**
     * 消息
     */
    private final String msg;

    InviteShareEnum(int status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public int getStatus() {
        return status;
    }

    public String getMsg() {
        return msg;
    }
}
