package com.irobotics.aiot.device.common;

/**
 *日志类型枚举类
 */
@SuppressWarnings("ALL")
public enum LogEnum {

    BIND("1","绑定"),
    UNBIND("-1","解绑"),
    SELF("1","自身"),
    SELFLESS("2","管理后台"),
    LOGIN_SUCCESS("0","登录成功"),
    LOGIN_FAIL("-1","登录失败"),
    SHARE_BIND("2", "分享绑定"),
    SHARE_UNBIND("-2", "分享解绑")
    ;

    /**
     * 日志标识
     */
    private final String code;
    /**
     * 信息
     */
    private final String msg;

    LogEnum(String code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
