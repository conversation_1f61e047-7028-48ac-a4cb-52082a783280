package com.irobotics.aiot.device.common;

/**
 * 数字枚举
 */
public enum NumberEnum {
    ONE(1),
    TWO(2),
    THRE<PERSON>(3),
    FOUR(4),
    FIVE(5),
    SIX(6),
    <PERSON>VE<PERSON>(7),
    EIGHT(8),
    NIGHT(9),
    <PERSON><PERSON>(10),
    T<PERSON><PERSON><PERSON><PERSON>(13),
    <PERSON><PERSON><PERSON><PERSON>(16),
    TWEN<PERSON>(20),
    TWENTY_SIX(26),
    TWENTY_FOUR(24),
    THIRTY_TWO(32),
    THIRTY_THREE(33),
    SIXTY_FOUR(64),
    SIXTY_FIVE(65),
    NINETY_ONE(91),

    ;

    /**
     * 数字
     */
    private int number;

    NumberEnum(int number) {
        this.number = number;
    }

    public int getNumber() {
        return number;
    }
}
