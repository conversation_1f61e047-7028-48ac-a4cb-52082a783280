package com.irobotics.aiot.device.common;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分页
 */
@Data
public class PageGetReq {

    /**
     * 默认的每页条数大小
     */
    private static final Integer DEFAULT_PAGE_SIZE = 30;

    /**
     * 页码
     */
    @Schema(description = "页码，默认第一页")
    private Integer page = 1;

    /**
     * 每页条数
     */
    @Schema(description = "每页条数，默认30条")
    private Integer pageSize = DEFAULT_PAGE_SIZE;

}
