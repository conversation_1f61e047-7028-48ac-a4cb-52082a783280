package com.irobotics.aiot.device.common;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 *@description 用户端标识
 *<AUTHOR>
 *@since 2022/10/9
 */
@AllArgsConstructor
public enum UserClientAgentEnum {
    /**
     * 非基站（Android、IOS、Andipad、IOSipad、Mini、Web、M）
     */
    NON_BASE("0", "非基站"),

    /**
     * 基站
     */
    BASE("1", "基站");

    @Getter
    @Setter
    private String type;
    @Getter
    @Setter
    private String desc;
}
