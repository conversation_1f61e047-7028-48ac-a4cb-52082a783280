package com.irobotics.aiot.device.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 布隆过滤器配置类
 */
@Component
@RefreshScope
public class BloomFilterConfig {

    /**
     * 是否初始化布隆过滤器
     */
    @Value("${device.bloom.init:false}")
    private boolean init;

    /**
     * 是否开启使用布隆过滤器
     */
    @Value("${device.bloom.used:false}")
    private boolean used;

    /**
     * 初始化布隆过滤器时，查询数据库每页的条数
     */
    @Value("${user.bloom.pageSize:300}")
    private Integer pageSize;

    public boolean getInit() {
        return init;
    }

    public boolean getUsed() {
        return used;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setInit(boolean init) {
        this.init = init;
    }

    public void setUsed(boolean used) {
        this.used = used;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
