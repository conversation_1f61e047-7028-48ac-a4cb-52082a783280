package com.irobotics.aiot.device.config;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.service.IDeviceInfoService;
import com.irobotics.aiot.device.utils.BloomFilterHelper;
import com.irobotics.aiot.device.utils.RedisBloomFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 布隆过滤器初始化配置
 */
@Component
@Slf4j
public class BloomFilterInitConfig {

    @Autowired
    private BloomFilterConfig bloomFilterConfig;

    @Autowired
    private IDeviceInfoService deviceInfoService;

    @Autowired
    private RedisBloomFilter bloomFilter;

    @Autowired
    private BloomFilterHelper<String> filterHelper;

    /**
     * 项目启动的时候，加载设备的sn和id到布隆过滤器中，启动之后，把device.bloom.init置为false，后续不再需要
     * 若清除了缓存，那么还是要开启的
     */
    @PostConstruct
    public void initDeviceInfoInBloomFilter() {
        if (bloomFilterConfig.getInit() && bloomFilterConfig.getUsed()) {
            Integer pageSize = bloomFilterConfig.getPageSize();
            log.info("开始把设备id、sn信息放到布隆过滤器中");
            Long start = System.currentTimeMillis();
            Integer page = 1;
            Long total = deviceInfoService.getCount();
            if (total <= 0) {
                return;
            }
            Long size = total < pageSize ? total : pageSize;
            Long pageCount = total / size + 1;
            for (int i = 0; i < pageCount; i++) {
                Page<DeviceInfoEntity> idPages = deviceInfoService.getDevicePage(page + i, pageSize);
                List<String> sns = idPages.getRecords().stream().filter(f -> Objects.nonNull(f)).map(m -> m.getSn()).collect(Collectors.toList());
                List<String> ides = idPages.getRecords().stream().filter(f -> Objects.nonNull(f)).map(m -> m.getId()).collect(Collectors.toList());
                bloomFilter.addMoreByBloomFilter(filterHelper, DeviceInfoCache.DEVICE_ID_SN_BLOOM_FILTER_KEY, ides);
                bloomFilter.addMoreByBloomFilter(filterHelper, DeviceInfoCache.DEVICE_ID_SN_BLOOM_FILTER_KEY, sns);
            }
            log.info("设备id、sn信息放到布隆过滤器完毕，数量{}， 花费:{}ms", total, (System.currentTimeMillis() - start));
        }
    }
}
