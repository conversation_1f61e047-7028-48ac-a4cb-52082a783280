package com.irobotics.aiot.device.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 第三方绑定关系配置
 */
@Component
@RefreshScope
public class DeviceBindConfig {

    /**
     * 是否同步绑定关系到mongo
     */
    @Value("${user.device.relation:false}")
    private boolean relation;

    public boolean getRelation() {
        return relation;
    }

    public void setRelation(boolean relation) {
        this.relation = relation;
    }
}
