package com.irobotics.aiot.device.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 设备相关的配置
 */
@Component
@RefreshScope
public class DeviceConfig {

    /**
     * 配网成功后是否移除bindKey，默认移除
     */
    @Value("${device.bindKey.remove:true}")
    private Boolean remove;

    /**
     * 配网成功后，如果不移除bindKey，指定bindKey的过期时间，单位为秒，默认120
     */
    @Value("${device.bindKey.ttl:120}")
    private Long ttl;

    public Boolean getRemove() {
        return remove;
    }

    public void setRemove(Boolean remove) {
        this.remove = remove;
    }

    public Long getTtl() {
        return ttl;
    }

    public void setTtl(Long ttl) {
        this.ttl = ttl;
    }
}
