package com.irobotics.aiot.device.config;

import com.irobotics.aiot.bravo.log.LogUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/10/11 18:00
 */
@Component
@ConfigurationProperties(prefix = "emq.auth.jwt")
@RefreshScope
public class EMQConfig {

    /**
     * 密钥
     */
    private String secret;

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getSecret() {
        if (StringUtils.isEmpty(secret)) {
            LogUtils.error("配置缺失 emq.auth.jwt.secret");
        }
        return secret;
    }
}
