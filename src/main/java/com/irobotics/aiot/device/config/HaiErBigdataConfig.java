package com.irobotics.aiot.device.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/19
 */
@Data
@Component
@ConfigurationProperties(prefix = "haier.auth")
@RefreshScope
public class HaiErBigdataConfig {

    /**
     * 海尔大数据接口服务地址，不带接口地址
     */
    private String url;
    /**
     * appid  业务接口携带
     */
    private String appId;
    /**
     * appKey  业务接口携带
     */
    private String appKey;
    /**
     * 登录用户名。获取usertoken
     */
    private String username;
    /**
     * 登录密码。获取usertoken
     */
    private String password;
    /**
     * 制定租户id-海尔
     */
    private String tenantId;
}
