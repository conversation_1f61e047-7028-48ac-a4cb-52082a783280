package com.irobotics.aiot.device.config;


import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Configuration
public class MybatisPlusConfig {

    @Value("${tenant.idColumn:tenant_id}")
    private String tenantIdColumn;
    @Value("${tenant.tablePre:t_device_}")
    private String tenantTablePre;
    @Value("${tenant.enbale:false}")
    private boolean tenantEnable;

    @Value("${mybatis-plus.page.limit:50000}")
    private Long limit;
    @Value("${tenant.tenantIgnoreTable}")
    private List<String> tenantIgnoreTable;

    /**
     * 分页插件
     */
    // 创建PaginationInterceptor拦截器     其实实现方式是基于分页插件进行实现的
    @Bean
    public MybatisPlusInterceptor paginationInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        //多租户配置
        if (tenantEnable) {
            tenantConfig(interceptor);
        }
        PaginationInnerInterceptor page = new PaginationInnerInterceptor();
        page.setDbType(DbType.MYSQL);
        //优化掉 join,false 不优化
        page.setOptimizeJoin(false);
        // 溢出总页数后是否进行处理,true返回首页，false 继续请求
        page.setOverflow(false);
        // 最大分页限制
        // page.setMaxLimit();
        interceptor.addInnerInterceptor(page);
        return interceptor;
    }

    /**
     * 租户配置
     *
     * @param interceptor
     */
    private void tenantConfig(MybatisPlusInterceptor interceptor){
        interceptor.addInnerInterceptor(new TenantLineInnerInterceptor(new TenantLineHandler() {
            @Override
            public Expression getTenantId() {
                Object contextValue = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
                if (Objects.isNull(contextValue)) {
                    LogUtils.error("MybatisPlusConfig >> 缺失租户id（厂商id）");
                }
                return Objects.nonNull(contextValue) ? new StringValue(contextValue.toString()) : null;
            }

            // 这是 default 方法, false 表示所有表都需要拼多租户条件,true表示不添加
            @Override
            public boolean ignoreTable(String tableName) {
                if (tenantIgnoreTable.contains(tableName)) {
                    return true;
                }
                return !tableName.startsWith(tenantTablePre);
            }
        }));
    }

}
