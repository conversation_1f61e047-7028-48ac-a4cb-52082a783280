package com.irobotics.aiot.device.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2022/3/28 14:33
 */
@Component
@RefreshScope
@ConfigurationProperties(prefix = "spring.redis")
public class RedisConfig {

    /**
     * 默认过期时间
     */
    private static final Integer DEFAULT_TTL = 5;

    /**
     * 缓存默认过期时间
     */
    private long ttl = DEFAULT_TTL;

    public long getTtl() {
        return ttl;
    }

    public void setTtl(long ttl) {
        this.ttl = ttl;
    }
}
