package com.irobotics.aiot.device.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/9/8 17:57
 */
@Component
@ConfigurationProperties(prefix = "snowflake")
@Data
@RefreshScope
public class SnowflakeConfig {

    /**
     * workId
     */
    private Long workId;

    /**
     * dataCenterId
     */
    private Long dataCenterId;
}
