package com.irobotics.aiot.device.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


/**
 * swagger配置类
 */
@SuppressWarnings("ALL")
@Configuration
public class SwaggerConfiguration implements WebMvcConfigurer {

    @Bean
    public OpenAPI customOpenAPI() {
        Contact contact = new Contact();
        contact.setName("杉川机器人");
        contact.setUrl("http://dev-sz-cn-apidocaiot.3irobotix.net:30001/doc.html");
        contact.setEmail("<EMAIL>");
        return new OpenAPI()
                .info(new Info()
                        .title("设备相关服务(device-service)")
                        .description("<div style='font-size:16px;color:red;'>设备相关服务(device-service)</div>")
                        .termsOfService("device-service")
                        .contact(contact)
                        .version("1.0")
                        .description("设备相关服务(device-service)")
                        .license(new License().name("Apache 2.0")
                                .url("http://dev-sz-cn-apidocaiot.3irobotix.net:30001/doc.html")));
    }
}
