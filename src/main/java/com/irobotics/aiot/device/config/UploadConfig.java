//package com.irobotics.aiot.device.config;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.multipart.MultipartResolver;
//import org.springframework.web.multipart.commons.CommonsMultipartResolver;
//
///**
// * <AUTHOR>
// * @description
// * @since 2022/1/13
// */
//@Configuration
//public class UploadConfig {
//
//    /**
//     * 内存最大的大小
//     */
//    private static final Integer MAX_IN_MEMORY_SIZE = 40960;
//
//    /**
//     * 最大上传大小
//     */
//    private static final Integer MAX_UPLOAD_SIZE = 5 * 1024 * 1024 * 100 * 2;
//
//    /**
//     * 显示声明CommonsMultipartResolver为mutipartResolver
//     *
//     * @return mutipartResolver
//     */
//    @Bean(name = "multipartResolver")
//    public MultipartResolver multipartResolver() {
//        CommonsMultipartResolver resolver = new CommonsMultipartResolver();
//        resolver.setDefaultEncoding("UTF-8");
//        //resolveLazily属性启用是为了推迟文件解析，以在在UploadAction中捕获文件大小异常
//        resolver.setResolveLazily(true);
//        resolver.setMaxInMemorySize(MAX_IN_MEMORY_SIZE);
//        //上传文件大小 5M 5*1024*1024
//        //1GB
//        resolver.setMaxUploadSize(MAX_UPLOAD_SIZE);
//        return resolver;
//    }
//}
