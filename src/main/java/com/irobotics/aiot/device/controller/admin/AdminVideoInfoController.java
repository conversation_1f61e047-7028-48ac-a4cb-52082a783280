package com.irobotics.aiot.device.controller.admin;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.entity.DeviceVideoInfoEntity;
import com.irobotics.aiot.device.service.IDeviceVideoInfoService;
import com.irobotics.aiot.device.utils.TenantIdHandleUtil;
import com.irobotics.aiot.device.vo.admin.AdminDeleteDeviceVideoInfoReq;
import com.irobotics.aiot.device.vo.admin.AdminDeviceBindVo;
import com.irobotics.aiot.device.vo.admin.AdminDeviceVideoInfoReq;
import com.irobotics.aiot.operate.annotation.OperateRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.PARAM_VALIDATE_FAIL;

/**
 * <p>
 *  主要是处理阿里云lv视频流相关业务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-25
 */
@Tag(name =  "admin-调用获取视频流接口")
@RestController
@RequestMapping("admin/video/")
public class AdminVideoInfoController {

    @Autowired
    private IDeviceVideoInfoService deviceVideoInfoService;


    @ApiLog(description = "上传视频三元组信息")
    @Operation(summary = "上传视频三元组信息")
    @OperateRecord(moduleName = "上传视频三元组信息", operateName = "上传视频三元组信息")
    @PostMapping("upload")
    public ResponseMessage<String> upload(MultipartFile file, @RequestParam String paramTenantId) {
        TenantIdHandleUtil.modifyHeaderTenantId(paramTenantId);
        if (StringUtils.isBlank(paramTenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "tenantId");
        }
        //优先使用参数的企业id
        TenantIdHandleUtil.handleTenantId(paramTenantId, false);
        return deviceVideoInfoService.upload(file,paramTenantId);
    }



    @ApiLog(description = "修改三元组信息")
    @Operation(summary = "修改三元组信息")
    @OperateRecord(moduleName = "修改三元组信息", operateName = "修改三元组信息")
    @PostMapping("update")
    public ResponseMessage<Boolean> update(@RequestBody DeviceVideoInfoEntity videoInfoEntity) {
        TenantIdHandleUtil.handleTenantId(videoInfoEntity.getTenantId(), false);
        if (StringUtils.isBlank(videoInfoEntity.getTenantId())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "tenantId");
        }
        if (StringUtils.isBlank(videoInfoEntity.getDeviceName())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceName");
        }
        if (StringUtils.isBlank(videoInfoEntity.getDeviceSecret())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceSecret");
        }
        if (StringUtils.isBlank(videoInfoEntity.getProductId())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "productId");
        }
        if (StringUtils.isBlank(videoInfoEntity.getProductKey())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "productKey");
        }
        return deviceVideoInfoService.update(videoInfoEntity);
    }

    @ApiLog(description = "删除三元组信息")
    @Operation(summary = "删除三元组信息")
    @OperateRecord(moduleName = "删除三元组信息", operateName = "删除三元组信息")
    @DeleteMapping("delete")
    public ResponseMessage<Boolean> delete(@RequestBody AdminDeleteDeviceVideoInfoReq req) {
        TenantIdHandleUtil.handleTenantId(req.getTenantId(), false);
        if (CollectionUtils.isEmpty(req.getIdList())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "idList");
        }
        return deviceVideoInfoService.delete(req.getIdList());
    }

    @ApiLog(description = "获取三元组信息列表")
    @Operation(summary = "获取三元组信息列表")
    @OperateRecord(moduleName = "获取三元组信息列表", operateName = "获取三元组信息列表")
    @PostMapping("page")
    public ResponseMessage<Page<DeviceVideoInfoEntity>> page(@RequestBody AdminDeviceVideoInfoReq req) {
        if (StringUtils.isBlank(req.getTenantId())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "tenantId");
        }
        TenantIdHandleUtil.handleTenantId(req.getTenantId(), false);
        return deviceVideoInfoService.page(req);
    }

}
