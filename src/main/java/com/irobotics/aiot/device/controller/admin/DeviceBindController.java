package com.irobotics.aiot.device.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.service.IDeviceBindUserService;
import com.irobotics.aiot.device.utils.TenantIdHandleUtil;
import com.irobotics.aiot.device.vo.admin.AdminDeviceBindReq;
import com.irobotics.aiot.device.vo.admin.AdminDeviceBindVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.PARAM_VALIDATE_FAIL;

/**
 * @author:tlh
 * @create: 2023-07-06 11:21:46
 * @Description: 整合用户后设备绑定关系
 */
@Tag(name  = "admin-整合用户后设备绑定关系")
@RestController
@RequestMapping("/admin/bind/")
public class DeviceBindController {


    @Autowired
    private IDeviceBindUserService deviceBindUserService;

    @ApiLog(description = "查询设备绑定信息列表")
    @Operation(summary = "查询设备绑定信息列表")
    @PostMapping(value = "/page")
    public ResponseMessage<Page<AdminDeviceBindVo>> page(@RequestBody AdminDeviceBindReq req){
        if (StringUtils.isBlank(req.getTenantId())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "tenantId");
        }
        //优先使用参数的企业id
        TenantIdHandleUtil.handleTenantId(req.getTenantId(), false);
        return deviceBindUserService.getPage(req);
    }

}
