package com.irobotics.aiot.device.controller.admin;


import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.basic.exception.ParameterValidateException;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.common.Constant;
import com.irobotics.aiot.device.common.DevicePushTag;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.entity.DeviceShadowEntity;
import com.irobotics.aiot.device.remote.DeviceShadowRemote;
import com.irobotics.aiot.device.service.DeviceControlService;
import com.irobotics.aiot.device.service.IDeviceInfoService;
import com.irobotics.aiot.device.service.KafkaService;
import com.irobotics.aiot.device.utils.BloomFilterHelper;
import com.irobotics.aiot.device.utils.ExcelUtil;
import com.irobotics.aiot.device.utils.RedisBloomFilter;
import com.irobotics.aiot.device.utils.TenantIdHandleUtil;
import com.irobotics.aiot.device.vo.DeviceControlReq;
import com.irobotics.aiot.device.vo.DeviceInfoPageGetReq;
import com.irobotics.aiot.device.vo.DeviceUserPageReq;
import com.irobotics.aiot.device.vo.excel.DeviceInfoVo;
import com.irobotics.aiot.operate.annotation.OperateRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.*;

/**
 * <AUTHOR>
 * @since 2021-09-16
 */
@SuppressWarnings("Duplicates")
@Tag(name = "admin-设备信息管理")
@RestController
@RequestMapping("/info")
public class DeviceInfoController {

    /**
     * 企业id常量
     */
    private static final String TENANT_ID = "tenantId";

    @Autowired
    private IDeviceInfoService deviceInfoService;

    @Autowired
    private DeviceShadowRemote deviceShadowRemote;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private DeviceControlService deviceControlService;

    @Autowired
    private DeviceInfoCache deviceInfoCache;

    @Autowired
    private RedisBloomFilter bloomFilter;

    @Autowired
    private BloomFilterHelper<String> filterHelper;

    @ApiLog(description = "查询设备信息列表")
    @Operation(summary = "查询设备信息列表。语言国际化，头部参数传递Accept-Language，值为zh_CN为中文，值为en_US为英文")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @PostMapping(value = "/page")
    public ResponseMessage<Page<DeviceInfoEntity>> devicePage(@RequestBody DeviceInfoPageGetReq pageGetReq) {
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId) && StringUtils.isBlank(pageGetReq.getTenantId())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        //优先使用参数的企业id
        TenantIdHandleUtil.handleTenantId(pageGetReq.getTenantId(), false);
        Page<DeviceInfoEntity> page = deviceInfoService.pageList(pageGetReq);
        return ResponseMessage.buildSuccess(page);
    }

    @ApiLog(description = "根据过滤条件导出设备信息")
    @Operation(summary = "根据过滤条件导出设备信息")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @PostMapping("/exportDeviceInfo")
    public void exportDeviceInfo(@RequestBody DeviceInfoPageGetReq pageGetReq,HttpServletResponse response) {
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId) && StringUtils.isBlank(pageGetReq.getTenantId())) {
            throw new ParameterValidateException("参数合法性校验失败:tenantId");
        }
        //优先使用参数的企业id
        TenantIdHandleUtil.handleTenantId(pageGetReq.getTenantId(), false);
        List<DeviceInfoVo> vo = deviceInfoService.exportDeviceInfo(pageGetReq);

        /*String fileName = LocalDate.now()+"_indexInfo";
        ExcelUtil.writeExcel(response,vo,
                fileName,"", DeviceInfoVo.class);*/

        deviceInfoService.exportSearchInfo(vo,response);
    }

    @ApiLog(description = "触发系统升级，返回false表示不在线")
    @Operation(summary = "触发系统升级,返回 false 表示不在线。语言国际化，头部参数传递Accept-Language，值为zh_CN为中文，值为en_US为英文")
    @GetMapping(value = "/trigger/upgrade")
    @OperateRecord(moduleName = "设备信息管理", operateName = "触发系统升级")
    public ResponseMessage<Boolean> triggerUpgrade(String deviceId) {
        if (StringUtils.isBlank(deviceId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceId");
        }
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheById(tenantId, deviceId);
        if (Objects.isNull(deviceInfo)) {
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA);
        }
        if (!deviceInfoService.ifOnline(deviceInfo.getSn())) {
            return ResponseMessage.buildSuccess(false);
        }
        //触发升级 ，发送升级命令
        String content = "{\"command\":\"upgrade\"}";
        kafkaService.senServiceInvoke(deviceId, tenantId, content, DevicePushTag.UPGRADE, Constant.SERVICE_INVOKE_KAFKA_TOPIC);
        return ResponseMessage.buildSuccess(true);
    }

    @ApiLog(description = "设备重置，只会对在线设备起作用，若操作的所有设备都是离线设备，则会返回code=17，非法操作")
    @Operation(summary = "设备重置，只会对在线设备起作用，若操作的所有设备都是离线设备，则会返回code=17，非法操作。语言国际化，头部参数传递Accept-Language，值为zh_CN为中文，值为en_US为英文")
    @PostMapping(value = "reset")
    @OperateRecord(moduleName = "设备信息管理", operateName = "设备重置")
    public ResponseMessage<Boolean> deviceReset(@RequestBody List<String> deviceIds) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceIds");
        }
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }

        List<DeviceInfoEntity> deviceInfos = deviceInfoService.getDevicesByIds(deviceIds);

        //key-sn，value-id
        Map<String, String> deviceIdAndSn = deviceInfos.stream().collect(Collectors.toMap(DeviceInfoEntity::getSn, DeviceInfoEntity::getId));

        List<String> sns = deviceInfos.stream().map(DeviceInfoEntity::getSn).collect(Collectors.toList());
        ResponseMessage<List<DeviceShadowEntity>> response = deviceShadowRemote.findAllBySN(sns);
        List<DeviceShadowEntity> shadows = response.getResult();
        if (CollectionUtils.isEmpty(shadows)) {
            LogUtils.error("deviceIds = {} is offline", deviceIds);
            return ResponseMessage.buildFail(ILLEGAL_OPERATE);
        }
        //在线设备
        List<DeviceShadowEntity> onLine = shadows.stream().filter(deviceShadowEntity -> deviceShadowEntity.getOnlineStatus()).collect(Collectors.toList());
        //离线设备
        List<DeviceShadowEntity> offLine = shadows.stream().filter(deviceShadowEntity -> deviceShadowEntity.getOnlineStatus()).collect(Collectors.toList());
        LogUtils.info("deviceReset >> online={}, offLine={}", JSONUtil.toJsonStr(onLine), JSONUtil.toJsonStr(offLine));

        if (CollectionUtils.isEmpty(onLine)) {
            LogUtils.error("deviceIds = {} is offline", deviceIds);
            return ResponseMessage.buildFail(ILLEGAL_OPERATE);
        }

        //在线设备的id
        List<String> deviceIdsOnline = new ArrayList<>();
        onLine.stream().forEach((DeviceShadowEntity item) -> {
            if (deviceIdAndSn.containsKey(item.getId())) {
                deviceIdsOnline.add(deviceIdAndSn.get(item.getId()));
            }
        });

        String content = "{\"command\":\"reset\"}";
        kafkaService.sendBatchServiceInvoke(deviceIdsOnline, tenantId, content, DevicePushTag.RESET, Constant.SERVICE_INVOKE_KAFKA_TOPIC);
        return ResponseMessage.buildSuccess();
    }

    @ApiLog(description = "设备远程控制")
    @Operation(summary = "设备远程控制,设备在线时发送，若操作的设备是离线设备，则会返回code=17，非法操作，返回 0: 表示成功，1：表示不在线,2：表示获取不到设备的状态，其它 代表异常。语言国际化，头部参数传递Accept-Language，值为zh_CN为中文，值为en_US为英文")
    @PostMapping(value = "control")
    @OperateRecord(moduleName = "设备信息管理", operateName = "设备远程控制")
    public ResponseMessage<Integer> control(@RequestBody DeviceControlReq req) {
        if (Objects.isNull(req)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL);
        }
        if (StringUtils.isBlank(req.getDeviceId())) {
            return ResponseMessage.buildFail(ILLEGAL_OPERATE);
        }
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        return deviceControlService.controlDevice(req, tenantId);
    }

    @GetMapping("/bloom")
    public ResponseMessage<Boolean> getById(@RequestParam String value) {
        return ResponseMessage.buildSuccess(bloomFilter.includeByBloomFilter(filterHelper, DeviceInfoCache.DEVICE_ID_SN_BLOOM_FILTER_KEY, value));
    }

    @ApiLog(description = "获取app用户下的设备列表")
    @Operation(summary = "获取app用户下的设备列表")
    @PostMapping(value = "/getDevicesByUserId")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    public ResponseMessage<Page<DeviceInfoEntity>> getDevicesByUserId(@Valid @RequestBody DeviceUserPageReq req){
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId) && StringUtils.isBlank(req.getTenantId())) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "tenantId");
        }
        //优先使用参数的企业id
        TenantIdHandleUtil.handleTenantId(req.getTenantId(), false);
        return deviceInfoService.getDevicesByUserId(req);
    }

    //todo 修改业务
//    @ApiLog(description = "清除设备信息")
//    @Operation(summary = "清除设备信息")
//    @PostMapping(value = "/clearSn")
//    public ResponseMessage<Boolean> cleanSN(@RequestBody CleanSNReq req) {
//        List<String> snList = req.getSnList();
//        if (CollectionUtils.isEmpty(snList)) {
//            return ResponseMessage.buildSuccess(false);
//        }
//        if (StringUtils.isBlank(req.getTenantId())) {
//            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "tenantId");
//        }
//        //预防厂商id（租户id）传递错误的问题，有限使用头部的厂商id（租户）作为条件，其次才是参数
//        TenantIdHandleUtil.handleTenantId(req.getTenantId());
//        // todo 远程调用，增加绑定关系
//        deviceInfoService.resetAndCleanSN(snList);
//        return ResponseMessage.buildSuccess(true);
//    }

}
