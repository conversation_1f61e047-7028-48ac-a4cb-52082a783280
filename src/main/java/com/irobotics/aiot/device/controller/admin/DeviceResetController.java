package com.irobotics.aiot.device.controller.admin;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.cache.DeleteRecordCache;
import com.irobotics.aiot.device.cache.UploadResultCache;
import com.irobotics.aiot.device.common.DeviceResponseCode;
import com.irobotics.aiot.device.entity.DeviceResetAuditEntity;
import com.irobotics.aiot.device.entity.DeviceResetRecordEntity;
import com.irobotics.aiot.device.service.DeviceResetAuditService;
import com.irobotics.aiot.device.service.DeviceResetService;
import com.irobotics.aiot.device.utils.PageHelper;
import com.irobotics.aiot.device.utils.TenantIdHandleUtil;
import com.irobotics.aiot.device.vo.*;
import com.irobotics.aiot.operate.annotation.OperateRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.PARAM_VALIDATE_FAIL;

/**
 * <AUTHOR>
 * @description
 * @since 2022/1/13
 */
@RestController
@Tag(name = "设备恢复出厂设置相关接口")
@RequestMapping("/admin/reset")
public class DeviceResetController {

    private static final Integer PAGE_SIZE = 15;

    @Resource
    private DeviceResetService deviceResetService;

    @Resource
    private UploadResultCache uploadResultCache;

    @Resource
    private DeleteRecordCache deleteRecordCache;

    @Resource
    DeviceResetAuditService deviceResetAuditService;

    @ApiLog(description = "检索上传sn的excel", serviceName = "device-service")
    @Operation(summary = "检索上传sn的excel")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @OperateRecord(moduleName = "设备恢复出厂设置", operateName = "检索上传sn的excel")
    @PostMapping("/uploadSn")
    public ResponseMessage<String> uploadSn(MultipartFile file, @RequestParam String paramTenantId) {
        TenantIdHandleUtil.modifyHeaderTenantId(paramTenantId);
        String tenantId = SystemContextUtils.getTenantId();
        List<String> snList = new ArrayList<>();
        try {
            EasyExcel.read(file.getInputStream(), new AnalysisEventListener() {
                @Override
                public void invoke(Object data, AnalysisContext context) {
                    snList.add((String) ((Map<Object, Object>) data).get(0));
                }

                //头部信息
                @Override
                public void invokeHeadMap(Map headMap, AnalysisContext context) {
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                }
            }).sheet().doRead();
        } catch (Exception e) {
            return ResponseMessage.buildFail(DeviceResponseCode.EXCEL_ERROR);
        }
        String taskId = UUID.randomUUID().toString();
        deviceResetService.listBySns(taskId, tenantId, snList);
        return ResponseMessage.buildSuccess(taskId);
    }

    @ApiLog(description = "检索上传sn的文本格式", serviceName = "device-service")
    @Operation(summary = "检索上传sn的文本格式")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @OperateRecord(moduleName = "设备恢复出厂设置", operateName = "检索上传sn的文本格式")
    @PostMapping("/uploadSnByText")
    public ResponseMessage<String> uploadSnByText(@RequestBody UploadSnReq uploadSnReq) {
        TenantIdHandleUtil.modifyHeaderTenantId(uploadSnReq.getParamTenantId());
        if (CollectionUtils.isEmpty(uploadSnReq.getSnList())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "snList");
        }
        String tenantId = SystemContextUtils.getTenantId();
        String taskId = UUID.randomUUID().toString();
        deviceResetService.listBySns(taskId, tenantId, uploadSnReq.getSnList());
        return ResponseMessage.buildSuccess(taskId);
    }

    @ApiLog(description = "检索上传用户名的excel", serviceName = "device-service")
    @Operation(summary = "检索上传用户名的excel")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @OperateRecord(moduleName = "设备恢复出厂设置", operateName = "检索上传用户名")
    @PostMapping("/uploadUsername")
    public ResponseMessage<String> uploadUsername(MultipartFile file, @RequestParam String paramTenantId) {
        TenantIdHandleUtil.modifyHeaderTenantId(paramTenantId);

        String tenantId = SystemContextUtils.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "tenantId");
        }
        List<String> usernameList = new ArrayList<>();
        try {
            EasyExcel.read(file.getInputStream(), new AnalysisEventListener() {
                @Override
                public void invoke(Object data, AnalysisContext context) {
                    usernameList.add((String) ((Map<Object, Object>) data).get(0));
                }

                //头部信息
                @Override
                public void invokeHeadMap(Map headMap, AnalysisContext context) {
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                }
            }).sheet().doRead();
        } catch (Exception e) {
            return ResponseMessage.buildFail(DeviceResponseCode.EXCEL_ERROR);
        }
        String taskId = UUID.randomUUID().toString();
        deviceResetService.listByUsernames(taskId, tenantId, usernameList);
        return ResponseMessage.buildSuccess(taskId);
    }

    @ApiLog(description = "检索上传用户名的文本格式", serviceName = "device-service")
    @Operation(summary = "检索上传用户名的文本格式")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @OperateRecord(moduleName = "设备恢复出厂设置", operateName = "检索上传用户名的文本格式")
    @PostMapping("/uploadUsernameByText")
    public ResponseMessage<String> uploadUsernameByText(@RequestBody UploadUsernameReq req) {
        TenantIdHandleUtil.modifyHeaderTenantId(req.getParamTenantId());
        String tenantId = SystemContextUtils.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "tenantId");
        }
        if (CollectionUtils.isEmpty(req.getUsernameList())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "usernameList");
        }
        String taskId = UUID.randomUUID().toString();
        deviceResetService.listByUsernames(taskId, tenantId, req.getUsernameList());
        return ResponseMessage.buildSuccess(taskId);
    }

    @ApiLog(description = "获取检索任务进度", serviceName = "device-service")
    @Operation(summary = "获取检索任务进度")
    @GetMapping("/getParsePercent")
    public ResponseMessage<Integer> getParsePercent(String taskId) {
        Integer percent = uploadResultCache.getFinishPercent(taskId);
        return ResponseMessage.buildSuccess(percent);
    }

    @ApiLog(description = "获取检索任务结果", serviceName = "device-service")
    @Operation(summary = "获取检索任务结果")
    @GetMapping("/getParseResult")
    public ResponseMessage<PageResp<IdSnUsernameResp>> getParseResult(String taskId, Integer page, Integer size) {
        if (StringUtils.isBlank(taskId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "taskId");
        }
        if (page == null) {
            page = 1;
        }
        if (size == null) {
            size = PAGE_SIZE;
        }
        List<IdSnUsernameResp> result = uploadResultCache.getResult(taskId);
        PageResp<IdSnUsernameResp> pageResp;
        if (CollectionUtils.isEmpty(result)) {
            pageResp = new PageResp<>();
        } else {
            pageResp = new PageResp<>(page, size, result);
        }
        return ResponseMessage.buildSuccess(pageResp);
    }

    @ApiLog(description = "获取删除任务进度", serviceName = "device-service")
    @Operation(summary = "获取删除任务进度")
    @OperateRecord(moduleName = "设备恢复出厂设置", operateName = "获取删除任务进度")
    @GetMapping("/getDeletePercent")
    public ResponseMessage<Integer> getDeletePercent(String taskId) {
        Integer percent = deleteRecordCache.getFinishPercent(taskId);
        return ResponseMessage.buildSuccess(percent);
    }

    @ApiLog(description = "获取删除任务结果", serviceName = "device-service")
    @Operation(summary = "获取删除任务结果")
    @GetMapping("/getDeleteResult")
    public ResponseMessage<PageResp<DeleteResp>> getDeleteResult(String taskId, Integer page, Integer size) {
        if (StringUtils.isBlank(taskId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "taskId");
        }
        if (page == null) {
            page = 1;
        }
        if (size == null) {
            size = PAGE_SIZE;
        }
        List<DeleteResp> result = deleteRecordCache.getResult(taskId);
        PageResp<DeleteResp> pageResp;
        if (CollectionUtils.isEmpty(result)) {
            pageResp = new PageResp<>();
        } else {
            pageResp = new PageResp<>(page, size, result);
        }
        return ResponseMessage.buildSuccess(pageResp);
    }

    @ApiLog(description = "根据设备id批量删除记录", serviceName = "device-service")
    @Operation(summary = "根据设备id批量删除记录")
    @OperateRecord(moduleName = "设备恢复出厂设置", operateName = "根据设备id批量删除记录")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @PostMapping("/deleteRecordByIds")
    public ResponseMessage<String> deleteRecordByIds(@RequestBody List<String> ids, HttpServletRequest request, @RequestParam String paramTenantId) {
        TenantIdHandleUtil.modifyHeaderTenantId(paramTenantId);

        String tenantId = SystemContextUtils.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "tenantId");
        }
        if (CollectionUtils.isEmpty(ids)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "ids");
        }
        String taskId = UUID.randomUUID().toString();
        String token = request.getHeader("Authorization");
        deviceResetService.deleteRecordByIds(token, taskId, tenantId, ids);
        return ResponseMessage.buildSuccess(taskId);
    }

    @ApiLog(description = "分页查询删除记录", serviceName = "device-service")
    @Operation(summary = "分页查询删除记录")
    @PostMapping("/findRecordPage")
    public ResponseMessage<PageHelper<DeviceResetRecordEntity>> findRecordPage(@RequestBody DeviceResetRecordPageReq pageReq) {
        if (null == pageReq) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "pageReq");
        }
        return deviceResetService.findRecordPage(pageReq);
    }

    @ApiLog(description = "申请删除SN", serviceName = "device-service")
    @Operation(summary = "申请删除SN")
    @OperateRecord(moduleName = "设备恢复出厂设置", operateName = "指定审核人申请删除SN")
    @PostMapping("/applyDelete")
    public ResponseMessage<DeviceResetAuditEntity> applyDelete(@RequestBody ApplyDelSnVo req) {
        return deviceResetAuditService.apply(req);
    }

    @ApiLog(description = "设备恢复出厂设置审核列表")
    @Operation(summary = "设备恢复出厂设置审核列表")
    @PostMapping("/findDeviceResetAuditRecord")
    public ResponseMessage<Page<DeviceResetAuditEntity>> findAuditRecordByPage(@RequestBody DeviceResetAuditRecordParam recordParam) {
        if (null == recordParam) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "recordParam");
        }
        return ResponseMessage.buildSuccess(deviceResetAuditService.getList(recordParam));
    }

    @ApiLog(description = "审核人删除SN", serviceName = "device-service")
    @Operation(summary = "审核人删除SN")
    @OperateRecord(moduleName = "设备恢复出厂设置", operateName = "审核人删除SN")
    @PostMapping("/confirmDelete")
    public ResponseMessage<DeviceResetAuditEntity> confirmDelete(HttpServletRequest request, @RequestBody DeviceResetAuditParam patam) {
        String taskId = UUID.randomUUID().toString();
        String token = request.getHeader("Authorization");
        if (StringUtils.isBlank(patam.getTenantId())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "tenantId");
        }
        return deviceResetAuditService.audit(token, taskId, patam);
    }

    @ApiLog(description = "根据记录id分页查询待删除SN列表", serviceName = "device-service")
    @Operation(summary = "根据记录id分页查询待删除SN列表")
    @PostMapping("/getSnsByPage")
    public ResponseMessage<PageResp<IdSnUsernameResp>> getSnsByPage(@RequestBody DeviceResetAuditPageParam auditPageParam) {
        return deviceResetAuditService.getIdSnByIdAndPage(auditPageParam);
    }
}
