package com.irobotics.aiot.device.controller.admin;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.common.DeviceResponseCode;
import com.irobotics.aiot.device.service.HaiErDeviceResetService;
import com.irobotics.aiot.device.utils.TenantIdHandleUtil;
import com.irobotics.aiot.device.vo.UploadSnReq;
import com.irobotics.aiot.operate.annotation.OperateRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.PARAM_VALIDATE_FAIL;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/18
 */
@RestController
@Tag(name = "海尔设备恢复出厂设置相关接口")
@RequestMapping("/admin/haier/reset")
public class HaiErDeviceResetController {

    @Resource
    HaiErDeviceResetService haiErDeviceResetService;

    @ApiLog(description = "检索上传sn的excel", serviceName = "device-service")
    @Operation(summary = "检索上传sn的excel")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @OperateRecord(moduleName = "海尔设备恢复出厂设置", operateName = "检索上传sn的excel")
    @PostMapping("/uploadSn")
    public ResponseMessage<String> uploadSn(MultipartFile file, @RequestParam String paramTenantId) {
        TenantIdHandleUtil.modifyHeaderTenantId(paramTenantId);
        List<String> snList = new ArrayList<>();
        try {
            EasyExcel.read(file.getInputStream(), new AnalysisEventListener() {
                @Override
                public void invoke(Object data, AnalysisContext context) {
                    snList.add((String) ((Map<Object, Object>) data).get(0));
                }

                //头部信息
                @Override
                public void invokeHeadMap(Map headMap, AnalysisContext context) {
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                }
            }).sheet().doRead();
        } catch (Exception e) {
            return ResponseMessage.buildFail(DeviceResponseCode.EXCEL_ERROR);
        }
        String taskId = UUID.randomUUID().toString();
        //设置sn至缓存
        haiErDeviceResetService.saveSnToCache(taskId, snList);
        return ResponseMessage.buildSuccess(taskId);
    }

    @ApiLog(description = "检索上传sn的文本格式", serviceName = "device-service")
    @Operation(summary = "检索上传sn的文本格式")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @OperateRecord(moduleName = "海尔设备恢复出厂设置", operateName = "检索上传sn的文本格式")
    @PostMapping("/uploadSnByText")
    public ResponseMessage<String> uploadSnByText(@RequestBody UploadSnReq uploadSnReq) {
        TenantIdHandleUtil.modifyHeaderTenantId(uploadSnReq.getParamTenantId());
        if (CollectionUtils.isEmpty(uploadSnReq.getSnList())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "snList");
        }
        String taskId = UUID.randomUUID().toString();
        //设置sn至缓存
        haiErDeviceResetService.saveSnToCache(taskId, uploadSnReq.getSnList());
        return ResponseMessage.buildSuccess(taskId);
    }

    @ApiLog(description = "海尔删除扫地机历史记录", serviceName = "device-service")
    @Operation(summary = "海尔删除扫地机历史记录")
    @OperateRecord(moduleName = "海尔设备恢复出厂设置", operateName = "海尔删除扫地机历史记录")
    @PostMapping("/haiErDeleteSweepRecord")
    public ResponseMessage<String> haiErDeleteSweepRecord(HttpServletRequest request, @RequestBody List<String> snList, @RequestParam String paramTenantId) {
        TenantIdHandleUtil.modifyHeaderTenantId(paramTenantId);
        String tenantId = SystemContextUtils.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "tenantId");
        }
        if (CollectionUtils.isEmpty(snList)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "snList");
        }
        String taskId = UUID.randomUUID().toString();
        String token = request.getHeader("Authorization");
        haiErDeviceResetService.deleteHaiErDeviceRecord(token, tenantId, taskId, snList);
        return ResponseMessage.buildSuccess();
    }
}