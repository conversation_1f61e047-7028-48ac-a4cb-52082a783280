package com.irobotics.aiot.device.controller.app;

import cn.hutool.core.collection.CollectionUtil;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.service.IDeviceBindUserService;
import com.irobotics.aiot.device.vo.app.BindDeviceInfoVo;
import com.irobotics.aiot.device.vo.app.MoveDeviceReq;
import com.irobotics.aiot.device.vo.app.UntieDeviceReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.PARAM_VALIDATE_FAIL;

/**
 * @author:tlh
 * @create: 2023-07-06 14:06:14
 * @Description:
 */
@Tag(name = "app-非智能家居下调用相关接口")
@RestController
@RequestMapping("/app/bind")
public class DeviceAppBindController {

    @Autowired
    private IDeviceBindUserService deviceBindUserService;



    @ApiLog(description = "根据userId获取绑定设备列表")
    @Operation(summary = "根据userId获取绑定设备列表")
    @GetMapping("/listByUserId")
    public ResponseMessage<List<BindDeviceInfoVo>> bindListByUserId(){
        String userId = SystemContextUtils.getId();
        return deviceBindUserService.getBindListByUserId(userId);
    }

    @ApiLog(description = "将设备移到顶部")
    @Operation(summary = "将设备移到顶部")
    @PostMapping(value = "/moveToTop")
    public ResponseMessage<Boolean> moveToTop(@RequestBody MoveDeviceReq req) {
        if (CollectionUtil.isEmpty(req.getIdList())){
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL,"idList");
        }
        return deviceBindUserService.moveToTop(req);
    }

    @ApiLog(description = "更新设备列表排序")
    @Operation(summary = "更新设备列表排序")
    @PostMapping(value = "/updateOrder")
    public ResponseMessage<Boolean> updateOrder(@RequestBody MoveDeviceReq req) {
        if (CollectionUtil.isEmpty(req.getIdList())){
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL,"idList");
        }
        return deviceBindUserService.updateOrder(req);
    }

    @ApiLog(description = "根据设备Id解绑设备")
    @Operation(summary = "根据设备Id解绑设备")
    // 兼容open-api，delete请求调用
    @RequestMapping(path = "/untie",method = {RequestMethod.POST,RequestMethod.DELETE})
    public ResponseMessage<Boolean> untieDevice(@RequestBody UntieDeviceReq req) {
        return deviceBindUserService.untieDevice(req);
    }
}
