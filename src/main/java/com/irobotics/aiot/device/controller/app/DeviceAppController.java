package com.irobotics.aiot.device.controller.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.basic.exception.AppRuntimeException;
import com.irobotics.aiot.bravo.basic.exception.NoDataFoundException;
import com.irobotics.aiot.bravo.basic.exception.ParameterValidateException;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.common.InviteShareEnum;
import com.irobotics.aiot.device.entity.Conversation;
import com.irobotics.aiot.device.service.*;
import com.irobotics.aiot.device.vo.*;
import com.irobotics.aiot.encryption.util.AESUtil;
import io.swagger.annotations.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.INNER_SERVER_ERROR;
import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.PARAM_VALIDATE_FAIL;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/9/16 19:39
 */
@Tag(name = "app-APP调用设备相关接口")
@RestController
@RequestMapping("/app")
public class DeviceAppController {


    /**
     * 企业id常量字符串
     */
    private static final String TENANT_ID = "tenantId";

    /**
     * 设备信息业务层
     */
    @Autowired
    private IDeviceInfoService deviceInfoService;

    /**
     * 设备绑定业务层
     */
    @Autowired
    private IDeviceBindService deviceBindService;

    /**
     * 设备分享业务层
     */
    @Autowired
    private IDeviceInviteShareService deviceInviteShareService;

    @Resource
    private DeviceResetService deviceResetService;

    @Autowired
    private IConversationService conversationService;

    /**
     * 机器发起绑定APP请求后，APP最终确认
     *
     * @param fromOldOpenApi 兼容旧OPEN-API接口返回结构，20240601之前open-api对应的配网接口OpenApiController#bindDevice
     *                       此参数不需要传，默认为false，如果通过旧OPEN-API调用，通过网关处理增加参数fromOldOpenApi且值为true
     */
    @ApiLog(description = "机器发起绑定APP请求后，APP最终确认")
    @Operation(summary = "机器发起绑定APP请求后,APP最终确认")
    @ApiResponses({@ApiResponse(code = 0, message = "result有值表示绑定成功，否则继续轮询")})
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "id"),
            @ApiImplicitParam(paramType = "header", name = TENANT_ID, required = true)})
    @PostMapping(value = "/bind/confirm")
    public ResponseMessage<?> bindAppConfirm(@RequestBody BindConfirmVo vo,
                                             @Schema(hidden = true)
                                             @RequestParam(defaultValue = "false") Boolean fromOldOpenApi) {
        if (Objects.isNull(vo)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL);
        }

        String bindKey = vo.getBindKey();
        LogUtils.info("bind confirming: {}", bindKey);
        if (org.apache.commons.lang.StringUtils.isEmpty(bindKey)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "bindKey");
        }
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        String userId = SystemContextUtils.getId();
        if (StringUtils.isBlank(userId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "userId");
        }
        String familyId = vo.getFamilyId();
        String roomId = vo.getRoomId();

        try {
            BindResp resp = deviceBindService.bindAppConfirm(SystemContextUtils.getId(), bindKey, familyId, roomId);
            LogUtils.info("bind confirmed: {}, {}", bindKey, resp);
            // 兼容开放平台返回
            if (fromOldOpenApi) {
                return ResponseMessage.buildSuccess(true);
            }
            return ResponseMessage.buildSuccess(resp);
        } catch (NoDataFoundException noData) {
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA, noData.getMessage());
        } catch (ParameterValidateException p) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, p.getMessage());
        } catch (AppRuntimeException app) {
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, app.getMessage());
        } catch (Exception e) {
            LogUtils.info("bind confirming出现异常,缓存中的key和入参的bindKey不一致: {}, exception={}", bindKey, e.getMessage());
            return ResponseMessage.buildFail(ResponseCode.CACHE_ERROR);
        }
    }

    /**
     * 分享设备给指定用户
     *
     * @param vo
     * @return
     */
    @ApiLog(description = "分享设备给指定用户")
    @Operation(summary = "分享设备给指定用户")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "id", required = true),
            @ApiImplicitParam(paramType = "header", name = "username", required = true),
            @ApiImplicitParam(paramType = "header", name = TENANT_ID, required = true)})
    @PostMapping("/share")
    public ResponseMessage<Boolean> shareRobot(@RequestBody ShareVo vo) {
        if (Objects.isNull(vo)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL);
        }
        String beInvited = vo.getBeInvited();
        List<String> targetIds = vo.getTargetIds();
        if (StringUtils.isBlank(beInvited)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "beInvited is null");
        }
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        try {
            beInvited = (AESUtil.Decrypt(beInvited, tenantId));
        } catch (Exception e) {
            LogUtils.error(e, "aes解密失败, beInvited={}, exceptionMsg={}", beInvited, e.getMessage());
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "被邀请的用户名格式有误");
        }
        if (CollectionUtils.isEmpty(targetIds)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "未选中目标");
        }
        String inviterId = SystemContextUtils.getId();
        String inviter = SystemContextUtils.getContextValue(ContextKey.USERNAME);
        try {
            return deviceInviteShareService.inviteUserShareRobot(beInvited, inviterId, inviter, targetIds);
        } catch (Exception e) {
            LogUtils.error(e, "分享设备给指定用户异常, vo={}, exceptionMsg={}", vo, e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
    }

    /**
     * 回应用户的分享
     *
     * @param vo
     * @return
     */
    @ApiLog(description = "回应用户的分享(包括设备分享以及家庭分享)")
    @Operation(summary = "回应用户的分享(包括设备分享以及家庭分享),头部参数的id为被邀请的用户的id。返回False 提示 该分享不存在")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "id", required = true),
            @ApiImplicitParam(paramType = "header", name = TENANT_ID, required = true)})
    @PostMapping("/shared/reply")
    public ResponseMessage<Boolean> sharedRobotReply(@RequestBody ShareReplyVo vo) {
        if (Objects.isNull(vo)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL);
        }
        String inviterId = vo.getInviterId();
        String targetId = vo.getTargetId();
        int dealType = vo.getDealType();
        Integer type = vo.getType();
        String familyId = vo.getFamilyId();
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        if (StringUtils.isBlank(inviterId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "inviterId");
        }
        if (StringUtils.isBlank(targetId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "targetId");
        }
        if (type != InviteShareEnum.DEVICE.getStatus() && type != InviteShareEnum.FAMILY.getStatus()) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "type");
        }
        // 共享设备时，家庭属性必填，因为共享家庭的时候，targetId就是家庭id
        /**if (type == InviteShareEnum.DEVICE.getStatus() && StringUtils.isBlank(familyId)) {
         return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "familyId");
         }**/
        // 家庭共享时，家庭名称必填，极光消息推送需要用到家庭名称
        if (type == InviteShareEnum.FAMILY.getStatus() && StringUtils.isBlank(vo.getFamilyName())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "familyName");
        }

        if (dealType != InviteShareEnum.AGREED.getStatus() && dealType != InviteShareEnum.REJECTED.getStatus()) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "dealType");
        }
        // 被邀请用户的id
        String beInvitedUserId = SystemContextUtils.getId();
        try {
            return ResponseMessage.buildSuccess(deviceInviteShareService.replyShared(type, familyId,
                    vo.getFamilyName(), beInvitedUserId, inviterId, targetId, dealType));
        } catch (AppRuntimeException e) {
            return ResponseMessage.buildFail(e.getCode(), e.getMessage());
        } catch (IllegalArgumentException e) {
            return ResponseMessage.buildFail(e);
        } catch (Exception e) {
            LogUtils.error(e, "回应分享异常，exceptionMsg={}", e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
    }

    /**
     * 获取用户的分享设备信息
     *
     * @param req
     * @return
     */
    @ApiLog(description = "获取用户的分享设备信息")
    @Operation(summary = "获取用户的分享设备信息，登录后请求。头部参数的id为用户id，用户名、用户图片会加密，设备的图片不会加密，app那边做好解密处理")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "id", required = true),
            @ApiImplicitParam(paramType = "header", name = "tenantId", required = true)})
    @PostMapping("/shared/device")
    public ResponseMessage<Page<DeviceShareVo>> getShareRobot(@RequestBody GetUserSharedReq req) {
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        if (Objects.isNull(req)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL);
        }
        if (StringUtils.isBlank(req.getTargetId())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "targetId");
        }
        // 用户id
        String userId = SystemContextUtils.getId();
        try {
            Page<DeviceShareVo> page = deviceInviteShareService.getAllSharedList(req);
            return ResponseMessage.buildSuccess(page);
        } catch (Exception e) {
            LogUtils.error(e, "获取用户分享信息异常, tenantId={}, userId={},req={}, exceptionMsg={}", tenantId, userId, req,
                    e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
    }

    /**
     * 分页获取用户的分享历史或获取共享家庭记录
     *
     * @param req
     * @return
     */
    @ApiLog(description = "分页获取用户的分享历史或获取共享家庭记录")
    @Operation(summary = "分页获取用户的分享历史，登录后请求。头部参数的id为用户id，用户名、用户图片会加密，设备的图片不会加密，app那边做好解密处理")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "id", required = true),
            @ApiImplicitParam(paramType = "header", name = "tenantId", required = true)})
    @PostMapping("/shared/history")
    public ResponseMessage<Page<DeviceShareHisVo>> getShareHis(@RequestBody UserSharedHisReq req) {
        String userId = SystemContextUtils.getId();
        String tenantId = SystemContextUtils.getTenantId();
        try {
            return deviceInviteShareService.getShareHis(req);
        } catch (Exception e) {
            LogUtils.error(e, "获取用户的分享历史异常, tenantId={}, userId={}, exceptionMsg={}",
                    tenantId, userId, e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
    }

    /**
     * 通过极光推送的消息拒绝或同意分享
     *
     * @param req
     * @return
     */
    @ApiLog(description = "通过极光推送的消息拒绝或同意分享")
    @Operation(summary = "通过极光推送的消息拒绝或同意分享")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "tenantId", required = true),
            @ApiImplicitParam(paramType = "header", name = "id", required = true)})
    @PostMapping("/share/record/change/status")
    public ResponseMessage<Boolean> changeShareRecordStatus(@RequestBody ShareRecordChangeStatusReq req) {
        try {
            return deviceInviteShareService.changeShareRecordStatus(req);
        } catch (AppRuntimeException ae) {
            return ResponseMessage.buildFail(ae.getCode(), ae.getMessage());
        } catch (Exception e) {
            LogUtils.error(e, "app通过极光推送的消息拒绝或同意分享异常, req={}, exceptionMsg={}", req, e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
    }

    /**
     * 删除分享信息
     *
     * @param shareId
     * @return
     */
    @ApiLog(description = "删除分享信息")
    @Operation(summary = "删除分享信息，取消分享。 分享设备时，若是分享方删除分享信息，则会把接受分享方的绑定关系删除；若是接受方删除分享信息，则会删除自己的绑定关系。头部参数的id为用户id")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "id", required = true),
            @ApiImplicitParam(paramType = "header", name = "tenantId", required = true)})
    @DeleteMapping("/shared/del")
    public ResponseMessage<Void> delShareRobot(@ApiParam("分享id") @RequestParam String shareId) {
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        String userId = SystemContextUtils.getId();
        try {
            deviceInviteShareService.cancelShare(shareId, userId);
            return ResponseMessage.buildSuccess();
        } catch (AppRuntimeException e) {
            return ResponseMessage.buildFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            LogUtils.error(e, "删除分享信息异常，exceptionException={}", e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);

        }
    }

    /**
     * 修改设备昵称
     *
     * @param req
     * @return
     */
    @ApiLog(description = "修改设备昵称")
    @Operation(summary = "修改设备昵称,头部参数tenantId")
    @PutMapping("/modify/nickname")
    public ResponseMessage<Boolean> modifyNickname(@RequestBody ModifyDeviceNicknameReq req) {
        try {
            String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
            return deviceInfoService.modifyNickname(tenantId, req.getDeviceId(), req.getNickname(), req.getSn(),
                    req.getMac());
        } catch (Exception e) {
            LogUtils.error(e, "修改设备昵称异常, req={}, exceptionMsg={}", req, e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
    }

    /**
     * 设备配置是否需要开启验证app密码
     *
     * @param vo
     * @return
     */
    @ApiLog(description = "设备配置是否需要开启验证app密码")
    @Operation(summary = "设备配置是否需要开启验证app密码，如果开启密码，app那边必须调用设置app密码的接口 /user-center/app/user/config/password 进行相对应的操作")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "id", required = true),
            @ApiImplicitParam(paramType = "header", name = "username", required = true),
            @ApiImplicitParam(paramType = "header", name = "tenantId", required = true)})
    @PutMapping("/config/verify")
    public ResponseMessage<Boolean> configVerifyPassword(@RequestBody DeviceConfigVerifyPasswordVo vo) {
        try {
            return deviceInfoService.configVerify(vo);
        } catch (Exception e) {
            LogUtils.error(e, "设备配置是否需要开启验证app密码异常, vo={}, exceptionMsg={}", vo, e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
    }

    /**
     * 获取bindKey
     *
     * @param deviceId
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "tenantId", required = true),
            @ApiImplicitParam(paramType = "header", name = "id", required = true)
    })
    @ApiLog(description = "基站获取bindKey")
    @Operation(summary = "基站获取bindKey")
    @GetMapping("/bindKey")
    public ResponseMessage<String> getBindKey(@RequestParam String deviceId) {
        try {
            if (StringUtils.isBlank(deviceId)) {
                return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceId");
            }
            return ResponseMessage.buildSuccess(deviceBindService.getBindKey(deviceId,
                    SystemContextUtils.getTenantId()));
        } catch (Exception e) {
            LogUtils.error(e, "获取bindKey失败，exceptionMsg={}", e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
    }

    /**
     * 二维码绑定
     * 目前基站只有扫码绑定一种方式
     *
     * @param vo
     * @return
     */
    @ApiLog(description = "app二维码绑定（相当于配网绑定）")
    @Operation(summary = "app二维码绑定（相当于配网绑定）")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "id", required = true),
            @ApiImplicitParam(paramType = "header", name = "username", required = true),
            @ApiImplicitParam(paramType = "header", name = "tenantId", required = true)})
    @PostMapping("/bind/orCode")
    public ResponseMessage<BindResp> orCodeBindConfirm(@RequestBody OrCodeBindVo vo) {
        try {
            return deviceBindService.orCodeBind(vo);
        } catch (Exception e) {
            LogUtils.error(e, "二维码绑定失败，exceptionMsg={}", e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
    }

    /**
     * 说明，设备里的媒体设备，存在一个自己的设备id，相当于设备与这个媒体设备进行一对一的关联关系
     *
     * @param vo
     * @return
     */
    @ApiLog(description = "更新设备信息的iotId")
    @Operation(summary = "更新设备信息的iotId")
    @PostMapping("/upload/iotId")
    public ResponseMessage<Boolean> resetDevice(@RequestBody IotIdVo vo) {
        try {
            return deviceInfoService.updateIotId(vo);
        } catch (Exception e) {
            LogUtils.error(e, "iotId信息上传失败, vo={}, exceptionMsg={}", vo, e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
    }

    /**
     * APP端控制，设备恢复出厂设置
     */
    @ApiLog(description = "APP端控制，设备恢复出厂设置")
    @Operation(summary = "APP端控制，设备恢复出厂设置")
    @PostMapping("reset/device/{deviceId}")
    public ResponseMessage<Void> resetDevice(@PathVariable String deviceId) {
        deviceResetService.reset(deviceId);
        return ResponseMessage.buildSuccess();
    }

    /**
     * 分页查找语音会话
     */
    @ApiLog(description = "分页查找语音会话")
    @Operation(summary = "分页查找语音会话")
    @PostMapping("conversation/page")
    public ResponseMessage<Page<Conversation>> page(@RequestBody ConversationPageReq req) {
        return conversationService.page(req);
    }
}
