package com.irobotics.aiot.device.controller.app;


import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.service.IDeviceVideoInfoService;
import com.irobotics.aiot.device.vo.app.VideoInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.PARAM_VALIDATE_FAIL;

/**
 * <p>
 *  主要是处理阿里云lv视频流相关业务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-25
 */
@Tag(name = "app-APP调用获取视频流接口")
@RestController
@RequestMapping("/app/video/")
public class DeviceVideoInfoController {

    @Autowired
    private IDeviceVideoInfoService deviceLvInfoService;


    @ApiLog(description = "app或设备 获取视频流三元组")
    @Operation(summary = "app或设备 获取视频流三元组")
    @GetMapping("getKey")
    public ResponseMessage<VideoInfoVo> getKey(@RequestParam("sn") String sn) {
        if (StringUtils.isBlank(sn)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "sn不能为空");
        }
        return deviceLvInfoService.getKey(sn);
    }


}
