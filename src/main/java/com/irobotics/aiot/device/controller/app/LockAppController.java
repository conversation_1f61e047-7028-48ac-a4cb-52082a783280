package com.irobotics.aiot.device.controller.app;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.service.LockService;
import com.irobotics.aiot.device.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.PARAM_VALIDATE_FAIL;

/**
 * @author: tlh
 * @date: 2022-05-12 13:56
 * @description: 门锁特定需求控制
 **/
@Tag(name = "app-APP调用门锁相关接口")
@RestController
@RequestMapping("/app/lock/")
public class LockAppController {

    /**
     * 门锁服务层
     */
    @Resource
    private LockService lockService;

    /**
     * 生成一次性密码
     *
     * @param lockKeyReq
     * @return
     */
    @ApiLog(description = "生成一次性密码")
    @Operation(summary = "生成一次性密码")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "id", required = true),
            @ApiImplicitParam(paramType = "header", name = "tenantId", required = true)})
    @PostMapping("generatorKey")
    public ResponseMessage<String> generatorKey(@RequestBody LockPasswordReq lockKeyReq) {
        if (StringUtils.isBlank(lockKeyReq.getSn())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "sn不能为空");
        }
        return lockService.generatorKey(lockKeyReq);
    }

    /**
     * 获取一次性有效密码记录
     *
     * @param lockKeyReq
     * @return
     */
    @ApiLog(description = "获取一次性有效密码记录")
    @Operation(summary = "获取一次性有效密码记录")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "id", required = true),
            @ApiImplicitParam(paramType = "header", name = "tenantId", required = true)})
    @PostMapping("passwordHis")
    public ResponseMessage<List<LockPasswordVo>> passwordHis(@RequestBody LockPasswordReq lockKeyReq) {
        if (StringUtils.isBlank(lockKeyReq.getSn())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "sn不能为空");
        }
        return lockService.getPasswordHis(lockKeyReq);
    }

    /**
     * 同步用户门锁多条记录
     *
     * @param req
     * @return
     */
    @ApiLog(description = "同步用户门锁多条记录")
    @Operation(summary = "同步用户门锁多条记录 头部参数的id为设备id")
    @PostMapping(value = "/sync")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "tenantId", required = true),
            @ApiImplicitParam(paramType = "header", name = "id", required = true),
            @ApiImplicitParam(paramType = "header", name = "username", required = true)
    })
    public ResponseMessage<Void> syncUserLockRecord(@RequestBody List<UserLockSyncReq> req) {
        return lockService.syncRecord(req);
    }

    /**
     * 拉取用户门锁记录
     *
     * @param req
     * @return
     */
    @ApiLog(description = "拉取用户门锁记录")
    @Operation(summary = "拉取用户门锁记录")
    @PostMapping(value = "/list")
    public ResponseMessage<List<UserLockVo>> getUserLockRecord(@RequestBody UserLockReq req) {
        return lockService.getRecords(req);
    }

    /**
     * 批量删除门锁多条记录
     *
     * @param deviceUserIds
     * @return
     */
    @ApiLog(description = "批量删除门锁多条记录")
    @Operation(summary = "批量删除门锁多条记录 头部参数的id为设备id")
    @DeleteMapping("/del")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "tenantId", required = true),
            @ApiImplicitParam(paramType = "header", name = "id", required = true),
    })
    public ResponseMessage<Boolean> delRecord(@RequestBody List<String> deviceUserIds) {
        return lockService.batchDel(deviceUserIds);
    }
}
