package com.irobotics.aiot.device.controller.base;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.basic.exception.ParameterValidateException;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.service.IBaseAuthService;
import com.irobotics.aiot.device.service.IBaseBindService;
import com.irobotics.aiot.device.vo.base.BaseBindVo;
import com.irobotics.aiot.device.vo.base.BaseUnAuthDeviceReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.INNER_SERVER_ERROR;

/**
 * 基站接口
 *
 * <AUTHOR>
 */
@Tag(name = "基站接口")
@RestController
@RequestMapping("/base")
public class BaseController {

    /**
     * 基站绑定业务层
     */
    @Autowired
    private IBaseBindService baseBindService;

    @Autowired
    private IBaseAuthService baseAuthService;


    @ApiLog(description = "通过baseid查询绑定的设备列表")
    @Operation(summary = "通过baseid查询绑定的设备列表")
    @GetMapping("/baseBindList")
    public ResponseMessage<BaseBindVo> baseBindList() {
        try {
            String baseId = SystemContextUtils.getId();
            return baseBindService.baseBind(baseId);
        } catch (ParameterValidateException e) {
            LogUtils.error(e, "通过baseid查询绑定的设备列表, exception={}", e.getMessage());
            return ResponseMessage.buildFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            LogUtils.error(e, "通过baseid查询绑定的设备列表, exception={}", e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
    }

    @ApiLog(description = "基站取消授权", serviceName = "smart-home-service")
    @Operation(summary = "基站取消授权", tags = {"基站相关接口"})
    @PostMapping("/baseUnAuth")
    public ResponseMessage<Boolean> baseUnAuth(@RequestBody BaseUnAuthDeviceReq baseUnAuthDeviceReq) {
        try {
            return baseAuthService.baseUnAuth(baseUnAuthDeviceReq);
        } catch (Exception e) {
            LogUtils.error(e,"smart-home-service baseUnAuth 基站取消授权 error msg:{}", e.getMessage());
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR);
        }
    }
}
