package com.irobotics.aiot.device.controller.device;

import com.irobotics.aiot.bravo.basic.base.CountryCity;
import com.irobotics.aiot.bravo.basic.base.LoginEntity;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.basic.exception.FastRuntimeException;
import com.irobotics.aiot.bravo.commons.current.ThreadUtils;
import com.irobotics.aiot.bravo.commons.util.JsonUtils;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.service.DeviceAuthService;
import com.irobotics.aiot.device.service.LogService;
import com.irobotics.aiot.device.utils.TenantIdHandleUtil;
import com.irobotics.aiot.device.vo.LoginKeyReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/9/26 16:32
 */
@SuppressWarnings("Duplicates")
@Tag(name = "device-机器授权相关接口")
@Slf4j
@RestController
@RequestMapping("/auth")
public class DeviceAuthController {

    @Value("${service.test.secret}")
    private String secret;

    @Autowired
    private DeviceAuthService authService;

    @Autowired
    private LogService logService;

    @ApiLog(description = "登录接口，带KEY")
    @Operation(summary = "登录接口,带KEY")
    @ApiImplicitParam(paramType = "header", name = "clientIp")
    @PostMapping("/login")
    public ResponseMessage<LoginEntity> loginWithKeyt(@RequestBody LoginKeyReq loginReq) {
        if (null == loginReq.getTenantId()) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "tenantId");
        }
        //预防厂商id（租户id）传递错误的问题，有限使用头部的厂商id（租户）作为条件，其次才是参数
        TenantIdHandleUtil.handleTenantId(loginReq.getTenantId());
        try {
            return ResponseMessage.buildSuccess(authService.login(loginReq));
        } catch (Exception e) {
            //保存登录失败的日志
            ThreadUtils.execute(() -> {
                String ip = SystemContextUtils.getContextValue(ContextKey.CLIENT_IP);
                CountryCity countryCity = authService.getCountryCity(ip);
                String country = Objects.isNull(countryCity) ? null : JsonUtils.toJSON(countryCity);
                logService.loginFail(loginReq, country);
            });
            LogUtils.error(e, "登录失败,loginReq={}, exceptionMsg={}", loginReq, e.getMessage());
            if (e instanceof FastRuntimeException) {
                FastRuntimeException fastRuntimeException = (FastRuntimeException) e;
                return ResponseMessage.buildFail(fastRuntimeException.getCode(), fastRuntimeException.getMessage());
            }
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR, e.getMessage());
        }
    }
}
