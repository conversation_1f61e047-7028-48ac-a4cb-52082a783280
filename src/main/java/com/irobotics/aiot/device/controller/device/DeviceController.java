package com.irobotics.aiot.device.controller.device;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.dto.BaiDuParam;
import com.irobotics.aiot.device.dto.ConversationParam;
import com.irobotics.aiot.device.service.DeviceResetService;
import com.irobotics.aiot.device.service.IBdService;
import com.irobotics.aiot.device.service.IDeviceBindService;
import com.irobotics.aiot.device.service.IDeviceMapService;
import com.irobotics.aiot.device.vo.*;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.PARAM_VALIDATE_FAIL;

/**
 * @description: 设备硬件一些数据，比如电量，音量等
 * @author: huangwa1911
 * @time: 2021/9/26 17:12
 */
@Tag(name = "device-设备相关接口")
@RestController
@RequestMapping("/device")
public class DeviceController {

    /**
     * 设备绑定业务层
     */
    @Autowired
    private IDeviceBindService deviceBindService;
    @Resource
    private DeviceResetService deviceResetService;
    @Resource
    private IBdService bdService;
    @Autowired
    private IDeviceMapService deviceMapService;

    /**
     * 机器发起绑定APP请求
     *
     * @param bind
     * @return
     */
    @ApiLog(description = "机器发起绑定APP请求")
    @Operation(summary = "机器发起绑定APP请求， 头部参数的id为设备id")
    @ApiResponses({@ApiResponse(code = 0, message = "成功"), @ApiResponse(code = 12, message = "参数错误")})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "tenantId", required = true),
            @ApiImplicitParam(paramType = "header", name = "id", required = true)
    })
    @PostMapping(value = "/bind/app")
    public ResponseMessage<Void> bindApp(@RequestBody BindAppReq bind) {
        LogUtils.info("bind app start: {}", bind);
        if (bind == null) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL);
        }
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "tenantId");
        }
        String deviceId = SystemContextUtils.getId();
        String userId = bind.getUserId();
        String key = bind.getKey();

        if (StringUtils.isBlank(deviceId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceId");
        }
        if (StringUtils.isBlank(userId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "userId");
        }
        if (StringUtils.isBlank(key)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "key");
        }
        deviceBindService.bindApp(deviceId, userId, key);
        LogUtils.info("bind app success: {}", bind);
        return ResponseMessage.buildSuccess();
    }


    @ApiLog(description = "机器通过按钮重置设备")
    @Operation(summary = "机器通过按钮重置设备")
    @PostMapping(value = "reset")
    public ResponseMessage<Void> reset() {
        deviceResetService.reset(SystemContextUtils.getId());
        return ResponseMessage.buildSuccess();
    }


    @ApiLog(description = "百度接口透传")
    @Operation(summary = "百度接口透传")
    @PostMapping(value = "baiduRequest")
    public ResponseMessage<BaiDuVo> baiduRequest(@RequestBody BaiDuParam param) {
        return bdService.baiduRequest(param);
    }


//
//    @ApiLog(description = "向百度云上报添加字典")
//    @Operation(summary = "向百度云上报添加字典")
//    @PostMapping(value = "addDict")
//    public ResponseMessage<Boolean> addDictPost(@RequestBody AddDictParam param) {
//        return bdService.addDictPost(param);
//    }
//
//    @ApiLog(description = "删除单个同义词")
//    @Operation(summary = "删除单个同义词")
//    @PostMapping(value = "delDict")
//    public ResponseMessage<Boolean> deleteDictPost(@RequestBody DelDictParam param) {
//        return bdService.deleteDictPost(param);
//    }
//
//    @ApiLog(description = "获取自定义词典列表")
//    @Operation(summary = "获取自定义词典列表")
//    @PostMapping(value = "listDict")
//    public ResponseMessage<Boolean> listDictPost(@RequestBody ListDictParam param) {
//        return bdService.listDictPost(param);
//    }
//
//    @ApiLog(description = "清空自定义词典列表")
//    @Operation(summary = "清空自定义词典列表")
//    @PostMapping(value = "clearDict")
//    public ResponseMessage<Boolean> clearDictPost(@RequestBody ClearDictParam param) {
//        return bdService.clearDictPost(param);
//    }
//
//    @ApiLog(description = "批量覆盖添加同义词")
//    @Operation(summary = "批量覆盖添加同义词")
//    @PostMapping(value = "batchAddDict")
//    public ResponseMessage<Boolean> clearDictPost(@RequestBody BatchAddDictParam param) {
//        return bdService.batchAddDictPost(param);
//    }
//
//    @ApiLog(description = "按type清空自定义词典")
//    @Operation(summary = "按type清空自定义词典")
//    @PostMapping(value = "clearTypeDict")
//    public ResponseMessage<Boolean> clearTypeDictPost(@RequestBody ClearTypeDictParam param) {
//        return bdService.clearTypeDictPost(param);
//    }


    @ApiLog(description = "上报属于设备处理回复协议的会话记录")
    @Operation(summary = "上报属于设备处理回复协议的会话记录")
    @PostMapping(value = "addConversation")
    public ResponseMessage<String> addConversation(@RequestBody ConversationParam param) {
        return bdService.addConversation(param);
    }

    @ApiLog(description = "上传设备当前地图")
    @Operation(summary = "上传设备当前地图")
    @PostMapping(value = "/upload/curMap")
    public ResponseMessage<Boolean> uploadCurMap(@NotNull @RequestBody DeviceCurMapParam curMapParam) {
        return deviceMapService.uploadCurMap(curMapParam);
    }

    @ApiLog(description = "删除设备当前地图")
    @Operation(summary = "删除设备当前地图")
    @DeleteMapping(value = "/del/curMap")
    public ResponseMessage<Boolean> delCurMap() {
        return deviceMapService.delCurMap(SystemContextUtils.getContextValue(ContextKey.SN));
    }
}
