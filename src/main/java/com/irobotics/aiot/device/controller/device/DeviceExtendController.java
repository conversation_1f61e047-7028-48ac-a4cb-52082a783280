package com.irobotics.aiot.device.controller.device;


import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.service.DeviceExtendService;
import com.irobotics.aiot.device.vo.DeviceAesDecryptParam;
import com.irobotics.aiot.device.vo.ServerTimeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name = "device-设备扩展信息相关接口")
@RestController
@RequestMapping("/device/extend")
public class DeviceExtendController {

    @Autowired
    private DeviceExtendService deviceExtendService;


    @ApiLog(description = "获取服务器时间(src为AES对称加密的密文，获取tenantId的md5值，并截取其中的第8到第24位转化为UTF-8作为秘钥，对(sn+ts)进行加密得到的")
    @Operation(summary = "获取服务器时间(src为AES对称加密的密文，获取tenantId的md5值，并截取其中的第8到第24位转化为UTF-8作为秘钥，对(sn+ts)进行加密得到的")
    @PostMapping("/getServerTime")
    public ResponseMessage<ServerTimeVo> getServerTime(@RequestBody @Valid DeviceAesDecryptParam param){
        String src = param.getSrc();
        String sn = param.getSn();
        String ts = param.getTs();
        Integer timeType = param.getTimeType();
        String tenantId = SystemContextUtils.getTenantId();
        return deviceExtendService.getServerTime(src,sn,ts,tenantId,timeType);
    }

}
