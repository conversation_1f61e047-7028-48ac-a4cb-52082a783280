package com.irobotics.aiot.device.controller.device;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.service.LockDeviceService;
import com.irobotics.aiot.device.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 门锁相关接口
 */
@SuppressWarnings("Duplicates")
@Tag(name = "门锁相关接口")
@Slf4j
@RestController
@RequestMapping("/lock")
public class LockDeviceController {

    /**
     * 门锁业务层
     */
    @Autowired
    private LockDeviceService lockDeviceService;

    /**
     * 注册获取passcode
     *
     * @param param
     * @return
     * @throws Exception
     */
    @ApiLog(description = "1.注册获取passcode")
    @Operation(summary = "1.注册获取passcode")
    @PostMapping("/getPasscode")
    public ResponseMessage<LockGetPcdRep> getPasscode(@RequestBody LockLoginParam param) throws Exception {
        return lockDeviceService.getPasscode(param);
    }

    /**
     * 注册交换门锁公钥
     *
     * @param lockBindReq
     * @return
     * @throws Exception
     */
    @ApiLog(description = "2.注册交换门锁公钥")
    @Operation(summary = "2.注册交换门锁公钥")
    @PostMapping("/publicKeyExchange")
    public ResponseMessage<LockBindRep> publicKeyExchange(@RequestBody LockBindReq lockBindReq) throws Exception {
        return lockDeviceService.publicKeyExchange(lockBindReq);
    }

    /**
     * 注册检查门锁
     *
     * @param param
     * @return
     * @throws Exception
     */
    @ApiLog(description = "3.注册检查门锁")
    @Operation(summary = "3.注册检查门锁")
    @PostMapping("/checkDevConfirm")
    public ResponseMessage<CheckDevConfirmRep> checkDevConfirm(@RequestBody CheckDevConfirmParam param) throws Exception {
        return lockDeviceService.checkDevConfirm(param);
    }

    /**
     * 门锁登录前获取服务端公钥
     *
     * @param param
     * @return
     * @throws Exception
     */
    @ApiLog(description = "4.门锁登录前获取服务端公钥")
    @Operation(summary = "4.门锁登录前获取服务端公钥")
    @PostMapping("/loginGetPub")
    public ResponseMessage<LoginGetPubRep> loginGetPub(@RequestBody LockLoginParam param) throws Exception {
        return lockDeviceService.loginGetPub(param);
    }

    /**
     * 门锁登录前获取验证信息
     *
     * @param lockLoginGetVerifyReq
     * @return
     * @throws Exception
     */
    @ApiLog(description = "5.门锁登录前获取验证信息")
    @Operation(summary = "5.门锁登录前获取验证信息")
    @PostMapping("/loginGetVerifyData")
    public ResponseMessage<LockLoginGetVerifyRep> loginGetVerifyData(@RequestBody LockLoginGetVerifyReq lockLoginGetVerifyReq) throws Exception {
        return lockDeviceService.loginGetVerifyData(lockLoginGetVerifyReq);
    }

    /**
     * 门锁登录
     *
     * @param loginEndReq
     * @return
     */
    @ApiLog(description = "6.门锁登录")
    @Operation(summary = "6.门锁登录")
    @ApiResponses({@ApiResponse(code = 0, message = "返回数据中，data里面会有两个token，AUTH：是http访问需要用到的token，EMQ_TOKEN：是连接emq的token")})
    @PostMapping("/loginEnd")
    public ResponseMessage<LoginEndRep> loginEnd(@RequestBody LoginEndReq loginEndReq) {
        return lockDeviceService.loginEnd(loginEndReq);
    }

    /**
     * 门锁重置
     *
     * @param resetLockReq
     * @return
     */
    @ApiLog(description = "门锁重置，删除绑定关系和ltmk")
    @Operation(summary = "门锁重置，删除绑定关系和ltmk")
    @PostMapping("/resetLock")
    public ResponseMessage<ResetLockRep> resetLock(@RequestBody ResetLockReq resetLockReq) {
        return lockDeviceService.resetLock(resetLockReq);
    }
}
