package com.irobotics.aiot.device.controller.device;


import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.service.RtcDeviceTokenInfoService;
import com.irobotics.aiot.device.vo.RtcActivateDeviceTokenVo;
import com.irobotics.aiot.device.vo.RtcDeviceNodeInfoReq;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 设备RTC秘钥以及token信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@RestController
@RequestMapping("/device/rtc")
public class RtcDeviceController {


    @Autowired
    private RtcDeviceTokenInfoService rtcDeviceTokenInfoService;

    @ApiLog(description = "激活设备，获取访问授权令牌")
    @Operation(summary = "激活设备，获取访问授权令牌")
    @PostMapping("/activateDevice")
    public ResponseMessage<RtcActivateDeviceTokenVo> activateDevice(@RequestBody @Valid RtcDeviceNodeInfoReq param){
        return rtcDeviceTokenInfoService.activateDevice(param);
    }


}
