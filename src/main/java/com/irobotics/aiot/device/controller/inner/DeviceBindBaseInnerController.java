package com.irobotics.aiot.device.controller.inner;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.entity.DeviceBindBaseEntity;
import com.irobotics.aiot.device.service.IDeviceBindBaseService;
import com.irobotics.aiot.device.service.IDeviceBindService;
import com.irobotics.aiot.device.vo.app.BindDeviceInfoVo;
import com.irobotics.aiot.device.vo.base.BaseBindDeviceReq;
import com.irobotics.aiot.device.vo.inner.BindBaseListReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.PARAM_VALIDATE_FAIL;

/**
 * @author:tlh
 * @create: 2023-10-16 20:08:47
 * @Description:
 */
@Tag(name = "inner-服务内部调用基站绑定相关接口")
@RestController
@RequestMapping("/inner/bindBase/")
public class DeviceBindBaseInnerController {

    @Autowired
    private IDeviceBindBaseService deviceBindBaseService;

    @ApiLog(description = "根据基站sn或者设备sn获取基站绑定列表")
    @Operation(summary = "根据基站sn或者设备sn获取基站绑定列表")
    @PostMapping("/bindListBySn")
    public ResponseMessage<List<DeviceBindBaseEntity>> bindListBySn(@RequestBody BindBaseListReq req){
        if (CollectionUtils.isEmpty(req.getSnList())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "snList不能为空");
        }
        return deviceBindBaseService.bindListByDeviceSn(req);
    }


    /**
     * user-center 调用
     * @param req
     * @return
     */
    @ApiLog(description = "基站绑定设备")
    @Operation(summary = "基站绑定设备")
    @PostMapping("/baseBindDevice")
    public ResponseMessage<Boolean> baseBindDevice(@RequestBody BaseBindDeviceReq req){
        SystemContextUtils.setContextValue(ContextKey.ID,req.getUserId());
        return ResponseMessage.buildSuccess(deviceBindBaseService.bind(req.getDeviceId(),req.getSn(),req.getUserId(),req.getBaseSn()));
    }
}
