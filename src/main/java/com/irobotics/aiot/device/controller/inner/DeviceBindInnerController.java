package com.irobotics.aiot.device.controller.inner;

import cn.hutool.core.collection.CollectionUtil;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.entity.DeviceBindUserEntity;
import com.irobotics.aiot.device.service.IDeviceBindUserService;
import com.irobotics.aiot.device.vo.AddOnlineAppSnReq;
import com.irobotics.aiot.device.vo.app.BindDeviceInfoVo;
import com.irobotics.aiot.device.vo.app.UntieDeviceReq;
import com.irobotics.aiot.device.vo.inner.OwnerListParam;
import com.irobotics.aiot.device.vo.inner.OwnerListVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.PARAM_VALIDATE_FAIL;

/**
 * @author:tlh
 * @create: 2023-07-07 11:02:39
 * @Description:
 */
@Tag(name = "inner-服务内部调用设备绑定相关接口")
@RestController
@RequestMapping("/inner/bind/")
public class DeviceBindInnerController {

    @Autowired
    private IDeviceBindUserService deviceBindUserService;


    @ApiLog(description = "根据设备Id解绑设备")
    @Operation(summary = "根据设备Id解绑设备")
    @PostMapping("/untie")
    public ResponseMessage<Boolean> untieDevice(@RequestBody UntieDeviceReq req) {
        if (StringUtils.isBlank(req.getDeviceId())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceId不能为空");
        }
        return deviceBindUserService.untieDevice(req);
    }


    @ApiLog(description = "根据userId获取绑定设备列表")
    @Operation(summary = "根据userId获取绑定设备列表")
    @GetMapping("/bindListByUserId")
    public ResponseMessage<List<BindDeviceInfoVo>> bindListByUserId(@RequestParam("userId") String userId){
        if (StringUtils.isBlank(userId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "userId不能为空");
        }
        return deviceBindUserService.getBindListByUserId(userId);
    }


    /**
     * smart-home-service 调用
     * @param list
     * @return
     */
    @ApiLog(description = "同步绑定关系")
    @Operation(summary = "同步绑定关系")
    @PostMapping("/syncBind")
    public ResponseMessage<Boolean> syncBind(@RequestBody List<DeviceBindUserEntity> list) {
        if (CollectionUtil.isEmpty(list)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "list");
        }
        return deviceBindUserService.addBatch(list);
    }


    /**
     * smart-home-service 调用
     * @param userId
     * @return
     */
    @ApiLog(description = "根据userId获取绑定设备列表")
    @Operation(summary = "根据userId获取绑定设备列表")
    @GetMapping("/getBindList")
    public ResponseMessage<List<DeviceBindUserEntity>> getBindList(@RequestParam("userId") String userId,@RequestParam("tenantId") String tenantId) {
        if (StringUtils.isBlank(userId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "userId");
        }
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "tenantId");
        }
        return deviceBindUserService.getBindList(tenantId,userId);
    }

    /**
     * smart-home-service 调用
     * @param ids
     * @return
     */
    @ApiLog(description = "根据id删除绑定关系")
    @Operation(summary = "根据id删除绑定关系")
    @DeleteMapping("/deleteByIds")
    public ResponseMessage<Boolean> deleteByIds(@RequestParam("ids") List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "ids");
        }
        return deviceBindUserService.deleteByIds(ids);
    }


    @ApiLog(description = "根据设备id获取设备拥有者列表")
    @Operation(summary = "根据设备id获取设备拥有者列表")
    @PostMapping("/getOwnerListById")
    public ResponseMessage<List<OwnerListVo>> getOwnerListById(@RequestBody OwnerListParam param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "设备id为空");
        }
        return deviceBindUserService.getOwnerListByDeviceId(param);
    }


    /**
     * user-center 调用
     * @param userIds
     * @return
     */
    @ApiLog(description = "根据userId删除绑定关系")
    @Operation(summary = "根据userId删除绑定关系")
    @DeleteMapping("/deleteByUserIds")
    public ResponseMessage<Boolean> deleteByUserIds(@RequestParam("userIds") List<String> userIds,@RequestParam("tenantId") String tenantId) {
        if (CollectionUtil.isEmpty(userIds)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "userIds");
        }
        if (StringUtils.isBlank(tenantId)){
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "tenantId");
        }
        return deviceBindUserService.deleteByUserIds(userIds,tenantId);
    }

    /**
     * smart-home-service 调用
     * @param req
     * @return
     */
    @ApiLog(description = "添加在线app的sn缓存，用于实时地图(smart-home-service 调用)")
    @Operation(summary = "添加在线app的sn缓存，用于实时地图(smart-home-service 调用)")
    @PostMapping("/addOnlineAppSnCacheForTempMap")
    public ResponseMessage<Boolean> addOnlineAppSnCacheForTempMap(@RequestBody AddOnlineAppSnReq req) {
        if(ObjectUtils.isEmpty(req)  || CollectionUtils.isEmpty(req.getSnList())){
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "req");
        }
        deviceBindUserService.addOnlineAppSnCacheForTempMap(req.getSnList());
        return ResponseMessage.buildSuccess(true);
    }

}
