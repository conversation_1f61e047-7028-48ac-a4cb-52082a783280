package com.irobotics.aiot.device.controller.inner;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.remote.ProductServiceRemote;
import com.irobotics.aiot.device.service.DeviceControlService;
import com.irobotics.aiot.device.service.DeviceResetService;
import com.irobotics.aiot.device.service.IDeviceInfoService;
import com.irobotics.aiot.device.service.IDeviceInviteShareService;
import com.irobotics.aiot.device.utils.TenantIdHandleUtil;
import com.irobotics.aiot.device.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.*;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/9/23 17:57
 */
@SuppressWarnings("Duplicates")
@Tag(name = "inner-服务内部调用设备相关接口")
@RestController
@RequestMapping("/inner/device")
public class DeviceInnerController {

    private static final String TENANT_ID = "tenantId";

    @Autowired
    private IDeviceInfoService deviceInfoService;

    @Autowired
    private DeviceControlService deviceControlService;

    @Autowired
    private ProductServiceRemote productServiceRemote;

    @Autowired
    private IDeviceInviteShareService deviceInviteShareService;

    @Autowired
    private DeviceInfoCache deviceInfoCache;

    @Autowired
    private DeviceResetService deviceResetService;

    @ApiLog(description = "根据设备id获取设备status")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @GetMapping(value = "/status")
    public ResponseMessage<Object> getRobotStatus(@RequestParam String tenantId, @RequestParam String deviceId) {
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        //预防厂商id（租户id）传递错误的问题，优先使用头部的厂商id（租户）作为条件，其次才是参数
        TenantIdHandleUtil.handleTenantId(tenantId);
        if (StringUtils.isBlank(deviceId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "robotId");
        }
        DeviceInfoEntity device = deviceInfoCache.getCacheById(tenantId, deviceId);
        if (Objects.isNull(device)) {
            LogUtils.error("设备不存在，tenantId={}, deviceId={}", tenantId, device);
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA, "设备不存在");
        }
        String sn = device.getSn();
        if (!deviceInfoService.ifOnline(sn)) {
            //离线，则直接返回null
            return ResponseMessage.buildSuccess(null);
        }
        return ResponseMessage.buildSuccess(deviceInfoService.getDeviceStatus(sn));
    }

    @ApiLog(description = "根据SN获取设备state")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @GetMapping(value = "/state")
    public ResponseMessage<Object> getRobotState(@RequestParam String tenantId, @RequestParam String sn) {
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        //预防厂商id（租户id）传递错误的问题，优先使用头部的厂商id（租户）作为条件，其次才是参数
        TenantIdHandleUtil.handleTenantId(tenantId);
        return ResponseMessage.buildSuccess(deviceInfoService.getDeviceStatus(sn));
    }

    @ApiLog(description = "根据SN号获取设备id")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @GetMapping(value = "/id")
    public ResponseMessage<String> getRobotId(@RequestParam String tenantId, @RequestParam String sn) {
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        //预防厂商id（租户id）传递错误的问题，优先使用头部的厂商id（租户）作为条件，其次才是参数
        TenantIdHandleUtil.handleTenantId(tenantId);
        if (StringUtils.isBlank(sn)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "sn");
        }
        DeviceInfoEntity device = deviceInfoCache.getCacheBySn(tenantId, sn);
        return ResponseMessage.buildSuccess(Objects.isNull(device) ? null : device.getId());
    }

    @ApiLog(description = "根据SN号获取设备信息")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @GetMapping(value = "/info")
    public ResponseMessage<DeviceInfoEntity> getRobotDetail(@RequestParam String tenantId, @RequestParam String sn) {
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        //预防厂商id（租户id）传递错误的问题，优先使用头部的厂商id（租户）作为条件，其次才是参数
        TenantIdHandleUtil.handleTenantId(tenantId);
        if (StringUtils.isBlank(sn)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "sn");
        }
        DeviceInfoEntity device = deviceInfoCache.getCacheBySn(tenantId, sn);
        return ResponseMessage.buildSuccess(device);
    }

    @ApiLog(description = "根据设备id查看设备是否激活")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @Operation(summary = "查看设备是否激活")
    @GetMapping(value = "/active")
    public ResponseMessage<Boolean> isActivatedRobot(@RequestParam String tenantId, @RequestParam String deviceId) {
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        //预防厂商id（租户id）传递错误的问题，优先使用头部的厂商id（租户）作为条件，其次才是参数
        TenantIdHandleUtil.handleTenantId(tenantId);
        if (StringUtils.isBlank(deviceId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        DeviceInfoEntity device = deviceInfoCache.getCacheById(tenantId, deviceId);
        return ResponseMessage.buildSuccess(device != null);
    }

    @ApiLog(description = "获取所有设备SN列表")
    @Operation(summary = "获取所有设备SN列表")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @GetMapping(value = "/sn")
    public ResponseMessage<List<String>> selectAllRobotSn(@RequestParam String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        //预防厂商id（租户id）传递错误的问题，优先使用头部的厂商id（租户）作为条件，其次才是参数
        TenantIdHandleUtil.handleTenantId(tenantId);
        return ResponseMessage.buildSuccess(deviceInfoService.selectAllRobotSn());
    }

    @ApiLog(description = "获取产品的设备数量")
    @Operation(summary = "获取产品的设备数量,参数长度为0会查询全部设备数量")
    @GetMapping(value = "/getDeviceCountByPro")
    public ResponseMessage<Map<String, Long>> getDeviceCountByPro(@RequestParam List<String> productIds) {
        if (productIds == null) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL);
        }
        return ResponseMessage.buildSuccess(deviceInfoService.getDeviceCountByPro(productIds));
    }

    @ApiLog(description = "设备远程控制")
    @Operation(summary = "设备远程控制,设备在线时发送，返回 0: 表示成功，1：表示不在线,2：表示获取不到设备的状态，其它 代表异常")
    @PostMapping(value = "control")
    public ResponseMessage<Integer> control(@RequestBody DeviceControlReq req) {
        if (Objects.isNull(req)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL);
        }
        if (StringUtils.isBlank(req.getDeviceId())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "未选中设备");
        }
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        return deviceControlService.controlDevice(req, tenantId);
    }

    @ApiLog(description = "通过设备id获取产品类型id")
    @Operation(summary = "通过设备id获取产品类型id,头部参数tenantId")
    @GetMapping("/classify")
    public ResponseMessage<String> getClassify(@RequestParam String deviceId) {
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        if (StringUtils.isBlank(deviceId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceId");
        }
        DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheById(tenantId, deviceId);
        if (Objects.isNull(deviceInfo)) {
            return ResponseMessage.buildFail(NOT_FOUND_DATA, "设备不存在");
        }
        String productId = deviceInfo.getProductId();
        return productServiceRemote.getClassifyId(productId);
    }

    @ApiLog(description = "删除分享的设备时，删除分享记录")
    @Operation(summary = "删除分享的设备时，删除分享记录,头部参数tenantId")
    @DeleteMapping("/share")
    public ResponseMessage<Boolean> delShare(@RequestBody DelShareReq req) {
        try {
            return deviceInviteShareService.delShare(req);
        } catch (Exception e) {
            LogUtils.error(e, "删除分享的设备时，删除分享记录失败，tenantId={}, userId={}, req={}", SystemContextUtils.getContextValue(ContextKey.TENANT_ID), SystemContextUtils.getId(), req);
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
    }

    @ApiLog(description = "根据设备ID集合获取设备信息")
    @Operation(summary = "根据设备ID集合获取设备信息", tags = {"设备查询相关接口"})
    @PostMapping("/getDeviceInfosByIds")
    public ResponseMessage<List<DeviceInfoEntity>> getDeviceInfosByIds(@RequestBody List<String> deviceIds) {
        return deviceInfoService.getDeviceInfosByIds(deviceIds);
    }

    @ApiLog(description = "根据设备ID集合获取设备昵称")
    @Operation(summary = "根据设备ID集合获取设备昵称")
    @PostMapping("/getDeviceNickNameByIds")
    public ResponseMessage<Map<String, String>> getDeviceNickNameByIds(@RequestBody List<String> ids) {
        return deviceInfoService.getDeviceNickNameByIds(ids);
    }

    @ApiLog(description = "根据产品型号代码获取所有设备信息，分页")
    @Operation(summary = "根据产品型号代码获取所有设备信息，分页")
    @GetMapping("/productModeCode")
    public ResponseMessage<Page<DeviceInfoEntity>> getDeviceByCode(@RequestParam String productModeCode,
                                                                   @RequestParam(required = false, defaultValue = "1") Integer page,
                                                                   @RequestParam(required = false, defaultValue = "30") Integer pageSize) {
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        try {
            return ResponseMessage.buildSuccess(deviceInfoService.getPageByCode(productModeCode, page, pageSize));
        } catch (Exception e) {
            LogUtils.error(e, "根据产品型号代码获取所有设备信息，分页，异常, productModeCode={}, page={}, pageSize={}, exceptionMs={}", productModeCode, page, pageSize, e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, e.getMessage());
        }
    }

    @ApiLog(description = "根据产品型号代码获取设备数量")
    @Operation(summary = "根据产品型号代码获取设备数量")
    @GetMapping("/count/productModeCode")
    public ResponseMessage<Integer> getCountByCode(@RequestParam String productModeCode) {
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        try {
            return ResponseMessage.buildSuccess(deviceInfoService.getCountByCode(productModeCode));
        } catch (Exception e) {
            LogUtils.error(e, "根据产品型号代码获取设备数量异常, productModeCode={}, exceptionMs={}", productModeCode, e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, e.getMessage());
        }
    }

    @ApiLog(description = "更新设备的在app中是否要验证密码")
    @Operation(summary = "更新设备的在app中是否要验证密码")
    @PutMapping("/update/verifyPassword")
    public ResponseMessage<Boolean> updateVerifyPassword(@RequestBody DeviceVerifyPasswordVo vo) {
        try {
            return deviceInfoService.updateVerifyPassword(vo);
        } catch (Exception e) {
            LogUtils.error(e, "更新设备的在app中是否要验证密码异常, vo={}, exceptionMsg={}", vo, e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, e.getMessage());
        }
    }

    @ApiLog(description = "通过设备sn获取设备列表")
    @Operation(summary = "通过设备sn获取设备列表")
    @PutMapping("/list/sns")
    public ResponseMessage<List<DeviceInfoEntity>> getList(@RequestBody List<DeviceSnAndTenantReq> req) {
        try {
            if (CollectionUtils.isEmpty(req)) {
                return ResponseMessage.buildSuccess(new ArrayList<>());
            }
            List<DeviceInfoEntity> list = new ArrayList<>();
            for (DeviceSnAndTenantReq item : req) {
                if (StringUtils.isBlank(item.getSn()) || StringUtils.isBlank(item.getTenantId())) {
                    continue;
                }
                DeviceInfoEntity device = deviceInfoCache.getCacheBySn(item.getTenantId(), item.getSn());
                if (Objects.nonNull(device)) {
                    list.add(device);
                }
            }
            return ResponseMessage.buildSuccess(list);
        } catch (Exception e) {
            LogUtils.error(e, "通过设备sn获取设备列表异常, req={}, exceptionMsg={}", req, e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, e.getMessage());
        }
    }

    /**
     * 重置设备昵称，把设备昵称重置为设备默认昵称,并删除分享记录
     *
     * @param req
     * @return
     */
    @ApiLog(description = "重置设备昵称，把设备昵称重置为设备默认昵称,并删除分享记录")
    @PutMapping("/resetNickname")
    public ResponseMessage<Boolean> resetNickname(@RequestBody ResetNicknameReq req) {
        try {
            return ResponseMessage.buildSuccess(deviceResetService.ResetNickname(req));
        } catch (Exception e) {
            LogUtils.error(e, "重置设备昵称失败,sn={}", req.getSn());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, e.getMessage());
        }
    }

    /**
     * 更新产品图片时，同步更新产品下所有设备冗余的产品图片字段
     *
     * @param vo
     * @return
     */
    @ApiLog(description = "更新产品图片时，同步更新产品下所有设备冗余的产品图片字段")
    @PutMapping("/update/photo")
    public ResponseMessage<Boolean> updatePhoto(@RequestBody UpdatePhotoVo vo) {
        try {
            return deviceInfoService.updatePhoto(vo);
        } catch (Exception e) {
            LogUtils.error(e, "更新产品图片时，同步更新产品下所有设备冗余的产品图片字段失败,req={}", vo);
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, e.getMessage());
        }
    }

    @ApiLog(description = "根据设备ID获取设备信息")
    @Operation(summary = "根据设备ID获取设备信息",  tags = {"inner-服务内部调用设备相关接口"})
    @GetMapping("/getDeviceInfoById/{id}")
    public ResponseMessage<DeviceInfoEntity> getDeviceInfoById(@PathVariable String id) {
        return deviceInfoService.getDeviceInfoById(id);
    }

    @ApiLog(description = "获取某个产品型号下前N%个活跃设备SN")
    @Operation(summary = "获取某个产品型号下前N%个活跃设备SN", tags = {"inner-服务内部调用设备相关接口"})
    @PostMapping("/getActiveDeviceSNByCode")
    public ResponseMessage<List<String>> getActiveDeviceSNByCode(@Valid @RequestBody DeviceInfoReq deviceInfoReq) {
        return ResponseMessage.buildSuccess(deviceInfoService.getActiveDeviceSNByCode(deviceInfoReq));
    }

    /**
     * 内部调用，设备恢复出厂设置
     */
    @ApiLog(description = "内部调用，设备恢复出厂设置")
    @Operation(summary = "内部调用，设备恢复出厂设置")
    @PostMapping("reset/device/{deviceId}")
    public ResponseMessage<Void> resetDevice(@PathVariable String deviceId) {
        deviceResetService.reset(deviceId);
        return ResponseMessage.buildSuccess();
    }


    /**
     * 根据设备id获取设备信息
     */
    @ApiLog(description = "内部调用，根据设备id获取设备信息")
    @Operation(summary = "内部调用，根据设备id获取设备信息")
    @GetMapping("getById")
    public ResponseMessage<DeviceInfoEntity> getById(@RequestParam String tenantId,@RequestParam String deviceId) {
        DeviceInfoEntity cacheById = deviceInfoCache.getCacheById(tenantId, deviceId);
        return ResponseMessage.buildSuccess(cacheById);
    }
}