package com.irobotics.aiot.device.controller.inner;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.entity.DeviceMapEntity;
import com.irobotics.aiot.device.service.IDeviceMapService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "inner-服务内部调用设备地图相关接口")
@RestController
@RequestMapping("/inner/device/map")
public class DeviceMapInnerController {

    @Autowired
    IDeviceMapService deviceMapService;

    @ApiLog(description = "获取设备当前地图")
    @Operation(summary = "获取设备当前地图")
    @GetMapping(value = "/getCurMap")
    public ResponseMessage<DeviceMapEntity> getCurMap(@RequestParam String sn) {
        return deviceMapService.getCurMap(sn);
    }
}
