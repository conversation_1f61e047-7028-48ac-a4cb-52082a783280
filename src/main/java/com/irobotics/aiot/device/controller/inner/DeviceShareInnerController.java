package com.irobotics.aiot.device.controller.inner;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.service.IDeviceInviteShareService;
import com.irobotics.aiot.device.vo.DelShareReq;
import com.irobotics.aiot.device.vo.ShareFamilyVo;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.INNER_SERVER_ERROR;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/12/23 17:56
 */
@SuppressWarnings("Duplicates")
@Tag(name = "inner-服务内部调用设备分享接口")
@RestController
@RequestMapping("/inner/share")
public class DeviceShareInnerController {
    /**
     * 设备分享业务层
     */
    @Resource
    private IDeviceInviteShareService inviteShareService;

    /**
     * 分享家庭
     *
     * @param vo
     * @return
     */
    @ApiLog(description = "分享家庭")
    @PostMapping("/family")
    public ResponseMessage<List<String>> shareFamily(@RequestBody ShareFamilyVo vo) {
        try {
            return inviteShareService.doShareFamily(vo);
        } catch (Exception e) {
            LogUtils.error(e, "分享家庭，保存分享信息失败");
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
    }

    /**
     * 被分享者主动退出家庭
     *
     * @param req
     * @return
     */
    @PostMapping("/remove")
    @ApiLog(description = "被分享者主动退出家庭")
    public ResponseMessage<Boolean> remove(@RequestBody DelShareReq req) {
        try {
            return inviteShareService.delShare(req);
        } catch (Exception e) {
            LogUtils.error(e, "分享者退出分享家庭，保存分享信息失败");
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
    }

    /**
     * 通过企业id和用户id删除分享信息
     *
     * @param tenantId
     * @param userId
     * @return
     */
    @ApiLog(description = "通过企业id和用户id删除分享信息，注销用户专用")
    @DeleteMapping("/del/userId")
    public ResponseMessage<Boolean> deleteByUserId(@RequestParam String tenantId, @RequestParam String userId) {
        try {
            return ResponseMessage.buildSuccess(inviteShareService.delByUserId(tenantId, userId));
        } catch (Exception e) {
            LogUtils.error(e, "通过企业id和用户id删除分享信息失败, tenantId={}, userId={}, exceptionMsg={}", tenantId, userId, e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, e.getMessage());
        }
    }
}
