package com.irobotics.aiot.device.controller.inner;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.device.remote.model.PackageInfoVo;
import com.irobotics.aiot.device.service.IDeviceUpgradeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * @description:
 * @author: pengfeng
 * @time: 2022/4/28 18:07
 */
@SuppressWarnings("Duplicates")
@Tag(name = "inner-服务内部调用设备升级接口")
@RestController
@RequestMapping("/inner/device")
public class DeviceUpgradeController {
    @Resource
    private IDeviceUpgradeService deviceUpgradeService;

    /**
     * 无用逻辑，业务没有使用--by:pengf1947
     *
     * @param info
     * @return
     */
    @PostMapping("/publishDevices")
    @ApiOperation("根据升级包主动下发所有符合条件设备升级")
    public ResponseMessage publishDevices(@RequestBody PackageInfoVo info) {
        if (null == info || StringUtils.isBlank(info.getProductModelCode()) || StringUtils.isBlank(info.getId())) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL);
        }
        deviceUpgradeService.publishDevices(info);
        return  ResponseMessage.buildSuccess();
    }

    /**
     * 创建OTA主动下发任务的设备详情信息
     *
     * @param info
     * @return
     */
    @PostMapping("/createPushUpgradeTask")
    @ApiOperation("创建OTA主动下发任务详情信息")
    @ApiImplicitParams(@ApiImplicitParam(paramType = "header", name = "tenantId", required = true))
    public ResponseMessage createPushUpgradeTask(@RequestHeader("tenantId") String tenantId, @RequestBody PackageInfoVo info) {
        if (null == info || StringUtils.isBlank(info.getTenantId()) ||
                StringUtils.isBlank(info.getProductModelCode()) || StringUtils.isBlank(info.getPushTaskId())) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL);
        }
        deviceUpgradeService.createPushUpgradeTask(info);
        return ResponseMessage.buildSuccess();
    }
}
