package com.irobotics.aiot.device.controller.inner;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.entity.ProductSnDetailActiveZoneEntity;
import com.irobotics.aiot.device.service.IProductSnDetailActiveZoneService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * sn激活 控制器
 */
@RestController
@RequestMapping("/inner/sn/active")
public class InnerProductSnDetailActiveZoneController {

    /**
     * sn激活 服务类
     */
    @Autowired
    private IProductSnDetailActiveZoneService productSnDetailActiveZoneService;

    /**
     * 保存一条已激活的sn信息
     * @param productSnDetailActiveZoneEntity
     * @return
     */
    @ApiLog(description = "保存一条已激活的sn信息")
    @Operation(summary  = "保存一条已激活的sn信息")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @PostMapping("/insert")
    public ResponseMessage<Boolean> saveActiveSn(@RequestBody ProductSnDetailActiveZoneEntity productSnDetailActiveZoneEntity) {
        try {
            return productSnDetailActiveZoneService.insertOne(productSnDetailActiveZoneEntity);
        } catch (Exception e) {
            LogUtils.error(e, "保存一条已激活的sn信息异常, exceptionMsg={}", e.getMessage());
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR, e.getMessage());
        }
    }

    /**
     * 通过sn删除一条已激活的sn信息
     * @param sn
     * @return
     */
    @ApiLog(description = "通过sn删除一条已激活的sn信息")
    @Operation(summary  = "通过sn删除一条已激活的sn信息")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @DeleteMapping("/sn")
    public ResponseMessage<Boolean> delBySn(@RequestParam String sn) {
        try {
            return productSnDetailActiveZoneService.delBySn(sn);
        } catch (Exception e) {
            LogUtils.error(e, "通过sn删除一条已激活的sn信息异常, exceptionMsg={}", e.getMessage());
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR, e.getMessage());
        }
    }

    /**
     * 通过sn列表删除已激活的sn信息
     * @param sns
     * @return
     */
    @ApiLog(description = "通过sn列表删除已激活的sn信息")
    @Operation(summary  = "通过sn列表删除已激活的sn信息")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @DeleteMapping("/sns")
    public ResponseMessage<Boolean> delBySn(@RequestBody List<String> sns) {
        try {
            return productSnDetailActiveZoneService.delBySns(sns);
        } catch (Exception e) {
            LogUtils.error(e, "通过sn列表删除已激活的sn信息, exceptionMsg={}", e.getMessage());
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR, e.getMessage());
        }
    }


    @ApiLog(description = "通过groupId获取sn数量")
    @Operation(summary  = "通过groupId获取sn数量")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @GetMapping("/countByGroupId")
    public ResponseMessage<Long> countByGroupId(@RequestParam String groupId) {
        try {
            return ResponseMessage.buildSuccess(productSnDetailActiveZoneService.countByGroupId(groupId));
        } catch (Exception e) {
            LogUtils.error(e, "通过sn列表删除已激活的sn信息, exceptionMsg={}", e.getMessage());
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR, e.getMessage());
        }
    }



    @ApiLog(description = "根据sn查询激活信息")
    @Operation(summary  = "根据sn查询激活信息")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @GetMapping("/getBySn")
    public ResponseMessage<ProductSnDetailActiveZoneEntity> getBySn(@RequestParam("sn") String sn){
        try {
            if (StringUtils.isBlank(sn)) {
                return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "sn");
            }
            return ResponseMessage.buildSuccess(productSnDetailActiveZoneService.getBySn(sn));
        } catch (Exception e) {
            LogUtils.error(e, "插入数据信息, exceptionMsg={}", e.getMessage());
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR, e.getMessage());
        }
    }


    @ApiLog(description = "根据产品型号id模糊查询sn")
    @Operation(summary  = "根据产品型号id模糊查询sn")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @GetMapping("/snCodes")
    public ResponseMessage<List<ProductSnDetailActiveZoneEntity>> snCodes(@RequestParam("productModeId") String productModeId,
                                                                          @RequestParam("sn") String sn){
        try {
            if (StringUtils.isBlank(sn)) {
                return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "sn");
            }
            if (StringUtils.isBlank(productModeId)) {
                return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "productModeId");
            }
            return ResponseMessage.buildSuccess(productSnDetailActiveZoneService.snCodes(productModeId,sn));
        } catch (Exception e) {
            LogUtils.error(e, "根据产品型号id模糊查询sn, exceptionMsg={}", e.getMessage());
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR, e.getMessage());
        }
    }


    @ApiLog(description = "批量获取已激活的sn")
    @Operation(summary  = "批量获取已激活的sn")
    @ApiImplicitParam(paramType = "header", name = "tenantId")
    @GetMapping("/getBySnList")
    public ResponseMessage<List<ProductSnDetailActiveZoneEntity>> getBySnList(List<String> snList){
        try {
            if (CollectionUtils.isEmpty(snList)) {
                return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "snList");
            }
            return ResponseMessage.buildSuccess(productSnDetailActiveZoneService.getBySnList(snList));
        } catch (Exception e) {
            LogUtils.error(e, "批量获取已激活的sn, exceptionMsg={}", e.getMessage());
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR, e.getMessage());
        }
    }

}
