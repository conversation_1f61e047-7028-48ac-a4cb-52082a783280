package com.irobotics.aiot.device.controller.mqtt;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.dto.MqttPropertyGetReqDTO;
import com.irobotics.aiot.device.service.IDeviceBindUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * @author:tlh
 * @create: 2023-07-07 11:02:39
 * @Description:
 */
@Tag(name = "inner-mqtt内部调用相关接口")
@RestController
@RequestMapping("/inner/mqtt/")
public class MqttTopicController {

    //mqtt secretKey
    private static final String MQTT_SECRET_KEY = "aBcDeFgH1+2kLmNopQrStUvWxYz1234567890ZaBcD=";

    @Autowired
    private IDeviceBindUserService deviceBindUserService;

    /**
     * mqtt 调用
     * @param param
     * @return
     */
    @ApiLog(description = "属性获取topic处理")
    @Operation(summary = "属性获取topic处理")
    @PostMapping("/propertyGetHandler")
    public ResponseMessage<Boolean> propertyGetHandler(@RequestBody MqttPropertyGetReqDTO param) {
        Object auth = SystemContextUtils.getContextValue(ContextKey.AUTH);
        if(Objects.isNull(auth) || !StringUtils.equals(String.valueOf(auth), MQTT_SECRET_KEY)){
            return ResponseMessage.buildFail(ResponseCode.NO_AUTH);
        }
        deviceBindUserService.propertyGetTopicHandler(param);
        return ResponseMessage.buildSuccess(true);
    }

}
