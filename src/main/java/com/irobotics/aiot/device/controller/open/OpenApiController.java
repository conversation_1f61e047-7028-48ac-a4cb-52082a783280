package com.irobotics.aiot.device.controller.open;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.basic.exception.AppRuntimeException;
import com.irobotics.aiot.bravo.basic.exception.NoDataFoundException;
import com.irobotics.aiot.bravo.basic.exception.ParameterValidateException;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.bravo.log.annotation.ApiLog;
import com.irobotics.aiot.device.dto.LargeModelParam;
import com.irobotics.aiot.device.service.IBdService;
import com.irobotics.aiot.device.service.IOpenDeviceBindService;
import com.irobotics.aiot.device.vo.BdSkillParam;
import com.irobotics.aiot.device.vo.BdSkillResponse;
import com.irobotics.aiot.device.vo.open.BatchExecuteResult;
import com.irobotics.aiot.device.vo.open.BindDeviceVo;
import com.irobotics.aiot.device.vo.open.OpenBindDeviceInfoVo;
import com.irobotics.aiot.device.vo.open.UnBindDeviceVo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.INNER_SERVER_ERROR;
import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.PARAM_VALIDATE_FAIL;

/**
 * 第三方SDK接口
 *
 * <AUTHOR>
 */
@Tag(name = "open-第三方接口")
@RestController
@RequestMapping("/open")
public class OpenApiController {


    /**
     * 第三方绑定业务层
     */
    @Autowired
    private IOpenDeviceBindService openDeviceBindService;

    @Resource
    private IBdService bdService;

    /**
     * qpp绑定设备
     *
     * @param vo
     * @return
     */
    @Deprecated(since = "20240601", forRemoval = true)
    @ApiLog(description = "app绑定设备")
    @Operation(summary = "app绑定设备")
    // @PostMapping("/bindDevice")
    public ResponseMessage<Boolean> bindDevice(@RequestBody BindDeviceVo vo) {
        try {
            return openDeviceBindService.bindDevice(vo);
        } catch (NoDataFoundException noData) {
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA, noData.getMessage());
        } catch (ParameterValidateException p) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, p.getMessage());
        } catch (AppRuntimeException app) {
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, app.getMessage());
        } catch (Exception e) {
            LogUtils.info("bind bindDevice出现异常,缓存中的key和入参的bindKey不一致: {}, exception={}", vo.getBindKey(),
                    e.getMessage());
            return ResponseMessage.buildFail(ResponseCode.CACHE_ERROR);
        }
    }

    /**
     * app解绑设备
     *
     * @param vo
     * @return
     */
    @Deprecated(since = "20240601", forRemoval = true)
    @ApiLog(description = "app解绑设备")
    @Operation(summary = "app解绑设备")
    // @DeleteMapping("/delete/device")
    public ResponseMessage<Boolean> deleteDevice(@RequestBody BindDeviceVo vo) {
        try {
            return openDeviceBindService.unBindDevice(vo);
        } catch (ParameterValidateException e) {
            LogUtils.error(e, "app绑定设备异常，vo={}, exception={}", vo, e.getMessage());
            return ResponseMessage.buildFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            LogUtils.error(e, "app解绑设备异常，vo={}, exception={}", vo, e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
    }


    @Deprecated(since = "20240601", forRemoval = true)
    @ApiLog(description = "通过userid查询绑定的设备列表")
    @Operation(summary = "通过userid查询绑定的设备列表")
    // @GetMapping("/bindDeviceList")
    public ResponseMessage<List<OpenBindDeviceInfoVo>> bindDeviceList() {
        try {
            String userId = SystemContextUtils.getId();
            return openDeviceBindService.deviceList(userId);
        } catch (ParameterValidateException e) {
            LogUtils.error(e, "通过userid查询绑定的设备列表, exception={}", e.getMessage());
            return ResponseMessage.buildFail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            LogUtils.error(e, "通过userid查询绑定的设备列表, exception={}", e.getMessage());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
    }

    @ApiLog(description = "批量解绑设备（用户-设备）")
    @ApiOperation(value = "批量解绑设备（用户-设备）")
    @ApiImplicitParam(paramType = "header", name = "tenantId", required = true)
    @PostMapping("/device/batchUnBind")
    public ResponseMessage<BatchExecuteResult> batchUnBindDevice(@RequestBody UnBindDeviceVo unBindDeviceVo) {
        return openDeviceBindService.batchUnBindDevice(unBindDeviceVo);
    }


    @ApiLog(description = "定制技能")
    @Operation(summary = "定制技能")
    @PostMapping("/bd/skill")
    public BdSkillResponse skill(@RequestHeader("authorization") String authorization,
                                             @RequestHeader("accesskey") String accessKey,
                                             @RequestHeader("timestamp") String timestamp,
                                             @RequestBody String jsonStr) {
        return bdService.skill(jsonStr, authorization, accessKey, timestamp);
    }


    @ApiLog(description = "根据协议获取回复词")
    @Operation(summary = "根据协议获取回复词")
    @PostMapping("/largemodel/getReply")
    public ResponseMessage<List<String>> getReply(@RequestBody LargeModelParam param) {
        return bdService.getReply(param);
    }

    @ApiLog(description = "test")
    @Operation(summary = "test")
    @PostMapping("/test")
    public BdSkillResponse test(@RequestBody BdSkillParam param) {
        return bdService.test(param);
    }

}