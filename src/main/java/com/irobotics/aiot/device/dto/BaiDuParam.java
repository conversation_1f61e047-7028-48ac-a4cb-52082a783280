package com.irobotics.aiot.device.dto;

import com.irobotics.aiot.device.vo.BdAddDictParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BaiDuParam {

    @Schema(description = "每个接口百度需要的参数，json字符串格式")
    private String baiduparam;


    @Schema(description = "baiduUrl 调用地址 例如：/v1/customDict/add;/v1/customDict/batchAdd ")
    private String baiduUrl;
}
