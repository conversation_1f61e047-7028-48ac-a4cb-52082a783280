package com.irobotics.aiot.device.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 百度语控会话记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="TConversation对象", description="百度语控会话记录表")
public class ConversationParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "sn",required = true)
    private String sn;

    @Schema(description = "用户输入语音",required = true)
    private String query;


    @Schema(description = "设备回复语音",required = true)
    private String reply;


}
