package com.irobotics.aiot.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 第三方用户设备绑定关系信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_device_bind_base")
@Schema(name ="DeviceBindBaseEntity对象", description="第三方用户设备绑定关系信息表")
public class DeviceBindBaseEntity extends Model {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    @Schema(description = "设备id")
    private String deviceId;

    @Schema(description = "设备sn")
    private String sn;

    @Schema(description = "绑定时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime bindTime;

    @Schema(description = "基站id")
    private String baseId;

    @Schema(description = "创建时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateId;

    @Schema(description = "更新时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "区域")
    private String zone;

    @Schema(description = "基站sn")
    private String baseSn;

    public DeviceBindBaseEntity() {
    }

    public DeviceBindBaseEntity(String id, String deviceId, String sn, LocalDateTime bindTime,
                                String baseId, String baseSn,LocalDateTime createTime) {
        this.id = id;
        this.deviceId = deviceId;
        this.sn = sn;
        this.bindTime = bindTime;
        this.baseId = baseId;
        this.createTime = createTime;
        this.baseSn = baseSn;
    }
}
