package com.irobotics.aiot.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.bravo.commons.util.SnowflakeUtil;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 关闭智能家居设备绑定关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_device_bind_user")
@Schema(name ="DeviceBindUserEntity对象", description="关闭智能家居设备绑定关系表")
public class DeviceBindUserEntity extends Model {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    @Schema(description = "设备id")
    private String deviceId;

    @Schema(description = "设备sn")
    private String sn;

    @Schema(description = "拥有者id")
    private String owner;

    @Schema(description = "绑定时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime bindTime;

    @Schema(description = "创建人")
    private String creatorId;

    @Schema(description = "创建时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateId;

    @Schema(description = "更新时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "区域")
    private String zone;

    @Schema(description = "最新控制时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime ctrTime;

    @Schema(description = "排序权重")
    private Integer deviceSortWeight;

    @Schema(description = "用户端类型（0、非基站（Android、IOS、Andipad、IOSipad、Mini、Web、M），1.基站）")
    private String userAgent;

    @Schema(description = "设备分享标识（0、正常，1、分享设备）")
    private String flag;

    @Schema(description = "产品ID")
    private String productId;

    @Schema(description = "数据添加来源：1：从t_smart_home_room_device同步；2：直接添加")
    private Integer source;

    @Schema(description = "分享状态（0、未分享，1、分享)")
    private String shareStatus;

    /**
     * 设备昵称
     */
    @TableField(exist = false)
    private String nickname;

    /**
     * 绑定设备的用户类型,和sso用户类型对应
     */
    @Schema(description = "用户类型: 1:自有账号，2:第三方授权账号;默认为值为 1")
    private Integer bindUserType;

    public DeviceBindUserEntity(String deviceId,String sn, String creatorId,String owner,String productId,String flag,Integer source,Integer bindUserType) {
        this.id = SnowflakeUtil.snowflakeId();
        this.deviceId = deviceId;
        this.sn = sn;
        this.owner = owner;
        this.bindTime = LocalDateTime.now();
        this.creatorId = creatorId;
        this.createTime = LocalDateTime.now();
        this.productId = productId;
        this.flag = flag;
        this.source = source;
        this.bindUserType = bindUserType;
    }

    public DeviceBindUserEntity(String id, String deviceId, String sn, String owner,String creatorId,Integer bindUserType,String productId) {
        this.id = id;
        this.deviceId = deviceId;
        this.sn = sn;
        this.owner = owner;
        this.creatorId = creatorId;
        this.bindTime = LocalDateTime.now();
        this.createTime = LocalDateTime.now();
        this.bindUserType = bindUserType;
        this.productId = productId;
    }

    public DeviceBindUserEntity() {
    }
}
