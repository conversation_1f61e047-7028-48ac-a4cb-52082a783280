package com.irobotics.aiot.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 设备信息实体
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_device_info")
@Schema(name  = "DeviceInfoEntity对象", description = "")
public class DeviceInfoEntity extends Model {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id（t_sn_detail的主键id）")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private String productId;

    /**
     * 说明，设备里的媒体设备，存在一个自己的设备id，相当于设备与这个媒体设备进行一对一的关联关系
     */
    @Schema(description = "虚拟设备id，json结构")
    private String iotId;

    /**
     * 虚拟设备id
     */
    @Schema(description = "虚拟设备id，相当与原工程类型")
    private String productModeCode;

    /**
     * mac地址
     */
    @Schema(description = "mac地址")
    private String mac;

    /**
     * 设备sn
     */
    @Schema(description = "设备sn")
    private String sn;

    /**
     * 密钥
     */
    @Schema(description = "密钥")
    @TableField(value = "`key`")
    private String key;

    /**
     * 软件版本号
     */
    @Schema(description = "软件版本号")
    private String versions;

    /**
     * 设备昵称
     */
    @Schema(description = "设备昵称")
    private String nickname;

    /**
     * 设备默认昵称
     */
    @Schema(description = "设备默认昵称")
    private String defaultNickname;

    /**
     * 测试设备是否已重置
     */
    @Schema(description = "测试设备是否已重置：0已重置，1未重置")
    private String resetStatus;

    /**
     * 最近在线时间
     */
    @Schema(description = "最近在线时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime onlineTime;

    /**
     * 最近离线时间
     */
    @Schema(description = "最近离线时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime offlineTime;

    /**
     * 登录IP
     */
    @Schema(description = "登录IP")
    private String ip;

    /**
     * 最近登录的城市
     */
    @Schema(description = "最近登录的城市")
    private String city;

    /**
     * 产品的图片
     */
    @Schema(description = "产品的图片，冗余字段")
    private String photoUrl;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    private String updateBy;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private String tenantId;

    /**
     * 区域
     */
    @Schema(description = "区域")
    private String zone;

    /**
     * 设备状态
     */
    @Schema(description = "设备状态")
    @TableField(exist = false)
    private boolean status;

    /**
     * 设备影子最后一次更新的时间
     */
    @Schema(description = "设备影子最后一次更新的时间")
    @TableField(exist = false)
    private Long timeStamp;

    /**
     * app是否开启密码验证
     */
    @Schema(description = "app是否开启密码验证，默认为否(0:否，1:是)")
    private Integer verifyPassword;

    /**
     * 当前登录城市是否禁止使用  0  不禁止使用  1  禁止使用
     */
    @Schema(description = "当前登录城市是否禁止使用  0  不禁止使用  1  禁止使用")
    private Integer disableCity;


    /**
     * 无参构造
     */
    public DeviceInfoEntity() {
    }

    /**
     * 有参构造
     */
    public DeviceInfoEntity(String deviceId, String sn, String mac) {
        this.id = deviceId;
        this.sn = sn;
        this.mac = mac;
    }

    /**
     * 有参构造
     */
    public DeviceInfoEntity(String id, String ip, String sn, String mac, String key, String productModeCode,
                            String photoUrl, String nickname, String defaultNickname, String city, String versions,
                            String productId) {
        this.id = id;
        this.ip = ip;
        this.sn = sn;
        this.mac = mac;
        this.key = key;
        this.productModeCode = productModeCode;
        this.photoUrl = photoUrl;
        this.nickname = nickname;
        this.defaultNickname = defaultNickname;
        this.city = city;
        this.versions = versions;
        this.productId = productId;
        this.createTime = LocalDateTime.now();
        this.onlineTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 重写
     */
    @Override
    public String toString() {
        return "DeviceInfoEntity{" +
                "id='" + id + '\'' +
                ", productId='" + productId + '\'' +
                ", productModeCode='" + productModeCode + '\'' +
                ", mac='" + mac + '\'' +
                ", sn='" + sn + '\'' +
                ", versions='" + versions + '\'' +
                ", nickname='" + nickname + '\'' +
                ", defaultNickname='" + defaultNickname + '\'' +
                ", resetStatus='" + resetStatus + '\'' +
                ", onlineTime=" + onlineTime +
                ", offlineTime=" + offlineTime +
                ", ip='" + ip + '\'' +
                ", city='" + city + '\'' +
                ", createBy='" + createBy + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", updateBy='" + updateBy + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", zone='" + zone + '\'' +
                ", verifyPassword=" + verifyPassword +
                '}';
    }
}
