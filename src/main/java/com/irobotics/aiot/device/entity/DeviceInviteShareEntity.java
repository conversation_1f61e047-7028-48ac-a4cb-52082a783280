package com.irobotics.aiot.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 设备分享邀请信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_device_invite_share")
@Schema(name  = "DeviceInviteShareEntity对象", description = "设备分享邀请信息表")
public class DeviceInviteShareEntity extends Model {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id，通过雪花算法生产")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 邀请人的用户id
     */
    @Schema(description = "邀请人的用户id")
    private String inviteId;

    /**
     * 被邀请人的用户id
     */
    @Schema(description = "被邀请人的用户id")
    private String beInviteId;

    /**
     * 设备id或家庭id
     */
    @Schema(description = "设备id或家庭id")
    private String targetId;

    /**
     * 类型,0-共享设备；1-共享家庭
     */
    @Schema(description = "0-共享设备；1-共享家庭")
    private Integer type;

    /**
     * 0-正常；1-已同意；2-已拒绝
     */
    @Schema(description = "0-正常；1-已同意；2-已拒绝")
    private Integer status;

    /**
     * 删除 1-邀请者删除；2-被邀请者删除
     */
    @Schema(description = "删除 1-邀请者删除；2-被邀请者删除")
    private Integer removed;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private String tenantId;

    /**
     * 区域
     */
    @Schema(description = "区域")
    private String zone;

    /**
     * 无参构造
     */
    public DeviceInviteShareEntity() {
    }

    /**
     * 有参构造
     */
    public DeviceInviteShareEntity(String id, String inviteId, String beInviteId, String targetId, int type, int status, String updateBy, String createBy) {
        this.id = id;
        this.inviteId = inviteId;
        this.beInviteId = beInviteId;
        this.targetId = targetId;
        this.type = type;
        this.status = status;
        this.updateBy = updateBy;
        this.createBy = createBy;
        this.updateTime = LocalDateTime.now();
        this.createTime = LocalDateTime.now();
    }
}
