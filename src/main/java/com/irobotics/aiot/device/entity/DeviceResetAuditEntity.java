package com.irobotics.aiot.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 设备出厂初始化审核表
 * @since 2023/5/18
 */
@Data
@TableName("t_device_reset_audit")
@Schema(name ="DeviceResetAuditEntity对象", description="设备出厂初始化审核表")
public class DeviceResetAuditEntity implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private String tenantId;

    /**
     * 申请需要删除SN
     */
    @Schema(description = "申请需要删除SN")
    private String applySnJson;

    /**
     * 需要删除SN
     */
    @Schema(description = "确认需要删除SN")
    private String delSnJson;

    /**
     * 未删除SN
     */
    @Schema(description = "确认不允许删除SN")
    private String noDelSnJson;

    /**
     * 审核状态
     */
    @Schema(description = "审核状态（0：未审核，1：审核通过，2：审核不通过，3：部分审核通过）",required = true)
    private Integer auditStatus;

    /**
     * 审核说明
     */
    @Schema(description = "审核说明(审核不通过时必填)")
    private String auditDesc;

    /**
     * 申请人
     */
    @Schema(description = "申请人")
    private String applyBy;

    /**
     * 申请时间
     */
    @Schema(description = "申请时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime applyTime;

    /**
     * 审核人
     */
    @Schema(description = "审核人")
    private String auditBy;

    /**
     * 审核时间
     */
    @Schema(description = "审核时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime auditTime;
}
