package com.irobotics.aiot.device.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @since 2022/1/17
 */
@Data
@Document(collection = "device_reset_record")
public class DeviceResetRecordEntity {

    /**
     * id
     */
    @Id
    private String id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 企业id
     */
    private String tenantId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 设备sn
     */
    private String sn;

    /**
     * 是否成功
     */
    private Boolean isSuccess;

    /**
     * 失败信息
     */
    private List<String> failMessages;

    /**
     * 时间
     */
    private Long time;
}
