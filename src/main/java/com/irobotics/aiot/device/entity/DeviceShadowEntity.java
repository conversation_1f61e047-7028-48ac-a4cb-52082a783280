package com.irobotics.aiot.device.entity;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/9/6 17:30
 * @desc 设备影子实体类
 */
@Data
public class DeviceShadowEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *设备SN
     */
    @Schema(description = "设备SN")
    private String id;

    /**
     *产品Key
     */
    @Schema(description = "产品Key")
    private String productKey;

    /**
     *物模型版本
     */
    @Schema(description = "物模型版本")
    private String modelVersion;

    /**
     *设备影子属性内容
     */
    @Schema(description = "设备影子属性内容")
    private JSONObject properties;

    /**
     *设备在线状态
     */
    @Schema(description = "设备在线状态：离线、在线")
    private Boolean onlineStatus;

    /**
     *区域
     */
    @Schema(description = "区域")
    private String zone;

    /**
     *租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     *创建者
     */
    @Schema(description = "创建者：设备ID")
    private String createBy;

    /**
     *创建时间
     */
    @Schema(description = "创建时间")
    private Long timestamp;
}
