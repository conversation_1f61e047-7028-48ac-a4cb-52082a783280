package com.irobotics.aiot.device.entity;

import lombok.Getter;
import lombok.Setter;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;

@Getter
@Setter
@Document("device_version_log")
public class DeviceVersionLog {

    /**
     * SN + hash(packageVersions的数据)
     */
    @Id
    private String id;
    private List<DevicePackage> packageVersions;
    private LocalDateTime createTime;
    private LocalDateTime lastLoginTime;

    @Getter
    @Setter
    public static class DevicePackage implements Comparable<DevicePackage> {

        private String packageType;
        private Integer version;
        private String versionName;
        private String ctrlVersion;

        @Override
        public int hashCode() {
            return 31 * packageType.hashCode() + 31 * version + 31 * versionName.hashCode() + 31 * ctrlVersion.hashCode();
        }

        @Override
        public int compareTo(@NotNull DevicePackage o) {
            int versionCompare = Integer.compare(version, o.getVersion());
            if (versionCompare != 0) {
                return versionCompare;
            }
            int versionNameCompare = this.versionName.compareTo(o.getVersionName());
            if (versionNameCompare != 0) {
                return versionNameCompare;
            }
            int ctrlVersionCompare = this.ctrlVersion.compareTo(o.getCtrlVersion());
            if (ctrlVersionCompare != 0) {
                return ctrlVersionCompare;
            }
            return this.packageType.compareTo(o.getPackageType());
        }
    }
}
