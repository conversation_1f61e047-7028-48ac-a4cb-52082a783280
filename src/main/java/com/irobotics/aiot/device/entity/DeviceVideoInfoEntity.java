package com.irobotics.aiot.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.web.util.OperatorUtils;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_device_video_info")
@Schema(name ="DeviceVideoInfoEntity对象", description="")
public class DeviceVideoInfoEntity extends Model {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "aiot云平台产品id")
    private String productId;

    @Schema(description = "sn")
    private String sn;

    @Schema(description = "提供商 1、阿里云")
    private Integer provider;

    @Schema(description = "设备sn")
    private String deviceName;

    @Schema(description = "阿里云产品key")
    private String productKey;

    @Schema(description = "阿里云设备秘钥")
    private String deviceSecret;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    public DeviceVideoInfoEntity(){

    }

    public DeviceVideoInfoEntity(String id,String productId,String sn,String deviceName, String productKey, String deviceSecret) {
        this.id = id;
        this.productId = productId;
        this.sn = sn;
        this.deviceName = deviceName;
        this.productKey = productKey;
        this.deviceSecret = deviceSecret;
        this.createBy = OperatorUtils.getOperateBy();
        this.createTime = LocalDateTime.now();
    }
}
