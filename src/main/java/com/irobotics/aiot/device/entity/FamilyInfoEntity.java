package com.irobotics.aiot.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 家庭表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_family_info")
@Schema(name  = "FamilyInfoEntity对象", description = "家庭表")
public class FamilyInfoEntity extends Model {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 家庭名称
     */
    @Schema(description = "家庭名称")
    private String familyName;

    /**
     * 家庭头像url
     */
    @Schema(description = "家庭头像url")
    private String headUrl;

    /**
     * 国家
     */
    @Schema(description = "国家")
    private String country;

    /**
     *
     */
    @Schema(description = "城市")
    private String city;

    /**
     * 地址
     */
    @Schema(description = "地址")
    private String address;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @Schema(description = "纬度")
    private BigDecimal latitude;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除（0：正常；低于0的数字：已删除）")
    private String deleteFlag;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    private String creatorId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    private String updateId;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    /**
     * 用户是否设置设备安全密码
     */
    @Schema(description = "用户是否设置设备安全密码(0:否，1:是)")
    private Integer secureFlag;

}
