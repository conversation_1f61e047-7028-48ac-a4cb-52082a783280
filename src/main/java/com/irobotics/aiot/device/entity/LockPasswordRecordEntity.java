package com.irobotics.aiot.device.entity;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * @author: tlh
 * @date: 2022-05-12 14:57
 * @description: 门锁一次性密码
 **/
@Data
@Document(collection = "lock_password_record")
public class LockPasswordRecordEntity {

    /**
     * id
     */
    @Id
    @Schema(description = "id")
    private String id;

    /**
     *用户id
     */
    @Schema(description = "用户id")
    private String userId;

    /**
     *企业id
     */
    @Schema(description = "企业id")
    private String tenantId;

    /**
     *设备sn
     */
    @Schema(description = "设备sn")
    private String sn;

    /**
     *创建时间
     */
    @Schema(description = "创建时间")
    private Long createTime;

    /**
     *到期时间
     */
    @Schema(description = "到期时间")
    private Long expireTime;
}
