package com.irobotics.aiot.device.entity;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * @description: 设备分享极光推送记录
 * @author: huangwa1911
 * @time: 2021/12/22 20:49
 */
@Data
@Document(collection = "notice_share_record")
public class NoticeShareRecordEntity {

    /**
     * 主键id
     */
    @Schema(description = "主键id，雪花算法生成")
    private String id;

    /**
     * 消息发送者
     */
    @Schema(description = "消息发送者")
    private String from;

    /**
     * 消息接收者
     */
    @Schema(description = "消息接收者")
    private String to;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;

    /**
     * 消息体
     */
    @Schema(description = "消息体")
    private String msg;

    /**
     * 状态
     */
    @Schema(description = "状态，0-正常；1-已同意；2-已拒绝；")
    private Integer status;

    /**
     * 类型
     */
    @Schema(description = "类型,0-共享设备消息，1-共享家庭消息")
    private Integer type;

    /**
     * 结果
     */
    @Schema(description = "结果：-1-失败、0-成功、1-免扰消息")
    private Integer result;

    /**
     * 分享记录id
     */
    @Schema(description = "分享记录id")
    private String shareId;

    /**
     * 设备或家庭id
     */
    @Schema(description = "设备或家庭id")
    private String targetId;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String createBy;

    /**
     *
     */
    @Schema(description = "创建时间")
    private Long createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Long updateTime;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private String tenantId;

    /**
     * 地区
     */
    @Schema(description = "地区")
    private String zone;

    /**
     * 设备昵称或家庭名称
     */
    @Schema(description = "设备昵称或家庭名称")
    private String name;

    /**
     * 用户名
     */
    @Schema(description = "用户名，接收者或发送者用户名，区别于当前用户")
    private String username;

    /**
     * 图片地址
     */
    @Schema(description = "图片地址，用户图片或设备图片（产品图片）")
    private String photoUrl;

    /**
     * 设备sn
     */
    @Schema(description = "设备sn")
    private String sn;

    /**
     * 无参构造
     */
    public NoticeShareRecordEntity() {
    }
}
