package com.irobotics.aiot.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 产品型号（设备型号）表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_product_mode")
@Schema(name  = "ProductModeEntity对象", description = "产品型号（设备型号）表")
public class ProductModeEntity extends Model {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     *企业id
     */
    @Schema(description = "企业id")
    private String tenantId;

    /**
     *区域
     */
    @Schema(description = "区域")
    private String zone;

    /**
     *产品型号代码
     */
    @Schema(description = "产品型号代码（由 企业英文缩写.产品型号名称 组成）(功能相当于原工程类型）")
    private String code;

    /**
     *产品型号名称
     */
    @Schema(description = "产品型号名称（由 英文字母 和 数字 组成）")
    private String label;

    /**
     *产品信息id
     */
    @Schema(description = "产品信息id")
    private String productInfoId;

    /**
     *别名前缀
     */
    @Schema(description = "别名前缀（如：LETGO）")
    private String aliasPrefix;

    /**
     *sn号后缀位数
     */
    @Schema(description = "sn号后缀位数")
    private Integer snSuffixBit;

    /**
     *标签
     */
    @Schema(description = "标签")
    private String tag;

    /**
     *创建时间
     */
    @Schema(description = "创建时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     *创建者
     */
    @Schema(description = "创建者")
    private String createBy;

    /**
     *更新时间
     */
    @Schema(description = "更新时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    /**
     *更新者
     */
    @Schema(description = "更新者")
    private String updateBy;

    /**
     *是否删除
     */
    @Schema(description = "是否删除（0：正常；低于0的数字：已删除）")
    private Integer deleteFlag;

    /**
     *产品名称
     */
    @Schema(description = "产品名称")
    @TableField(exist = false)
    private String productName;

    /**
     *图片地址
     */
    @Schema(description = "图片地址")
    @TableField(exist = false)
    private String photoUrl;


    /**
     *是否开启防串货功能  0 不开启  1  开启
     */
    @Schema(description = "是否开启防串货功能  0 不开启  1  开启")
    private Integer preventCrossSelling;

    /**
     *是否开启防串货功能  0 不开启  1  开启
     */
    @Schema(description = "销售地区是否全球 0 全球  1  具体地区  详见 t_product_mode_sell_region")
    private Integer sellRegion;


    @Schema(description = "sellRegion=1时-销售地区-具体地区")
    @TableField(exist = false)
    private List<ProductModeSellRegionEntity> productModeSellRegions;

}
