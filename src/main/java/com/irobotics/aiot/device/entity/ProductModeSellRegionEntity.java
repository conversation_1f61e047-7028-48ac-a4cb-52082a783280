package com.irobotics.aiot.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 产品型号售卖地区表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_product_mode_sell_region")
@Schema(name = "ProductModeSellRegionEntity对象", description = "产品型号售卖地区表")
public class ProductModeSellRegionEntity extends Model {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 产品型号id
     */
    @Schema(description = "产品型号id")
    private String productModeId;


    /**
     * 地区id
     */
    @Schema(description = "地区id")
    private String regionId;

    /**
     * 地区id
     */
    @Schema(description = "地区中文名称")
    private String regionNameCh;

}
