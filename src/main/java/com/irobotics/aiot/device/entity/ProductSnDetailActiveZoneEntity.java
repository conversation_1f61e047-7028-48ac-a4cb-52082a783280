package com.irobotics.aiot.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 地区SN激活表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_product_sn_detail_active_zone")
@Schema(name  = "ProductSnDetailActiveZoneEntity对象", description = "")
public class ProductSnDetailActiveZoneEntity extends Model {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * sn号
     */
    @Schema(description = "sn号", required = true)
    private String sn;

    /**
     * sn组id
     */
    @Schema(description = "sn组id")
    private String groupId;

    /**
     * 密钥
     */
    @Schema(description = "密钥", required = true)
    @TableField(value = "`key`")
    private String key;

    /**
     * 激活时间
     */
    @Schema(description = "激活时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime activeTime;

    /**
     * 产品型号
     */
    @Schema(description = "产品型号", required = true)
    private String productModeId;

    /**
     * 区域
     */
    @Schema(description = "区域")
    @TableField(value = "`zone`")
    private String zone;

    /**
     * 创建者
     */
    @Schema(description = "创建者 用户ID")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 租户id
     */
    @Schema(description = "租户id", required = true)
    private String tenantId;

}
