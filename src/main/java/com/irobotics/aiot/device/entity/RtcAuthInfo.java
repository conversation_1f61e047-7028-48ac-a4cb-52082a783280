package com.irobotics.aiot.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用于存储声网平台的APP ID。RESTful API的key和secret
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_rtc_auth_info")
@ApiModel(value="TRtcAuthInfo对象", description="用于存储声网平台的APP ID。RESTful API的key和secret")
public class RtcAuthInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    @Schema(description = "声网的APP ID")
    private String rtcAppId;

    @Schema(description = "RESTful API的key（在声网平台开发配置中的key）")
    private String clientKey;

    @Schema(description = "RESTful API的secret（在声网平台开发配置中的secret）")
    private String clientSecret;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "是否激活。0表示激活，1表示未激活")
    private Integer active;

    @Schema(description = "描述/补充")
    private String description;

    @Schema(description = "创建时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    @Schema(description = "创建用户")
    private String createBy;

    @Schema(description = "更新用户")
    private String updateBy;


}
