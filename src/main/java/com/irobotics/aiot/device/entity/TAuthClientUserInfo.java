package com.irobotics.aiot.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Data
@Schema(name  = "TAuthClientUserInfo对象", description = "")
public class TAuthClientUserInfo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;

    /**
     * 用户名
     */
    @Schema(description = "用户名 脱敏加密")
    private String usernameHide;

    /**
     * 用户名
     */
    @Schema(description = "用户名 AES加密")
    private String usernameEnc;

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickname;

    /**
     * 手机号
     */
    @Schema(description = "手机号，脱敏")
    private String phoneHide;

    /**
     * 手机号
     */
    @Schema(description = "手机号，加密")
    private String phoneEnc;

    /**
     * 手机号
     */
    @Schema(description = "手机号，不加密")
    private String phoneShow;

    /**
     * 语言
     */
    @Schema(description = "语言")
    private String lang;

    /**
     * 邮箱地址
     */
    @Schema(description = "邮箱地址，只限于数据内部查询和模糊查询")
    private String emailHide;

    /**
     * 邮箱地址
     */
    @Schema(description = "邮箱地址，APP数据交付,加密方式,按已定的,AES加密,加密key(MD516（企业id）),app提交过来就是加密的数据字段")
    private String emailEnc;

    /**
     * 邮箱地址
     */
    @Schema(description = "邮箱地址,后台显示 但是中间已经隐藏相关信息")
    private String emailShow;

    /**
     * 头像URL
     */
    @Schema(description = "头像URL")
    private String avatarUrl;

    /**
     * 通知设置
     */
    @Schema(description = "通知设置，结构为JSON{\"ios\":\"xxxx\",\"base\":\"xxxxxx\",\"android\":\"xxx\"}。说明，xxx里面是对应某个端的极光推送的registerID ")
    private String noticeSetting;

    /**
     * app密码
     */
    @Schema(description = "app密码，密文（base64+盐）。用于点击设备时，需要验证这个密码，这个取决于设备是否设置需要验证密码")
    private String appPassword;

    /**
     * 区域
     */
    @Schema(description = "区域")
    private String zone;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 创建者
     */
    @Schema(description = "创建者 用户ID")
    private String createBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    /**
     * 修改者
     */
    @Schema(description = "修改者")
    private String updateBy;

    /**
     * 密文
     */
    @Schema(description = "密文（base64+盐）")
    private String password;

    /**
     * app产品
     */
    @Schema(description = "app产品")
    private String projectType;

    /**
     * 手机系统 (1：Android , 2：IOS，3：其他)
     */
    @TableField(exist = false)
    private Integer phoneSys;
}
