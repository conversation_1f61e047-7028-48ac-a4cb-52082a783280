package com.irobotics.aiot.device.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 用户门锁配置记录
 * @since 2022/5/6
 */
@Data
@Document(collection = "user_lock_record")
public class UserLockRecordEntity {

    /**
     * id
     */
    @Id
    private String id;
    /** 租户ID*/
    private String tenantId;
    /** 设备ID*/
    private String deviceId;
    /** 设备用户id*/
    private String deviceUserId;
    /** 名称*/
    private String name;
    /** 角色（owner：我，admin：管理员，customer：常访客）*/
    private String role;
    /** 起始时间*/
    private Long beginTime;
    /** 失效时间*/
    private Long endTime;
    /** 凭据列表信息*/
    private JSONObject lockCfg;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     *创建人
     */
    private String createBy;

    /**
     *更新时间
     */
    private Date updateTime;

    /**
     *更信任
     */
    private String updateBy;

}
