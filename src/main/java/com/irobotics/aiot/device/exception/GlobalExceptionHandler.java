package com.irobotics.aiot.device.exception;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.log.LogUtils;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.stream.Collectors;


@ControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 绑定异常处理
     *
     * @param e 异常
     * @return 结果
     */
    @ExceptionHandler(BindException.class)
    @ResponseBody
    public ResponseMessage<Object> bindExceptionHandler(BindException e) {
        String message = e.getBindingResult().getAllErrors().stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.joining());
        LogUtils.error(e, "绑定异常处理--->{}", e.getMessage());
        return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, message);
    }

    /**
     * ConstraintViolationException 异常
     *
     * @param e 异常
     * @return 结果
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseBody
    public ResponseMessage<Object> constraintViolationExceptionHandler(ConstraintViolationException e) {
        String message = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining());
        LogUtils.error(e, "ConstraintViolationException 异常处理--->{}", e.getMessage());
        return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, message);
    }

    /**
     * MethodArgumentNotValidException异常
     *
     * @param e 异常
     * @return 结果
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public ResponseMessage<Object> methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getAllErrors().stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.joining());
        LogUtils.error(e, "MethodArgumentNotValidException 异常处理--->{}", e.getMessage());
        return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, message);
    }
}