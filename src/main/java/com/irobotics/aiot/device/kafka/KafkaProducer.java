package com.irobotics.aiot.device.kafka;

import com.irobotics.aiot.bravo.log.LogUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/10/15 15:18
 */
@Component
public class KafkaProducer {

    @Autowired
    private KafkaTemplate kafkaTemplate;

    /**
     * kafka发送消息，指定topic
     *
     * @param topic kafka topic
     * @param data  消息内容
     */
    public void sendMessage(String topic, String data) {
        try {
            LogUtils.info("kafka发送消息，topic={}, content={}", topic, data);
            kafkaTemplate.send(topic, data);
        } catch (Exception e) {
            LogUtils.error("kafka发送消息异常，topic={}, data={}, exceptionMsg={}", topic, data, e.getMessage());
        }
    }
}
