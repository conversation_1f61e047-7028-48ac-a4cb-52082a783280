package com.irobotics.aiot.device.mapper;

import com.irobotics.aiot.device.entity.DeviceBindBaseEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.irobotics.aiot.device.vo.base.BaseBindVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 第三方用户设备绑定关系信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
public interface DeviceBindBaseMapper extends BaseMapper<DeviceBindBaseEntity> {

    @Select("SELECT tdb.base_id, tdb.device_id,tdb.sn,t.owner,td.mac,td.nickname " +
            " FROM t_device_bind_base tdb left join t_device_info td on tdb.sn = td.sn left join t_device_bind_user t on tdb.sn = t.sn WHERE tdb.base_id = #{baseId}" +
            " AND tdb.tenant_id = #{tenantId} ORDER BY tdb.bind_time DESC limit 1")
    List<BaseBindVo> getBindListByBaseId(@Param("baseId") String baseId, @Param("tenantId") String tenantId);
}
