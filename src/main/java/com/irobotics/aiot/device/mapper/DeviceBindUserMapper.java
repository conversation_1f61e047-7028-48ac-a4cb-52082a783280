package com.irobotics.aiot.device.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.device.entity.DeviceBindUserEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.irobotics.aiot.device.remote.model.BindIdVo;
import com.irobotics.aiot.device.vo.admin.AdminDeviceBindReq;
import com.irobotics.aiot.device.vo.admin.AdminDeviceBindVo;
import com.irobotics.aiot.device.vo.app.BindDeviceInfoVo;
import com.irobotics.aiot.device.vo.open.OpenBindDeviceInfoVo;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 关闭智能家居设备绑定关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-04
 */
public interface DeviceBindUserMapper extends BaseMapper<DeviceBindUserEntity> {

    /**
     *
     * 根据设备id，修改基站的owner
     * @param owner
     * @param deviceId
     * @return
     */
    int updateOwnerByDeviceId(@Param("owner") String owner, @Param("deviceId") String deviceId);

    /**
     * 根据设备id列表获取绑定用户id列表
     * @param tenantId
     * @param deviceIdList
     * @return
     */
    List<BindIdVo> getBindListByDeviceIdList(@Param("tenantId") String tenantId, @Param("deviceIdList") List<String> deviceIdList);

    /**
     * 根据用户id列表获取绑定设备id列表
     * @param tenantId
     * @param userIdList
     * @return
     */
    List<BindIdVo> getBindListByUserIdList(@Param("tenantId") String tenantId, @Param("userIdList") List<String> userIdList);

    /**
     * 获取绑定关系
     * @param page
     * @param tenantId
     * @param sn
     * @param userId
     * @param beginTime
     * @param endTime
     * @return
     */
    Page<AdminDeviceBindVo> getBindList(IPage<AdminDeviceBindReq> page, @Param("tenantId") String tenantId,
                                        @Param("sn") String sn, @Param("userId") String userId,
                                        @Param("beginTime") Timestamp beginTime, @Param("endTime") Timestamp endTime);

    /**
     * 重置基站用户为设备owner
     * @param deviceId
     * @return
     */
    int resetOwner(@Param("deviceId") String deviceId);

    /**
     * 根据用户id,获取绑定关系和设备昵称
     * @param tenantId
     * @param userId
     * @return
     */
    List<DeviceBindUserEntity> getListByUserId(@Param("tenantId") String tenantId,@Param("userId") String userId);


    /**
     * 据用户id,获取绑定关系和设备信息
     * @param userId
     * @return
     */
    List<BindDeviceInfoVo> getDeviceInfoAndBindByUserId(@Param("userId") String userId);

    /**
     * 开放平台对应的绑定关系
     * @param userId
     * @param tenantId
     * @return
     */
    List<OpenBindDeviceInfoVo> getOpenApiListByUserId(@Param("userId") String userId,@Param("tenantId") String tenantId);
}
