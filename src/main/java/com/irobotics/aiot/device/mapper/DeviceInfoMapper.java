package com.irobotics.aiot.device.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
public interface DeviceInfoMapper extends BaseMapper<DeviceInfoEntity> {

    /**
     * 通过产品id列表获取设备的数量
     *
     * @param productIds
     * @return Map<String, Long> key-产品id,value-数量
     */
    @InterceptorIgnore(tenantLine = "1")
    @Select("<script>" +
            "select product_id, COALESCE(count(id),0) as device_num from t_device_info where product_id in (" +
            " <foreach collection='productIds' index='index' item='productId' separator=','> " +
            "     #{productId} " +
            " </foreach>" +
            ") group by product_id" +
            "</script>")
    List<Map<String, Long>> selectCountByProuctId(@Param("productIds") List<String> productIds);

    /**
     * 更新设备的产品图片
     * 非唯一索引(product_id, tenant_id)
     *
     * @param tenantId
     * @param productId
     * @param photoUrl
     * @return
     */
    @InterceptorIgnore(tenantLine = "1")
    @Update("update t_device_info set photo_url=#{photoUrl} where product_id=#{productId} and tenant_id=#{tenantId}")
    int updatePhoto(@Param("tenantId") String tenantId, @Param("productId") String productId, @Param("photoUrl") String photoUrl);

    /**
     * 通过设备id获取设备信息
     *
     * @param tenantId
     * @param deviceId
     * @return
     */
    @InterceptorIgnore(tenantLine = "1")
    @Select("select id, product_id, product_mode_code, mac, sn, versions, nickname, default_nickname, reset_status, online_time, offline_time," +
            " `key`,photo_url,ip, city,tenant_id, zone,verify_password from t_device_info where id=#{deviceId} and tenant_id=#{tenantId} ")
    DeviceInfoEntity getById(String tenantId, String deviceId);

    /**
     * 通过设备sn获取设备信息
     *
     * @param tenantId
     * @param sn
     * @return
     */
    @InterceptorIgnore(tenantLine = "1")
    @Select("select id, product_id, product_mode_code, mac, sn, versions, nickname, default_nickname, reset_status, online_time, offline_time," +
            " `key`,photo_url,ip, city,tenant_id, zone, verify_password from t_device_info where sn=#{sn} and  tenant_id=#{tenantId} ")
    DeviceInfoEntity getBySn(String tenantId, String sn);

    /**
     * 通过设备mac获取设备信息
     *
     * @param tenantId
     * @param mac
     * @return
     */
    @InterceptorIgnore(tenantLine = "1")
    @Select("select id, product_id, product_mode_code, mac, sn, versions, nickname, default_nickname, reset_status, online_time, offline_time," +
            " `key`,photo_url,ip, city,tenant_id, zone, verify_password from t_device_info where mac=#{mac}  and tenant_id=#{tenantId} ")
    DeviceInfoEntity getByMac(String tenantId, String mac);

    /**
     * 通过设备sn和mac获取设备信息
     *
     * @param sn
     * @param mac
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "1")
    @Select("select id, product_id, product_mode_code, mac, sn, versions, nickname, default_nickname, reset_status, online_time, offline_time," +
            " `key`, photo_url, ip, city,tenant_id, zone, verify_password from t_device_info where sn=#{sn} and mac=#{mac}  and tenant_id=#{tenantId}")
    DeviceInfoEntity getBySnAndMac(String sn, String mac, String tenantId);

    /**
     * 通过产品型号代码分页获取设备列表
     *
     * @param page
     * @param productModeCode
     * @param tenantId
     * @return
     */
    @InterceptorIgnore(tenantLine = "1")
    @Select("select id, sn, mac, product_mode_code,versions, product_id, tenant_id,verify_password from t_device_info where product_mode_code=#{productModeCode} and tenant_id=#{tenantId}")
    Page<DeviceInfoEntity> getPageByCode(IPage<DeviceInfoEntity> page, String productModeCode, String tenantId);

    /**
     * 分页获取设备的id和sn列表
     *
     * @param page
     * @return
     */
    @InterceptorIgnore(tenantLine = "1")
    @Select("select id, sn from t_device_info")
    Page<DeviceInfoEntity> getPage(IPage<DeviceInfoEntity> page);

    /**
     * 获取设备的数量
     *
     * @return
     */
    @InterceptorIgnore(tenantLine = "1")
    @Select("select count(1) from t_device_info")
    Long selectCount();

    /**
     * 通过设备id更新iotId
     *
     * @param deviceId
     * @param iotId
     * @return
     */
    @InterceptorIgnore(tenantLine = "1")
    @Update("update t_device_info set iot_id=#{iotId} where id=#{deviceId}")
    int updateIotId(String deviceId, String iotId);

    /**
     * 根据产品模型代码与租户id获取设备数量
     *
     * @param productModeCode 产品模型代码
     * @param tenantId        租户id
     * @return 设备数量
     */
    @InterceptorIgnore(tenantLine = "1")
    @Select("select count(id) from t_device_info where product_mode_code=#{productModeCode} and tenant_id=#{tenantId}")
    Long getCountByCode(String productModeCode, String tenantId);
}