package com.irobotics.aiot.device.mapper;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.device.entity.DeviceInviteShareEntity;
import com.irobotics.aiot.device.vo.DeviceShareHisVo;
import com.irobotics.aiot.device.vo.DeviceShareVo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 设备分享邀请信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
public interface DeviceInviteShareMapper extends BaseMapper<DeviceInviteShareEntity> {

    /**
     * 分页获取设备共享历史信息
     *
     * @param page
     * @param userId   用户id
     * @param targetId 设备id或家庭id
     * @param type     共享的类型
     * @return
     */
    Page<DeviceShareHisVo> getDeviceShareHis(IPage<DeviceShareHisVo> page, String userId, String targetId, int type);

    /**
     * 获取邀请者的对应的设备共享记录
     *
     * @param page
     * @param userId
     * @param deviceId
     * @return
     */
    @Select("select dis.id, dis.be_invite_id as userId, dis.target_id, info.sn, info.mac, info.nickname as name, info.product_mode_code, info.iot_id, dis.status, dis.type, dis.removed, dis.create_time from t_device_invite_share dis " +
            "left join t_device_info info on dis.target_id=info.id  where dis.invite_id=#{userId} and dis.target_id=#{deviceId} and dis.type=0 order by dis.create_time desc")
    Page<DeviceShareVo> getListByInviter(IPage<DeviceShareVo> page, String userId, String deviceId);

    /**
     * 获取被分享者对应的设备共享记录
     *
     * @param page
     * @param userId   被分享者（被邀请者）用户id
     * @param deviceId 设备id
     * @return
     */
    @Select("select dis.id, dis.invite_id as userId, dis.target_id, info.sn,info.mac, info.nickname as name,info.product_mode_code, info.iot_id, dis.status, dis.type, dis.removed, dis.create_time from t_device_invite_share dis " +
            "left join t_device_info info on dis.target_id=info.id  where dis.be_invite_id=#{userId} and dis.target_id=#{deviceId} and dis.type=0  order by dis.create_time desc")
    Page<DeviceShareVo> getListByBeInviter(IPage<DeviceShareVo> page, String userId, String deviceId);

    /**
     * 通过id获取一条记录
     *
     * @param tenantId
     * @param id
     * @return
     */
    @Select("select id, invite_id, be_invite_id, target_id, type, status, removed, create_by, create_time, update_by, update_time, tenant_id, zone from t_device_invite_share where id=#{id} and tenant_id=#{tenantId}")
    DeviceInviteShareEntity getById(String tenantId, String id);

    /**
     * 删除一条记录
     *
     * @param deviceId
     * @return
     */
    @Delete("delete from t_device_invite_share where target_id=#{deviceId}")
    int deleteByDeviceId(String deviceId);

    /**
     * 通过用户id删除记录
     *
     * @param tenantId
     * @param userId   用户id（分享者或者被分享者）
     * @return
     */
    @InterceptorIgnore(tenantLine = "1")
    @Delete("delete from t_device_invite_share where tenant_id=#{tenantId} and (invite_id=#{userId} or be_invite_id=#{userId})")
    int deleteByUserId(String tenantId, String userId);

}
