package com.irobotics.aiot.device.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.irobotics.aiot.device.entity.ProductSnDetailActiveZoneEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * sn激活表mapper
 */
public interface ProductSnDetailActiveZoneMapper extends BaseMapper<ProductSnDetailActiveZoneEntity> {

    /**
     * 模糊查询sn
     * @param productModeId
     * @param sn
     * @return
     */
    @InterceptorIgnore(tenantLine = "1")
    @Select("select * from t_product_sn_detail_active_zone where product_mode_id=#{productModeId} and sn like concat('%',#{sn},'%') " +
            " order by create_time desc")
    List<ProductSnDetailActiveZoneEntity> snCodes(@Param("productModeId") String productModeId, @Param("sn") String sn);
}
