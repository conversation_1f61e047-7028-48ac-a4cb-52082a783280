//package com.irobotics.aiot.device.remote;
//
//import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
//import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
//import com.irobotics.aiot.bravo.log.LogUtils;
//import com.irobotics.aiot.device.vo.activiti.TaskWaitingVo;
//import com.irobotics.aiot.device.vo.activiti.WorkflowParam;
//import org.springframework.cloud.openfeign.FallbackFactory;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.stereotype.Component;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestParam;
//
///**
// * <AUTHOR>
// * @description: 流程引擎服务远程接口
// * @date 2023/11/1 14:58
// */
//@FeignClient(name = "activiti-common", fallbackFactory = ActivitiCommonRemote.ActivitiCommonRemoteFallback.class)
//public interface ActivitiCommonRemote {
//
//    @PostMapping("/inner/workflow/start")
//    ResponseMessage<String> startWorkflow(@RequestBody WorkflowParam workflowParam);
//
//    @GetMapping("/admin/workflow/getWaitTaskInfoByBizId")
//    ResponseMessage<TaskWaitingVo> getWaitTaskInfoByBizId(@RequestParam String bizId, @RequestParam String procDefineKey);
//
//
//    @Component
//    class ActivitiCommonRemoteFallback implements FallbackFactory<ActivitiCommonRemote> {
//        @Override
//        public ActivitiCommonRemote create(Throwable cause) {
//            return new ActivitiCommonRemote() {
//                @Override
//                public ResponseMessage<String> startWorkflow(WorkflowParam workflowParam) {
//                    LogUtils.error("远程启动流程实例失败,workflowParamVo ={}", workflowParam.toString());
//                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "远程启动流程实例失败，processKey:" + workflowParam.getProcessKey());
//                }
//
//                @Override
//                public ResponseMessage<TaskWaitingVo> getWaitTaskInfoByBizId(String bizId, String procDefineKey) {
//                    LogUtils.error("根据流程定义key和业务单据ID远程查询待办任务失败,bizId ={},procDefineKey={}", bizId, procDefineKey);
//                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "根据业务单据ID远程查询待办任务失败，bizId:" + bizId);
//                }
//            };
//        }
//    }
//}