package com.irobotics.aiot.device.remote;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.entity.DeviceShadowEntity;
import com.irobotics.aiot.device.remote.model.ClearFlowParam;
import org.springframework.cloud.openfeign.FallbackFactory;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备影子服务远程调用
 */
@SuppressWarnings("ALL")
@FeignClient(name = "device-shadow-service", fallbackFactory = DeviceShadowRemote.HystrixFallbackFactory.class)
public interface DeviceShadowRemote {

    /**
     * 通过设备sn获取设备影子信息
     * @param sn
     * @return
     */
    @GetMapping("/device-shadow/{sn}")
    ResponseMessage<DeviceShadowEntity> findBySN(@PathVariable @ApiParam("设备SN") String sn);

    /**
     * 通过sn列表获取设备影子列表
     * @param sns
     * @return
     */
    @PostMapping(value = "/device-shadow/all", consumes = MediaType.APPLICATION_JSON_VALUE )
    ResponseMessage<List<DeviceShadowEntity>> findAllBySN(@RequestBody @ApiParam("设备SN") List<String> sns);


    /**
     * 通过sn批量清除设备流水
     *
     * @param param
     * @return
     */
    @DeleteMapping("/inner/clear/flow/bySn")
    ResponseMessage<Boolean> flow(@RequestBody ClearFlowParam param);

    /**
     * 通过userId批量清除设备流水
     *
     * @param param
     * @return
     */
    @DeleteMapping("/inner/clear/flow/byUserId")
    ResponseMessage<Boolean> network(@RequestBody ClearFlowParam param);

    /**
     * 工厂类
     */
    @Component
    class HystrixFallbackFactory implements FallbackFactory<DeviceShadowRemote> {

        @Override
        public DeviceShadowRemote create(Throwable throwable) {
            return new DeviceShadowRemote() {

                @Override
                public ResponseMessage<DeviceShadowEntity> findBySN(String sn) {
                    LogUtils.error("获取一个设备影子异常,HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "获取一个设备影子");
                }

                @Override
                public ResponseMessage<List<DeviceShadowEntity>> findAllBySN(List<String> sns) {
                    LogUtils.error("获取多个设备影子异常,HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "获取多个设备影子");
                }

                @Override
                public ResponseMessage<Boolean> flow(ClearFlowParam param) {
                    LogUtils.error("通过sn批量清除设备流水异常,DataStatisticsServiceRemote-flow参数:{}", param);
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过sn批量清除设备流水异常");
                }

                @Override
                public ResponseMessage<Boolean> network(ClearFlowParam param) {
                    LogUtils.error("通过userId批量清除设备流水异常,DataStatisticsServiceRemote-network参数:{}", param);
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过userId批量清除设备流水异常");
                }
            };
        }
    }
}
