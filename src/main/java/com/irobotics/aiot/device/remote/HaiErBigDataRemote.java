package com.irobotics.aiot.device.remote;

import cn.hutool.core.lang.Tuple;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.config.HaiErBigdataConfig;
import com.irobotics.aiot.device.utils.SignUtil;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/18
 */
@Component
public class HaiErBigDataRemote {

    @Resource
    HaiErBigdataConfig haiErBigdataConfig;

    private final static String URL_HAIER_LOGIN = "/system-manager-rest/login";
    private final static String URL_HAIER_DEL_SWEEP_RECORD = "/bigdata-mobile-rest/delete/v3/common/if/delSweepRecord";
    private final static String PARAMS_TYPE = "bigdata";
    private final static String PARAMS_CODE = "openplatform";

    /**
     * 调用海尔大数据登录接口，获取user token
     *
     * @param username 用户名
     * @param password 密码
     * @return user token
     */
    public Tuple getUserAccessToken(String username, String password) {
        String finalUrl = haiErBigdataConfig.getUrl() + URL_HAIER_LOGIN;
        Map<String, String> loginBodyMap = new HashMap<>();
        loginBodyMap.put("username", username);
        loginBodyMap.put("password", password);
        loginBodyMap.put("type", PARAMS_TYPE);
        loginBodyMap.put("sysCode", PARAMS_CODE);
        String result = "";
        JSONObject resultObj = null;
        try {
            result = HttpUtil.post(finalUrl, JSONUtil.toJsonStr(loginBodyMap));
            resultObj = JSONUtil.parseObj(result);
        } catch (Exception ex) {
            LogUtils.error("海尔大数据接口-登录接口发起请求时出现异常,{}", ex.getMessage(), ex);
        }
        if (resultObj == null) {
            LogUtils.error("海尔大数据接口-登录接口接口结果解析为空");
            return new Tuple(false, "接口结果解析为空");
        }
        if (!resultObj.getStr("retCode").equalsIgnoreCase("0")) {
            String mes = "登录异常，请联系管理员查看日志";
            LogUtils.error("海尔大数据-登录接口调用出现异常 {} ", result);
            return new Tuple(false, mes);
        }
        return new Tuple(true, resultObj.getJSONObject("data").getStr("Access-User-Token"));
    }

    /**
     * 调用海尔大数据删除扫地机历史记录接口
     *
     * @param sns sn集合
     * @return 清除结果
     */
    public Tuple deleteSweepRecord(List<String> sns) {
        Tuple aTuple = getUserAccessToken(haiErBigdataConfig.getUsername(), haiErBigdataConfig.getPassword());
        if (!(boolean) aTuple.get(0)) {
            return aTuple;
        }
        String userToken = aTuple.get(1);

        String finalUrl = haiErBigdataConfig.getUrl() + URL_HAIER_DEL_SWEEP_RECORD;
        String ts = String.valueOf(System.currentTimeMillis());

        String sequenceId = UUID.randomUUID().toString().toLowerCase(Locale.ROOT).replace("-", "");
        Map<String, Object> reqBody = new HashMap<>();
        reqBody.put("sn", ts);
        reqBody.put("deviceSnList", sns);
        String reqBodyStr = JSONUtil.toJsonStr(reqBody);
        String appId = haiErBigdataConfig.getAppId();
        String appKey = haiErBigdataConfig.getAppKey();
        String sign = SignUtil.getHaiErSign(appId, appKey, ts, reqBodyStr, finalUrl);

        Map<String, String> headers = new HashMap<>();
        headers.put("appId", appId);
        headers.put("appKey", appKey);
        headers.put("Access-User-Token", userToken);
        headers.put("sign", sign);
        headers.put("timestamp", ts);
        headers.put("language", "zh-cn");
        headers.put("sequenceId", sequenceId);
        headers.put("Content-Type", "application/json;charset=UTF-8");
        String resultStr = "";
        JSONObject resultObj = null;
        try {
            resultStr = HttpUtil.createPost(finalUrl).addHeaders(headers).body(reqBodyStr).execute().body();
            resultObj = JSONUtil.parseObj(resultStr);
        } catch (Exception ex) {
            LogUtils.error("海尔大数据接口-删除扫地机历史记录发起请求时出现异常,{}", ex.getMessage(), ex);
        }
        if (resultObj == null) {
            String mes = "接口结果解析为空";
            LogUtils.error("海尔大数据接口-登录接口 {}", mes);
            return new Tuple(false, mes);
        }
        if (!resultObj.getStr("retCode").equalsIgnoreCase("1000")) {
            LogUtils.info("海尔大数据接口-删除扫地机历史记录调用时出现异常 {}", resultStr);
        }
        return new Tuple(true, resultObj.getStr("retInfo"));
    }
}
