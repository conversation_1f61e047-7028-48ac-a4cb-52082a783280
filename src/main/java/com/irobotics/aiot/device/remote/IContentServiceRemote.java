package com.irobotics.aiot.device.remote;

import com.irobotics.aiot.bravo.basic.exception.AppRuntimeException;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.remote.model.PackageInfoVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 升级服务远程调用
 */
@FeignClient(name = "content-service", fallbackFactory = IContentServiceRemote.HystrixFallbackFactory.class)
public interface IContentServiceRemote {


    /**
     * 根据设备ID
     * @return
     */
    @DeleteMapping("inner/policies/sign/device/{deviceId}")
    PackageInfoVo delByDeviceId(@PathVariable String deviceId);

    /**
     * 工厂类
     */
    @Component
    class HystrixFallbackFactory implements FallbackFactory<IContentServiceRemote> {

        @Override
        public IContentServiceRemote create(final Throwable throwable) {
            return (String device) -> {
                LogUtils.error("content-service 根据设备ID删除相关用户签署的协议失败，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                throw new AppRuntimeException("content-service 根据设备ID删除相关用户签署的协议失败");
            };
        }
    }
}
