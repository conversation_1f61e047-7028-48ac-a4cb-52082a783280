package com.irobotics.aiot.device.remote;

import com.irobotics.aiot.bravo.basic.exception.AppRuntimeException;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.remote.model.PackageInfoVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 升级服务远程调用
 */
@FeignClient(name = "upgrade-service", fallbackFactory = IUpgradeRemote.HystrixFallbackFactory.class)
public interface IUpgradeRemote {


    /**
     * 通过产品型号代码和版本获取升级包信息
     *
     * @param productModelCode
     * @param versionCode
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "/upgrade/package/getPackageByProductModelCodeAndVersion")
    PackageInfoVo getPackageByProductModelCodeAndVersion(@RequestParam("productModelCode") String productModelCode, @RequestParam("versionCode") String versionCode);

    /**
     * 工厂类
     */
    @Component
    class HystrixFallbackFactory implements FallbackFactory<IUpgradeRemote> {

        @Override
        public IUpgradeRemote create(final Throwable throwable) {
            //在这里创建接口内的服务方法使用匿名内部类的方式
            return (String productModelCode, String versionCode) -> {
                LogUtils.error("upgrade-service 根据产品型号及版本类型获取升级包失败，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                throw new AppRuntimeException("upgrade-service 根据产品型号及版本类型获取升级包失败");
            };
        }
    }
}
