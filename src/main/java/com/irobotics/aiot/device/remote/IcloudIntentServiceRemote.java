package com.irobotics.aiot.device.remote;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.remote.model.SyncDeviceReportReq;
import com.irobotics.aiot.device.vo.SyncBindReq;
import com.irobotics.aiot.device.vo.SyncUntieReq;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @author: byx
 * @date: 20223-02-08
 **/
@FeignClient(value = "icloud-intent-service", fallbackFactory = IcloudIntentServiceRemote.IcloudIntentServiceRemoteFallbackFactory.class)
public interface IcloudIntentServiceRemote {

    /**
     * 同步设备
     * @param param
     * @return
     */
    @PostMapping(value = "/inner/icloud/syncDeviceReport")
    ResponseMessage<Void> syncDeviceReport(@RequestBody SyncDeviceReportReq param);

    /**
     * 获取语控用户
     * @return
     */
    @GetMapping(value = "/inner/icloud/getAllUser")
    ResponseMessage<List<String>> getAllUser();

    /**
     * 向第三方平台发送绑定信息
     * @param req
     * @return
     */
    @PostMapping("/inner/icloud/syncBindInfo")
    ResponseMessage<Boolean> syncBindInfo(@RequestBody SyncBindReq req);

    /**
     * 向第三方平台发送解绑信息
     * @param req
     * @return
     */
    @PostMapping("/inner/icloud/syncUntieInfo")
    ResponseMessage<Boolean> syncUntieInfo(@RequestBody SyncUntieReq req);
    /**
     * 工厂类
     */
    @Component
    class IcloudIntentServiceRemoteFallbackFactory implements FallbackFactory<IcloudIntentServiceRemote> {

        @Override
        public IcloudIntentServiceRemote create(Throwable throwable) {
            return new IcloudIntentServiceRemote() {
                @Override
                public ResponseMessage<Void> syncDeviceReport(SyncDeviceReportReq param) {
                    LogUtils.error("同步设备到icloud-intent-service异常，参数:{}", param);
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "同步设备到icloud-intent-service");
                }

                @Override
                public ResponseMessage<List<String>> getAllUser() {
                    LogUtils.error("获取语控用户异常");
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "获取语控用户");
                }

                @Override
                public ResponseMessage<Boolean> syncBindInfo(SyncBindReq req) {
                    LogUtils.error("调用icloud-intent-service向第三方服务发送新增绑定关系异常，参数:{}", req.toString());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "调用icloud-intent-service向第三方服务发送新增绑定关系");
                }

                @Override
                public ResponseMessage<Boolean> syncUntieInfo(SyncUntieReq req) {
                    LogUtils.error("调用icloud-intent-service向第三方服务发送解绑信息异常,tenantId:{},deviceId:{}", req.getTenantId(), req.getDeviceId());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "调用icloud-intent-service向第三方服务发送解绑信息");
                }
            };
        }
    }
}
