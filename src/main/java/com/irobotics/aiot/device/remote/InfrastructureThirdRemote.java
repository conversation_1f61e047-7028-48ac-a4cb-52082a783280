package com.irobotics.aiot.device.remote;

import com.irobotics.aiot.bravo.basic.base.CountryCity;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.entity.NoticeShareRecordEntity;
import com.irobotics.aiot.device.vo.JPushFamilyShareReq;
import com.irobotics.aiot.device.vo.JPushShareReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

/**
 * 第三方基础服务远程调用
 */
@FeignClient(name = "infrastructure-third", fallbackFactory = InfrastructureThirdRemote.HystrixFallbackFactory.class)
public interface InfrastructureThirdRemote {

    /**
     * 推送分享消息
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/inner/push/share", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<Boolean> pushShare(@RequestBody JPushShareReq req);

    /**
     * 推送家庭分享消息
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/inner/push/share/family", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Async
    void pushFamilyShare(@RequestBody JPushFamilyShareReq req);

    /**
     * 通过记录id获取分享推送记录信息
     *
     * @param recordId
     * @return
     */
    @GetMapping("/inner/notice/share/records/one")
    ResponseMessage<NoticeShareRecordEntity> checkShareRecord(@RequestParam String recordId);


    /**
     * 通过shareId删除分享推送记录
     *
     * @param tenantId
     * @param shareId
     * @return
     */
    @DeleteMapping("/inner/notice/share/records/delByShareId")
    ResponseMessage<Boolean> delByShareId(@RequestParam String tenantId, @RequestParam String shareId);


    /**
     * 通过id信息获取城市信息
     *
     * @param host
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "/geo/ip/info")
    ResponseMessage<CountryCity> getIpInfo(@RequestParam("host") String host);

    /**
     * 取消分享推送消息
     */
    @PostMapping("inner/push/cancel-share/{targetId}/{uid}")
    void pushCancelShareMsg(@PathVariable String targetId, @PathVariable String uid);

    /**
     * 根据设备ID修改极光消息为失效状态
     */
    @PatchMapping("/inner/notice/share/records/invalid/target/{targetId}")
    void invalidateMsgByTargetId(@PathVariable String targetId);

    @PatchMapping("/inner/notice/share/records/invalid/target/{targetId}/{uid}")
    void invalidateMsgByTargetIdAndUid(@PathVariable String targetId, @PathVariable String uid);


    /**
     * 工厂类
     */
    @Component
    @Slf4j
    class HystrixFallbackFactory implements FallbackFactory<InfrastructureThirdRemote> {

        @Override
        public InfrastructureThirdRemote create(final Throwable throwable) {
            // 在这里创建接口内的服务方法使用匿名内部类的方式
            return new InfrastructureThirdRemote() {

                @Override
                public ResponseMessage<Boolean> pushShare(JPushShareReq req) {
                    LogUtils.error("推送分享消息异常， HystrixFallback-req={}, exceptionMsg={}", req, throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "推送分享消息异常");
                }

                @Override
                public void pushFamilyShare(JPushFamilyShareReq req) {
                    LogUtils.error("推送家庭分享消息异常， HystrixFallback-req={}, exceptionMsg={}", req, throwable.getMessage());
                }

                @Override
                public ResponseMessage<NoticeShareRecordEntity> checkShareRecord(String recordId) {
                    LogUtils.error("通过记录id获取分享推送记录信息异常， HystrixFallback-recordId={}, exceptionMsg={}", recordId,
                            throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过记录id获取分享推送记录信息异常");
                }

                @Override
                public ResponseMessage<Boolean> delByShareId(String tenantId, String shareId) {
                    LogUtils.error("通过shareId删除分享推送记录信息异常， HystrixFallback-shareId={}, exceptionMsg={}", shareId,
                            throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过shareId删除分享推送记录信息异常");
                }

                @Override
                public ResponseMessage<CountryCity> getIpInfo(String host) {
                    LogUtils.error("IP分析服务异常HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "IP分析服务");
                }

                @Override
                public void pushCancelShareMsg(String targetId, String uid) {
                    log.error("pushCancelShareMsg error,targetId:{}, uid:{}", targetId, uid, throwable);
                }

                @Override
                public void invalidateMsgByTargetId(String targetId) {
                    log.error("call invalidateMsgByTargetId error, targetId: {}", targetId, throwable);
                }

                @Override
                public void invalidateMsgByTargetIdAndUid(String targetId, String uid) {
                    log.error("invalidateMsgByTargetIdAndUid error targetId:{}, uid:{}", targetId, uid, throwable);
                }
            };
        }
    }
}
