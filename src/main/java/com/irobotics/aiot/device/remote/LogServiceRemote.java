package com.irobotics.aiot.device.remote;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.vo.BindLogEntity;
import com.irobotics.aiot.device.vo.DeviceLoginLogEntity;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @description: 日志服务远程调用接口
 * @author: huangwa1911
 * @time: 2021/10/25 9:17
 */
@FeignClient(name = "log-service", fallbackFactory = LogServiceRemote.HystrixFallbackFactory.class)
public interface LogServiceRemote {

    /**
     * 插入一条登录日志
     *
     * @param deviceLogEntity
     * @return
     */
    @PostMapping(value = "/inner/log/device/login", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<String> loginLog(@RequestBody DeviceLoginLogEntity deviceLogEntity);

    /**
     * 插入一条登出日志
     *
     * @param deviceLoginLogEntity
     * @return
     */
    @PostMapping(value = "/inner/log/device/logout", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<String> logoutLog(@RequestBody DeviceLoginLogEntity deviceLoginLogEntity);

    /**
     * 插入多条绑定日志
     *
     * @param bindLogEntities
     * @return
     */
    @PostMapping(value = "/inner/log/bind/report/list", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<Void> reportList(@RequestBody List<BindLogEntity> bindLogEntities);

    /**
     * 根据sn列表删除app日志
     *
     * @param snList
     * @return
     */
    @PostMapping(value = "/inner/log/app/deleteBySns", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<Void> deleteAppLogBySns(@RequestBody List<String> snList);

    /**
     * 根据sn删除app日志
     *
     * @param sn
     * @return
     */
    @DeleteMapping(value = "/inner/log/app/deleteBySn", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<Void> deleteAppLogBySn(String sn);

    /**
     * 根据sn列表删除设备日志
     *
     * @param snList
     * @return
     */
    @PostMapping(value = "/inner/log/device/deleteBySns", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<Void> deleteDeviceLogBySns(@RequestBody List<String> snList);

    /**
     * 根据sn删除设备日志
     *
     * @param sn
     * @return
     */
    @DeleteMapping(value = "/inner/log/device/deleteBySn", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<Void> deleteDeviceLogBySn(String sn);

    /**
     * 插入一条绑定或解绑日志
     * @param bindLogEntity
     * @return
     */
    @PostMapping(value = "/inner/log/bind/report", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<String> report(@RequestBody BindLogEntity bindLogEntity);

    /**
     * 工厂类
     */
    @Component
    class HystrixFallbackFactory implements FallbackFactory<LogServiceRemote> {

        @Override
        public LogServiceRemote create(Throwable throwable) {
            return new LogServiceRemote() {

                @Override
                public ResponseMessage<String> loginLog(DeviceLoginLogEntity deviceLogEntity) {
                    LogUtils.error("插入一条登录日志异常HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "插入一条登录日志");
                }

                @Override
                public ResponseMessage<String> logoutLog(DeviceLoginLogEntity deviceLoginLogEntity) {
                    LogUtils.error("插入一条登出日志异常HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "插入一条登出日志");
                }

                @Override
                public ResponseMessage<Void> reportList(List<BindLogEntity> bindLogEntities) {
                    LogUtils.error("插入多条绑定日志异常HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "插入多条绑定日志");
                }

                @Override
                public ResponseMessage<Void> deleteAppLogBySns(List<String> snList) {
                    LogUtils.error("根据sn列表删除app日志异常,HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "根据sn列表删除app日志异常");
                }

                @Override
                public ResponseMessage<Void> deleteAppLogBySn(String sn) {
                    LogUtils.error("根据sn删除app日志异常,HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "根据sn删除app日志异常");
                }

                @Override
                public ResponseMessage<Void> deleteDeviceLogBySns(List<String> snList) {
                    LogUtils.error("根据sn列表删除device日志异常,HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "根据sn列表删除device日志异常");
                }

                @Override
                public ResponseMessage<Void> deleteDeviceLogBySn(String sn) {
                    LogUtils.error("根据sn删除device日志异常,HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "根据sn删除device日志异常");
                }

                @Override
                public ResponseMessage<String> report(BindLogEntity bindLogEntity) {
                    LogUtils.error("插入一条绑定或解绑日志异常，LogServiceRemote-report参数:{}", bindLogEntity);
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "插入一条绑定或解绑日志");
                }
            };
        }
    }
}
