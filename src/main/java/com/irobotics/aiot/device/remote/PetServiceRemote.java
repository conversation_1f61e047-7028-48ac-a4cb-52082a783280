package com.irobotics.aiot.device.remote;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.remote.model.ClearFlowParam;
import com.irobotics.aiot.device.vo.DeviceLoginLogEntity;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author:tlh
 * @create: 2023-09-23 16:05:42
 * @Description:
 */
@FeignClient(name = "pet-service", fallbackFactory = PetServiceRemote.PetServiceRemoteFallbackFactory.class)
public interface PetServiceRemote {


    /**
     * 根据sn,删除绑定的猫咪
     * @param snList
     * @return
     */
    @DeleteMapping(value = "/inner/pet/info/deleteBySns", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Async
    void deleteBySns(@RequestBody List<String> snList);

    @Component
    class PetServiceRemoteFallbackFactory implements FallbackFactory<PetServiceRemote> {

        @Override
        public PetServiceRemote create(Throwable throwable) {
            return new PetServiceRemote() {

                @Override
                public void deleteBySns(List<String> snList) {
                    LogUtils.error("根据sn,删除绑定的猫咪异常HystrixFallback-exceptionMsg={}", throwable.getMessage());
                }
            };
        }
    }
}
