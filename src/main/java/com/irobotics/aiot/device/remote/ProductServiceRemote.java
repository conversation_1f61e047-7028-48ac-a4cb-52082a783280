package com.irobotics.aiot.device.remote;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.entity.ProductModeEntity;
import com.irobotics.aiot.device.entity.RtcAuthInfo;
import com.irobotics.aiot.device.remote.model.InnerDeviceRegisterParam;
import com.irobotics.aiot.device.remote.model.ProductBasicLangConfigEntity;
import com.irobotics.aiot.device.remote.model.ProductInfoEntity;
import com.irobotics.aiot.device.vo.InnerDeviceLoginVo;
import com.irobotics.aiot.device.vo.NodeInfoFromDeviceParam;
import com.irobotics.aiot.device.vo.ProductSnDetailEntity;
import com.irobotics.aiot.device.vo.RtcDeviceNodeInfoVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品服务远程调用
 */
@FeignClient(name = "product-service", fallbackFactory = ProductServiceRemote.HystrixFallbackFactory.class)
public interface ProductServiceRemote {

    /**
     * 通过产品型号代码获取产品型号名称
     *
     * @param tenantId
     * @param code     产品型号代码(原工程类型)
     * @return
     */
    @GetMapping("/inner/productMode/by/code")
    ResponseMessage<ProductModeEntity> getByCode(@RequestParam String tenantId, @RequestParam String code);

    /**
     * 通过产品型号代码列表获取产品型号信息列表
     *
     * @param tenantId
     * @param codes
     * @return
     */
    @PutMapping(value = "/inner/productMode/by/codes", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<List<ProductModeEntity>> getListByCode(@RequestParam String tenantId, @RequestBody List<String> codes);

    /**
     * 通过设备sn获取snDetail信息
     *
     * @param tenantId
     * @param sn
     * @return
     */
    @GetMapping("/inner/snDetail/sn")
    ResponseMessage<ProductSnDetailEntity> getBySn(@RequestParam String tenantId, @RequestParam String sn);

    /**
     * 通过设备sn修改sn的激活状态
     *
     * @param tenantId
     * @param sn
     * @param active
     * @return
     */
    @PutMapping("/inner/snDetail/update/active")
    ResponseMessage<Integer> updateSnDetailForActive(@RequestParam String tenantId, @RequestParam String sn, @RequestParam Integer active);

    /**
     * 通过设备sn（全模糊查询）和激活状态获取所有snDetail信息
     *
     * @param tenantId
     * @param sn
     * @param active
     * @return
     */
    @GetMapping("/inner/snDetail/like/sn")
    ResponseMessage<List<ProductSnDetailEntity>> getLikeSn(@RequestParam String tenantId, @RequestParam String sn, Integer active);

    /**
     * 根据产品id获取分类id
     *
     * @param productId 产品id
     * @return
     */
    @GetMapping(value = "/inner/productInfo/classify", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<String> getClassifyId(@RequestParam String productId);

    /**
     * 批量设置sn未激活
     *
     * @param snList
     * @return
     */
    @PostMapping(value = "/inner/snDetail/setSnUnActiveBatch", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<Void> setSnUnActiveBatch(@RequestBody List<String> snList);

    /**
     * 设置sn未激活
     *
     * @param sn
     * @return
     */
    @PutMapping(value = "/inner/snDetail/setSnUnActiveOne", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<Void> setSnUnActiveOne(@RequestParam String sn);

    /**
     * 根据sn列表返回激活sn列表
     *
     * @param snList
     * @return
     */
    @PostMapping(value = "/inner/snDetail/activeSnList", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<List<String>> getActiveSnList(@RequestBody List<String> snList);

    /**
     * 通过code和sn获取产品型号信息和sn信息
     *
     * @param sn
     * @param code
     * @param register 是否是注册，注册的话，会把已注册的sn信息查询出来
     * @return
     */
    @GetMapping("/inner/snDetail/codeAndSn")
    ResponseMessage<InnerDeviceLoginVo> getByCodeAdSn(@RequestParam String sn, @RequestParam String code, @RequestParam Boolean register);

    /**
     * 通过sn获取sn信息
     *
     * @param sn
     * @return
     */
    @GetMapping("/inner/snDetail/getSnDetail")
    ResponseMessage<ProductSnDetailEntity> getSnDetail(@RequestParam String sn);

    /**
     * 通过id获取产品型号信息
     *
     * @param ids
     * @return
     */
    @GetMapping("/inner/productMode/getModeByIds")
    ResponseMessage<List<ProductModeEntity>> getModeByIds(@RequestBody List<String> ids);

    /**
     * 保存一条已激活的sn信息
     *g
     * @param productSnDetailActiveZoneEntity
     * @return
     */
//    @PostMapping("/inner/sn/active")
//    ResponseMessage<Boolean> saveActiveSn(@RequestBody ProductSnDetailActiveZoneEntity productSnDetailActiveZoneEntity);

    /**
     * 通过sn删除一条已激活的sn信息
     *
     * @param sn
     * @return
     */
//    @DeleteMapping("/inner/sn/active/sn")
//    ResponseMessage<Boolean> delBySn(@RequestParam String sn);

    /**
     * 通过sn列表删除已激活的sn信息
     *
     * @param sns
     * @return
     */
//    @DeleteMapping("/inner/sn/active/sns")
//    ResponseMessage<Boolean> delBySn(@RequestBody List<String> sns);

    /**
     * 设备注册SN信息以及产品型号信息
     *
     * @param param
     * @return
     */
    @PostMapping("/inner/device/register")
    ResponseMessage<DeviceInfoEntity> deviceRegister(@RequestBody InnerDeviceRegisterParam param);

    /**
     * 根据产品信息id列表获取产品信息列表
     * @param productIds
     * @return
     */
    @PostMapping("/inner/productInfo/list/ids")
    ResponseMessage<List<ProductInfoEntity>> getListByProductIds(@RequestBody List<String> productIds);

    @GetMapping("/inner/rtc/getAppIdInfo")
    ResponseMessage<RtcAuthInfo> getAppIdInfo();

    @PostMapping("/inner/rtc/getNodeInfoFromDevice")
    ResponseMessage<RtcDeviceNodeInfoVo> getNodeInfoFromDevice(@RequestBody NodeInfoFromDeviceParam param);


    /**
     * 通过sn获取sn信息
     *
     * @param sn
     * @return
     */
    @GetMapping("/inner/snDetail/getSnDetailSellRegin")
    ResponseMessage<ProductSnDetailEntity> getSnDetailSellRegin(@RequestParam String sn);

    @GetMapping("/inner/productBasicLangConfig/get")
    ResponseMessage<ProductBasicLangConfigEntity> getProdBasicLangConfig(@RequestParam("productId") String productId, @RequestParam("lang") String lang);


    /**
     * 工厂类
     */
    @Component
    class HystrixFallbackFactory implements FallbackFactory<ProductServiceRemote> {

        @Override
        public ProductServiceRemote create(Throwable throwable) {
            return new ProductServiceRemote() {

                @Override
                public ResponseMessage<ProductModeEntity> getByCode(String tenantId, String code) {
                    LogUtils.error("通过产品型号代码获取产品型号信息系异常，HystrixFallback-tenantId={}, code={}, exceptionMsg={}", tenantId, code, throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过产品型号代码获取产品型号信息系异常");
                }

                @Override
                public ResponseMessage<List<ProductModeEntity>> getListByCode(String tenantId, List<String> codes) {
                    LogUtils.error("通过产品型号代码列表获取产品型号信息列表异常，HystrixFallback-tenantId={}, codes={}, exceptionMsg={}", tenantId, codes, throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过产品型号代码列表获取产品型号信息列表异常");
                }

                @Override
                public ResponseMessage<ProductSnDetailEntity> getBySn(String tenantId, String sn) {
                    LogUtils.error("通过设备sn获取snDetail信息异常，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过设备sn获取snDetail信息");
                }

                @Override
                public ResponseMessage<Integer> updateSnDetailForActive(String tenantId, String sn, Integer active) {
                    LogUtils.error("通过设备sn修改sn的激活状态异常，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过设备sn修改sn的激活状态");
                }

                @Override
                public ResponseMessage<List<ProductSnDetailEntity>> getLikeSn(String tenantId, String sn, Integer active) {
                    LogUtils.error("通过设备sn（全模糊查询）和激活状态获取所有snDetail信息异常，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过设备sn（全模糊查询）和激活状态获取所有snDetail信息");
                }

                @Override
                public ResponseMessage<String> getClassifyId(String productId) {
                    LogUtils.error("根据产品id获取分类id异常，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "根据产品id获取分类id");
                }

                @Override
                public ResponseMessage<Void> setSnUnActiveBatch(List<String> snList) {
                    LogUtils.error("批量设置sn未激活失败，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "批量设置sn未激活失败");
                }

                @Override
                public ResponseMessage<Void> setSnUnActiveOne(String sn) {
                    LogUtils.error("设置sn未激活失败，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "设置sn未激活失败");
                }

                @Override
                public ResponseMessage<List<String>> getActiveSnList(List<String> snList) {
                    LogUtils.error("根据sn列表返回激活sn列表，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "根据sn列表返回激活sn列表");
                }

                @Override
                public ResponseMessage<InnerDeviceLoginVo> getByCodeAdSn(String sn, String code, Boolean register) {
                    LogUtils.error("通过code和sn获取产品型号信息和sn信息，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过code和sn获取产品型号信息和sn信息");
                }

                @Override
                public ResponseMessage<ProductSnDetailEntity> getSnDetail(String sn) {
                    LogUtils.error("通过sn获取sn信息，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过sn获取sn信息");
                }

                @Override
                public ResponseMessage<List<ProductModeEntity>> getModeByIds(List<String> ids) {
                    LogUtils.error("通过id获取产品型号信息，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过id获取产品型号信息");
                }

//                @Override
//                public ResponseMessage<Boolean> saveActiveSn(ProductSnDetailActiveZoneEntity productSnDetailActiveZoneEntity) {
//                    LogUtils.error("保存一条已激活的sn信息，HystrixFallback-exceptionMsg={}", throwable.getMessage());
//                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "保存一条已激活的sn信息");
//                }

//                @Override
//                public ResponseMessage<Boolean> delBySn(String sn) {
//                    LogUtils.error("通过sn删除一条已激活的sn信息，HystrixFallback-exceptionMsg={}", throwable.getMessage());
//                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过sn删除一条已激活的sn信息");
//                }

//                @Override
//                public ResponseMessage<Boolean> delBySn(List<String> sns) {
//                    LogUtils.error("通过sn列表删除已激活的sn信息，HystrixFallback-exceptionMsg={}", throwable.getMessage());
//                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过sn列表删除已激活的sn信息");
//                }

                @Override
                public ResponseMessage<DeviceInfoEntity> deviceRegister(InnerDeviceRegisterParam param) {
                    LogUtils.error("设备注册SN信息以及产品型号信息，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "设备注册SN信息以及产品型号信息");
                }

                @Override
                public ResponseMessage<List<ProductInfoEntity>> getListByProductIds(List<String> productIds) {
                    LogUtils.error("根据产品id获取产品信息异常，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "根据产品id获取产品信息异常");
                }

                @Override
                public ResponseMessage<RtcAuthInfo> getAppIdInfo() {
                    LogUtils.error("获取RTC配置信息异常：HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY,"获取RTC配置信息异常");
                }

                @Override
                public ResponseMessage<RtcDeviceNodeInfoVo> getNodeInfoFromDevice(NodeInfoFromDeviceParam param) {
                    LogUtils.error("获取设备秘钥异常：HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY,"获取设备秘钥异常");
                }


                @Override
                public ResponseMessage<ProductSnDetailEntity> getSnDetailSellRegin (String sn) {
                    LogUtils.error("通过设备sn获取getSnDetailSellRegin信息异常，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过设备sn获取getSnDetailSellRegin信息");
                }

                @Override
                public ResponseMessage<ProductBasicLangConfigEntity> getProdBasicLangConfig(String productId, String lang) {
                    LogUtils.error("获取产品多语言异常：HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY,"根据产品id获取最大版本号异常");
                }
            };
        }
    }
}
