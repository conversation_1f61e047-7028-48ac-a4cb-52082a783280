package com.irobotics.aiot.device.remote;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ClientType;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.entity.FamilyInfoEntity;
import com.irobotics.aiot.device.remote.model.BindIdVo;
import com.irobotics.aiot.device.vo.*;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 智能家居服务远程调用
 */
@FeignClient(name = "smart-home-service", fallbackFactory = SmartHomeServiceRemote.HystrixFallbackFactory.class)
public interface SmartHomeServiceRemote {

    /**
     * 用户家庭绑定设备
     *
     * @param param
     * @return
     */
    @PostMapping("/smartHome/device/bind")
    ResponseMessage<Boolean> bindDevice(@RequestBody RoomBindDeviceReq param);

    /**
     * 用户家庭绑定分享设备
     *
     * @param param
     * @return
     */
    @PostMapping("/smartHome/device/shareBind")
    ResponseMessage<Boolean> bindShareDevice(@RequestBody RoomBindShareDeviceReq param);

    /**
     * 根据用户ID解绑分享设备
     *
     * @param param
     * @return
     */
    @PostMapping("/smartHome/device/userUntieDevice")
    ResponseMessage<Boolean> userUntieDevice(@RequestBody UserUniteShareDeviceReq param);

    /**
     * 当clientType=PHONe时，id为用户id，查询的时设备id列表；当clientType为ROBOT时，则id为设备id，查询的是用户id列表
     *
     * @param tenantId
     * @param clientType
     * @param id
     * @return
     */
    @GetMapping(value = "/smartHome/inner/device/bindList")
    ResponseMessage<List<String>> getBindList(@RequestParam String tenantId, @RequestParam("clientType") ClientType clientType, @RequestParam("id") String id);

    /**
     * 根据设备id列表获取绑定用户id列表
     *
     * @param deviceIdList
     * @return
     */
    @PostMapping(value = "/smartHome/inner/device/bindListByDeviceIdList", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<List<BindIdVo>> getBindListByDeviceIdList(@RequestBody List<String> deviceIdList);

    /**
     * 根据用户id列表获取绑定设备id列表
     *
     * @param userIdList
     * @return
     */
    @PostMapping(value = "/smartHome/inner/device/bindListByUserIdList", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<List<BindIdVo>> getBindListByUserIdList(@RequestBody List<String> userIdList);

    /**
     * (共享家庭)共享成员同意/拒绝接口
     *
     * @param req
     * @return
     */
    @PostMapping("/smartHome/userFamily/confirm")
    ResponseMessage<Boolean> confirm(@RequestBody ShareFamilyReplyReq req);

    /**
     * 管理员撤销家庭分享
     *
     * @param revokeDto
     * @return
     */
    @PostMapping("/smartHome/userShare/revoke")
    ResponseMessage<Boolean> revoke(@RequestBody RevokeDto revokeDto);

    /**
     * 通过 familyIds，批量获取家庭信息
     *
     * @param ids
     * @return
     */
    @PostMapping("/inner/smartHome/familyInfo/getFamilyInfoByIds")
    ResponseMessage<List<FamilyInfoEntity>> getFamilyInfoByIds(@RequestBody List<String> ids);

    /**
     * 通过设备id列表删除房间绑定关系
     *
     * @param deviceIdList
     * @return
     */
    @PostMapping(value = "/smartHome/inner/device/deleteRoomBindByDeviceIds", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<Void> deleteRoomBindByDeviceIds(@RequestBody List<String> deviceIdList);

    /**
     * 通过设备id删除房间绑定关系
     *
     * @param deviceId
     * @return
     */
    @DeleteMapping(value = "/smartHome/inner/device/deleteRoomBindByDeviceId")
    ResponseMessage<Void> deleteRoomBindByDeviceId(@RequestParam("deviceId") String deviceId);

    /**
     * 根据sn获取设备拥有者
     *
     * @param tenantId
     * @param sn
     * @return
     */
    @GetMapping("/inner/smartHome/roomDevice/getOwnerBySn")
    ResponseMessage<String> getOwnerBySn(@RequestParam String tenantId, @RequestParam("sn") String sn);

    /**
     * 工厂类
     */
    @Component
    class HystrixFallbackFactory implements FallbackFactory<SmartHomeServiceRemote> {

        @Override
        public SmartHomeServiceRemote create(Throwable throwable) {
            return new SmartHomeServiceRemote() {

                @Override
                public ResponseMessage<Boolean> bindDevice(RoomBindDeviceReq param) {
                    LogUtils.error("用户家庭绑定设备异常，HystrixFallback-param={}, exceptionMsg={}", param, throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "用户家庭绑定设备异常");
                }

                @Override
                public ResponseMessage<Boolean> bindShareDevice(RoomBindShareDeviceReq param) {
                    LogUtils.error("用户家庭绑定分享设备异常，HystrixFallback-param={}, exceptionMsg={}", param, throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "用户家庭绑定分享设备异常");
                }

                @Override
                public ResponseMessage<Boolean> userUntieDevice(UserUniteShareDeviceReq param) {
                    LogUtils.error("根据用户ID解绑分享设备异常，HystrixFallback-param={}, exceptionMsg={}", param, throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "根据用户ID解绑分享设备异常");
                }

                @Override
                public ResponseMessage<List<String>> getBindList(String tenantId, ClientType clientType, String id) {
                    if (ClientType.ROBOT == clientType) {
                        LogUtils.error("根据用户id获取设备id列表，HystrixFallback-tenantId={}, clientType={}, id, exceptionMsg={}", tenantId, clientType, id, throwable.getMessage());
                    } else {
                        LogUtils.error("根据设备获取用户id列表，HystrixFallback-tenantId={}, clientType={}, id, exceptionMsg={}", tenantId, clientType, id, throwable.getMessage());
                    }
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "根据clientType获取用户id或设备id异常");
                }

                @Override
                public ResponseMessage<List<BindIdVo>> getBindListByDeviceIdList(List<String> deviceIdList) {
                    LogUtils.error("根据设备id列表获取绑定用户id列表异常，HystrixFallback-param={}, exceptionMsg={}", deviceIdList, throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "根据设备id列表获取绑定用户id列表异常");
                }

                @Override
                public ResponseMessage<List<BindIdVo>> getBindListByUserIdList(List<String> userIdList) {
                    LogUtils.error("根据用户id列表获取绑定设备id列表异常，HystrixFallback-param={}, exceptionMsg={}", userIdList, throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "根据用户id列表获取绑定设备id列表异常");
                }

                @Override
                public ResponseMessage<Boolean> confirm(ShareFamilyReplyReq req) {
                    LogUtils.error("(共享家庭)共享成员同意/拒绝接口异常，HystrixFallback-req={}, exceptionMsg={}", req, throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, " (共享家庭)共享成员同意/拒绝接口异常");
                }

                @Override
                public ResponseMessage<Boolean> revoke(RevokeDto revokeDto) {
                    LogUtils.error("管理员撤销家庭分享异常，HystrixFallback-req={}, exceptionMsg={}", revokeDto, throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, " 管理员撤销家庭分享异常");
                }

                @Override
                public ResponseMessage<List<FamilyInfoEntity>> getFamilyInfoByIds(List<String> ids) {
                    LogUtils.error("通过 familyIds，批量获取家庭信息异常，HystrixFallback-ids={}, exceptionMsg={}", ids, throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, " 通过 familyIds，批量获取家庭信息异常");
                }

                @Override
                public ResponseMessage<Void> deleteRoomBindByDeviceIds(List<String> deviceIdList) {
                    LogUtils.error("通过设备id删除房间绑定关系异常，HystrixFallback-param={}, exceptionMsg={}", deviceIdList, throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "删除房间绑定关系失败");
                }

                @Override
                public ResponseMessage<Void> deleteRoomBindByDeviceId(String deviceId) {
                    LogUtils.error("通过设备id删除房间绑定关系异常，HystrixFallback-param={}, exceptionMsg={}", deviceId, throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "删除房间绑定关系失败");
                }

                @Override
                public ResponseMessage<String> getOwnerBySn(String tenantId, String sn) {
                    LogUtils.error("通过设备sn获取绑定关系异常，HystrixFallback-param={}, exceptionMsg={}", sn, throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过设备sn获取绑定关系异常");
                }
            };
        }
    }
}
