package com.irobotics.aiot.device.remote;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.remote.model.TAuthClientUserExt;
import com.irobotics.aiot.device.entity.TAuthClientUserInfo;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 用户服务远程调用
 */
@FeignClient(name = "user-center", fallbackFactory = TAuthUserRemote.HystrixFallbackFactory.class)
public interface TAuthUserRemote {

    /**
     * 通过用户名获取用户信息
     *
     * @param tenantId 厂商id
     * @param username 用户名
     * @return
     */
    @GetMapping(value = "/inner/user/one/username", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<TAuthClientUserInfo> getByUsername(@RequestParam String tenantId, @RequestParam @ApiParam("用户名") String username);

    /**
     * 通过用户名列表获取用户信息列表
     *
     * @param usernameList
     * @return
     */
    @PostMapping(value = "/inner/user/listByUsernameList", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<List<TAuthClientUserInfo>> getListByUsernameList(@RequestBody List<String> usernameList);

    /**
     * 通过用户id列表获取用户信息列表
     *
     * @param tenantId 厂商id
     * @param ids      用户id集合
     * @return
     */
    @PostMapping(value = "/inner/user/list/ids", consumes = MediaType.APPLICATION_JSON_VALUE)
    ResponseMessage<List<TAuthClientUserInfo>> getListByIds(@RequestParam String tenantId, @RequestBody List<String> ids);

    /**
     * 通过用户id名获取用户ext信息
     *
     * @param tenantId 厂商id
     * @param userId      用户id
     */
    @PostMapping("/inner/user/userExt/userId")
    ResponseMessage<TAuthClientUserExt> getUserExtByUserId(@RequestParam String tenantId, @RequestParam String userId);

    /**
     * 工厂类
     */
    @Component
    class HystrixFallbackFactory implements FallbackFactory<TAuthUserRemote> {

        @Override
        public TAuthUserRemote create(Throwable throwable) {
            return new TAuthUserRemote() {
                @Override
                public ResponseMessage<TAuthClientUserInfo> getByUsername(String tenantId, String username) {
                    LogUtils.error("通过用户名获取用户信息异常，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过用户名获取用户信息");
                }

                @Override
                public ResponseMessage<List<TAuthClientUserInfo>> getListByUsernameList(List<String> usernameList) {
                    LogUtils.error("通过用户名列表获取用户信息列表异常，HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过用户名列表获取用户信息列表异常");
                }

                @Override
                public ResponseMessage<List<TAuthClientUserInfo>> getListByIds(String tenantId, List<String> ids) {
                    LogUtils.error("通过用户id列表获取用户信息列表异常， HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过用户id列表获取用户信息列表");
                }

                @Override
                public ResponseMessage<TAuthClientUserExt> getUserExtByUserId(String tenantId, String userId) {
                    LogUtils.error("通过用户id名获取用户ext信息， HystrixFallback-exceptionMsg={}", throwable.getMessage());
                    return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY, "通过用户id列表获取用户信息列表");
                }
            };
        }
    }
}
