package com.irobotics.aiot.device.remote.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @since 2022/7/7
 */
@Data
@Schema(name  = "ClearFlowParam", description = "清除流水相关参数")
public class ClearFlowParam {

    @Schema(description = "sn")
    private List<String> snList;

    @Schema(description = "userId")
    private List<String> userIdList;

    @Schema(description = "删除开始时间,yyyy-MM-dd")
    private String beginTime;

    @Schema(description = "删除结束时间,yyyy-MM-dd")
    private String endTime;

    @Schema(description = "租户ID")
    private String tenantId;

    public ClearFlowParam snList(List<String> snList) {
        this.snList = snList;
        return this;
    }

    public ClearFlowParam tenantId(String tenantId) {
        this.tenantId = tenantId;
        return this;
    }

}
