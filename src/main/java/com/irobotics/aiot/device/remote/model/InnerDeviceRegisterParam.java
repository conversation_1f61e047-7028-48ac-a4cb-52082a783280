package com.irobotics.aiot.device.remote.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备注册请求参数
 */
@Schema(name  = "设备注册请求参数")
@Data
public class InnerDeviceRegisterParam {

    /**
     *设备sn
     */
    @Schema(description = "设备sn", required = true)
    private String sn;

    /**
     *设备key
     */
    @Schema(description = "设备的key", required = true)
    private String key;

    /**
     *产品型号代码
     */
    @Schema(description = "产品型号代码", required = true)
    private String productModelCode;

    /**
     *无参构造
     */
    public InnerDeviceRegisterParam(){}

    /**
     *有参构造
     */
    public InnerDeviceRegisterParam(String sn, String key, String productModelCode){
        this.sn = sn;
        this.key = key;
        this.productModelCode = productModelCode;
    }
}
