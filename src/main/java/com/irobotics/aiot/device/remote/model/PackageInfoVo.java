package com.irobotics.aiot.device.remote.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 升级包信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Data
@Schema(name  = "升级包信息响应实体类")
public class PackageInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键id")
    private String id;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private String tenantId;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private String productId;

    /**
     * 产品型号代码（原工程类型）
     */
    @Schema(description = "产品型号代码（原工程类型）")
    private String productModelCode;

    /**
     * 包类型
     */
    @Schema(description = "包类型")
    private String packageType;

    /**
     * 版本名称
     */
    @Schema(description = "版本名称")
    private String versionName;

    /**
     * 版本代码
     */
    @Schema(description = "版本代码")
    private String versionCode;

    /**
     * 打包人
     */
    @Schema(description = "打包人")
    private String packUser;

    /**
     * 内部描述
     */
    @Schema(description = "内部描述")
    private String innerDesc;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 升级包md5值
     */
    @Schema(description = "升级包md5值")
    private String md5;

    /**
     * 升级包地址
     */
    @Schema(description = "升级包地址")
    private String packageUrl;

    /**
     * 升级包大小
     */
    @Schema(description = "升级包大小")
    private String packageSize;

    /**
     * 包状态(0:开发包1:测试包2:生产包)
     */
    @Schema(description = "包状态(0:开发包1:测试包2:生产包)")
    private Integer packageStatus;

    /**
     * 发布状态(0:待发布1:已发布)
     */
    @Schema(description = "发布状态(0:待发布1:已发布)")
    private Integer publishStatus;

    /**
     * 发布描述
     */
    @Schema(description = "发布描述")
    private String publishDesc;

    /**
     * 测试状态(0:待测试1:自测中2:自测通过,待测试验证
     * 3:自测不通过4:测试验证通过5:测试验证不通过)
     */
    @Schema(description = "测试状态(0:待测试1:自测中2:自测通过,待测试验证3:自测不通过4:测试验证通过5:测试验证不通过)")
    private Integer testStatus;

    /**
     * 自测结论
     */
    @Schema(description = "自测结论")
    private String selfTestConclusion;

    /**
     * 测试结论
     */
    @Schema(description = "测试结论")
    private String testConclusion;

    /**
     * 测试报告url
     */
    @Schema(description = "测试报告url")
    private String testReportUrl;

    /**
     * 测试人员
     */
    @Schema(description = "测试人员")
    private String testBy;

    /**
     * 测试时间
     */
    @Schema(description = "测试时间")
    private Date testTime;


    /**
     * 包配置策略主键id
     */
    @TableField(exist = false)
    @Schema(description = "包配置策略主键id")
    private String packageStrategyId;
    /**
     * 发布时间
     */
    @TableField(exist = false)
    @Schema(description = "发布时间")
    private Date publishTime;

    /**
     * 是否必经版本(0:否1:是)
     */
    @TableField(exist = false)
    @Schema(description = "是否必经版本(0:否1:是)")
    private Integer feature;

    /**
     * 地区
     */
    @Schema(description = "地区")
    private String zone;

    /**
     * 最低兼容版本(小于该版栖的用户将强制升级)
     */
    @Schema(description = "最低兼容版本(小于该版栖的用户将强制升级)")
    @TableField(exist = false)
    private Integer minVersion;

    /**
     * 是否静默升级(0:否1:是)
     */
    @Schema(description = "是否静默升级(0:否1:是)")
    @TableField(exist = false)
    private Integer silence;

    /**
     * 升级策略
     */
    @Schema(description = "升级策略")
    @TableField(exist = false)
    private String upgradeStrategy;

    /**
     * 页面类型
     */
    @Schema(description = "页面类型：区分权限 1:设备升级发布 2:固件测试 3:固件发布上线")
    @TableField(exist = false)
    private Integer searchType;

    /**
     * 主动下发任务Id
     */
    @Schema(description = "主动下发任务Id")
    private String pushTaskId;
}
