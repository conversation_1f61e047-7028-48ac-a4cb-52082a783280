package com.irobotics.aiot.device.remote.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 产品基础多语言配置
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Data
@Schema(name="ProductBasicLangConfigEntity对象", description="产品基础多语言配置")
public class ProductBasicLangConfigEntity implements Serializable {

    /**
     * id
     */
    private String id;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private String productId;

    /**
     * 产品名称
     */
    @Schema(description = "产品名称")
    private String productName;

    /**
     * 语言
     */
    @Schema(description = "语言（中文：zh，英语：en）")
    private String lang;

    /**
     * 企业id
     */
    @Schema(description = "企业id")
    private String tenantId;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
