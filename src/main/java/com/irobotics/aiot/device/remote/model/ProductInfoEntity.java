package com.irobotics.aiot.device.remote.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 产品信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_product_info")
@Schema(name  = "ProductInfoEntity对象", description = "产品信息表")
public class ProductInfoEntity extends Model {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 产品类别id
     */
    @Schema(description = "产品类别id", required = true)
    private String productClassifyId;

    /**
     * 通讯协议
     */
    @Schema(description = "通讯协议（1：WiFi-蓝牙，2：Wi-Fi，3：蓝牙）", required = true)
    private Integer communicationProtocol;

    /**
     * 名称
     */
    @Schema(description = "名称", required = true)
    private String name;

    /**
     * 说明
     */
    @Schema(description = "说明")
    @TableField(value = "`describe`")
    private String describe;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    private String updateBy;

    /**
     * 区域
     */
    @Schema(description = "区域", required = true)
    @TableField(value = "`zone`")
    private String zone;

    /**
     * 企业id
     */
    @Schema(description = "企业id", required = true)
    private String tenantId;

    /**
     * 产品物模型模板id
     */
    @Schema(description = "产品物模型模板id", required = true)
    private String produtThingModelTemplateId;

    /**
     * 图片地址
     */
    @Schema(description = "图片地址", required = true)
    private String photoUrl;

    /**
     * 设备蓝牙前缀
     */
    @Schema(description = "设备蓝牙前缀（由2-16位的数字或字母组成）")
    private String dmsPrefix;

    /**
     * 热点名称前缀
     */
    @Schema(description = "热点名称前缀(50个字符)")
    private String wifiPrefix;

    /**
     * 配网引导标题
     */
    @Schema(description = "配网引导标题", required = true)
    private String guideTitle;

    /**
     * 配网引导说明
     */
    @Schema(description = "配网引导说明", required = true)
    private String guideDesc;

    /**
     * 配网引导图片
     */
    @Schema(description = "配网引导图片", required = true)
    private String guideUrl;

    /**
     * 配网步骤
     */
    @Schema(description = "配网步骤", required = true)
    private String distributionNetworkStep;

    /**
     * 配网热点图片
     */
    @Schema(description = "配网热点图片（通讯协议为 1：WiFi-蓝牙，2：Wi-Fi 时必填）")
    private String hotspotImgUrl;

    /**
     * 状态
     */
    @Schema(description = "状态（0：开发中，1：测试中，2：已发布）")
    private Integer status;

    /**
     * 设备数量
     */
    @Schema(description = "设备数量")
    @TableField(exist = false)
    private long deviceNum;

    /**
     * 是否清除设备历史记录
     */
    @Schema(description = "是否清除设备历史记录（0：否，1：是）")
    private Integer clearHistory;

    /**
     * 排序（越小越靠前）
     */
    @Schema(description = "排序（越小越靠前）")
    private Integer sort;
}
