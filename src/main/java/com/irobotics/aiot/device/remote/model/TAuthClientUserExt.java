package com.irobotics.aiot.device.remote.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-07
 */
@Getter
@Setter
public class TAuthClientUserExt {


    /**
     * 用户ID
     */
    private String id;

    /**
     * ssoid
     */
    private String ssoId;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 国家，IP解析得出
     */
    private String country;

    /**
     * 城市，IP解析得出
     */
    private String city;


    private String ratingRecord;

    /**
     * token的版本号，用于控制TOKEN过期
     */
    private Long tokenVersion;

    /**
     * 最后在线时间
     */
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime onlineTime;

    /**
     * 最后离线时间
     */
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime offlineTime;

    /**
     * -1 ：永久，0 ：解封； 其他具体时间值：解封时间
     */
    private Long disableTime;

    /**
     * 创建时间
     */
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新时间
     */
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 区域
     */
    private String zone;

    /**
     * 手机系统 (1：Android , 2：IOS，3：其他)
     */
    private Integer phoneSys;

    /**
     * 手机品牌
     */
    private String phoneBrand;

    /**
     * 产品类型，对应旧业务的project_type(工程类型)
     */
    private String projectType;

    /**
     * 语言
     */
    private String lang;

    /**
     * 通知设置
     */
    private String noticeSetting;

    /**
     * 内测用户标识（1:是，0:否）
     */
    private Integer betaFlag;

    /**
     * app密码
     */
    private String appPassword;

    /**
     * 当前包版本号
     */
    private String versionCode;

    /**
     * 当前包版本名称
     */
    private String versionName;

    /**
     * 是否开启智能家居(1：开启，2：关闭)
     */
    private Integer enableSmartHome;

}
