package com.irobotics.aiot.device.repository;

import com.irobotics.aiot.device.repository.vo.DeviceBindCollection;
import com.mongodb.client.result.DeleteResult;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * mongoDB保存第三方绑定设备的绑定关系
 */
@Repository
public class DeviceBindRepository {

    /**
     * mongoTemplate
     */
    private final MongoTemplate mongoTemplate;

    /**
     * 构造
     *
     * @param mongoTemplate
     */
    public DeviceBindRepository(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    /**
     * 保存 绑定关系到MongoDB中
     *
     * @param deviceBindCollection
     * @return
     */
    public DeviceBindCollection save(DeviceBindCollection deviceBindCollection) {
        return mongoTemplate.save(deviceBindCollection);
    }

    /**
     * 删除
     *
     * @param userId
     * @param sn
     * @return
     */
    public long remove(String userId, String sn) {
        String id = userId + "_" + sn;
        List<Criteria> list = new ArrayList<>();
        list.add(Criteria.where("id").is(id));
        Criteria[] criteria = new Criteria[list.size()];
        list.toArray(criteria);
        Criteria c = new Criteria().andOperator(criteria);
        Query query = new Query(c);
        DeleteResult remove = mongoTemplate.remove(query, DeviceBindCollection.class);
        return remove.getDeletedCount();
    }

    public long remove(String userId, List<String> snList) {
        List<String> ids = new ArrayList<>();
        for (String sn : snList) {
            ids.add(userId + "_" + sn);
        }
        List<Criteria> list = new ArrayList<>();
        list.add(Criteria.where("id").in(ids));
        Criteria[] criteria = new Criteria[list.size()];
        list.toArray(criteria);
        Criteria c = new Criteria().andOperator(criteria);
        Query query = new Query(c);
        DeleteResult remove = mongoTemplate.remove(query, DeviceBindCollection.class);
        return remove.getDeletedCount();
    }
}