package com.irobotics.aiot.device.repository;

import com.irobotics.aiot.device.entity.DeviceResetRecordEntity;
import com.irobotics.aiot.device.utils.MongoUtil;
import com.irobotics.aiot.device.utils.PageHelper;
import com.irobotics.aiot.device.vo.DeviceResetRecordPageReq;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description
 * @since 2022/1/17
 */
@Repository
public class DeviceResetRecordRepository {

    private final MongoTemplate mongoTemplate;

    private final MongoUtil<DeviceResetRecordEntity> mongoUtil;

    /**
     * 构造
     * @param mongoTemplate
     * @param mongoUtil
     */
    public DeviceResetRecordRepository(MongoTemplate mongoTemplate, MongoUtil<DeviceResetRecordEntity> mongoUtil) {
        this.mongoTemplate = mongoTemplate;
        this.mongoUtil = mongoUtil;
    }

    /**
     * 保存
     * @param deviceResetRecordEntity
     * @return
     */
    public DeviceResetRecordEntity save(DeviceResetRecordEntity deviceResetRecordEntity) {
        return mongoTemplate.save(deviceResetRecordEntity);
    }

    /**
     * 分页
     * @param pageReq
     * @return
     */
    public PageHelper<DeviceResetRecordEntity> findPage(DeviceResetRecordPageReq pageReq) {
        Integer page = pageReq.getPage();
        Integer pageSize = pageReq.getPageSize();

        List<Criteria> list = new ArrayList<>();

        if (StringUtils.isNotBlank(pageReq.getUserId())) {
            list.add(Criteria.where("userId").is(pageReq.getUserId()));
        }
        if (StringUtils.isNotBlank(pageReq.getUsername())) {
            list.add(Criteria.where("userName").is(pageReq.getUsername()));
        }
        if (StringUtils.isNotBlank(pageReq.getTenantId())) {
            list.add(Criteria.where("tenantId").is(pageReq.getTenantId()));
        }
        if (StringUtils.isNotBlank(pageReq.getTaskId())) {
            list.add(Criteria.where("taskId").is(pageReq.getTaskId()));
        }
        if (StringUtils.isNotBlank(pageReq.getDeviceId())) {
            list.add(Criteria.where("deviceId").is(pageReq.getDeviceId()));
        }
        if (StringUtils.isNotBlank(pageReq.getSn())) {
            list.add(Criteria.where("sn").regex(Pattern.compile("^.*" + pageReq.getSn() + ".*$")));
        }
        if (pageReq.getIsSuccess() != null) {
            list.add(Criteria.where("isSuccess").is(pageReq.getIsSuccess()));
        }
        if (Objects.nonNull(pageReq.getBeginTime()) && pageReq.getBeginTime() > 0) {
            list.add(Criteria.where("time").gte(pageReq.getBeginTime()));
        }
        if (Objects.nonNull(pageReq.getEndTime()) && pageReq.getEndTime() > 0) {
            list.add(Criteria.where("time").lte(pageReq.getEndTime()));
        }
        if (!CollectionUtils.isEmpty(list)) {
            Criteria[] criteria = new Criteria[list.size()];
            list.toArray(criteria);
            Criteria c = new Criteria().andOperator(criteria);
            Query query = new Query(c);
            query.with(Sort.by(Sort.Direction.DESC, "time"));
            long count = mongoTemplate.count(query, DeviceResetRecordEntity.class);
            mongoUtil.start(page, pageSize, query);
            List<DeviceResetRecordEntity> deviceResetRecordEntities = mongoTemplate.find(query, DeviceResetRecordEntity.class);
            return mongoUtil.pageHelper(count, deviceResetRecordEntities);
        }

        Query query = new Query();
        query.with(Sort.by(Sort.Direction.DESC, "time"));
        long count = mongoTemplate.count(query, DeviceResetRecordEntity.class);
        mongoUtil.start(page, pageSize, query);
        List<DeviceResetRecordEntity> deviceResetRecordEntities = mongoTemplate.find(query, DeviceResetRecordEntity.class);
        return mongoUtil.pageHelper(count, deviceResetRecordEntities);
    }
}
