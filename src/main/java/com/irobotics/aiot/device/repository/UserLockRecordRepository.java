package com.irobotics.aiot.device.repository;

import com.irobotics.aiot.device.entity.UserLockRecordEntity;
import com.irobotics.aiot.device.utils.MongoUtil;
import com.irobotics.aiot.device.vo.UserLockReq;
import com.mongodb.client.result.DeleteResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: qwei
 * @time: 2022/5/6 17:07
 */
@Repository
public class UserLockRecordRepository {

    private static final Integer MAX_PAGE_SIZE = 50;
    private static final String TENANT_ID = "tenantId";
    private static final String DEVICE_ID = "deviceId";

    private final MongoTemplate mongoTemplate;

    private final MongoUtil<UserLockRecordEntity> mongoUtil;

    /**
     * 构造
     * @param mongoTemplate
     * @param mongoUtil
     */
    public UserLockRecordRepository(MongoTemplate mongoTemplate, MongoUtil<UserLockRecordEntity> mongoUtil) {
        this.mongoTemplate = mongoTemplate;
        this.mongoUtil = mongoUtil;
    }

    /**
     * 保存
     * @param recordEntity
     * @return
     */
    public UserLockRecordEntity save(UserLockRecordEntity recordEntity) {
        return mongoTemplate.save(recordEntity);
    }

    /**
     * 保存多条
     * @param recordEntitys
     */
    public void saveList(List<UserLockRecordEntity> recordEntitys) {
        mongoTemplate.insert(recordEntitys, UserLockRecordEntity.class);
    }

    /**
     * 更新保存
     * @param query
     * @param update
     */
    public void upsert(Query query, Update update){
        mongoTemplate.upsert(query, update, UserLockRecordEntity.class);
    }

    /**
     * 通过id获取一条
     * @param id
     * @return
     */
    public UserLockRecordEntity findById(String id) {
        Query query = new Query();
        Criteria criteria = Criteria.where("id").is(id);
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, UserLockRecordEntity.class);
    }

    /**
     * 获取一条
     * @param tenantId
     * @param deviceId
     * @param deviceUserId
     * @return
     */
    public UserLockRecordEntity getOne(String tenantId, String deviceId, String deviceUserId) {
        List<Criteria> list = new ArrayList<>();
        list.add(Criteria.where(TENANT_ID).is(tenantId));
        list.add(Criteria.where(DEVICE_ID).is(deviceId));
        list.add(Criteria.where("deviceUserId").is(deviceUserId));
        Criteria[] criteria = new Criteria[list.size()];
        list.toArray(criteria);
        Criteria c = new Criteria().andOperator(criteria);
        Query query = new Query(c);
        return mongoTemplate.findOne(query, UserLockRecordEntity.class);
    }

    /**
     * 删除
     */
    public void del() {
        mongoTemplate.dropCollection(UserLockRecordEntity.class);
    }

    /**
     * 删除
     * @param tenantId
     * @param deviceId
     * @return
     */
    public long remove(String tenantId, String deviceId) {
        List<Criteria> list = new ArrayList<>();
        list.add(Criteria.where(TENANT_ID).is(tenantId));
        list.add(Criteria.where(DEVICE_ID).is(deviceId));
        Criteria[] criteria = new Criteria[list.size()];
        list.toArray(criteria);
        Criteria c = new Criteria().andOperator(criteria);
        Query query = new Query(c);
        DeleteResult remove = mongoTemplate.remove(query, UserLockRecordEntity.class);
        return remove.getDeletedCount();
    }

    /**
     * 批量删除
     * @param tenantId
     * @param deviceId
     * @param deviceUserIds
     * @return
     */
    public long batchDel(String tenantId, String deviceId, List<String> deviceUserIds){
        List<Criteria> list = new ArrayList<>();
        list.add(Criteria.where(TENANT_ID).is(tenantId));
        list.add(Criteria.where(DEVICE_ID).is(deviceId));
        list.add(Criteria.where("deviceUserId").in(deviceUserIds));
        Criteria[] criteria = new Criteria[list.size()];
        list.toArray(criteria);
        Criteria c = new Criteria().andOperator(criteria);
        Query query = new Query(c);
        DeleteResult remove = mongoTemplate.remove(query, UserLockRecordEntity.class);
        return remove.getDeletedCount();
    }

    /**
     * 获取数据
     * @param req
     * @return
     */
    public List<UserLockRecordEntity> getRecords(UserLockReq req) {
        if (StringUtils.isBlank(req.getDeviceId())) {
            return new ArrayList<>();
        }

        Integer page = 1;
        Integer pageSize = MAX_PAGE_SIZE; //最多返回50个
        List<Criteria> list = new ArrayList<>();
        list.add(Criteria.where(DEVICE_ID).is(req.getDeviceId()));
        list.add(Criteria.where(TENANT_ID).is(req.getTenantId()));
        Criteria[] criteria = new Criteria[list.size()];
        list.toArray(criteria);
        Criteria c = new Criteria().andOperator(criteria);
        Query query = new Query(c);
        mongoUtil.start(page, pageSize, query);
        List<UserLockRecordEntity> recordList = mongoTemplate.find(query, UserLockRecordEntity.class);
        long count = mongoTemplate.count(query, UserLockRecordEntity.class);
        return mongoUtil.pageHelper(count, recordList).getList();
    }

}
