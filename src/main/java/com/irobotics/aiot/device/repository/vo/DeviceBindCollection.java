package com.irobotics.aiot.device.repository.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 设备绑定关系的mongo集合
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name  = "设备绑定关系的mongo集合")
@Document(collection = "device_bind_rel")
public class DeviceBindCollection {

    /**
     * id，格式：userId_sn
     */
    @Schema(description = "id，格式：userId_sn", required = true)
    @Id
    private String id;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", required = true)
    private Long createTime;

    /**
     * toString
     *
     * @return
     */
    @Override
    public String toString() {
        return "DeviceBindCollection{" +
                "id='" + id + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
