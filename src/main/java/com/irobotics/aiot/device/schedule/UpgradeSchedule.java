package com.irobotics.aiot.device.schedule;

import com.alibaba.fastjson.JSONObject;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.common.Constant;
import com.irobotics.aiot.device.remote.IUpgradeRemote;
import com.irobotics.aiot.device.remote.model.PackageInfoVo;
import com.irobotics.aiot.device.vo.KafkaData;
import com.irobotics.aiot.device.vo.UpgradeSetModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 设备定时检查升级失败消息
 *
 * <AUTHOR>
 * @since 2022-04-29
 */
@Component
@Slf4j
public class UpgradeSchedule {

    /**
     * 版本代码的下表
     */
    private static final Integer VERSION_CODE_INDEX = 3;

    /**
     * redis模板
     */
    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 升级服务
     */
    @Autowired
    private IUpgradeRemote upgradeRemote;

    /**
     * kafka
     */
    @Resource
    private KafkaTemplate kafkaTemplate;


    /**
     * 暂时设置不开启定时器
     */
    @Scheduled(cron = "0 0 5 31 2 ?")
    public void resendErrorMsg() {
        Set<String> keys = redisTemplate.keys("errorUpgrade:*");
        if (null == keys || keys.isEmpty()) {
            return;
        }
        for (String key : keys) {
            String[] keyArray = key.split(":");
            String productKey = keyArray[1];
            String versionCode = keyArray[VERSION_CODE_INDEX];
            PackageInfoVo info = upgradeRemote.getPackageByProductModelCodeAndVersion(productKey, versionCode);
            if (null == info) {
                log.error("redis 升级消息重发失败：{}--{} 升级包信息获取失败", productKey, versionCode);
                continue;
            }
            List<String> sns = redisTemplate.opsForList().range(key, 0, redisTemplate.opsForList().size(key) - 1);
            if (null == sns || sns.isEmpty()) {
                redisTemplate.delete(key);
                continue;
            }

            UpgradeSetModel model = new UpgradeSetModel(Integer.valueOf(info.getPackageSize()), info.getProductModelCode(),info.getPackageType(),
                    info.getVersionName(), info.getVersionCode(), info.getPackageUrl(), info.getMd5(), false, info.getSilence().equals(1));
            String body = JSONObject.toJSONString(model);

            KafkaData data = new KafkaData();
            for (String sn : sns) {
                data.setData(body);
                data.setTimestamp(System.currentTimeMillis());
                data.setMessageId(data.getTimestamp());
                data.setProductKey(productKey);
                data.setTenantId(info.getTenantId());
                data.setTopic(Constant.MQTT_BASE_TOPIC + productKey + Constant.BASE_PREF + sn + Constant.MOTHOD_UPGRADE_SET);
                data.setSn(sn);
                data.setVersion(info.getVersionCode());
                String kafkaData = JSONObject.toJSONString(data);
                LogUtils.info("kafka推送设备OTA升级消息,topic为:{},内容为:{}", Constant.UPGRADE_SET_TOPIC, kafkaData);
                kafkaTemplate.send(Constant.UPGRADE_SET_TOPIC, kafkaData);
                redisTemplate.opsForList().remove("errorUpgrade:" + productKey + ":" + versionCode, 1, sn);
            }
        }
    }
}
