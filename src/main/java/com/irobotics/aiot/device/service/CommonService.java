package com.irobotics.aiot.device.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.basic.exception.AppRuntimeException;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.entity.DeviceShadowEntity;
import com.irobotics.aiot.device.entity.NoticeShareRecordEntity;
import com.irobotics.aiot.device.remote.DeviceShadowRemote;
import com.irobotics.aiot.device.remote.InfrastructureThirdRemote;
import com.irobotics.aiot.device.remote.ProductServiceRemote;
import com.irobotics.aiot.device.remote.TAuthUserRemote;
import com.irobotics.aiot.device.remote.model.ProductBasicLangConfigEntity;
import com.irobotics.aiot.device.remote.model.ProductInfoEntity;
import com.irobotics.aiot.device.remote.model.TAuthClientUserExt;
import com.irobotics.aiot.device.vo.JPushFamilyShareReq;
import com.irobotics.aiot.device.vo.JPushShareReq;
import com.irobotics.aiot.device.vo.app.BindDeviceInfoVo;
import com.irobotics.aiot.device.vo.open.OpenBindDeviceInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.irobotics.aiot.device.common.NumberEnum.SIXTEEN;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2022/3/24 17:03
 */
@Component
public class CommonService {

    /**
     * 基础服务
     */
    @Autowired
    private InfrastructureThirdRemote infrastructureThirdRemote;

    @Autowired
    private DeviceShadowRemote deviceShadowRemote;

    @Autowired
    private ProductServiceRemote productServiceRemote;

    @Autowired
    private TAuthUserRemote authUserRemote;

    /**
     * 推送分享消息
     *
     * @param jPushShareReq
     */
    @Async
    public void pushShare(JPushShareReq jPushShareReq) {
        infrastructureThirdRemote.pushShare(jPushShareReq);
    }


    @Async
    public void shareInvalidate(String targetId,String uid,boolean pushMsg){
        // 设置分享消息为无效状态
        infrastructureThirdRemote.invalidateMsgByTargetIdAndUid(targetId,uid);
        // 推送用户取消解绑
        if(pushMsg){
            infrastructureThirdRemote.pushCancelShareMsg(targetId,uid);
        }
    }

    /**
     * 推送家庭分享消息
     *
     * @param jPushFamilyShareReq
     */
    @Async
    public void pushFamilyShare(JPushFamilyShareReq jPushFamilyShareReq) {
        infrastructureThirdRemote.pushFamilyShare(jPushFamilyShareReq);
    }

    /**
     * 检查是否有分享记录
     *
     * @param recordId
     * @return
     */
    public ResponseMessage<NoticeShareRecordEntity> checkShareRecord(String recordId) {
        return infrastructureThirdRemote.checkShareRecord(recordId);
    }


    /**
     * 删除分享记录
     * @param tenantId
     * @param shareId
     * @return
     */
    public ResponseMessage<Boolean> delByShareId(String tenantId, String shareId) {
        return infrastructureThirdRemote.delByShareId(tenantId,shareId);
    }


    public List<OpenBindDeviceInfoVo> formatBindDeviceInfo(List<OpenBindDeviceInfoVo> openBindDeviceInfoVoList){
        List<String>  snList = openBindDeviceInfoVoList.stream().map(OpenBindDeviceInfoVo::getSn).distinct().collect(Collectors.toList());
        Map<String,DeviceShadowEntity> deviceShadowMap = this.getDeviceShadowMap(snList);
        List<OpenBindDeviceInfoVo> resultList = new ArrayList<>();
        openBindDeviceInfoVoList.stream().forEach(e->{
            DeviceShadowEntity  deviceShadowEntity = deviceShadowMap.get(e.getSn());
            if (Objects.nonNull(deviceShadowEntity)){
                e.setOnlineStatus(deviceShadowEntity.getOnlineStatus());
                e.setProperties(deviceShadowEntity.getProperties());
            }
            // 设置设备默认状态
            if (Objects.isNull(e.getOnlineStatus())){
                e.setOnlineStatus(false);
            }
            resultList.add(e);
        });
        return resultList;
    }

    public List<BindDeviceInfoVo> formatBindDeviceInfoList(List<BindDeviceInfoVo> bindDeviceInfoVos){
        String userId = SystemContextUtils.getId();
        String tenantId = SystemContextUtils.getTenantId();
        ResponseMessage<TAuthClientUserExt> userExtByUserId = authUserRemote.getUserExtByUserId(tenantId, userId);
        String lang;
        if (!userExtByUserId.isSuccess()){
            LogUtils.error("/inner/user/userExt/userId -- 获取用户信息失败");
            lang = SystemContextUtils.getContextValue(ContextKey.LANG);
        } else {
            lang = userExtByUserId.getResult().getLang();
        }

        List<String>  snList = bindDeviceInfoVos.stream().map(BindDeviceInfoVo::getSn).distinct().collect(Collectors.toList());
        Map<String,DeviceShadowEntity> deviceShadowMap = this.getDeviceShadowMap(snList);
        List<String> productIdList = bindDeviceInfoVos.stream().map(BindDeviceInfoVo::getProductId).distinct().collect(Collectors.toList());
        Map<String, ProductInfoEntity> productInfoEntityMap = this.getProductInfoEntityByProductId(productIdList);
        List<BindDeviceInfoVo> resultList = new ArrayList<>();
        bindDeviceInfoVos.stream().forEach(e->{
            DeviceShadowEntity  deviceShadowEntity = deviceShadowMap.get(e.getSn());
            e.setStatus(0);
            e.setOnlineStatus(false);
            if (Objects.nonNull(deviceShadowEntity) && null != deviceShadowEntity.getOnlineStatus() && deviceShadowEntity.getOnlineStatus()) {
                e.setStatus(1);
                e.setOnlineStatus(true);
            }
            ProductInfoEntity productInfoEntity = productInfoEntityMap.get(e.getProductId());
            if (Objects.nonNull(productInfoEntity)){
                e.setPhotoUrl(productInfoEntity.getPhotoUrl());
                ResponseMessage<ProductBasicLangConfigEntity> prodBasicLangConfig = productServiceRemote.getProdBasicLangConfig(productInfoEntity.getId(), lang);
                if (prodBasicLangConfig.isSuccess()
                        && prodBasicLangConfig.getResult() != null
                        && StringUtil.isNotBlank(prodBasicLangConfig.getResult().getProductName()) ) {
                    e.setProductName(prodBasicLangConfig.getResult().getProductName());
                } else {
                    e.setProductName(productInfoEntity.getName());
                }
            }
            resultList.add(e);
        });
        return resultList;
    }


    /**
     * 通过sn获取设备影子信息
     * @param snList
     * @return
     */
    private Map<String,DeviceShadowEntity> getDeviceShadowMap(List<String>  snList){
        ResponseMessage<List<DeviceShadowEntity>> deviceShadowResult = deviceShadowRemote.findAllBySN(snList);
        if (!deviceShadowResult.isSuccess()){
            LogUtils.info("获取设备影子信息异常:{}",deviceShadowResult);
        }
        Map<String,DeviceShadowEntity> deviceShadowMap = new HashMap<>(SIXTEEN.getNumber());
        if (deviceShadowResult.isSuccess()){
            List<DeviceShadowEntity> deviceShadowList = deviceShadowResult.getResult();
            if (!CollectionUtils.isEmpty(deviceShadowList)){
                for (DeviceShadowEntity entity:deviceShadowList) {
                    deviceShadowMap.put(entity.getId(),entity);
                }
            }
        }
        return deviceShadowMap;
    }


    private Map<String, ProductInfoEntity> getProductInfoEntityByProductId(List<String> productIds) {
        Map<String, ProductInfoEntity> resultMap = new HashMap<>();
        if (CollectionUtil.isEmpty(productIds)){
            return resultMap;
        }
        ResponseMessage<List<ProductInfoEntity>> message = productServiceRemote.getListByProductIds(productIds);
        if (!message.isSuccess()){
            LogUtils.info("获取产品信息异常",message);
            throw new AppRuntimeException(ResponseCode.INNER_SERVER_ERROR);
        }
        List<ProductInfoEntity>  productInfoEntityList = message.getResult();
        if (CollectionUtil.isNotEmpty(productInfoEntityList)) {
            resultMap = productInfoEntityList.stream().collect(Collectors.toMap(k->k.getId(), Function.identity()));
        }
        return resultMap;
    }

}
