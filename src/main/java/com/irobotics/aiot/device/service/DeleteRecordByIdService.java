package com.irobotics.aiot.device.service;

import com.irobotics.aiot.device.vo.DeleteResp;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @since 2022/2/12
 */

public interface DeleteRecordByIdService {

    /**
     * 通过记录id删除一条记录
     *
     * @param taskId
     * @param tenantId
     * @param deviceId
     * @param resultList
     */
    void deleteRecordById(String taskId, String tenantId, String deviceId, List<DeleteResp> resultList);
}
