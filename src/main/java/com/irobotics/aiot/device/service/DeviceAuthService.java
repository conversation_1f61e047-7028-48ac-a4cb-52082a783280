package com.irobotics.aiot.device.service;

import com.irobotics.aiot.bravo.basic.base.CountryCity;
import com.irobotics.aiot.device.vo.LoginEntityVO;
import com.irobotics.aiot.device.vo.LoginKeyReq;

/**
 * 设备授权相关
 */
public interface DeviceAuthService {

    /**
     * 登录
     *
     * @param loginReq
     * @return
     */
    LoginEntityVO login(LoginKeyReq loginReq);

    /**
     * 通过ip获取城市信息
     *
     * @param ip
     * @return
     */
    CountryCity getCountryCity(String ip);
}
