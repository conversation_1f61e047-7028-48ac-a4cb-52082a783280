package com.irobotics.aiot.device.service;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.commons.util.JsonUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.common.Constant;
import com.irobotics.aiot.device.common.DevicePushTag;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.entity.DeviceShadowEntity;
import com.irobotics.aiot.device.remote.DeviceShadowRemote;
import com.irobotics.aiot.device.utils.MqttContentUtil;
import com.irobotics.aiot.device.vo.DeviceControlReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 设备控制相关
 */
@Service
public class DeviceControlService {

    /**
     * 成功
     */
    private static final int SUCCESS = 0;
    /**
     * 设备离线
     */
    private static final int ROBOT_OFFLINE = 1;
    /**
     * 获取设备影子失败
     */
    private static final int GET_SHADOW_FAIL = 2;

    /**
     * 设备影子
     */
    @Autowired
    private DeviceShadowRemote deviceShadowRemote;

    /**
     * kafka
     */
    @Autowired
    private KafkaService kafkaService;

    /**
     * 设备缓存
     */
    @Autowired
    private DeviceInfoCache deviceInfoCache;

    /**
     * 控制设备
     *
     * @param req
     * @param tenantId
     * @return
     */
    public ResponseMessage<Integer> controlDevice(DeviceControlReq req, String tenantId) {
        final DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheById(tenantId, req.getDeviceId());
        //通过设备sn获取设备影子
        final ResponseMessage<DeviceShadowEntity> response = deviceShadowRemote.findBySN(deviceInfo.getSn());
        final DeviceShadowEntity deviceShadow = response.getResult();
        if (Objects.isNull(deviceShadow)) {
            LogUtils.error("设备远程控制，远程调用获取设备影子失败，req={}", JsonUtils.toJSON(req));
            return ResponseMessage.buildSuccess(GET_SHADOW_FAIL);
        }
        //设备离线
        if (!deviceShadow.getOnlineStatus()) {
            return ResponseMessage.buildSuccess(ROBOT_OFFLINE);
        }
        //发送控制命令
        kafkaService.senServiceInvoke(req.getDeviceId(), tenantId, MqttContentUtil.joinContent(req.getContent()), DevicePushTag.CONTROL, Constant.SERVICE_INVOKE_KAFKA_TOPIC);
        return ResponseMessage.buildSuccess(SUCCESS);
    }
}
