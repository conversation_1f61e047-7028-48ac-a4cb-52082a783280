package com.irobotics.aiot.device.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.entity.DeviceResetAuditEntity;
import com.irobotics.aiot.device.vo.*;

public interface DeviceResetAuditService extends IService<DeviceResetAuditEntity> {

    /**
     * 根据内码与分页参数查询SN列表
     * @param param 内码与分页参数
     * @return SN列表
     */
    ResponseMessage<PageResp<IdSnUsernameResp>> getIdSnByIdAndPage(DeviceResetAuditPageParam param);
    /**
     * 分页查询
     *
     * @param param 分页参数
     * @return 审核记录
     */
    Page<DeviceResetAuditEntity> getList(DeviceResetAuditRecordParam param);

    /**
     * 申请删除SN列表下的设备历史记录
     *
     * @param param 申请删除参数
     * @return 设备历史记录
     */
    ResponseMessage<DeviceResetAuditEntity> apply(ApplyDelSnVo param);

    /**
     * 审核并删除申请SN列表对应的设备历史记录
     *
     * @param param 待审核实体信息
     * @return 已审核实体信息
     */
    ResponseMessage<DeviceResetAuditEntity> audit(String token,String taskId, DeviceResetAuditParam param);
}
