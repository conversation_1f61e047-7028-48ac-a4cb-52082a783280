package com.irobotics.aiot.device.service;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.entity.DeviceResetRecordEntity;
import com.irobotics.aiot.device.utils.PageHelper;
import com.irobotics.aiot.device.vo.DeleteResp;
import com.irobotics.aiot.device.vo.DeviceResetRecordPageReq;
import com.irobotics.aiot.device.vo.ResetNicknameReq;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @since 2022/1/13
 */
public interface DeviceResetService {

    /**
     * 通过sn列表获取记录
     *
     * @param taskId
     * @param tenantId
     * @param snList
     */
    void listBySns(String taskId, String tenantId, List<String> snList);

    /**
     * 通过用户名列表获取记录
     *
     * @param taskId
     * @param tenantId
     * @param usernameList
     */
    void listByUsernames(String taskId, String tenantId, List<String> usernameList);

    /**
     * 通过记录id列表获取记录
     *
     * @param token
     * @param taskId
     * @param tenantId
     * @param ids
     */
    void deleteRecordByIds(String token, String taskId, String tenantId, List<String> ids);

    /**
     * 通过Sn列表获取记录
     *
     * @param token
     * @param taskId
     * @param tenantId
     * @param sns
     */
    ResponseMessage<String> deleteRecordBySns(String token, String taskId, String tenantId, List<String> sns);

    /**
     * 分页
     *
     * @param pageReq
     * @return
     */
    ResponseMessage<PageHelper<DeviceResetRecordEntity>> findRecordPage(DeviceResetRecordPageReq pageReq);

    /**
     * 重置设备昵称，删除分享记录
     *
     * @param req
     * @return
     */
    boolean ResetNickname(ResetNicknameReq req);

    void saveRecordToMongo(String token, String tenantId, String taskId, List<DeleteResp> resultList);

    /**
     * 设备恢复出厂设置，清空设备所有相关数据
     */
    void reset(String deviceId);

}
