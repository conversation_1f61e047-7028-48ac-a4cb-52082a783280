package com.irobotics.aiot.device.service;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/12 11:03
 * @desc ExcelListener
 */
@Service
@Slf4j
public class ExcelListener<T> extends AnalysisEventListener<T> {


    @Getter
    @Setter
    List<T> dataList = new ArrayList<>();

    private int count = 0;

    @Override
    public void invoke(T t, AnalysisContext context) {
        //数据存储到list，供批量处理，或后续自己业务逻辑处理。

        count += 1;
        dataList.add(t);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("all excel data parsed, count:{}", count);
    }


}