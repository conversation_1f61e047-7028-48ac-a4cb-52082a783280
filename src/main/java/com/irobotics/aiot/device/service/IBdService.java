package com.irobotics.aiot.device.service;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.dto.BaiDuParam;
import com.irobotics.aiot.device.dto.ConversationParam;
import com.irobotics.aiot.device.dto.LargeModelParam;
import com.irobotics.aiot.device.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IBdService {

    /**
     * 上报 添加字典
     * @param param
     * @return
     */
    ResponseMessage<Boolean> addDictPost(AddDictParam param);

    /**
     * 删除单个同义词
     * @param param
     * @return
     */
    ResponseMessage<Boolean> deleteDictPost(DelDictParam param);

    /**
     * 获取自定义词典列表
     * @param param
     * @return
     */
    ResponseMessage<Boolean> listDictPost(ListDictParam param);

    /**
     * 清空自定义词典列表
     * @param param
     * @return
     */
    ResponseMessage<Boolean> clearDictPost(ClearDictParam param);

    /**
     * 批量覆盖添加同义词
     * @param param
     * @return
     */
    ResponseMessage<Boolean> batchAddDictPost(BatchAddDictParam param);

    /**
     * 按type清空自定义词典
     * @param param
     * @return
     */
    ResponseMessage<Boolean> clearTypeDictPost(ClearTypeDictParam param);

    /**
     * 定制技能
     * @param jsonStr
     * @param authorization
     * @param accessKey
     * @param timestamp
     * @return
     */
    BdSkillResponse skill(String jsonStr, String authorization, String accessKey, String timestamp);

    /**
     * 定制技能
     * @return
     */
    BdSkillResponse test(BdSkillParam param);


    /**
     * 根据协议获取回复词
     * @param authorization
     * @param accessKey
     * @param timestamp
     * @return
     */
    ResponseMessage<List<String>> getReply(LargeModelParam param);

    /**
     * 上报属于设备处理回复协议的会话记录
     * @return
     */
    ResponseMessage<String> addConversation(ConversationParam param);


    /**
     * 百度接口
     * @return
     */
    ResponseMessage<BaiDuVo> baiduRequest (BaiDuParam param);

}
