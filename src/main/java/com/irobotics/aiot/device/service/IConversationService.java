package com.irobotics.aiot.device.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.entity.Conversation;
import com.irobotics.aiot.device.vo.ConversationPageReq;

/**
 * <AUTHOR>
 */
public interface IConversationService {

    /**
     * 根据sn删除会话
     * @param sn
     */
    void deleteBySn(String sn);

    /**
     * 分页查找语音会话
     * @param pageReq
     * @return
     */
    ResponseMessage<Page<Conversation>> page(ConversationPageReq pageReq);
}
