package com.irobotics.aiot.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.entity.DeviceBindBaseEntity;
import com.irobotics.aiot.device.vo.inner.BindBaseListReq;

import java.util.List;

/**
 * <p>
 * 第三方用户设备绑定关系信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
public interface IDeviceBindBaseService extends IService<DeviceBindBaseEntity> {

    /**
     * 基站和设备的绑定关系
     * 基站和设备只有条绑定关系
     * @param deviceId
     * @param sn
     * @param baseId
     * @param baseSn
     * @return
     */
    Boolean bind(String deviceId,String sn,String baseId,String baseSn);

    /**
     * 根据sn获取基站绑定关系
     * @param req
     * @return
     */
    ResponseMessage<List<DeviceBindBaseEntity>> bindListByDeviceSn(BindBaseListReq req);

    /**
     *获取基站绑定关系
     * @param baseId
     * @param baseSn
     * @param devId
     * @param devSn
     * @return
     */
    ResponseMessage<List<DeviceBindBaseEntity>> getBaseBind(String baseId, String baseSn, String devId, String devSn);
}
