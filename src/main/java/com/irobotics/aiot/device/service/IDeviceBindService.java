package com.irobotics.aiot.device.service;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.vo.BindResp;
import com.irobotics.aiot.device.vo.OrCodeBindVo;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
public interface IDeviceBindService {

    /**
     * 设备发起绑定app请求
     *
     * @param deviceId 设备id
     * @param userId   用户id
     * @param key      绑定key
     */
    void bindApp(String deviceId, String userId, String key);

    /**
     * app确认设备发起的绑定app请求
     *
     * @param userId   用户id
     * @param key      绑定key
     * @param roomId   房间id
     * @param familyId 家庭id
     * @return
     */
    BindResp bindAppConfirm(String userId, String key, String familyId, String roomId);

    /**
     * 获取服务器颁发的bindKey，并把bindKey与设备id放入缓存
     * @return
     */
    String getBindKey(String deviceId, String tenantId);

    /**
     * 二维码绑定
     * 分享绑定
     *
     * @param vo
     * @return
     */
    ResponseMessage<BindResp> orCodeBind(OrCodeBindVo vo);
}
