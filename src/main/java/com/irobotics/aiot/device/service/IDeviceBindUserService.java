package com.irobotics.aiot.device.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.dto.MqttPropertyGetReqDTO;
import com.irobotics.aiot.device.entity.DeviceBindUserEntity;
import com.irobotics.aiot.device.vo.admin.AdminDeviceBindReq;
import com.irobotics.aiot.device.vo.admin.AdminDeviceBindVo;
import com.irobotics.aiot.device.vo.app.BindDeviceInfoVo;
import com.irobotics.aiot.device.vo.app.MoveDeviceReq;
import com.irobotics.aiot.device.vo.app.UntieDeviceReq;
import com.irobotics.aiot.device.vo.inner.OwnerListParam;
import com.irobotics.aiot.device.vo.inner.OwnerListVo;

import java.util.List;

/**
 * <p>
 * 关闭智能家居设备绑定关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-04
 */
public interface IDeviceBindUserService extends IService<DeviceBindUserEntity> {

    /**
     * 绑定关系，新设备返回true
     * @param deviceId
     * @param nickname
     * @param sn
     * @param productId
     * @return
     */
    Boolean bind(String deviceId,String nickname,String sn,String productId);

    /**
     * 分享绑定关系
     * @param deviceId
     * @param nickname
     * @param sn
     * @param productId
     * @param owner
     * @return
     */
    Boolean shareBind(String deviceId,String nickname,String sn,String productId,String owner);

    /**
     * 根据userId解绑分享绑定
     * @param deviceId
     * @param userId
     * @return
     */
    Boolean untieShareBind(String deviceId,String userId);


    /**
     * 通过设备id批量删除设备所有绑定关系
     * @param deviceIds
     */
    void deleteBindByDeviceIds(List<String> deviceIds);


    /**
     * 获取绑定关系
     * @param req
     * @return
     */
    ResponseMessage<Page<AdminDeviceBindVo>> getPage(AdminDeviceBindReq req);

    /**
     * 设备解绑
     * @param req
     * @return
     */
    ResponseMessage<Boolean> untieDevice(UntieDeviceReq req);

    /**
     * 通过userId 获取绑定设备列表
     * @param userId
     * @return
     */
    ResponseMessage<List<BindDeviceInfoVo>> getBindListByUserId(String userId);

    /**
     * 添加在线app的sn缓存，用于实时地图
     * @param snList
     */
    void addOnlineAppSnCacheForTempMap(List<String> snList);

    /**
     * 批量新增
     * @param list
     * @return
     */
    ResponseMessage<Boolean> addBatch(List<DeviceBindUserEntity> list);

    /**
     * 获取绑定关系
     * @param userId
     * @param tenantId
     * @return
     */
    ResponseMessage<List<DeviceBindUserEntity>> getBindList(String tenantId,String userId);


    /**
     *
     * 根据id删除绑定关系
     * @param ids
     * @return
     */
    ResponseMessage<Boolean> deleteByIds(List<String> ids);


    /**
     * 移动设备到顶部
     * @param req
     * @return
     */
    ResponseMessage<Boolean> moveToTop(MoveDeviceReq req);

    /**
     * 跟新设备列表排序
     * @param req
     * @return
     */
    ResponseMessage<Boolean> updateOrder(MoveDeviceReq req);


    /**
     * 根据设备id获取设备绑定的用户
     * @param param
     * @return
     */
    ResponseMessage<List<OwnerListVo>> getOwnerListByDeviceId(OwnerListParam param);


    /**
     * 根据userId删除绑定关系
     * @param userIds
     * @param tenantId
     * @return
     */
    ResponseMessage<Boolean> deleteByUserIds(List<String> userIds,String tenantId);

    /**
     * 属性获取处理
     * @param param
     */
    void propertyGetTopicHandler(MqttPropertyGetReqDTO param);
}
