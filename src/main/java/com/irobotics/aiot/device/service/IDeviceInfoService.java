package com.irobotics.aiot.device.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.irobotics.aiot.bravo.basic.base.CountryCity;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.vo.*;
import com.irobotics.aiot.device.vo.excel.DeviceInfoVo;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
public interface IDeviceInfoService extends IService<DeviceInfoEntity> {

    /**
     * 检查sn的合法性
     *
     * @param snDetail sn信息
     * @param sn       目标sn
     * @param key      sn的key
     */
    void checkSn(ProductSnDetailEntity snDetail, String sn, String key);

    /**
     * 设备登录
     *
     * @param tenantId        企业id
     * @param mac             参数mac
     * @param sn              参数sn
     * @param key             参数key
     * @param ip              ip地址
     * @param info            设备信息
     * @param countryLoc
     * @param productModeCode 产品型号代码
     * @param curVersions     当前版本
     * @param countryCity     区域信息
     * @return
     */
    LoginEntityVO doLogin(String tenantId, String mac, String sn, String key, String ip, DeviceInfoEntity info, String countryLoc, String productModeCode, String curVersions, CountryCity countryCity);


    /**
     * 设备注册
     *
     * @param tenantId        租户id
     * @param sn              设备sn
     * @param mac             设备mac
     * @param key
     * @param ip              ip
     * @param productModeCode 产品型号代码
     * @param countryLoc
     * @param curVersions
     * @param countryCity
     * @return
     */
    LoginEntityVO doRegister(String tenantId, String sn, String mac, String key,
                             String ip, String productModeCode, String countryLoc, String curVersions,
                             CountryCity countryCity);

    /**
     * 分页获取设备信息
     *
     * @param pageGetReq
     * @return
     */
    Page<DeviceInfoEntity> pageList(DeviceInfoPageGetReq pageGetReq);

    /**
     * 通过设备sn和mac获取设备信息
     *
     * @param sn  设备sn
     * @param mac 设备mac
     * @return
     */
    DeviceInfoEntity getBySnAndMac(String sn, String mac);

    /**
     * 通过设备sn获取设备是否在线
     *
     * @param sn 设备sn
     * @return
     */
    Boolean ifOnline(String sn);

    /**
     * 通过sn获取设备状态信息
     *
     * @param sn
     * @return
     */
    JSONObject getDeviceStatus(String sn);

    /**
     * 通过设备id集合获取设备信息集合
     *
     * @param deviceIds 设备id集合
     * @return
     */
    List<DeviceInfoEntity> getDevicesByIds(List<String> deviceIds);

    /**
     * 获取设备所有sn列表
     *
     * @return
     */
    List<String> selectAllRobotSn();

    /**
     * 通过产品id列表获取设备数量
     *
     * @param productIds 产品id列表
     * @return Map<string, Long> key-产品id,value-设备数量
     */
    Map<String, Long> getDeviceCountByPro(List<String> productIds);

    /**
     * 根据设备ID集合获取设备信息
     *
     * @param deviceIds 设备id列表
     * @return
     */
    ResponseMessage<List<DeviceInfoEntity>> getDeviceInfosByIds(List<String> deviceIds);

    /**
     * 修改设备昵称
     *
     * @param tenantId 租户id
     * @param deviceId 设备id
     * @param nickname 昵称
     * @param sn       设备sn
     * @param mac      设备mac
     * @return
     */
    ResponseMessage<Boolean> modifyNickname(String tenantId, String deviceId, String nickname, String sn, String mac);

    /**
     * 根据设备ID集合获取设备昵称
     *
     * @param deviceIds 设备id列表
     * @return Map<String, String> key-设备id,value-设备昵称
     */
    ResponseMessage<Map<String, String>> getDeviceNickNameByIds(List<String> deviceIds);

    /**
     * 通过设备id列表获取设备信息
     *
     * @param tenantId
     * @param ids
     * @return
     */
    List<DeviceInfoEntity> getByIds(String tenantId, List<String> ids);

    /**
     * 分页通过产品型号代码获取设备信息
     *
     * @param productModeCode 产品型号代码
     * @param page            页码
     * @param pageSize        页大小
     * @return
     */
    Page<DeviceInfoEntity> getPageByCode(String productModeCode, Integer page, Integer pageSize);

    /**
     * 通过产品型号代码获取设备数量
     *
     * @param productModeCode 产品型号代码
     * @return
     */
    Integer getCountByCode(String productModeCode);

    /**
     * 更新设备的是否在app上验证密码的属性字段
     *
     * @param vo
     * @return
     */
    ResponseMessage<Boolean> updateVerifyPassword(DeviceVerifyPasswordVo vo);

    /**
     * 配置设备是否需要开启验证app密码
     *
     * @param vo
     * @return
     */
    ResponseMessage<Boolean> configVerify(DeviceConfigVerifyPasswordVo vo);

    /**
     * 获取所有设备的数量
     *
     * @return
     */
    Long getCount();

    /**
     * 分页获取设备信息
     *
     * @param page
     * @param pageSize
     * @return
     */
    Page<DeviceInfoEntity> getDevicePage(Integer page, Integer pageSize);

    /**
     * 更新iotId信息
     *
     * @param vo
     * @return
     */
    ResponseMessage<Boolean> updateIotId(IotIdVo vo);

    /**
     * 获取app用户下的设备列表
     *
     * @param req
     * @return
     */
    ResponseMessage<Page<DeviceInfoEntity>> getDevicesByUserId(DeviceUserPageReq req);

    /**
     * 重置设备昵称为默认昵称
     *
     * @param sn
     * @return
     */
    ResponseMessage<Boolean> resetNickname(String sn);

    /**
     * 更新指定产品的设备的容易字段，产品图片
     *
     * @param vo
     * @return
     */
    ResponseMessage<Boolean> updatePhoto(UpdatePhotoVo vo);

    /**
     * 根据设备ID获取设备信息
     *
     * @param id
     * @return
     */
    ResponseMessage<DeviceInfoEntity> getDeviceInfoById(String id);

    /**
     * 获取某个产品型号下前N%个活跃设备SN
     *
     * @param deviceInfoReq 产品型号
     * @return 活跃设备SN
     */
    List<String> getActiveDeviceSNByCode(DeviceInfoReq deviceInfoReq);

    /**
     * 根据过滤条件导出设备信息
     * @param pageGetReq
     * @return
     */
    List<DeviceInfoVo> exportDeviceInfo(DeviceInfoPageGetReq pageGetReq);

    /**
     * 导出vo数据
     * @param vo
     * @param response
     */
    void exportSearchInfo(List<DeviceInfoVo> vo, HttpServletResponse response);


    /**
     * 通过sn获取最近登录城市
     */
    List<DeviceInfoCityVo> getCityBySns(String tenantId,List<String> sns);

}