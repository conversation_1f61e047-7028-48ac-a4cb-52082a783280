package com.irobotics.aiot.device.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.common.InviteShareEnum;
import com.irobotics.aiot.device.entity.DeviceInviteShareEntity;
import com.irobotics.aiot.device.vo.*;

import java.util.List;

/**
 * <p>
 * 设备分享邀请信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
public interface IDeviceInviteShareService extends IService<DeviceInviteShareEntity> {

    /**
     * 分享设备给指定用户
     *
     * @param beInvited 被邀请者用户名
     * @param inviterId 邀请者用户id
     * @param inviter   邀请者用户名
     * @param targetIds 目标ids
     * @return
     */
    ResponseMessage<Boolean> inviteUserShareRobot(String beInvited, String inviterId, String inviter, List<String> targetIds);

    /**
     * 回应用户的分享
     *
     * @param type            类型，0-共享设备，1-共享家庭
     * @param familyId        家庭id
     * @param familyName      家庭名称
     * @param beInvitedUserId 被邀请的用户id
     * @param targetId        邀请者的用户id
     * @param deviceId        设备id
     * @param dealType        回应类型 1-同意，2-拒绝
     * @return
     */
    boolean replyShared(int type, String familyId, String familyName, String beInvitedUserId, String targetId, String deviceId, int dealType);

    /**
     * 获取当前用户分享信息
     *
     * @param req
     * @return
     */
    Page<DeviceShareVo> getAllSharedList(GetUserSharedReq req);

    /**
     * 删除(取消)分享信息
     *
     * @param shareId 主键id
     * @param userId  用户id
     * @return
     */
    boolean cancelShare(String shareId, String userId);

    /**
     * 根据用户id与设备id删除分享信息
     *
     * @param req
     * @return
     */
    ResponseMessage<Boolean> delShare(DelShareReq req);

    /**
     * 获取当前用户的分享信息历史
     *
     * @param req
     * @return
     */
    ResponseMessage<Page<DeviceShareHisVo>> getShareHis(UserSharedHisReq req);

    /**
     * 分享家庭，保存分享信息
     *
     * @param vo
     * @return
     */
    ResponseMessage<List<String>> doShareFamily(ShareFamilyVo vo);

    /**
     * 修改分享记录的状态，并且增加绑定状态，只能对正常的状态（不是同意也不是拒绝的状态）
     *
     * @param familyName 家庭名称
     * @param familyId   家庭id
     * @param shareId 分享id
     * @param status 状态，同意或拒绝
     * @return
     */
    boolean changeStatus(String familyName, String familyId, String shareId, Integer status);

    /**
     * 通过设备id删除对应的分享记录
     *
     * @param deviceId
     * @return
     */
    boolean delByDeviceId(String deviceId);

    /**
     * 通过企业id和用户id删除分享记录，用户注销专用
     *
     * @param tenantId
     * @param userId
     * @return
     */
    boolean delByUserId(String tenantId, String userId);

    /**
     * 通过分享推送记录同意或拒绝分享
     *
     * @param req
     * @return
     */
    ResponseMessage<Boolean> changeShareRecordStatus(ShareRecordChangeStatusReq req);

    /**
     * 被分享者解绑设备，删除分享者记录
     * @param userId
     * @param deviceId
     * @param inviteShareEnum 仅标识  DEVICE(0, "共享设备"),FAMILY(1, "共享家庭"),
     */
    void delByInvitee(String userId, String deviceId, InviteShareEnum inviteShareEnum);
}
