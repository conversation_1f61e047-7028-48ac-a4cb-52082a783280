package com.irobotics.aiot.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.entity.DeviceMapEntity;
import com.irobotics.aiot.device.vo.DeviceCurMapParam;

/**
 * <p>
 * 扫地机设备地图表 服务类
 * </p>
 */
public interface IDeviceMapService extends IService<DeviceMapEntity> {

    /**
     * 获取设备当前地图
     * @param sn
     * @return
     */
    ResponseMessage<DeviceMapEntity> getCurMap(String sn);

    /**
     * 上传设备当前地图
     * @param curMapParam
     * @return
     */
    ResponseMessage<Boolean> uploadCurMap(DeviceCurMapParam curMapParam);

    /**
     * 删除设备当前地图
     * @return
     */
    ResponseMessage<Boolean> delCurMap(String sn);
}
