package com.irobotics.aiot.device.service;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.remote.model.PackageInfoVo;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-27
 */
public interface IDeviceUpgradeService {

    /**
     * 将productModelCode下的所有设备 推送kafka升级消息
     *
     * @param info
     * @return
     */
    void publishDevices(PackageInfoVo info);

    /**
     * 创建OTA主动下发任务的设备详情信息
     *
     * @param info
     */
    void createPushUpgradeTask(PackageInfoVo info);

    /**
     * 判断设备是否需要升级OTA
     *
     * @param device
     */
    void checkDeviceUpgrade(DeviceInfoEntity device);

}
