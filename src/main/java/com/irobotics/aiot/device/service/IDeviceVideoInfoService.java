package com.irobotics.aiot.device.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.entity.DeviceVideoInfoEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.irobotics.aiot.device.vo.admin.AdminDeviceVideoInfoReq;
import com.irobotics.aiot.device.vo.app.VideoInfoVo;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-25
 */
public interface IDeviceVideoInfoService extends IService<DeviceVideoInfoEntity> {

    /**
     * 返回key
     * @param sn
     * @return
     */
    ResponseMessage<VideoInfoVo> getKey(String sn);


    /**
     * 上传三元组信息
     * @param file
     * @param paramTenantId
     * @return
     */
    ResponseMessage<String> upload(MultipartFile file,String paramTenantId);


    /**
     *
     * 修改数据
     * @param deviceVideoInfoEntity
     * @return
     */
    ResponseMessage<Boolean> update(DeviceVideoInfoEntity deviceVideoInfoEntity);


    /**
     * 分页
     * @param adminDeviceVideoInfoReq
     * @return
     */
    ResponseMessage<Page<DeviceVideoInfoEntity>> page(AdminDeviceVideoInfoReq adminDeviceVideoInfoReq);


    /**
     * 删除
     * @param idList
     * @return
     */
    ResponseMessage<Boolean> delete(List<String> idList);
}
