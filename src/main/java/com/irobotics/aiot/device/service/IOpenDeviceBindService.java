package com.irobotics.aiot.device.service;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.vo.open.BatchExecuteResult;
import com.irobotics.aiot.device.vo.open.BindDeviceVo;
import com.irobotics.aiot.device.vo.open.OpenBindDeviceInfoVo;
import com.irobotics.aiot.device.vo.open.UnBindDeviceVo;

import java.util.List;

/**
 * 开放SDK服务类
 */
public interface IOpenDeviceBindService {

    /**
     * 用户直接绑定设备
     *
     * @param vo
     * @return
     */
    ResponseMessage<Boolean> bindDevice(BindDeviceVo vo);

    /**
     * 解绑设备（删除设备绑定关系）
     *
     * @param vo
     * @return
     */
    ResponseMessage<Boolean> unBindDevice(BindDeviceVo vo);

    /**
     * 通过userid查询绑定的设备列表
     *
     * @param userId
     * @return
     */
    ResponseMessage<List<OpenBindDeviceInfoVo>> deviceList(String userId);

    /**
     * 批量解绑设备（设备-用户）
     *
     * @param unBindDeviceVo 设备-用户集合
     * @return 解绑结果
     */
    ResponseMessage<BatchExecuteResult> batchUnBindDevice(UnBindDeviceVo unBindDeviceVo);
}
