package com.irobotics.aiot.device.service;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.entity.ProductSnDetailActiveZoneEntity;

import java.util.List;

/**
 * sn激活 服务类
 */
public interface IProductSnDetailActiveZoneService {

    /**
     * 根据sn查询激活信息
     * @param sn
     * @return
     */
    ProductSnDetailActiveZoneEntity getBySn(String sn);

    /**
     * 插入数据
     * @param productSnDetailActiveZoneEntity
     * @return
     */
    ResponseMessage<Boolean> insertOne(ProductSnDetailActiveZoneEntity productSnDetailActiveZoneEntity);

    /**
     * 根据sn删除数据
     * @param sn
     * @return
     */
    ResponseMessage<Boolean> delBySn(String sn);

    /**
     * 根据sn批量删除数据
     * @param sns
     * @return
     */
    ResponseMessage<Boolean> delBySns(List<String> sns);

    /**
     * 根据组id统计激活量
     * @param groupId
     * @return
     */
    Long countByGroupId(String groupId);

    /**
     * 根据产品型号id模糊查询sn
     * @param productModeId
     * @param sn
     * @return
     */
    List<ProductSnDetailActiveZoneEntity> snCodes(String productModeId, String sn);

    /**
     * 批量获取已激活的sn
     * @param snList
     * @return
     */
    List<ProductSnDetailActiveZoneEntity> getBySnList(List<String> snList);
}
