package com.irobotics.aiot.device.service;

import java.util.List;

public interface IcloudIntentService {

    /**
     * 单条保存
     * @param userId
     * @param sn
     */
    void insert(String userId, String sn);

    /**
     * 单条删除
     * @param userId
     * @param sn
     */
    void remove(String userId, String sn);

    /**
     * 用户列表批量保存
     * @param userIds
     * @param sn
     */
    void batchInsertByUserIds(List<String> userIds, String sn);

    /**
     * 用户列表批量删除
     * @param userIds
     * @param sn
     */
    void batchRemoveByUserIds(List<String> userIds, String sn);

    /**
     * 设备SN列表批量保存
     * @param userId
     * @param sns
     */
    void batchInsertBySns(String userId, List<String> sns);

    /**
     * 设备SN列表批量删除
     * @param userId
     * @param sns
     */
    void batchRemoveBySns(String userId, List<String> sns);
}
