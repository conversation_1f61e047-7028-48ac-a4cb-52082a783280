package com.irobotics.aiot.device.service;

import com.irobotics.aiot.device.common.DevicePushTag;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;

import java.util.List;

/**
 * kafka服务类
 */
public interface KafkaService {

    /**
     * 生成服务调用消息内容
     *
     * @param deviecId 设备id
     * @param content  消息主体
     * @param pushTag  消息发送类型
     * @return json
     */
    String genServiceInvokeMsg(String deviecId, String tenantId, String content, DevicePushTag pushTag);

    /**
     * 发送单条kafka消息
     *
     * @param deviceId 设备id
     * @param tenantId 厂商id
     * @param content  指令内容
     * @param pushTag  指令类型
     * @param topic    kafka topic
     */
    void senServiceInvoke(String deviceId, String tenantId, String content, DevicePushTag pushTag, String topic);

    /**
     * 发送多条kafka消息
     *
     * @param deviceIds 设备id集合
     * @param pushTag   指令类型
     * @param topic     kafka topic
     */
    void sendBatchServiceInvoke(List<String> deviceIds, String tenantId, String content, DevicePushTag pushTag, String topic);

    /**
     *  统计激活设备
     * @param infoEntity
     */
    void deviceRegister(DeviceInfoEntity infoEntity);
}
