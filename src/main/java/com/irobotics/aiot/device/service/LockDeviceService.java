package com.irobotics.aiot.device.service;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.vo.*;

/**
 * 门锁服务类
 */
public interface LockDeviceService {


    /**
     * 获取passcode
     *
     * @param param
     * @return
     * @throws Exception
     */
    ResponseMessage<LockGetPcdRep> getPasscode(LockLoginParam param) throws Exception;

    /**
     * 交换公钥
     *
     * @param lockBindReq
     * @return
     * @throws Exception
     */
    ResponseMessage<LockBindRep> publicKeyExchange(LockBindReq lockBindReq) throws Exception;

    /**
     * 注册检查门锁
     *
     * @param param
     * @return
     * @throws Exception
     */
    ResponseMessage<CheckDevConfirmRep> checkDevConfirm(CheckDevConfirmParam param) throws Exception;

    /**
     * 门锁登录前获取服务端公钥
     *
     * @param param
     * @return
     * @throws Exception
     */
    ResponseMessage<LoginGetPubRep> loginGetPub(LockLoginParam param) throws Exception;

    /**
     * 门锁登录前获取验证信息
     *
     * @param lockLoginGetVerifyReq
     * @return
     * @throws Exception
     */
    ResponseMessage<LockLoginGetVerifyRep> loginGetVerifyData(LockLoginGetVerifyReq lockLoginGetVerifyReq) throws Exception;

    /**
     * 门锁登录
     *
     * @param loginEndReq
     * @return
     */
    ResponseMessage<LoginEndRep> loginEnd(LoginEndReq loginEndReq);

    /**
     * 门锁重置
     *
     * @param resetLockReq
     * @return
     */
    ResponseMessage<ResetLockRep> resetLock(ResetLockReq resetLockReq);
}
