package com.irobotics.aiot.device.service;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.vo.LockPasswordReq;
import com.irobotics.aiot.device.vo.LockPasswordVo;
import com.irobotics.aiot.device.vo.UserLockVo;
import com.irobotics.aiot.device.vo.UserLockReq;
import com.irobotics.aiot.device.vo.UserLockSyncReq;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-12
 */
public interface LockService {
    /**
     * 生成一次性密码
     * @param keyReq
     * @return
     */
    ResponseMessage<String> generatorKey(LockPasswordReq keyReq);

    /**
     *  获取一次性有效密码
     * @param keyReq
     * @return
     */
    ResponseMessage<List<LockPasswordVo>> getPasswordHis(LockPasswordReq keyReq);

    /**
     * 批量删除记录
     * @param deviceUserIds
     * @return
     */
    ResponseMessage<Boolean> batchDel(List<String> deviceUserIds);

    /**
     * 拉取门锁记录
     * @param req
     * @return
     */
    ResponseMessage<List<UserLockVo>> getRecords(UserLockReq req);

    /**
     * 同步门锁记录
     * @param list
     * @return
     */
    ResponseMessage<Void> syncRecord(List<UserLockSyncReq> list);
}
