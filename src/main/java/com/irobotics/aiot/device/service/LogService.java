package com.irobotics.aiot.device.service;

import com.irobotics.aiot.device.vo.BindLogEntity;
import com.irobotics.aiot.device.vo.LoginKeyReq;

import java.util.List;

/**
 * 日志服务类
 */
public interface LogService {

    /**
     * 登录失败，插入一条日志
     *
     * @param req
     * @param country 城市
     */
    void loginFail(LoginKeyReq req, String country);

    /**
     * 插入一条登录日志
     *
     * @param productModeCode 工程类型
     * @param sn              sn
     * @param mac             mac
     * @param tenantId        租户id
     * @param key             登录带的key
     * @param deviceId        设备第
     * @param ip              ip
     * @param country         城市
     * @param version         版本
     */
    void loginLog(String productModeCode, String sn, String mac, String tenantId, String key, String deviceId, String ip, String country, String version);

    /**
     * 绑定日志
     *
     * @param type
     * @param operaType
     * @param deviceId
     */
    void bindLog(String type, String operaType, String deviceId);

    /**
     * 批量写入绑定或解绑日志
     *
     * @param bindLogEntity 日志集合
     */
    void bindLogList(List<BindLogEntity> bindLogEntity);
}