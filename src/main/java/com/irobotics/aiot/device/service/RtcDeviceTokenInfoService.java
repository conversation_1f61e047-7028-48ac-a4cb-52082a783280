package com.irobotics.aiot.device.service;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.device.vo.RtcActivateDeviceTokenVo;
import com.irobotics.aiot.device.vo.RtcDeviceNodeInfoReq;

/**
 * <p>
 * 设备RTC秘钥以及token信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
public interface RtcDeviceTokenInfoService {

    /**
     * 设备接口：激活设备，获取访问授权令牌
     * @param param：mac地址和sn
     * @return
     */
    ResponseMessage<RtcActivateDeviceTokenVo> activateDevice(RtcDeviceNodeInfoReq param);

}
