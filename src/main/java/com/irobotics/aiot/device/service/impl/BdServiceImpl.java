package com.irobotics.aiot.device.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.commons.util.SnowflakeUtil;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.dto.BaiDuParam;
import com.irobotics.aiot.device.dto.ConversationParam;
import com.irobotics.aiot.device.dto.LargeModelParam;
import com.irobotics.aiot.device.entity.Conversation;
import com.irobotics.aiot.device.entity.DeviceShadowEntity;
import com.irobotics.aiot.device.entity.ReplyWords;
import com.irobotics.aiot.device.mapper.ConversationMapper;
import com.irobotics.aiot.device.mapper.ReplyWordsMapper;
import com.irobotics.aiot.device.remote.DeviceShadowRemote;
import com.irobotics.aiot.device.service.IBdService;
import com.irobotics.aiot.device.service.IDeviceInfoService;
import com.irobotics.aiot.device.utils.BdPostRequest;
import com.irobotics.aiot.device.vo.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Slf4j
@RefreshScope
@Service
public class BdServiceImpl implements IBdService {

    public static final String WATERSTRING = "当前为较干水量。";
    public static final String WINDSTRING = "当前吸力为关。";
    public static final String RESERVED_SIDE_BRUSH_KEY_STRING = ",预计还可使用";
    public static final String VOLUMESTRING = "当前音量低。";


    @Value("${largemodel.replytext:大模型处理中}")
    private String LARGEFLAGREPLYTEXT;


    @Value("${largemodel.domain:greeting,failure}")
    private String largemodelDomain;

    @Autowired
    private ReplyWordsMapper replyWordsMapper;

    @Autowired
    private DeviceShadowRemote deviceShadowRemote;

    @Autowired
    private ConversationMapper conversationMapper;

    @Autowired
    private BdPostRequest bdPostRequest;

    private static final String STRING_TYPE = "STRING";

    private static final String TIME_TYPE = "TIME";

    private static final String BEGIN = "begin";

    private static final String END = "end";

    private static final Pattern PATTERN = Pattern.compile("\\#\\{([a-zA-Z0-9_]+?)}");

    private static final String TIME_SLOT = "time";

    private static final String LOCATION_SLOT = "location";

    private static final String FURNITURE_SLOT = "furniture";

    private static final String NOT_FOUND = "notfound";

    /**
    匹配符-电量*
     */
    private static final String BATTERY = "battery";

    /**
     匹配符-耗材*
     */
    private static final String CONSUMABLE = "consumable";

    /**
     设备信息-电量*
     */
    private static final String QUANTITY_KEY = "quantity";

    /**
     匹配符-吸力*
     */
    private static final String WIND = "wind";


    /**
     匹配符-水量*
     */
    private static final String WATER = "water";


    /**
     匹配符-音量*
     */
    private static final String VOLUME = "volume";

    /**
     设备信息-主刷*
     */
    private static final String RESERVED_MAIN_BRUSH_KEY = "reserved_main_brush";

    /**
     设备信息-边刷*
     */
    private static final String RESERVED_SIDE_BRUSH_KEY = "reserved_side_brush";

    /**
     设备信息-尘盒滤网*
     */
    private static final String RESERVED_HYPA_KEY = "reserved_hypa";

    /**
     设备信息-滚筒拖布*
     */
    private static final String RESERVED_MOP_LIFE_KEY = "reserved_mop_life";


    private static final String OK = "好的";

    private static final String CONTENTYPE = "Content-Type";

    private static final String APPLICATIONJSON = "application/json";

    OkHttpClient client = new OkHttpClient();

    @Autowired
    private IDeviceInfoService deviceInfoService;


    @Override
    public ResponseMessage<Boolean> addDictPost(AddDictParam param) {
        BdAddDictParam bdAddDictParam;
        bdAddDictParam = param.getParam();
        try {
            bdPostRequest.postRequest("/v1/customDict/add", JSON.toJSONString(bdAddDictParam),null);
        } catch (Exception e) {
            LogUtils.error("新增单个同义词异常：", e);
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR);
        }
        return ResponseMessage.buildSuccess();
    }

    @Override
    public ResponseMessage<Boolean> deleteDictPost(DelDictParam param) {
        BdDelDictParam bdDelDictParam;
        bdDelDictParam = param.getParam();
        try {
            bdPostRequest.postRequest("/v1/customDict/delete", JSON.toJSONString(bdDelDictParam),null);
        } catch (Exception e) {
            LogUtils.error("删除单个同义词异常：", e);
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR);
        }
        return ResponseMessage.buildSuccess();
    }

    @Override
    public ResponseMessage<Boolean> listDictPost(ListDictParam param) {
        BdListDictParam bdListDictParam;
        bdListDictParam = param.getParam();
        try {
            bdPostRequest.postRequest("/v1/customDict/list", JSON.toJSONString(bdListDictParam),null);
        } catch (Exception e) {
            LogUtils.error("获取自定义词典列表异常：", e);
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR);
        }
        return ResponseMessage.buildSuccess();
    }

    @Override
    public ResponseMessage<Boolean> clearDictPost(ClearDictParam param) {
        BdClearDictParam bdClearDictParam;
        bdClearDictParam = param.getParam();
        try {
            bdPostRequest.postRequest("/v1/customDict/clear", JSON.toJSONString(bdClearDictParam),null);
        } catch (Exception e) {
            LogUtils.error("清空自定义词典列表异常：", e);
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR);
        }
        return ResponseMessage.buildSuccess();
    }

    @Override
    public ResponseMessage<Boolean> batchAddDictPost(BatchAddDictParam param) {
        BdBatchAddDictParam bdBatchAddDictParam;
        bdBatchAddDictParam = param.getParam();
        try {
            bdPostRequest.postRequest("/v1/customDict/batchAdd", JSON.toJSONString(bdBatchAddDictParam),null);
        } catch (Exception e) {
            LogUtils.error("批量覆盖添加同义词异常：", e);
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR);
        }
        return ResponseMessage.buildSuccess();
    }

    @Override
    public ResponseMessage<Boolean> clearTypeDictPost(ClearTypeDictParam param) {
        BdClearTypeDictParam clearTypeDictParam;
        clearTypeDictParam = param.getParam();
        try {
            bdPostRequest.postRequest("/v1/customDict/clearType", JSON.toJSONString(clearTypeDictParam),null);
        } catch (Exception e) {
            LogUtils.error("按type清空自定义词典异常：", e);
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR);
        }
        return ResponseMessage.buildSuccess();
    }

    @Override
    public BdSkillResponse skill(String jsonStr, String authorization, String accessKey, String timestamp) {
        BdSkillResponse response = new BdSkillResponse();
        BdSkillParam param;
        try {
            param = JSON.parseObject(jsonStr, BdSkillParam.class);
        } catch (Exception e) {
            LogUtils.error("入参json字符串转换异常,jsonStr:{}", jsonStr);
            response.setErrCode(400);
            return response;
        }
        //鉴权
        if (!authenticateRequest(timestamp, authorization, accessKey, jsonStr)) {
            response.setErrCode(1001);
            return response;
        }
        String conversationId = SnowflakeUtil.snowflakeId();
        BeanUtil.copyProperties(param, response);
        response.setErrCode(0);
        response.setTts(getTts(param));

        //保存会话记录
        if(StringUtils.isNotBlank(response.getTts().getContent()) && !LARGEFLAGREPLYTEXT.equals(response.getTts().getContent())){
            //属于我们处理的保存，不属于我们处理的由设备上报
            Conversation conversation = new Conversation();
            conversation.setSn(param.getDevice().getAk());
            conversation.setId(conversationId);
            conversation.setCreateTime(LocalDateTime.now());
            JSONObject data = new JSONObject();
            data.put("1", param.getQuery());
            data.put("2", response.getTts().getContent());
            conversation.setConversation(data.toJSONString());
            conversationMapper.insert(conversation);
        }
        return response;
    }

    @Override
    public BdSkillResponse test(BdSkillParam param) {
        BdSkillResponse response = new BdSkillResponse();
        BeanUtil.copyProperties(param, response);
        response.setErrCode(0);

        response.setTts(getTts(param));
        return response;
    }

    private Tts getTts(BdSkillParam param) {
        StringBuilder sb = new StringBuilder();
        String nluInfos = param.getNluInfos();
        String sn = param.getDevice().getAk();
        //处理回复词
        getReply(nluInfos, sn, sb);
        Tts tts = new Tts();
        tts.setContent(sb.toString());
        tts.setFlag(0);
        return tts;
    }


    private void getReply(String nluInfos, String sn, StringBuilder sb) {
        JSONArray intentArray = JSON.parseArray(nluInfos);
        for (int i = 0; i < intentArray.size(); i++) {
            JSONObject intentJson = intentArray.getJSONObject(i);
            //生成回复词
            String rw = generateReplyWord(intentJson, sn);
            // 从第二个意图开始，如果回复词以“好的”开头，去除“好的”，以及后面的标点符号
            if (i != 0) {
                if (rw.startsWith(OK)) {
                    int index = 2;
                    while (index < rw.length()) {
                        if (!isNotChineseDigitOrAsciiLetter(rw.charAt(index))) {
                            break;
                        }
                        index++;
                    }
                    rw = rw.substring(index);
                }
                rw = LARGEFLAGREPLYTEXT.equals(rw) ? "" : rw;
            }
            sb.append(rw);
        }
    }

    private String generateReplyWord(JSONObject intentJson, String sn) {
        String replyWord = "";
        String domain = intentJson.getString("domain");
        String intent = intentJson.getString("intent");
        //大模型接口调用
        try {
            if(Arrays.asList(largemodelDomain.split(",")).contains(domain)){
                replyWord = LARGEFLAGREPLYTEXT;
                return replyWord;
            }
        }catch (Exception e){
            LogUtils.error("大模型兜底调用异常:{}",e);
        }
        JSONObject slots = intentJson.getJSONObject("slots") !=null ? intentJson.getJSONObject("slots") : new JSONObject();
        StandardEvaluationContext context = new StandardEvaluationContext();
        Map<String, String> replaceMap = new HashMap<>(16);
        // 1.参数检查（多轮）
        // String s = paramCheck(intent, slots);
        // if (StringUtils.isNotBlank(s)) {
        //        return s;
        // }
        // 1.参数检查（位置是否存在）
        String s1 = duelNotFound(domain, intent, slots);
        if (StringUtils.isNotBlank(s1)) {
            return s1;
        }
        // 2.设置参数（每个槽位对应的数量）
        List<String> slotList = new ArrayList<>();
        for (String key : slots.keySet()) {
            slotList.add(key);
            JSONArray slotJsonArray = slots.getJSONArray(key);
            context.setVariable(key, slotJsonArray.size());
        }
        // 3.设置替换词
        setReplaceMap(replaceMap, slots);
        // 4.按照优先级匹配
        LambdaQueryWrapper<ReplyWords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ReplyWords::getDomain, domain)
                .eq(ReplyWords::getHandleFlag, 0)
                .eq(ReplyWords::getIntention, intent)
                .ne(ReplyWords::getConditionExpression, NOT_FOUND)
                .orderByAsc(ReplyWords::getPriority);
        if (CollectionUtils.isNotEmpty(slotList)) {
            queryWrapper.in(ReplyWords::getSlot, slotList);
        }


        List<ReplyWords> replyWords1 = replyWordsMapper.selectList(queryWrapper);
        LogUtils.info("获取回复词模板：{}",JSON.toJSONString(replyWords1));
        if(CollectionUtils.isEmpty(replyWords1)){
            //匹配不到模板
            return replyWord;
        }
        for (ReplyWords replyWords : replyWords1) {
            try {
            String conditionExpression = replyWords.getConditionExpression();
            if (StringUtils.isNotBlank(conditionExpression)) {
                ExpressionParser parser = new SpelExpressionParser();
                Expression expression = parser.parseExpression(conditionExpression);
                Boolean result = expression.getValue(context, Boolean.class);
                if (result) {
                    replyWord = replyWords.getReplyWord();
                    Matcher matcher = PATTERN.matcher(replyWord);
                    while (matcher.find()) {
                        String fieldName = matcher.group(1);
                        replyWord = replyWord.replace(matcher.group(0), replaceMap.get(fieldName) == null ?
                                getDeviceInfo(sn, fieldName) : replaceMap.get(fieldName));
                    }
                    break;
                }
            } else {
                replyWord = replyWords.getReplyWord();
                Matcher matcher = PATTERN.matcher(replyWord);
                while (matcher.find()) {
                    String fieldName = matcher.group(1);
                    replyWord = replyWord.replace(matcher.group(0), replaceMap.get(fieldName) == null ?
                            getDeviceInfo(sn, fieldName) : replaceMap.get(fieldName));
                }
                break;
            }

            }catch (Exception e){
                log.info("回复词解析失败：{}",JSON.toJSON(replyWords));
                return replyWord;
            }
        }
        LogUtils.info("获取回复词：{}",replyWord);
        return replyWord;
    }

    public static boolean isNotChineseDigitOrAsciiLetter(char ch) {
        if (ch >= '\u4e00' && ch <= '\u9fa5') {
            return false;
        }
        if (Character.isDigit(ch)) {
            return false;
        }
        if (ch >= 'A' && ch <= 'Z') {
            return false;
        }else if(ch >= 'a' && ch <= 'z'){
            return false;
        }else {
            return true;
        }
    }

    private String duelNotFound(String domain, String intent, JSONObject slots) {
        if(slots ==null){
            return null;
        }
        String location = duelNotFound(domain, intent, slots, LOCATION_SLOT);
        if (StringUtils.isNotBlank(location)) {
            return location;
        }
        return duelNotFound(domain, intent, slots, FURNITURE_SLOT);
    }

    private String duelNotFound(String domain, String intent, JSONObject slots, String slot) {
        JSONArray slotsJSONArray = slots.getJSONArray(slot);
        if (slotsJSONArray != null) {
            for (int i = 0; i < slotsJSONArray.size(); i++) {
                JSONObject slotsJSONObject = slotsJSONArray.getJSONObject(i);
                String value = slotsJSONObject.getString("value");
                String text = slotsJSONObject.getString("text");
                if (StringUtils.isNotBlank(value)) {
                    //不存在的位置
                    if (value.equals(text)) {
                        LambdaQueryWrapper<ReplyWords> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(ReplyWords::getDomain, domain)
                                .eq(ReplyWords::getIntention, intent)
                                .eq(ReplyWords::getSlot, slot)
                                .eq(ReplyWords::getConditionExpression, NOT_FOUND);
                        List<ReplyWords> list = replyWordsMapper.selectList(queryWrapper);
                        if (CollectionUtils.isNotEmpty(list)) {
                            return list.get(0).getReplyWord();
                        }
                    }
                }
            }
        }
        return null;
    }
    private boolean checkHasTime(JSONObject slots) {
        for (String key : slots.keySet()) {
            if (TIME_SLOT.equals(key)) {
                return true;
            }
        }
        return false;
    }

    private boolean checkHasTimeBegin(JSONObject slots) {
        JSONObject jsonObject = slots.getJSONObject(TIME_SLOT);
        return jsonObject.getJSONObject(BEGIN) != null;
    }

    private boolean checkHasTimeEnd(JSONObject slots) {
        JSONObject jsonObject = slots.getJSONObject(TIME_SLOT);
        return jsonObject.getJSONObject(END) != null;
    }

    /**
        获取设备属性
     */
    private String getDeviceInfo(String sn, String fieldName) {
        ResponseMessage<DeviceShadowEntity> response = deviceShadowRemote.findBySN(sn);
        LogUtils.info("设备属性查询结果：{}：sn:{}", JSON.toJSONString(response),sn);
        // 获取电池电量
        if (BATTERY.equals(fieldName)) {
            if (response.isSuccess()) {
                DeviceShadowEntity deviceShadowEntity = response.getResult();
                if (deviceShadowEntity != null) {
                    JSONObject properties = deviceShadowEntity.getProperties();
                    if (properties != null) {
                        String battery = properties.getString(QUANTITY_KEY);
                        return StringUtils.isNotBlank(battery) ? battery : "";
                    }
                }
            }
        } else if (CONSUMABLE.equals(fieldName)) {
            // 获取耗材信息
            StringBuilder sb = new StringBuilder();
            if (response.isSuccess()) {
                DeviceShadowEntity deviceShadowEntity = response.getResult();
                if (deviceShadowEntity != null) {
                    JSONObject properties = deviceShadowEntity.getProperties();
                    if (properties != null) {
                        Integer use1 = properties.getInteger(RESERVED_MAIN_BRUSH_KEY);
                        if (use1 != null) {
                            sb.append("机器人主刷已使用百分之").append(use1*100/360).append(RESERVED_SIDE_BRUSH_KEY_STRING).append(Math.max(360 - use1, 0)).append("小时。");
                        }
                        Integer use2 = properties.getInteger(RESERVED_SIDE_BRUSH_KEY);
                        if (use2 != null) {
                            sb.append("机器人边刷已使用百分之").append(use2*100/180).append(RESERVED_SIDE_BRUSH_KEY_STRING).append(Math.max(180 - use2, 0)).append("小时。");
                        }
                        Integer use3 = properties.getInteger(RESERVED_HYPA_KEY);
                        if (use3 != null) {
                            sb.append("机器人尘盒滤网已使用百分之").append(use3*100/180).append(RESERVED_SIDE_BRUSH_KEY_STRING).append(Math.max(180 - use3, 0)).append("小时。");
                        }
                        Integer use4 = properties.getInteger(RESERVED_MOP_LIFE_KEY);
                        if (use4 != null) {
                            sb.append("机器人滚筒拖布已使用百分之").append(use4*100/180).append(RESERVED_SIDE_BRUSH_KEY_STRING).append(Math.max(180 - use4, 0)).append("小时。");
                        }
                    }
                }
                return sb.toString();
            }
        }else if (WIND.equals(fieldName)) {
             // 获取吸力
            return getWindString(response);
        }else if (WATER.equals(fieldName)) {
            // 获取水量
            return getWaterString(response);
        }else if (VOLUME.equals(fieldName)) {
            // 获取音量
            return getVolumeString(response);
        }
        return "";
    }


    private String getVolumeString(ResponseMessage<DeviceShadowEntity> response) {
        StringBuilder sb = new StringBuilder();
        if (!response.isSuccess()) {
            sb.append(VOLUMESTRING);
            return sb.toString();
        }
        DeviceShadowEntity deviceShadowEntity = response.getResult();
        if (deviceShadowEntity == null) {
            sb.append(VOLUMESTRING);
            return sb.toString();
        }
        JSONObject properties = deviceShadowEntity.getProperties();
        if (properties == null) {
            sb.append(VOLUMESTRING);
            return sb.toString();
        }
        Integer water = properties.getInteger(VOLUME);
        if (water == null) {
            sb.append(VOLUMESTRING);
            return sb.toString();
        }
        sb.append("当前音量为"+water);
        return sb.toString();
    }

    private String getWaterString(ResponseMessage<DeviceShadowEntity> response) {
        StringBuilder sb = new StringBuilder();
        if (!response.isSuccess()) {
            sb.append(WATERSTRING);
            return sb.toString();
        }
        DeviceShadowEntity deviceShadowEntity = response.getResult();
        if (deviceShadowEntity == null) {
            sb.append(WATERSTRING);
            return sb.toString();
        }
        JSONObject properties = deviceShadowEntity.getProperties();
        if (properties == null) {
            sb.append(WATERSTRING);
            return sb.toString();
        }
        Integer water = properties.getInteger(WATER);
        if (water == null) {
            sb.append(WATERSTRING);
            return sb.toString();
        }
        switch (water){
            case 0:
                sb.append(WATERSTRING);
                break;
            case 1:
                sb.append("当前为中档水量。");
                break;
            case 2:
                sb.append("当前为很湿水量。");
                break;
            default:
                sb.append(WATERSTRING);

        }
        return sb.toString();
    }

    private String getWindString(ResponseMessage<DeviceShadowEntity> response) {
        StringBuilder sb = new StringBuilder();
        if (!response.isSuccess()) {
            sb.append(WINDSTRING);
            return sb.toString();
        }
        DeviceShadowEntity deviceShadowEntity = response.getResult();
        if (deviceShadowEntity == null) {
            sb.append(WINDSTRING);
            return sb.toString();
        }
        JSONObject properties = deviceShadowEntity.getProperties();
        if (properties == null) {
            sb.append(WINDSTRING);
            return sb.toString();
        }
        Integer wind = properties.getInteger(WIND);
        if (wind == null) {
            sb.append(WINDSTRING);
            return sb.toString();
        }
        switch (wind){
            case 0:
                sb.append(WINDSTRING);
                break;
            case 1:
                sb.append("当前为安静吸力。");
                break;
            case 2:
                sb.append("当前为中档吸力。");
                break;
            case 3:
                sb.append("当前为强力吸力。");
                break;
            default:
                sb.append(WINDSTRING);
        }
        return sb.toString();
    }

    private void setReplaceMap(Map<String, String> replaceMap, JSONObject slots) {
        for (String key : slots.keySet()) {
            JSONArray slotJsonArray = slots.getJSONArray(key);
            String slotType = null;
            if (CollectionUtils.isNotEmpty(slotJsonArray) && slotJsonArray.size() > 0) {
                slotType = slotJsonArray.getJSONObject(0).getString("slot_type");
            }
            if (STRING_TYPE.equals(slotType)) {
                StringBuilder slotValue = new StringBuilder();
                for (int i = 0; i < slotJsonArray.size(); i++) {
                    JSONObject slotJson = slotJsonArray.getJSONObject(i);
                    if (i != 0) {
                        slotValue.append("、");
                    }
                    slotValue.append(slotJson.getString("text"));
                }
                replaceMap.put(key, slotValue.toString());
            } else if (TIME_TYPE.equals(slotType)) {
                // 取第一个时间
                JSONObject timeslot = slotJsonArray.getJSONObject(0);
                JSONObject value = timeslot.getJSONObject("value");
                if (value.containsKey(BEGIN)) {
                    JSONObject begin = value.getJSONObject(BEGIN);
                    replaceMap.put("time.begin.date", begin.getString("date"));
                    replaceMap.put("time.begin.time", begin.getString("time"));
                } else if (value.containsKey(END)) {
                    JSONObject end = value.getJSONObject(END);
                    replaceMap.put("time.end.date", end.getString("date"));
                    replaceMap.put("time.end.time", end.getString("time"));
                }
            }
        }
    }

    private static final String ACCESS_KEY = "935cf088427cbe99";

    private static final String SECRET_KEY = "e17a11a870be6775342d822828f8c8d3";


    /**
     * 判断请求合法性
     *
     * @param timestamp   系统时间戳-毫秒
     * @param signature   签名
     * @param accessKey   ak
     * @param requestBody 请求内容.HTTP请求和HTTPS请求里的最原始的Body String 字符串
     * @return 是否合法
     */
    public boolean authenticateRequest(String timestamp, String signature,
                                              String accessKey, String requestBody) {
        // 时间戳判断-时间有效性判断(建议上下5分钟之内认为有效)
        long now = System.currentTimeMillis();
        long currentTime = Long.parseLong(timestamp);
        // 绝对值小于5分钟之内认为有效
        long timeDiff = Math.abs(now - currentTime);
        if (timeDiff > 5 * 50 * 1000) {
            // 时间不在5分钟之内认为无效
            return false;
        }
        // 防重判断. 这里暂时不实现
        // 可以利用 requestBody 里的 logId 作为防重key

        // 签名校验
        // 根据具体算法计算请求签名
        String calculatedSignature = calculateSignature(timestamp, accessKey, requestBody);
        LogUtils.info("authenticateRequest-calculatedSignature:{}",calculatedSignature);
        // 如果请求签名不匹配，说明请求未经授权，拒绝鉴权
        return signature.equals(calculatedSignature);
        // 鉴权通过
    }

    /**
     * 计算签名
     *
     * @param timestamp   时间戳
     * @param accessKey   ak
     * @param requestBody 请求内容.是HTTP请求和HTTPS请求里的最原始的Body String 字符串
     * @return 签名
     */
    private String calculateSignature(String timestamp, String accessKey, String requestBody) {
        try {
            // 使用HMAC-SHA256算法进行签名计算
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            // 将请求内容body加入签名计算
            String toBeSigned = accessKey + timestamp + requestBody;
            byte[] signatureBytes = mac.doFinal(toBeSigned.getBytes(StandardCharsets.UTF_8));
            // 将签名结果进行Base64编码
            return Base64.getEncoder().encodeToString(signatureBytes);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            return null;
        }
    }


    @Override
    public ResponseMessage<List<String>> getReply(LargeModelParam param) {
        List<String> reply = new ArrayList<>();
        //鉴权
        LogUtils.info("largemodel-getReply-param:{}",JSON.toJSONString(param));
        String sn = param.getAk();
//        String nluInfos = param.getNluInfos();
        if(StringUtils.isBlank(sn)){
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "sn不能为空");
        }

        if(CollUtil.isEmpty(param.getNluInfos())){
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "nluInfos不能为空");
        }
//        JSONArray intentArray = JSON.parseArray(nluInfos);
        for (int i = 0; i <  param.getNluInfos().size(); i++) {
            JSONObject intentJson = param.getNluInfos().get(i);
            //生成回复词
            String rw = generateReplyWord(intentJson, sn);
            reply.add(rw);
        }
        return ResponseMessage.buildSuccess(reply);
    }

    @Override
    public ResponseMessage<String> addConversation(ConversationParam param) {
        LogUtils.info("addConversation-param:{}",param);
        Conversation conversation = new Conversation();
        conversation.setSn(param.getSn());
        conversation.setId(SnowflakeUtil.snowflakeId());
        conversation.setCreateTime(LocalDateTime.now());
        JSONObject data = new JSONObject();
        data.put("1", param.getQuery());
        data.put("2", param.getReply());
        conversation.setConversation(data.toJSONString());
        conversationMapper.insert(conversation);
        return ResponseMessage.buildSuccess(param.getSn());
    }

    @Override
    public ResponseMessage<BaiDuVo> baiduRequest(BaiDuParam param) {
        BaiDuVo baiDuVo = new BaiDuVo();
        try {
            LogUtils.info("百度接口透传-param:{}",JSON.toJSONString(param));
            String result = bdPostRequest.postRequest(param.getBaiduUrl(), param.getBaiduparam(), null);
            LogUtils.info("百度接口透传-result:{}",JSON.toJSONString(result));
            baiDuVo.setBaiduResult(result);
        } catch (Exception e) {
            LogUtils.error("新增单个同义词异常：", e);
            baiDuVo.setBaiduResultFlag(1);
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR);
        }
        return ResponseMessage.buildSuccess(baiDuVo);
    }

}
