package com.irobotics.aiot.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.device.entity.Conversation;
import com.irobotics.aiot.device.mapper.ConversationMapper;
import com.irobotics.aiot.device.service.IConversationService;
import com.irobotics.aiot.device.vo.ConversationPageReq;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ConversationServiceImpl implements IConversationService {

    @Autowired
    private ConversationMapper conversationMapper;

    @Override
    public void deleteBySn(String sn) {
        LambdaQueryWrapper<Conversation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Conversation::getSn, sn);
        conversationMapper.delete(queryWrapper);
    }

    @Override
    public ResponseMessage<Page<Conversation>> page(ConversationPageReq pageReq) {
        if (ObjectUtils.isEmpty(pageReq) || StringUtils.isBlank(pageReq.getSn())) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL);
        }
        LambdaQueryWrapper<Conversation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Conversation::getSn, pageReq.getSn());
        Page<Conversation> conversationPage = conversationMapper.selectPage(new Page<>(pageReq.getPage(), pageReq.getPageSize()), queryWrapper);
        return ResponseMessage.buildSuccess(conversationPage);
    }
}
