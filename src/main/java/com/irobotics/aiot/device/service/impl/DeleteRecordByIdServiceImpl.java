package com.irobotics.aiot.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.Constants;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.entity.DeviceInviteShareEntity;
import com.irobotics.aiot.device.mapper.DeviceInfoMapper;
import com.irobotics.aiot.device.mapper.DeviceInviteShareMapper;
import com.irobotics.aiot.device.remote.LogServiceRemote;
import com.irobotics.aiot.device.remote.ProductServiceRemote;
import com.irobotics.aiot.device.remote.SmartHomeServiceRemote;
import com.irobotics.aiot.device.service.DeleteRecordByIdService;
import com.irobotics.aiot.device.service.IDeviceBindUserService;
import com.irobotics.aiot.device.service.IProductSnDetailActiveZoneService;
import com.irobotics.aiot.device.vo.DeleteResp;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @since 2022/2/12
 */

@Service
public class DeleteRecordByIdServiceImpl implements DeleteRecordByIdService {

    @Resource
    private DeviceInfoMapper deviceInfoMapper;

    @Resource
    private DeviceInviteShareMapper deviceInviteShareMapper;

    @Resource
    private SmartHomeServiceRemote smartHomeServiceRemote;

    @Resource
    private LogServiceRemote logServiceRemote;

    @Autowired
    private DeviceInfoCache deviceInfoCache;

    @Autowired
    private IDeviceBindUserService deviceBindUserService;

    @Autowired
    private IProductSnDetailActiveZoneService productSnDetailActiveZoneService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRecordById(String taskId, String tenantId, String deviceId, List<DeleteResp> resultList) {
        //获取设备sn
        QueryWrapper<DeviceInfoEntity> deviceInfoQueryWrapper = new QueryWrapper<>();
        deviceInfoQueryWrapper.lambda().eq(DeviceInfoEntity::getId, deviceId).eq(DeviceInfoEntity::getTenantId, tenantId);
        DeviceInfoEntity deviceInfo = deviceInfoMapper.selectOne(deviceInfoQueryWrapper);
        String sn = null;
        if (deviceInfo != null) {
            sn = deviceInfo.getSn();
        }

        DeleteResp resp = new DeleteResp(deviceId, true, sn);

        //删除设备分享记录
        try {
            QueryWrapper<DeviceInviteShareEntity> deviceInviteShareEntityQueryWrapper = new QueryWrapper<>();
            deviceInviteShareEntityQueryWrapper.lambda().eq(DeviceInviteShareEntity::getTargetId, deviceId).eq(DeviceInviteShareEntity::getTenantId, tenantId);
            deviceInviteShareMapper.delete(deviceInviteShareEntityQueryWrapper);
        } catch (Exception e) {
            LogUtils.error(e, "taskId:{},删除设备分享记录:{}", taskId, e.getMessage());
            resp.getFailMessages().add("删除设备分享记录失败");
            resp.setIsSuccess(false);
        }
        // 删除非智能家居下的绑定关系
        List<String> devices = new ArrayList<>();
        devices.add(deviceId);
        deviceBindUserService.deleteBindByDeviceIds(devices);
        //智能家居下删除设备房间绑定关系
        ResponseMessage<Void> deleteRoomBindResult = smartHomeServiceRemote.deleteRoomBindByDeviceId(deviceId);
        if (!deleteRoomBindResult.isSuccess()) {
            LogUtils.error("taskId:{},删除设备房间绑定关系:{}", taskId, deleteRoomBindResult.getMsg());
            resp.setIsSuccess(false);
            resp.getFailMessages().add("删除设备房间绑定关系失败");
        }

        //删除设备日志
        if (sn != null) {
            ResponseMessage<Void> deleteAppLogResult = logServiceRemote.deleteAppLogBySn(sn);
            if (!deleteAppLogResult.isSuccess()) {
                LogUtils.error("taskId:{},删除app日志:{}", taskId, deleteAppLogResult.getMsg());
                resp.setIsSuccess(false);
                resp.getFailMessages().add("删除app日志失败");
            }
            ResponseMessage<Void> deleteDeviceLogResult = logServiceRemote.deleteDeviceLogBySn(sn);
            if (!deleteDeviceLogResult.isSuccess()) {
                LogUtils.error("taskId:{},删除设备日志:{}", taskId, deleteDeviceLogResult.getMsg());
                resp.setIsSuccess(false);
                resp.getFailMessages().add("删除设备日志失败");
            }
            ResponseMessage<Boolean> setSnUnActiveResult = productSnDetailActiveZoneService.delBySn(sn);
            if (!setSnUnActiveResult.isSuccess()) {
                LogUtils.error("taskId:{},将sn修改为未激活:{}", taskId, setSnUnActiveResult.getMsg());
                resp.setIsSuccess(false);
                resp.getFailMessages().add("设置sn未激活失败");
            }
        }

        //删除设备信息
        try {
            QueryWrapper<DeviceInfoEntity> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.lambda().eq(DeviceInfoEntity::getId, deviceId).eq(DeviceInfoEntity::getTenantId, tenantId);
            deviceInfoMapper.delete(deleteWrapper);
            //删除缓存
            if (Objects.nonNull(deviceInfo)) {
                deviceInfoCache.clear(deviceInfo.getTenantId(), deviceInfo);
            }
        } catch (Exception e) {
            LogUtils.error(e, "taskId:{},删除设备信息:{}", taskId, e.getMessage());
            resp.setIsSuccess(false);
            resp.getFailMessages().add("删除设备信息失败");
        }

        resultList.add(resp);

        if (!resp.getIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }
}
