package com.irobotics.aiot.device.service.impl;

import com.irobotics.aiot.bravo.basic.base.CountryCity;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.basic.exception.FastRuntimeException;
import com.irobotics.aiot.bravo.basic.exception.ParameterValidateException;
import com.irobotics.aiot.bravo.commons.util.JsonUtils;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.mapper.DeviceInfoMapper;
import com.irobotics.aiot.device.remote.InfrastructureThirdRemote;
import com.irobotics.aiot.device.service.DeviceAuthService;
import com.irobotics.aiot.device.service.IDeviceInfoService;
import com.irobotics.aiot.device.service.IDeviceVersionLogService;
import com.irobotics.aiot.device.vo.LoginEntityVO;
import com.irobotics.aiot.device.vo.LoginKeyReq;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/9/24 18:19
 */
@Service
public class DeviceAuthServiceImpl implements DeviceAuthService {

    /**
     * 设备信息
     */
    @Autowired
    private IDeviceInfoService deviceInfoService;

    /**
     * 服务类型
     */
    @Value("${application.type}")
    private String applicationType;

    /**
     * 设备缓存
     */
    @Autowired
    private DeviceInfoCache deviceInfoCache;

    /**
     * 设备mapper
     */
    @Autowired
    private DeviceInfoMapper deviceInfoMapper;

    @Autowired
    private IDeviceVersionLogService deviceVersionLogService;

    /**
     * ip分析
     */
    @Autowired
    private InfrastructureThirdRemote geoIPRemote;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public LoginEntityVO login(LoginKeyReq loginReq) {
        String productModeCode = loginReq.getProductModeCode();
        String sn = loginReq.getSn();
        String mac = loginReq.getMac();
        String tenantId = loginReq.getTenantId();
        String key = loginReq.getKeyt();

        // 检测输入参数
        if (StringUtils.isBlank(key)) {
            throw new ParameterValidateException("参数缺失key");
        }
        if (StringUtils.isEmpty(productModeCode)) {
            throw new ParameterValidateException("参数缺失productModeCode");
        }
        if (StringUtils.isEmpty(sn)) {
            throw new ParameterValidateException("参数缺失sn");
        }
        if (StringUtils.isEmpty(mac)) {
            throw new ParameterValidateException("参数缺失mac");
        }
        if (StringUtils.isEmpty(tenantId)) {
            throw new ParameterValidateException("参数缺失tenantId");
        }
        if (CollectionUtils.isEmpty(loginReq.getPackageVersions())) {
            throw new ParameterValidateException("版本信息packageVersions缺失");
        }
        LoginEntityVO loginEntityVO = login(tenantId, productModeCode, sn, mac, key, JsonUtils.toJSON(loginReq.getPackageVersions()));
        loginEntityVO.setMaxUpgradeTime(30);
        try {
            deviceVersionLogService.save(sn, loginReq.getPackageVersions());
        }catch (Exception e){
            LogUtils.error("保存设备版本流水出错：", e);
        }
        return loginEntityVO;
    }

    private LoginEntityVO login(String tenantId, String productModeCode, String sn, String mac, String key, String curVersions) {
        String ip = SystemContextUtils.getContextValue(ContextKey.CLIENT_IP);
        CountryCity countryCity = getCountryCity(ip);
        String countryLoc = countryCity != null ? JsonUtils.toJSON(countryCity) : null;

        DeviceInfoEntity info;
        String wrongMac = "00:00:00:00:00:00";
        if (mac.equals(wrongMac)) { //设备BUG，服务端补锅
            info = deviceInfoCache.getCacheBySn(tenantId, sn);
            if (info == null) { //还未注册的错误设备，使用sn作为mac继续注册
                mac = sn;
            } else if (mac.equals(info.getMac())) { //已经注册的错误设备，还没被同样的错误mac顶掉,将mac换成sn
                mac = sn;
                info.setMac(mac);
            } else if (info.getMac().startsWith(wrongMac)) { //已经注册的错误设备，已经被同样的错误mac顶掉,将mac换成sn
                mac = sn;
                info.setMac(mac);
            } else {
                throw new FastRuntimeException(ResponseCode.ILLEGAL_OPERATE, "sn已被另一台设备激活");
            }
        } else { //正常设备
            info = deviceInfoMapper.getByMac(tenantId, mac);
        }
        //该mac已经被激活
        if (info != null) {
            return deviceInfoService.doLogin(tenantId, mac, sn, key, ip, info, countryLoc, productModeCode, curVersions, countryCity);
        }
        //设备未激活的情况
        return deviceInfoService.doRegister(tenantId, sn, mac, key, ip, productModeCode, countryLoc, curVersions, countryCity);
    }

    @Override
    public CountryCity getCountryCity(String ip) {
        ResponseMessage<CountryCity> ipInfo = null;
        try {
            ipInfo = geoIPRemote.getIpInfo(ip);
            return ipInfo.getResult();
        } catch (Exception e) {
            LogUtils.error("ip={} 设备登录地分析失败,{}...", ip, ipInfo == null ? "" : ipInfo.getMsg());
        }
        return null;
    }
}
