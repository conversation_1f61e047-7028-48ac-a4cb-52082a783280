package com.irobotics.aiot.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.commons.util.SnowflakeUtil;
import com.irobotics.aiot.device.common.LogEnum;
import com.irobotics.aiot.device.entity.DeviceBindBaseEntity;
import com.irobotics.aiot.device.mapper.DeviceBindBaseMapper;
import com.irobotics.aiot.device.service.IDeviceBindBaseService;
import com.irobotics.aiot.device.service.LogService;
import com.irobotics.aiot.device.vo.inner.BindBaseListReq;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 第三方用户设备绑定关系信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
@Service
public class DeviceBindBaseServiceImpl extends ServiceImpl<DeviceBindBaseMapper, DeviceBindBaseEntity> implements IDeviceBindBaseService {

    @Autowired
    private LogService logService;

    /**
     *  1、正常流程基站出厂已经烧录好，设备sn和基站sn 是一对一关系不会发生变更
     *  2、公司内部测试会出现 重新更改设备sn，这种情况保存最新基站sn 和 设备sn的绑定关系，
     *    删除 （1）原设备sn 和 基站绑定关系
     *        （2）原基站sn 和 设备的绑定关系
     *
     * @param deviceId
     * @param sn
     * @param baseId
     * @param baseSn
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean bind(String deviceId, String sn,String baseId,String baseSn) {
        LambdaQueryWrapper<DeviceBindBaseEntity> query = new LambdaQueryWrapper<>();
        query.eq(DeviceBindBaseEntity::getSn,sn);
        query.eq(DeviceBindBaseEntity::getDeviceId,deviceId);
        query.eq(DeviceBindBaseEntity::getBaseSn,baseSn);
        query.eq(DeviceBindBaseEntity::getBaseId,baseId);
        query.last(" limit 1");
        DeviceBindBaseEntity entity = this.baseMapper.selectOne(query);
        // 已经存在绑定关系
        if (Objects.nonNull(entity)){
            //原用户重新绑定，直接返回成功
            logService.bindLog(LogEnum.BIND.getCode(), LogEnum.SELF.getCode(),deviceId);
            return false;
        }else {
            // 删除sn 和 基站原本的绑定关系
            LambdaQueryWrapper<DeviceBindBaseEntity> deleteQuery = new LambdaQueryWrapper<>();
            deleteQuery.eq(DeviceBindBaseEntity::getSn,sn)
                    .or().eq(DeviceBindBaseEntity::getBaseSn,baseSn);
            this.baseMapper.delete(deleteQuery);
        }
        // 增加绑定关系
        DeviceBindBaseEntity addEntity = new DeviceBindBaseEntity(SnowflakeUtil.snowflakeId(),deviceId,sn,
                LocalDateTime.now(), baseId,baseSn,LocalDateTime.now());
        this.save(addEntity);
        logService.bindLog(LogEnum.BIND.getCode(), LogEnum.SELF.getCode(),deviceId);
        return true;
    }


    @Override
    public ResponseMessage<List<DeviceBindBaseEntity>> bindListByDeviceSn(BindBaseListReq req) {
        LambdaQueryWrapper<DeviceBindBaseEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (req.getFlag()){
            // // 模糊搜索
            // if (req.getSearchType() == 1){
            //     queryWrapper.like(DeviceBindBaseEntity::getBaseSn,req.getSnList().get(0));
            // }else {
                queryWrapper.in(DeviceBindBaseEntity::getBaseSn,req.getSnList());
            // }
        }else {
            queryWrapper.in(DeviceBindBaseEntity::getSn,req.getSnList());
        }
        List<DeviceBindBaseEntity> list = this.baseMapper.selectList(queryWrapper);
        return ResponseMessage.buildSuccess(list);
    }

    @Override
    public ResponseMessage<List<DeviceBindBaseEntity>> getBaseBind(String baseId, String baseSn, String devId, String devSn) {
        LambdaQueryWrapper<DeviceBindBaseEntity> queryWrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isBlank(baseId) && StringUtils.isBlank(baseSn) && StringUtils.isBlank(devId) &&StringUtils.isBlank(devSn)){
            return ResponseMessage.buildSuccess(new ArrayList<>());
        }
        if(StringUtils.isNotBlank(baseId)){
            queryWrapper.eq(DeviceBindBaseEntity::getBaseId, baseId);
        }
        if(StringUtils.isNotBlank(baseSn)){
            queryWrapper.eq(DeviceBindBaseEntity::getBaseSn, baseSn);
        }
        if(StringUtils.isNotBlank(devId)){
            queryWrapper.eq(DeviceBindBaseEntity::getDeviceId, devId);
        }
        if(StringUtils.isNotBlank(devSn)){
            queryWrapper.eq(DeviceBindBaseEntity::getSn, devSn);
        }
        return ResponseMessage.buildSuccess(this.list(queryWrapper));
    }
}
