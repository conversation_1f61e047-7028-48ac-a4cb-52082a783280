package com.irobotics.aiot.device.service.impl;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.Constants;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.exception.AppRuntimeException;
import com.irobotics.aiot.bravo.basic.exception.NoDataFoundException;
import com.irobotics.aiot.bravo.basic.exception.ParameterValidateException;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.cache.BindKeyCache;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.common.DeviceKey;
import com.irobotics.aiot.device.config.DeviceConfig;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.remote.IContentServiceRemote;
import com.irobotics.aiot.device.remote.IcloudIntentServiceRemote;
import com.irobotics.aiot.device.remote.SmartHomeServiceRemote;
import com.irobotics.aiot.device.service.IDeviceBindService;
import com.irobotics.aiot.device.service.IDeviceBindUserService;
import com.irobotics.aiot.device.service.IDeviceInviteShareService;
import com.irobotics.aiot.device.utils.RandomUtil;
import com.irobotics.aiot.device.vo.*;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.irobotics.aiot.bravo.basic.constant.ContextKey.ENABLE_SMART_HOME;
import static com.irobotics.aiot.bravo.basic.constant.ContextKey.TENANT_ID;
import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.NOT_FOUND_DATA;
import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.PARAM_VALIDATE_FAIL;

/**
 * <p>
 * 设备绑定信息服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
@Service
public class DeviceBindServiceImpl implements IDeviceBindService {


    /**
     * bindKey的长度
     */
    private static final Integer BIND_KEY_LENGTH = 6;

    /**
     * 设备缓存
     */
    @Autowired
    private DeviceInfoCache deviceInfoCache;

    /**
     * 智能家居
     */
    @Autowired
    private SmartHomeServiceRemote smartHomeServiceRemote;

    /**
     * 设备共享
     */
    @Autowired
    private IDeviceInviteShareService inviteShareService;

    /**
     * bindKey缓存
     */
    @Autowired
    private BindKeyCache bindKeyCache;

    @Autowired
    private DeviceConfig deviceConfig;

    @Autowired
    private IDeviceBindUserService deviceBindUserService;

    @Resource
    private IContentServiceRemote contentServiceRemote;

    @Resource
    private IcloudIntentServiceRemote cloudIntentServiceRemote;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private final String VOICE_TENANT_KEY = "voiceTenantKey";


    /**
     * 设备发起绑定app请求
     *
     * @param deviceId 设备id
     * @param userId   用户id
     * @param key      绑定key
     */
    @Override
    public void bindApp(String deviceId, String userId, String key) {
        if (StringUtils.isEmpty(key) || userId == null || StringUtils.isBlank(userId)) {
            throw new ParameterValidateException("key|userId");
        }
        BindKey bindKey = new BindKey();
        bindKey.setDeviceId(deviceId);
        bindKey.setBindKey(key);
        String tenantId = SystemContextUtils.getContextValue(TENANT_ID);
        bindKeyCache.saveBindKey(bindKey, userId, tenantId, DeviceKey.USER);
    }

    /**
     * app确认设备发起的绑定app请求
     *
     * @param userId 用户id
     * @param key    绑定key
     * @return
     */
    @Override
    public BindResp bindAppConfirm(String userId, String key, String familyId, String roomId) {
        String tenantId = SystemContextUtils.getContextValue(TENANT_ID);
        BindKey bindKey = bindKeyCache.getBindKey(userId, tenantId, DeviceKey.USER);
        LogUtils.info("bindAppConfirm cache key: {}", bindKey);
        if (bindKey == null) {
            // 通过衫川平台UID获取绑定key为空时，开始取开放平台UID获取绑定key
            // 兼容APP-SDK-OPENAPI的20240601前已发布版本的接口调用
            String openUserId = SystemContextUtils.getContextValue(ContextKey.OPEN_USER_ID);
            if (StringUtils.isNotBlank(openUserId)) {
                bindKey = bindKeyCache.getBindKey(openUserId, tenantId, DeviceKey.USER);
            }
            if (bindKey == null) {
                throw new NoDataFoundException("缓存中无bindKey");
            }
        }
        if (!Objects.equals(bindKey.getBindKey(), key)) {
            throw new ParameterValidateException("bindKey验证不通过,缓存中的设备的bindKey和app的bindKey不一致");
        }
        String deviceId = bindKey.getDeviceId();
        DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheById(tenantId, deviceId);
        if (Objects.isNull(deviceInfo)) {
            throw new NoDataFoundException("设备不存在，deviceId=" + deviceId + ", tenantId=" + tenantId);
        }

        Boolean isNew = false;
        // 开启智能家居
        if (Constants.openSmartHome.equals(SystemContextUtils.getContextValue(ENABLE_SMART_HOME))) {
            if (StringUtils.isBlank(familyId)) {
                throw new ParameterValidateException("familyId");
            }
            // 远程调用保存用户房间设备之间的绑定关系,覆盖保存,返回false代表的是设备拥有者重新配网
            ResponseMessage<Boolean> responseMessage =
                    smartHomeServiceRemote.bindDevice(new RoomBindDeviceReq(familyId, roomId, deviceId,
                            deviceInfo.getNickname(), deviceInfo.getSn(), deviceInfo.getProductId()));
            if (!responseMessage.isSuccess()) {
                String msg = "机器发起绑定APP请求后，APP最终确认，发生异常.userId=%s,deviceId=%s,tenantId=%s,key=%s,familyId=%s, " +
                        "roomId=%s,responseMessage=%s";
                String format = String.format(msg, userId, deviceId, tenantId, key, familyId, roomId,
                        responseMessage.getMsg());
                LogUtils.error(format);
                throw new AppRuntimeException("绑定房间异常");
            }
            if (responseMessage.getResult()) {
                // 重新配网的用户如果不是该设备的拥有者，则删除对应设备的分享记录
                inviteShareService.delByDeviceId(deviceId);
            }
            isNew = responseMessage.getResult();
        } else {
            // 非智能家居绑定流程
            isNew = deviceBindUserService.bind(deviceId, deviceInfo.getNickname(), deviceInfo.getSn(),
                    deviceInfo.getProductId());
        }
        //向语控第三方服务器发送新增绑定信息
        if (checkIfVoiceControl()) {
            ResponseMessage<Boolean> syncResult = cloudIntentServiceRemote.syncBindInfo(new SyncBindReq(tenantId, deviceId, userId));
            if (!syncResult.isSuccess() || !syncResult.getResult()) {
                LogUtils.error("向第三方平台同步新增绑定关系失败，tenantId:{},userId:{},deviceId:{}", tenantId, deviceId, userId);
            }
        }
        // 清除设备已签署的协议记录
        contentServiceRemote.delByDeviceId(deviceId);
        BindResp respEntity = new BindResp();
        respEntity.setNew(isNew);
        respEntity.setSn(deviceInfo.getSn());
        respEntity.setDeviceId(deviceId);
        if (!deviceConfig.getRemove()) {
            Long ttl = Objects.isNull(deviceConfig.getTtl()) || deviceConfig.getTtl() <= 0 ? 120 :
                    deviceConfig.getTtl();
            // 确认绑定成功后，改变bindKey的过期时间为120秒
            BindKey bk = new BindKey();
            bk.setDeviceId(deviceId);
            bk.setBindKey(key);
            bindKeyCache.saveBindKeyWithExpire(bk, userId, tenantId, DeviceKey.USER, ttl, TimeUnit.SECONDS);
        } else {
            bindKeyCache.removeBindKey(userId, tenantId, DeviceKey.USER);
        }
        return respEntity;
    }

    @Override
    public String getBindKey(String deviceId, String tenantId) {
        String randomBindKey = RandomUtil.getRandomBindKey(BIND_KEY_LENGTH);
        BindKey bindKey = new BindKey();
        bindKey.setBindKey(randomBindKey);
        bindKey.setDeviceId(deviceId);
        bindKeyCache.saveBindKey(bindKey, deviceId, tenantId, DeviceKey.DEVICE);
        return randomBindKey;
    }

    @Override
    public ResponseMessage<BindResp> orCodeBind(OrCodeBindVo vo) {
        String bindKey = vo.getBindKey();
        String familyId = vo.getFamilyId();
        String roomId = vo.getRoomId();
        String userId = SystemContextUtils.getId();
        String tenantId = SystemContextUtils.getTenantId();
        String deviceId = vo.getDeviceId();
        if (StringUtils.isBlank(bindKey)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "bindKey");
        }
        if (Constants.openSmartHome.equals(SystemContextUtils.getContextValue(ENABLE_SMART_HOME)) && StringUtils.isBlank(familyId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "familyId");
        }
        if (StringUtils.isBlank(deviceId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceId");
        }
        DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheById(tenantId, deviceId);
        if (Objects.isNull(deviceInfo)) {
            LogUtils.error("二维码绑定时，设备不存在，deviceId={}, bindKey={}, tenantId={}, userId={}", deviceId, bindKey,
                    tenantId, userId);
            return ResponseMessage.buildFail(NOT_FOUND_DATA, "设备不存在");
        }
        BindKey cacheBindKey = bindKeyCache.getBindKey(deviceId, tenantId, DeviceKey.DEVICE);
        LogUtils.info("cacheBindKey缓存信息:{}", cacheBindKey);
        if (Objects.isNull(cacheBindKey)) {
            LogUtils.error("二维码绑定时，bindKey不存在，deviceId={}, bindKey={}, tenantId={}, userId={}", deviceId, bindKey,
                    tenantId, userId);
            return ResponseMessage.buildFail(NOT_FOUND_DATA, "bindKey不存在");
        }
        if (!Objects.equals(bindKey, cacheBindKey.getBindKey())) {
            LogUtils.error("二维码绑定时，bindKey校验不通过，deviceId={}, bindKey={}, tenantId={}, userId={}", deviceId, bindKey,
                    tenantId, userId);
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "bindKey验证不通过，服务器颁发的的bindKey与绑定时的bindKey不一致");
        }

        /*ResponseMessage<Boolean> responseMessage = smartHomeServiceRemote.bindShareDevice(new
        RoomBindShareDeviceReq(userId, familyId, deviceId, deviceInfo.getNickname(), deviceInfo.getSn(), owner,
        deviceInfo.getProductId()));
        if (!responseMessage.isSuccess()) {
            LogUtils.error("扫码绑定时，绑定设备异常，tenantId={}, familyId={}, orCodeUser={}, deviceId={}, owner={}, nickname={},
             msg={}",
                    tenantId, familyId, userId, deviceId, owner, deviceInfo.getNickname(), responseMessage.getMsg());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, "设备分享异常");
        }*/
        // 开启智能家居
        Boolean isNew;
        if (Constants.openSmartHome.equals(SystemContextUtils.getContextValue(ENABLE_SMART_HOME))) {
            // 远程调用保存用户房间设备之间的绑定关系,覆盖保存,返回false代表的是设备拥有者重新配网
            ResponseMessage<Boolean> responseMessage =
                    smartHomeServiceRemote.bindDevice(new RoomBindDeviceReq(familyId, roomId, deviceId,
                            deviceInfo.getNickname(), deviceInfo.getSn(), deviceInfo.getProductId()));
            if (!responseMessage.isSuccess()) {
                String msg = "扫码绑定时，绑定设备异常，userId=%s,deviceId=%s,tenantId=%s,bindKey=%s,familyId=%s, roomId=%s," +
                        "responseMessage=%s";
                String format = String.format(msg, userId, deviceId, tenantId, bindKey, familyId, roomId,
                        responseMessage.getMsg());
                LogUtils.error(format);
                throw new AppRuntimeException("绑定房间异常");
            }
            if (responseMessage.getResult()) {
                // 重新配网的用户如果不是该设备的拥有者，则删除对应设备的分享记录
                inviteShareService.delByDeviceId(deviceId);
            }
            isNew = responseMessage.getResult();
        } else {
            // 非智能家居绑定流程
            isNew = deviceBindUserService.bind(deviceId, deviceInfo.getNickname(), deviceInfo.getSn(),
                    deviceInfo.getProductId());
        }
        //向语控第三方服务器发送新增绑定信息
        if (checkIfVoiceControl()) {
            ResponseMessage<Boolean> syncResult = cloudIntentServiceRemote.syncBindInfo(new SyncBindReq(tenantId, deviceId, userId));
            if (!syncResult.isSuccess() || !syncResult.getResult()) {
                LogUtils.error("向第三方平台同步新增绑定关系失败，tenantId:{},userId:{},deviceId:{}", tenantId, deviceId, userId);
            }
        }


        BindResp respEntity = new BindResp();
        respEntity.setNew(isNew);
        respEntity.setSn(deviceInfo.getSn());
        respEntity.setDeviceId(deviceId);

        // 绑定成功后，移除缓存中的key
        bindKeyCache.removeBindKey(deviceId, tenantId, DeviceKey.DEVICE);
        return ResponseMessage.buildSuccess(respEntity);
    }

    private boolean checkIfVoiceControl () {
        String tenantId = SystemContextUtils.getTenantId();
        String voiceControlTenantId = (String) redisTemplate.opsForValue().get(VOICE_TENANT_KEY);
        LogUtils.info("检查是否需要上报属性，tenantId:{},voiceTanantId:{}", tenantId, voiceControlTenantId);
        if (StringUtils.isBlank(voiceControlTenantId)) {
            return false;
        }
        String[] split = voiceControlTenantId.split(",");
        for (String s : split) {
            if (s.equals(tenantId)) {
                return true;
            }
        }
        return false;
    }
}
