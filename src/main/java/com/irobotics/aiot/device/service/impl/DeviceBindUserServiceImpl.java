package com.irobotics.aiot.device.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.basic.exception.AppRuntimeException;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.common.*;
import com.irobotics.aiot.device.dto.MqttPropertyGetReqDTO;
import com.irobotics.aiot.device.entity.DeviceBindUserEntity;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.entity.ProductModeEntity;
import com.irobotics.aiot.device.entity.TAuthClientUserInfo;
import com.irobotics.aiot.device.mapper.DeviceBindUserMapper;
import com.irobotics.aiot.device.mapper.DeviceInfoMapper;
import com.irobotics.aiot.device.remote.DeviceShadowRemote;
import com.irobotics.aiot.device.remote.IcloudIntentServiceRemote;
import com.irobotics.aiot.device.remote.ProductServiceRemote;
import com.irobotics.aiot.device.remote.TAuthUserRemote;
import com.irobotics.aiot.device.remote.model.ClearFlowParam;
import com.irobotics.aiot.device.service.*;
import com.irobotics.aiot.device.vo.ResetNicknameReq;
import com.irobotics.aiot.device.vo.SyncUntieReq;
import com.irobotics.aiot.device.vo.admin.AdminDeviceBindReq;
import com.irobotics.aiot.device.vo.admin.AdminDeviceBindVo;
import com.irobotics.aiot.device.vo.app.BindDeviceInfoVo;
import com.irobotics.aiot.device.vo.app.MoveDeviceReq;
import com.irobotics.aiot.device.vo.app.UntieDeviceReq;
import com.irobotics.aiot.device.vo.inner.OwnerListParam;
import com.irobotics.aiot.device.vo.inner.OwnerListVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.PARAM_VALIDATE_FAIL;
import static com.irobotics.aiot.device.common.Constant.DEVICE_FLAG_0;
import static com.irobotics.aiot.device.common.Constant.DEVICE_SHARE_STATUS_1;

/**
 * <p>
 * 关闭智能家居设备绑定关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-04
 */
@Service
@RefreshScope
@Slf4j
public class DeviceBindUserServiceImpl extends ServiceImpl<DeviceBindUserMapper, DeviceBindUserEntity> implements IDeviceBindUserService {

    //在线用户sn缓存key
    private static final String ONLINE_APP_SN_KEY = "OnLineApp:sn:";

    @Autowired
    private LogService logService;

    @Autowired
    private IDeviceInviteShareService inviteShareService;

    @Autowired
    private IMongoService mongoService;

    @Autowired
    private IcloudIntentService icloudIntentService;

    @Autowired
    private DeviceShadowRemote dataStatisticsServiceRemote;

    @Autowired
    private TAuthUserRemote authUserRemote;

    @Autowired
    @Lazy
    private DeviceResetService deviceResetService;

    @Autowired
    private CommonService commonService;
    @Autowired
    private DeviceInfoCache deviceInfoCache;

    @Autowired
    private IConversationService conversationService;

    @Resource
    private IcloudIntentServiceRemote cloudIntentServiceRemote;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private final String VOICE_TENANT_KEY = "voiceTenantKey";

    @Autowired
    private ProductServiceRemote productServiceRemote;

    @Autowired
    private DeviceInfoMapper deviceInfoMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean bind(String deviceId, String nickname, String sn, String productId) {
        String tenantId = SystemContextUtils.getTenantId();
        LambdaQueryWrapper<DeviceBindUserEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceBindUserEntity::getDeviceId, deviceId);
        queryWrapper.eq(DeviceBindUserEntity::getUserAgent, UserClientAgentEnum.NON_BASE.getType());
        List<DeviceBindUserEntity> list = this.list(queryWrapper);
        LogUtils.info("bind-list绑定记录:{}", JSON.toJSONString(list));
        String userId = SystemContextUtils.getId();
        List<String> ids = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> deviceIds =
                    list.stream().filter(e -> userId.equals(e.getOwner())).map(DeviceBindUserEntity::getDeviceId).collect(Collectors.toList());
            ids = list.stream().map(DeviceBindUserEntity::getId).collect(Collectors.toList());
            // 原用户已经绑定直接返回
            if (CollectionUtil.isNotEmpty(deviceIds)) {
                logService.bindLog(LogEnum.BIND.getCode(), LogEnum.SELF.getCode(), deviceId);
                //重新配网，设备默认昵称根据新规则更改
                setDefaultNickName(sn, tenantId);
                return false;
            }
        }

        // 删除流水信息
        delDeviceFlow(List.of(sn));
        // 删除设备有关语音会话
        conversationService.deleteBySn(sn);

        // 设备已绑定，删除原设备信息
        if (CollectionUtil.isNotEmpty(ids)) {
            // 删除绑定记录
            this.baseMapper.deleteBatchIds(ids);
            // 重置用户名
            ResetNicknameReq nicknameReq = new ResetNicknameReq();
            nicknameReq.setDeviceId(deviceId);
            nicknameReq.setInviter(true);
            nicknameReq.setSn(sn);
            deviceResetService.ResetNickname(nicknameReq);
            // 删除mongo保存的绑定关系
            List<String> createIds = list.stream().map(DeviceBindUserEntity::getId).collect(Collectors.toList());
            mongoService.batchRemoveByUserIds(createIds, sn);
            // 删除语控关系
            icloudIntentService.batchRemoveByUserIds(createIds, sn);
            // 使分享消息失效，
            list.forEach(data -> commonService.shareInvalidate(data.getDeviceId(), data.getCreatorId(), true));
        }else {
            //重新配网，设备默认昵称根据新规则更改
            setDefaultNickName(sn, tenantId);
        }
        String openUserId = (String) SystemContextUtils.getContextValue(ContextKey.OPEN_USER_ID);
        DeviceBindUserEntity addEntity = null;
        // 增加绑定关系
        if (StringUtils.isNotBlank(openUserId)) {
            addEntity = new DeviceBindUserEntity(deviceId, sn, userId, userId, productId, DEVICE_FLAG_0,
                    BindSourceEnum.DEVICE_BIND.getCode(), BindUserTypeEnum.OPEN_API.getCode());
        } else {
            addEntity = new DeviceBindUserEntity(deviceId, sn, userId, userId, productId, DEVICE_FLAG_0,
                    BindSourceEnum.DEVICE_BIND.getCode(), BindUserTypeEnum.SELF.getCode());
        }
        this.save(addEntity);
        // 重置基站拥有者
        this.baseMapper.updateOwnerByDeviceId(userId, deviceId);
        // 新增mongo保存的绑定关系
        List<String> userIds = new ArrayList<>();
        userIds.add(userId);
        mongoService.batchInsertByUserIds(userIds, sn);
        // 新增语控关系
        icloudIntentService.batchInsertByUserIds(userIds, sn);
        logService.bindLog(LogEnum.BIND.getCode(), LogEnum.SELF.getCode(), deviceId);
        // TODO 后期增加缓存与删除缓存
        return true;
    }

    private void setDefaultNickName(String sn, String tenantId) {
        try {
            DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheBySn(tenantId, sn);
            if(StringUtils.isNotBlank(deviceInfo.getProductModeCode())){
                ResponseMessage<ProductModeEntity> getProductMode = productServiceRemote.getByCode(tenantId, deviceInfo.getProductModeCode());
                LogUtils.info("根据产品型号代码查询产品型号:{}", JSON.toJSONString(getProductMode));
                if (!getProductMode.isSuccess()) {
                    LogUtils.info("无法根据该产品型号代码查询到产品型号:{}", getProductMode);
                }
                if (getProductMode.getResult() == null) {
                    LogUtils.info("无法根据该产品型号代码查询到产品型号:{}", getProductMode);
                }
                String defaultNickname = genNickName(sn, getProductMode.getResult());
                if(StringUtils.isNotBlank(defaultNickname) && defaultNickname.equals(deviceInfo.getDefaultNickname())){
                    //规则没改，不需要重新生成
                    return;
                }
                if(StringUtils.isNotBlank(deviceInfo.getNickname()) && deviceInfo.getNickname().equals(deviceInfo.getDefaultNickname())){
                    deviceInfo.setNickname(defaultNickname);
                }else {
                    deviceInfo.setNickname(deviceInfo.getNickname());
                }
                deviceInfo.setDefaultNickname(defaultNickname);
            }
            LogUtils.info("resetNickname-deviceInfo:{}", JSON.toJSONString(deviceInfo));
            UpdateWrapper<DeviceInfoEntity> wrapper = new UpdateWrapper<>();
            LambdaUpdateWrapper<DeviceInfoEntity> lambda = wrapper.lambda();
            lambda.eq(DeviceInfoEntity::getSn, sn).eq(DeviceInfoEntity::getTenantId, tenantId)
                    .set(DeviceInfoEntity::getNickname, deviceInfo.getNickname())
                    .set(DeviceInfoEntity::getDefaultNickname, deviceInfo.getDefaultNickname());
            deviceInfoMapper.update(null, wrapper);
            deviceInfoCache.clear(tenantId, deviceInfo);
        }catch (Exception e){
            LogUtils.info("修改默认昵称失败：{}",e);
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean shareBind(String deviceId, String nickname, String sn, String productId, String owner) {
        String userId = SystemContextUtils.getId();
        String tenantId = SystemContextUtils.getTenantId();
        // 判断是否存在分享设备
        LambdaQueryWrapper<DeviceBindUserEntity> queryWrapper = new QueryWrapper<DeviceBindUserEntity>().lambda();
        queryWrapper.eq(DeviceBindUserEntity::getCreatorId, userId).eq(DeviceBindUserEntity::getDeviceId, deviceId).eq(DeviceBindUserEntity::getFlag, Constant.DEVICE_FLAG_1).eq(DeviceBindUserEntity::getUserAgent, UserClientAgentEnum.NON_BASE.getType());
        long count = this.count(queryWrapper);
        if (count > 0) {
            return false;
        }
        // 创建新的分享绑定关系
        DeviceBindUserEntity deviceBindUser = new DeviceBindUserEntity(deviceId, sn, userId, owner, productId,
                Constant.DEVICE_FLAG_1, BindSourceEnum.DEVICE_BIND.getCode(), BindUserTypeEnum.SELF.getCode());
        this.save(deviceBindUser);
        // 更新拥有者设备的分享状态
        LambdaUpdateWrapper<DeviceBindUserEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(DeviceBindUserEntity::getShareStatus, DEVICE_SHARE_STATUS_1);
        updateWrapper.eq(DeviceBindUserEntity::getDeviceId, deviceId);
        updateWrapper.eq(DeviceBindUserEntity::getOwner, owner);
        this.update(updateWrapper);

        // 新增mongo保存的绑定关系
        mongoService.insert(userId, sn);
        // 新增语控关系
        icloudIntentService.insert(userId, sn);
        logService.bindLog(LogEnum.SHARE_BIND.getCode(), LogEnum.SELF.getCode(), deviceId);
        // TODO 后期增加缓存处理
        return true;
    }


    @Override
    public Boolean untieShareBind(String deviceId, String userId) {
        // 判断是否存在分享设备
        LambdaQueryWrapper<DeviceBindUserEntity> queryWrapper = new QueryWrapper<DeviceBindUserEntity>().lambda();
        queryWrapper.eq(DeviceBindUserEntity::getCreatorId, userId).eq(DeviceBindUserEntity::getDeviceId, deviceId).eq(DeviceBindUserEntity::getFlag, Constant.DEVICE_FLAG_1).last(" LIMIT 1");
        DeviceBindUserEntity entity = this.getOne(queryWrapper);
        if (Objects.isNull(entity)) {
            // 可能被邀请者还没有接受邀请
            return false;
        }
        // 删除共享设备
        this.baseMapper.deleteById(entity.getId());
        mongoService.remove(userId, entity.getSn());
        icloudIntentService.remove(userId, entity.getSn());
        // redisService.deleteDeviceInfo(param.getBeInviter());
        logService.bindLog(LogEnum.SHARE_UNBIND.getCode(), LogEnum.SELF.getCode(), deviceId);
        return true;
    }


    @Override
    public void deleteBindByDeviceIds(List<String> deviceIds) {
        LambdaQueryWrapper<DeviceBindUserEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DeviceBindUserEntity::getDeviceId, deviceIds);
        List<DeviceBindUserEntity> list = this.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            // 删除绑定关系
            this.remove(queryWrapper);
            // 删除流水
            List<String> snList =
                    list.stream().map(DeviceBindUserEntity::getSn).distinct().collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(snList)) {
                ClearFlowParam param = new ClearFlowParam();
                param.setTenantId(list.get(0).getTenantId());
                param.setSnList(snList);
                dataStatisticsServiceRemote.flow(param);
            }
        }
    }

    private void delDeviceFlow(List<String> snList) {
        ClearFlowParam param = new ClearFlowParam();
        param.setTenantId(SystemContextUtils.getTenantId());
        param.setSnList(snList);
        dataStatisticsServiceRemote.flow(param);
    }

    @Override
    public ResponseMessage<Page<AdminDeviceBindVo>> getPage(AdminDeviceBindReq req) {
        // 判断搜索用户名是否存在，如果存在先去 user-center， 找到 userId
        String userId = null;
        if (StringUtils.isNotBlank(req.getUsername())) {
            // 远程获取userId
            ResponseMessage<TAuthClientUserInfo> responseMessage = authUserRemote.getByUsername(req.getTenantId(),
                    req.getUsername());
            if (responseMessage == null || responseMessage.getResult() == null) {
                return ResponseMessage.buildFail(ResponseCode.USER_NOT_EXISTS);
            }
            userId = responseMessage.getResult().getId();
        }
        Page<AdminDeviceBindReq> page = new Page<>(req.getPage(), req.getPageSize());
        Timestamp beginTime = null;
        Timestamp endTime = null;
        if (null != req.getBeginTime() && 0 != req.getBeginTime()) {
            beginTime = new Timestamp(req.getBeginTime());
        }
        if (null != req.getEndTime() && 0 != req.getBeginTime()) {
            endTime = new Timestamp(req.getEndTime());
        }
        Page<AdminDeviceBindVo> bindVoPage = this.baseMapper.getBindList(page, req.getTenantId(), req.getSn(), userId
                , beginTime, endTime);
        if (bindVoPage.getTotal() == 0) {
            return ResponseMessage.buildSuccess(bindVoPage);
        } else {
            List<AdminDeviceBindVo> bindVoList = bindVoPage.getRecords();
            Integer row = bindVoPage.getRecords().size();
            Set<String> userIdSet = new HashSet<>(row);
            Set<String> deviceIdSet = new HashSet<>(row);
            bindVoList.forEach(e -> {
                userIdSet.add(e.getUserId());
                deviceIdSet.add(e.getDeviceId());
            });
            // 远程获取用户名
            ResponseMessage<List<TAuthClientUserInfo>> userList = authUserRemote.getListByIds(req.getTenantId(),
                    new ArrayList<>(userIdSet));
            Integer userTotal = userList.getResult() != null ? userList.getResult().size() : 0;
            // key 为 userId , value 为 username
            Map<String, String> userMap = new HashMap<>(userTotal);
            if (userTotal > 0) {
                userMap =
                        userList.getResult().stream().filter(e -> StringUtils.isNotBlank(e.getId())).collect(Collectors.toMap(TAuthClientUserInfo::getId, TAuthClientUserInfo::getUsername));
            }
            for (AdminDeviceBindVo vo : bindVoList) {
                vo.setUsername(userMap.get(vo.getUserId()));
            }
            bindVoPage.setRecords(bindVoList);
        }
        return ResponseMessage.buildSuccess(bindVoPage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseMessage<Boolean> untieDevice(UntieDeviceReq req) {
        // region 兼容APP-SDK-OPENAPI的20240601前已发布版本的接口调用
        if (StringUtils.isBlank(req.getDeviceId())){
            DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheBySn(SystemContextUtils.getTenantId(), req.getSn());
            if (Objects.isNull(deviceInfo)) {
                return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA);
            }
            req.setDeviceId(deviceInfo.getId());
        }
        if (StringUtils.isBlank(req.getDeviceId())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceId或SN不能为空");
        }
        // endregion
        LambdaQueryWrapper<DeviceBindUserEntity> queryWrapper = new QueryWrapper<DeviceBindUserEntity>().lambda();
        queryWrapper.eq(DeviceBindUserEntity::getDeviceId, req.getDeviceId());
        queryWrapper.eq(DeviceBindUserEntity::getUserAgent, UserClientAgentEnum.NON_BASE.getType());
        List<DeviceBindUserEntity> deviceList = this.list(queryWrapper);
        if (CollectionUtil.isEmpty(deviceList)) {
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA);
        }
        String owner = deviceList.get(0).getOwner();
        String userId = SystemContextUtils.getId();
        List<String> userIds =
                deviceList.stream().map(DeviceBindUserEntity::getCreatorId).distinct().collect(Collectors.toList());
        String sn = deviceList.get(0).getSn();
        if (!userId.equals(owner)) {
            // 不是设备拥有者，但是有设备绑定记录，就是被分享者
            if (userIds.contains(userId)) {
                // 删除设备绑定信息
                // @see 方法 untieShareBind(String deviceId, String userId)
                mongoService.remove(userId, sn);
                icloudIntentService.remove(userId, sn);
                // 删除邀请记录
                inviteShareService.delByInvitee(userId, req.getDeviceId(), InviteShareEnum.DEVICE);
                this.remove(queryWrapper.eq(DeviceBindUserEntity::getCreatorId, userId).eq(DeviceBindUserEntity::getFlag, Constant.DEVICE_FLAG_1));
                logService.bindLog(LogEnum.SHARE_UNBIND.getCode(), LogEnum.SELF.getCode(), req.getDeviceId());
                // 还需要移除分享记录
                return ResponseMessage.buildSuccess(true);
            }
            // 既不是拥有者，也没有设备绑定记录的创建者，属于非法操作
            return ResponseMessage.buildFail(ResponseCode.ILLEGAL_OPERATE);
        }
        // 删除设备绑定
        this.remove(queryWrapper);
        // 删除流水信息
        delDeviceFlow(List.of(sn));
        // 重置基站用户为基站owner
        this.baseMapper.resetOwner(req.getDeviceId());
        // 重置用户名
        ResetNicknameReq nicknameReq = new ResetNicknameReq();
        nicknameReq.setDeviceId(req.getDeviceId());
        nicknameReq.setInviter(true);
        nicknameReq.setSn(sn);
        deviceResetService.ResetNickname(nicknameReq);
        mongoService.batchRemoveByUserIds(userIds, sn);
        icloudIntentService.batchRemoveByUserIds(userIds, sn);
        // 使分享消息失效，
        deviceList.forEach(data -> commonService.shareInvalidate(data.getDeviceId(), data.getCreatorId(), true));
        // 向语控第三方服务器发送解绑信息
        if (checkIfVoiceControl()) {
            ResponseMessage<Boolean> syncResult = cloudIntentServiceRemote.syncUntieInfo(new SyncUntieReq(SystemContextUtils.getTenantId(), req.getDeviceId()));
            if (!syncResult.isSuccess() || !syncResult.getResult()) {
                log.error("向第三方服务发送解绑信息失败,tenantId:{},deviceId:{}", SystemContextUtils.getTenantId(), req.getDeviceId());
            }
        }
        // commonService.delDeviceShareInvalidate(req.getDeviceId());
        return ResponseMessage.buildSuccess(true);
    }

    // 重置设备，清空设备所有相关数据
    private void resetDeviceById(String deviceId) {
        Wrapper<DeviceBindUserEntity> queryWrapper = lambdaQuery().eq(DeviceBindUserEntity::getDeviceId, deviceId)
                .eq(DeviceBindUserEntity::getUserAgent, UserClientAgentEnum.NON_BASE.getType()).getWrapper();
        List<DeviceBindUserEntity> deviceBindList = this.list(queryWrapper);
        if (CollectionUtil.isEmpty(deviceBindList)) {
            return;
        }
        // 检查操作是否合法，如果用户操作比较owner是否等于请求主体ID、通过机器上的按钮重置设备比较deviceId是否等于请求主体ID
        DeviceBindUserEntity deviceBindUserEntity = deviceBindList.get(0);
        String id = SystemContextUtils.getId();
        if (deviceBindUserEntity.getDeviceId().equals(id)
                && deviceBindUserEntity.getOwner().equals(id)) {
            // 非法操作
            return;
        }
        // 删除设备绑定
        this.remove(queryWrapper);
        // 删除流水信息
        String sn = deviceBindUserEntity.getSn();
        delDeviceFlow(List.of(sn));
        // 重置基站用户为基站owner
        this.baseMapper.resetOwner(deviceId);
        // 重置用户名
        ResetNicknameReq nicknameReq = new ResetNicknameReq();
        nicknameReq.setDeviceId(deviceId);
        nicknameReq.setInviter(true);
        nicknameReq.setSn(sn);
        deviceResetService.ResetNickname(nicknameReq);
        if (CollectionUtils.isNotEmpty(deviceBindList)) {
            // UID去重
            Set<String> userIdset = new HashSet<>(deviceBindList.size());
            // 使分享失效，
            deviceBindList.forEach(data -> {
                userIdset.add(data.getCreatorId());
                commonService.shareInvalidate(data.getDeviceId(), data.getCreatorId(), true);
            });
            List<String> userIds = userIdset.stream().toList();
            // 移除数据
            mongoService.batchRemoveByUserIds(userIds, sn);
            // 移除语控
            icloudIntentService.batchRemoveByUserIds(userIds, sn);
        }
    }

    @Override
    public ResponseMessage<List<BindDeviceInfoVo>> getBindListByUserId(String userId) {
        List<BindDeviceInfoVo> resultList = this.baseMapper.getDeviceInfoAndBindByUserId(userId);
        if (CollectionUtil.isEmpty(resultList)) {
            return ResponseMessage.buildSuccess(resultList);
        }
        resultList = commonService.formatBindDeviceInfoList(resultList);
        if(CollectionUtils.isNotEmpty(resultList)){
            List<String> snList = resultList.stream().map(t -> t.getSn()).collect(Collectors.toList());
            addOnlineAppSnCacheForTempMap(snList);
        }
        return ResponseMessage.buildSuccess(resultList);
    }

    /**
     * 添加在线app的sn缓存，用于实时地图
     * @param snList
     */
    @Async
    public void addOnlineAppSnCacheForTempMap(List<String> snList) {
        if(CollectionUtils.isEmpty(snList)){
            return;
        }
        try {
            snList.forEach(sn -> {
                redisTemplate.opsForValue().set(ONLINE_APP_SN_KEY+sn, 1, 5, TimeUnit.MINUTES);
                //测试
                Object o = redisTemplate.opsForValue().get(ONLINE_APP_SN_KEY + sn);
                LogUtils.info("添加在线app的sn缓存成功,sn:{},value:{}",sn,o);
            });
        }catch (Exception e){
            LogUtils.error(e,"添加在线app的sn缓存失败");
        }
    }

    @Override
    public ResponseMessage<Boolean> addBatch(List<DeviceBindUserEntity> list) {
        this.saveBatch(list);
        return ResponseMessage.buildSuccess(true);
    }

    @Override
    public ResponseMessage<List<DeviceBindUserEntity>> getBindList(String tenantId, String userId) {
        return ResponseMessage.buildSuccess(this.baseMapper.getListByUserId(tenantId, userId));
    }

    @Override
    public ResponseMessage<Boolean> deleteByIds(List<String> ids) {
        this.baseMapper.deleteBatchIds(ids);
        return ResponseMessage.buildSuccess(true);
    }


    @Override
    public ResponseMessage<Boolean> moveToTop(MoveDeviceReq req) {
        String userId = SystemContextUtils.getId();
        LambdaQueryWrapper<DeviceBindUserEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceBindUserEntity::getCreatorId, userId).orderByDesc(DeviceBindUserEntity::getDeviceSortWeight).last("LIMIT 1");
        DeviceBindUserEntity lastEntity = this.baseMapper.selectOne(queryWrapper);
        if (Objects.isNull(lastEntity)) {
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA);
        }
        // 找到最大deviceSortWeight
        int initSortWeight = lastEntity.getDeviceSortWeight();
        this.sortDevice(initSortWeight, req.getIdList());
        return ResponseMessage.buildSuccess(true);
    }


    @Override
    public ResponseMessage<Boolean> updateOrder(MoveDeviceReq req) {
        int initSortWeight = 0;
        this.sortDevice(initSortWeight, req.getIdList());
        return ResponseMessage.buildSuccess(true);
    }


    /**
     * 对设备id，进行排序
     *
     * @param initSortWeight 初始权重
     * @param idList         需要排序的设备id
     */
    private void sortDevice(int initSortWeight, List<String> idList) {
        String userId = SystemContextUtils.getId();
        LambdaQueryWrapper<DeviceBindUserEntity> query = new LambdaQueryWrapper<>();
        query.eq(DeviceBindUserEntity::getCreatorId, userId).in(DeviceBindUserEntity::getDeviceId, idList);
        List<DeviceBindUserEntity> list = this.baseMapper.selectList(query);
        if (CollectionUtil.isEmpty(list)) {
            throw new AppRuntimeException(ResponseCode.NOT_FOUND_DATA);
        }
        initSortWeight = initSortWeight + list.size();
        for (String id : idList) {
            for (DeviceBindUserEntity bindUserEntity : list) {
                if (bindUserEntity.getDeviceId().equals(id)) {
                    bindUserEntity.setDeviceSortWeight(initSortWeight);
                }
            }
            initSortWeight--;
        }
        this.updateBatchById(list);
    }

    @Override
    public ResponseMessage<List<OwnerListVo>> getOwnerListByDeviceId(OwnerListParam param) {
        param.setIds(param.getIds().stream().distinct().collect(Collectors.toList()));
        LambdaQueryWrapper<DeviceBindUserEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (param.getFlag()) {
            queryWrapper.in(DeviceBindUserEntity::getDeviceId, param.getIds());
            queryWrapper.eq(DeviceBindUserEntity::getFlag, DEVICE_FLAG_0);
        } else {
            queryWrapper.in(DeviceBindUserEntity::getDeviceId, param.getIds());
        }
        List<DeviceBindUserEntity> list = this.baseMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(list)) {
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA);
        }
        Map<String, List<DeviceBindUserEntity>> entityMap =
                list.stream().collect(Collectors.groupingBy(DeviceBindUserEntity::getDeviceId));
        List<OwnerListVo> retList = new ArrayList<>(param.getIds().size());
        for (String deviceId : param.getIds()) {
            OwnerListVo vo = new OwnerListVo();
            Set<String> userIdList = new HashSet<>();
            List<DeviceBindUserEntity> groupList = entityMap.get(deviceId);
            if (CollectionUtils.isEmpty(groupList)) {
                vo.setUserIdList(userIdList);
                vo.setDeviceId(deviceId);
            } else {
                // 存在绑定关系
                groupList.forEach(e -> {
                    vo.setTenantId(e.getTenantId());
                    vo.setDeviceId(deviceId);
                    userIdList.add(e.getCreatorId());
                });
                vo.setUserIdList(userIdList);
            }
            retList.add(vo);
        }
        return ResponseMessage.buildSuccess(retList);
    }

    @Override
    public ResponseMessage<Boolean> deleteByUserIds(List<String> userIds, String tenantId) {
        LambdaQueryWrapper<DeviceBindUserEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceBindUserEntity::getTenantId, tenantId);
        // 删除分享设备 和 和自己配网设备
        queryWrapper.and(e -> e.in(DeviceBindUserEntity::getCreatorId, userIds).or().in(DeviceBindUserEntity::getOwner, userIds));
        int row = this.baseMapper.delete(queryWrapper);
        return ResponseMessage.buildSuccess(row > 0);
    }

    @Override
    public void propertyGetTopicHandler(MqttPropertyGetReqDTO param) {
        if(Objects.isNull(param) || StringUtils.isBlank(param.getTopic())){
            return;
        }
        String topic = param.getTopic();
        try {
            //topic格式：/mqtt/产品id/sn/thing/service/property/get
            String[] split = topic.split("/");
            String sn = split[3];
            addOnlineAppSnCacheForTempMap(ListUtil.toList(sn));
        }catch (Exception e){
            log.error("propertyGetTopicHandler topic获取sn失败,topic:{}",topic, e);
        }
    }

    private boolean checkIfVoiceControl () {
        String tenantId = SystemContextUtils.getTenantId();
        String voiceControlTenantId = (String) redisTemplate.opsForValue().get(VOICE_TENANT_KEY);
        LogUtils.info("检查是否需要上报属性，tenantId:{},voiceTanantId:{}", tenantId, voiceControlTenantId);
        if (StringUtils.isBlank(voiceControlTenantId)) {
            return false;
        }
        String[] split = voiceControlTenantId.split(",");
        for (String s : split) {
            if (s.equals(tenantId)) {
                return true;
            }
        }
        return false;
    }


    public String genNickName(String sn, ProductModeEntity productModeEntity) {
        String aliasPrefix = productModeEntity.getAliasPrefix();
        Integer snSuffixBit = productModeEntity.getSnSuffixBit();
        int snLength = sn.length();
        return aliasPrefix + (snLength <= snSuffixBit ? sn : (sn.substring(snLength - snSuffixBit)));
    }
}
