package com.irobotics.aiot.device.service.impl;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.service.DeviceExtendService;
import com.irobotics.aiot.device.vo.ServerTimeVo;
import com.irobotics.aiot.encryption.util.AESUtil;
import org.springframework.stereotype.Service;

import java.util.Random;


@Service
public class DeviceExtendServiceImpl implements DeviceExtendService {

    @Override
    public ResponseMessage<ServerTimeVo> getServerTime(String src, String sn, String ts, String tenantId, Integer timeType) {
        //解密获取tenantId
        String decrypt = null;
        try {
            decrypt = AESUtil.Decrypt(src, tenantId);
        } catch (Exception e) {
            LogUtils.error("解密异常:{}",e.getMessage());
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL,"解密失败");
        }
        boolean verification = decrypt.equals(sn + ts);
        if (!verification){
            LogUtils.error("解密验证失败:decrypt{},src:{}",decrypt,src);
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL,"解密结果与所传参数不一致");
        }
        // 校验参数
        // 返回服务器时间
        ServerTimeVo serverTimeVo = new ServerTimeVo();
        String millisTime = String.valueOf(System.currentTimeMillis());
        String time = millisTime;
        Random random = new Random();
        if (timeType.equals(0)) {
//            线上JDK17不支持下面这种语法
//            time = millisTime + random.nextInt(100000, 999999);
            time = millisTime + (100000 + random.nextInt(899999));
        } else if (timeType.equals(1)) {
//            time = millisTime + random.nextInt(100, 999);
            time = millisTime + (100 + random.nextInt(899));
        } else if (timeType.equals(2)) {
            time = millisTime;
        } else if (timeType.equals(3)) {
            time = millisTime.substring(0, millisTime.length() - 3);
        }
        serverTimeVo.setTimeStamp(time);
        serverTimeVo.setTimeType(timeType);
        return ResponseMessage.buildSuccess(serverTimeVo);
    }
}
