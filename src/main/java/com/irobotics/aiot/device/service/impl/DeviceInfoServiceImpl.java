package com.irobotics.aiot.device.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.plugins.IgnoreStrategy;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.irobotics.aiot.bravo.basic.base.CountryCity;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ClientType;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.basic.exception.AppRuntimeException;
import com.irobotics.aiot.bravo.basic.exception.FastRuntimeException;
import com.irobotics.aiot.bravo.commons.util.CSVUtils;
import com.irobotics.aiot.bravo.commons.util.JsonUtils;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.bravo.token.TokenUtils;
import com.irobotics.aiot.bravo.token.UserToken;
import com.irobotics.aiot.bravo.web.util.OperatorUtils;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.common.CommonEnum;
import com.irobotics.aiot.device.common.DeviceResponseCode;
import com.irobotics.aiot.device.common.DeviceVerifyEmum;
import com.irobotics.aiot.device.config.BloomFilterConfig;
import com.irobotics.aiot.device.entity.*;
import com.irobotics.aiot.device.mapper.DeviceBindUserMapper;
import com.irobotics.aiot.device.mapper.DeviceInfoMapper;
import com.irobotics.aiot.device.remote.DeviceShadowRemote;
import com.irobotics.aiot.device.remote.ProductServiceRemote;
import com.irobotics.aiot.device.remote.SmartHomeServiceRemote;
import com.irobotics.aiot.device.remote.model.BindIdVo;
import com.irobotics.aiot.device.remote.model.InnerDeviceRegisterParam;
import com.irobotics.aiot.device.service.*;
import com.irobotics.aiot.device.utils.EMQJwtUtil;
import com.irobotics.aiot.device.utils.LocalDateTimeUtils;
import com.irobotics.aiot.device.utils.TenantIdHandleUtil;
import com.irobotics.aiot.device.vo.*;
import com.irobotics.aiot.device.vo.excel.DeviceInfoVo;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
@Service
public class DeviceInfoServiceImpl extends ServiceImpl<DeviceInfoMapper, DeviceInfoEntity> implements IDeviceInfoService {

    private static final Integer NORMAL_PAGE_SIZE = 10;

    private static final String DEVICE_ID = "deviceId";

    @Autowired
    private DeviceInfoMapper deviceInfoMapper;

    @Autowired
    private DeviceShadowRemote deviceShadowRemote;

    @Autowired
    private ProductServiceRemote productServiceRemote;

    @Autowired
    private EMQJwtUtil emqJwtUtil;

    @Autowired
    private LogService logService;

    @Value("${application.type}")
    private String applicationType;

    @Autowired
    private DeviceInfoCache deviceInfoCache;

    @Resource
    private KafkaService kafkaService;

    @Autowired
    private BloomFilterConfig bloomFilterConfig;

    @Autowired
    private SmartHomeServiceRemote smartHomeServiceRemote;

    @Resource
    private IDeviceUpgradeService iDeviceUpgradeService;


    @Autowired
    private DeviceBindUserMapper deviceBindUserMapper;

    @Autowired
    private IProductSnDetailActiveZoneService productSnDetailActiveZoneService;

    @Autowired
    private IcloudIntentService icloudIntentService;

    @Override
    public void checkSn(ProductSnDetailEntity snDetail, String sn, String key) {
        if (snDetail == null) {
            throw new FastRuntimeException(ILLEGAL_OPERATE, "sn不存在");
        }
        String keyt = snDetail.getKey();
        if (StringUtils.isBlank(keyt)) {
            throw new FastRuntimeException(ILLEGAL_OPERATE, "服务器中该sn【" + sn + "】数据异常，key不存在");
        }
        if (!Objects.equals(key, keyt)) {
            throw new FastRuntimeException(ILLEGAL_OPERATE, "key异常，sn【" + sn + "】的key【" + keyt + "】与参数的key【" + key +
                    "】不一致");
        }
    }

    @Override
    public LoginEntityVO doLogin(String tenantId, String mac, String sn, String key, String ip, DeviceInfoEntity info
            , String countryLoc, String productModeCode, String curVersions, CountryCity countryCity) {
        LogUtils.info("device login begin, tenantId={}, mac={}, sn={}, key={}, productModelCode={}",
                tenantId, mac, sn, key, productModeCode);
        // 代表通过mac获取到的设备信息中的sn与参数的sn不相同
        // 即为登录的设备所携带的mac已经被另一台已经注册到服务器的设备的注册
        if (!Objects.equals(info.getSn(), sn)) {
            throw new FastRuntimeException(ILLEGAL_OPERATE,
                    "mac已被占用,已注册的设备mac【" + info.getMac() + "】，sn【" + info.getSn() + "】；当前的mac【" + mac + "】,sn【" + sn + "】");
        }
        if (!Objects.equals(productModeCode, info.getProductModeCode())) {
            throw new FastRuntimeException(ILLEGAL_OPERATE, "产品型号代码");
        }
        // 校验sn的key
        if (StringUtils.isBlank(info.getKey()) || StringUtils.isBlank(info.getPhotoUrl())) {
            ResponseMessage<InnerDeviceLoginVo> productResponseMessage = productServiceRemote.getByCodeAdSn(sn,
                    productModeCode, false);
            if (!productResponseMessage.isSuccess()) {
                throw new FastRuntimeException(INNER_SERVER_ERROR, productResponseMessage.getMsg());
            }
            InnerDeviceLoginVo productResult = productResponseMessage.getResult();
            ProductSnDetailEntity snDetailEntity = productResult.getSnDetailEntity();
            checkSn(snDetailEntity, sn, key);
            if (StringUtils.isBlank(info.getKey())) {
                info.setKey(snDetailEntity.getKey());
            }
            ProductModeEntity productModeEntity  = productResult.getProductModeEntity();
            info.setPhotoUrl(productModeEntity.getPhotoUrl());

        } else {
            if (!Objects.equals(info.getKey(), key)) {
                throw new FastRuntimeException(ILLEGAL_OPERATE, "key错误");
            }
        }
        info.setCity(countryLoc);
        info.setIp(ip);
        info.setOnlineTime(LocalDateTime.now());
        if (!StringUtils.isBlank(curVersions)) {
            info.setVersions(curVersions);
        }
        // 检查设备是否需要重置
        int resetCode = 0;
        if (info.getResetStatus() != null && Objects.equals(info.getResetStatus(), "1")) {
            info.setResetStatus("0");
            resetCode = 1;
        }

        // 检查设备是否需要强制升级
        iDeviceUpgradeService.checkDeviceUpgrade(info);
        //判断此设备是否被禁用
        setDisableCity(countryCity, info,sn);
        info.setUpdateTime(LocalDateTime.now());
        deviceInfoMapper.updateById(info);
        logService.loginLog(productModeCode, sn, mac, tenantId, key, info.getId(), ip, countryLoc, curVersions);
        LoginEntityVO loginEntityVO = newLoginEntity(info, tenantId, countryCity);
        // 返回未重置状态给设备
        loginEntityVO.setResetCode(resetCode);
        deviceInfoCache.clear(tenantId, info);
        return loginEntityVO;
    }

    // 设备登录的逻辑移到产品服务，减少一次远程调用
    @Override
    public LoginEntityVO doRegister(String tenantId, String sn, String mac, String key, String ip,
                                    String productModeCode, String countryLoc, String curVersions,
                                    CountryCity countryCity) {
        LogUtils.info("device register begin, tenantId={}, mac={}, sn={}, key={}, productModelCode={}",
                tenantId, mac, sn, key, productModeCode);
        ResponseMessage<DeviceInfoEntity> registerResponseMessage =
                productServiceRemote.deviceRegister(new InnerDeviceRegisterParam(sn, key, productModeCode));
        if (!registerResponseMessage.isSuccess()) {
            LogUtils.error("设备注册SN信息以及产品型号信息异常, exceptionMsg={}", registerResponseMessage.getMsg());
            throw new FastRuntimeException(registerResponseMessage.getCode(), registerResponseMessage.getMsg());
        }
        DeviceInfoEntity robotInsert = registerResponseMessage.getResult();
        robotInsert.setMac(mac);
        robotInsert.setCity(countryLoc);
        robotInsert.setVersions(curVersions);
        try {
            //判断此设备是否被禁用
            setDisableCity(countryCity, robotInsert,sn);
            int insertRes = deviceInfoMapper.insert(robotInsert);
            if (insertRes <= 0) {
                // 正常不会走到这里
                LogUtils.error(new IllegalStateException(), "SN={} 设备激活注册信息失败,需要重置激活状态...", sn);
                throw new FastRuntimeException(INNER_SERVER_ERROR, "设备激活注册信息失败,需要重置激活状态" + sn);
            }
            // 将激活设备进行统计
            robotInsert.setTenantId(SystemContextUtils.getTenantId());
            LogUtils.info("统计激活设备:", robotInsert.toString());
            kafkaService.deviceRegister(robotInsert);

            // 检查设备是否需要强制升级
            iDeviceUpgradeService.checkDeviceUpgrade(robotInsert);

        } catch (Exception e) {
            // 发生异常，回写sn的激活状态
            ResponseMessage<Boolean> delResponse = productSnDetailActiveZoneService.delBySn(sn);
            if (!delResponse.isSuccess()) {
                throw new FastRuntimeException(INNER_SERVER_ERROR, "远程调用修改sn激活状态失败,sn=" + sn);
            }
            if (!delResponse.getResult()) {
                throw new FastRuntimeException(INNER_SERVER_ERROR, "设备激活授权码失败,请重试" + sn);
            }
            LogUtils.error(e, "设备注册失败,sn={},mac={}, tenantId={}, productModeCode={}, key={}, exceptionMsg={}, ", sn,
                    mac, tenantId, productModeCode, key, e.getMessage());
            throw new AppRuntimeException(INNER_SERVER_ERROR, "设备注册失败！");
        }
        logService.loginLog(productModeCode, sn, mac, tenantId, key, robotInsert.getId(), ip, countryLoc, curVersions);
        // 把设备信息写入到布隆过滤器中
        if (bloomFilterConfig.getUsed()) {
            deviceInfoCache.addIdOrSnBloomFilter(robotInsert.getId());
            deviceInfoCache.addIdOrSnBloomFilter(robotInsert.getSn());
        }
        deviceInfoCache.clear(tenantId, robotInsert);
        return newLoginEntity(robotInsert, tenantId, countryCity);
    }

    private void setDisableCity(CountryCity countryCity, DeviceInfoEntity robotInsert,String sn) {
        Integer disableCity = 1;
        ResponseMessage<ProductSnDetailEntity> snDetailRemote = productServiceRemote.getSnDetailSellRegin(sn);
        LogUtils.info("setDisableCity-getSnDetail:{}", JSONObject.toJSON(snDetailRemote));
        if (!snDetailRemote.isSuccess() || snDetailRemote.getResult() == null) {
            robotInsert.setDisableCity(0);
            return;
        }
        ProductSnDetailEntity snDetail = snDetailRemote.getResult();
        if (snDetail.getDisableNotSellRegion() != null && snDetail.getDisableNotSellRegion() == 0) {
            robotInsert.setDisableCity(0);
            return;
        }
        if (countryCity == null) {
            robotInsert.setDisableCity(0);
            return;
        }
        List<ProductModeSellRegionEntity> productModeSellRegions = snDetail.getProductModeSellRegions();
        if (!CollectionUtils.isEmpty(productModeSellRegions)) {
            List<String> cityNames = productModeSellRegions.stream().map(ProductModeSellRegionEntity::getRegionNameCh).collect(Collectors.toList());
            for(String cityName:cityNames) {
                if (cityName.equals("中国大陆")) {
                    cityName = "中国";
                }
                if (cityName.equals(countryCity.getCountry())) {
                    disableCity = 0;
                }
            }
        } else {
            disableCity = 0;
        }
        robotInsert.setDisableCity(disableCity);
    }

    @Override
    public Page<DeviceInfoEntity> pageList(DeviceInfoPageGetReq pageGetReq) {
        QueryWrapper<DeviceInfoEntity> wrapper = new QueryWrapper<>();
        LambdaQueryWrapper<DeviceInfoEntity> lambda = wrapper.lambda();

        String id = pageGetReq.getId();
        String productModeCode = pageGetReq.getProductModeCode();
        String sn = pageGetReq.getSn();
        String mac = pageGetReq.getMac();
        String nickname = pageGetReq.getNickname();
        Long beginTime = pageGetReq.getBeginTime();
        Long endTime = pageGetReq.getEndTime();

        Integer pages = pageGetReq.getPage();
        Integer pageSize = pageGetReq.getPageSize();

        Page<DeviceInfoEntity> page = new Page<>();
        page.setCurrent(pages);
        page.setSize(pageSize);
        // 模糊搜索
        if (CommonEnum.LIKE_SEARCH.getCode().equals(pageGetReq.getSearchType())) {
            this.likeSearch(lambda, pageGetReq);

        } else {
            this.exactSearch(lambda, pageGetReq);
        }
        if (StringUtils.isNotBlank(pageGetReq.getProductId())) {
            lambda.eq(DeviceInfoEntity::getProductId, pageGetReq.getProductId());
        }
        if (null != beginTime && beginTime > 0) {
            LocalDateTime startTimeTimeMilli = LocalDateTimeUtils.milliToLocalDateTime(beginTime);
            lambda.gt(DeviceInfoEntity::getCreateTime, startTimeTimeMilli);
        }
        if (null != endTime && endTime > 0) {
            LocalDateTime endTimeMilli = LocalDateTimeUtils.milliToLocalDateTime(endTime);
            lambda.lt(DeviceInfoEntity::getCreateTime, endTimeMilli);
        }
        // 对packageType固件包类型 进行搜索
        if (StringUtils.isNotBlank(pageGetReq.getPackageType())) {
            applyJSONSearch(wrapper, "versions", "packageType", "'" + pageGetReq.getPackageType() + "'");
        }
        // 补充versions中的version的搜索
        // 正则表达式判断是否为数字，
        String regex = "^\\d+(\\.\\d+)?$";
        if (StringUtils.isNotBlank(pageGetReq.getVersion())) {
            if (pageGetReq.getVersion().matches(regex)) {
                applyJSONSearch(wrapper, "versions", "version", pageGetReq.getVersion());
            } else {
                applyJSONSearch(wrapper, "versions", "version", "'" + pageGetReq.getVersion() + "'");
            }
        }
        // 补充city中的country的搜索
        if (StringUtils.isNotBlank(pageGetReq.getCountry())) {
            applyJSONSearch(wrapper, "city", "country", "'" + pageGetReq.getCountry() + "'");
        }
        // 补充city中的city的搜索
        if (StringUtils.isNotBlank(pageGetReq.getCity())) {
            applyJSONSearch(wrapper, "city", "city", "'" + pageGetReq.getCity() + "'");
        }

        lambda.orderByDesc(DeviceInfoEntity::getCreateTime).orderByDesc(DeviceInfoEntity::getUpdateTime);
        Page<DeviceInfoEntity> deviceInfoPage = deviceInfoMapper.selectPage(page, wrapper);
        try {
            if (Objects.nonNull(deviceInfoPage) && Objects.nonNull(deviceInfoPage.getRecords())) {
                handleDeviceInfoStatus(deviceInfoPage.getRecords());
            }
        } catch (Exception e) {
            LogUtils.error(e, "回写设备状态异常, exceptionMsg={}", e.getMessage());
        }
        return deviceInfoPage;
    }

    private void applyJSONSearch(QueryWrapper<DeviceInfoEntity> wrapper, String json, String column, String value) {
        wrapper.apply(
                "case " +
                        "when JSON_VALID(" + json + ") then JSON_CONTAINS(" + json + ", json_object('" + column + "',"
                        + value + ")) " +
                        "else false " +
                        "end");
    }

    /**
     * 模糊搜索条件
     *
     * @param lambda
     * @param pageGetReq
     */
    private void likeSearch(LambdaQueryWrapper<DeviceInfoEntity> lambda, DeviceInfoPageGetReq pageGetReq) {
        if (StringUtils.isNotBlank(pageGetReq.getId())) {
            lambda.like(DeviceInfoEntity::getId, pageGetReq.getId());
        }
        if (StringUtils.isNotBlank(pageGetReq.getProductModeCode())) {
            lambda.like(DeviceInfoEntity::getProductModeCode, pageGetReq.getProductModeCode());
        }
        if (StringUtils.isNotBlank(pageGetReq.getSn())) {
            lambda.like(DeviceInfoEntity::getSn, pageGetReq.getSn());
        }
        // 当未输入SN时，sn批量查询才生效
        if (StringUtils.isBlank(pageGetReq.getSn()) && CollectionUtil.isNotEmpty(pageGetReq.getSnList())) {
            lambda.like(DeviceInfoEntity::getSn, pageGetReq.getSnList().get(0));
        }
        if (StringUtils.isNotBlank(pageGetReq.getMac())) {
            lambda.like(DeviceInfoEntity::getMac, pageGetReq.getMac());
        }
        if (StringUtils.isNotBlank(pageGetReq.getNickname())) {
            lambda.like(DeviceInfoEntity::getNickname, pageGetReq.getNickname());
        }
    }

    /**
     * 精准搜索
     *
     * @param lambda
     * @param pageGetReq
     */
    private void exactSearch(LambdaQueryWrapper<DeviceInfoEntity> lambda, DeviceInfoPageGetReq pageGetReq) {
        if (StringUtils.isNotBlank(pageGetReq.getId())) {
            lambda.eq(DeviceInfoEntity::getId, pageGetReq.getId());
        }
        if (StringUtils.isNotBlank(pageGetReq.getProductModeCode())) {
            lambda.eq(DeviceInfoEntity::getProductModeCode, pageGetReq.getProductModeCode());
        }
        if (StringUtils.isNotBlank(pageGetReq.getSn())) {
            lambda.eq(DeviceInfoEntity::getSn, pageGetReq.getSn());
        }
        // 当未输入SN时，sn批量查询才生效
        if (StringUtils.isBlank(pageGetReq.getSn()) && CollectionUtil.isNotEmpty(pageGetReq.getSnList())) {
            lambda.in(DeviceInfoEntity::getSn, pageGetReq.getSnList());
        }
        if (StringUtils.isNotBlank(pageGetReq.getMac())) {
            lambda.eq(DeviceInfoEntity::getMac, pageGetReq.getMac());
        }
        if (StringUtils.isNotBlank(pageGetReq.getNickname())) {
            lambda.eq(DeviceInfoEntity::getNickname, pageGetReq.getNickname());
        }
    }

    /**
     * 通过设备sn和mac获取设备信息
     *
     * @param sn  设备sn
     * @param mac 设备mac
     * @return
     */
    @Override
    public DeviceInfoEntity getBySnAndMac(String sn, String mac) {
        QueryWrapper<DeviceInfoEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DeviceInfoEntity::getSn, sn)
                .eq(DeviceInfoEntity::getMac, mac);
        return deviceInfoMapper.selectOne(wrapper);
    }

    /**
     * 通过设备sn获取设备是否在线
     *
     * @param sn 设备sn
     * @return
     */
    @Override
    public Boolean ifOnline(String sn) {
        if (StringUtils.isBlank(sn)) {
            throw new AppRuntimeException("通过设备sn获取设备状态>> sn不能为空");
        }
        ResponseMessage<DeviceShadowEntity> resposne = deviceShadowRemote.findBySN(sn);
        if (!resposne.isSuccess()) {
            return false;
        }
        DeviceShadowEntity shadowEntity = resposne.getResult();
        if (Objects.isNull(shadowEntity)) {
            return false;
        }
        return shadowEntity.getOnlineStatus();
    }

    /**
     * 通过sn获取设备状态信息
     *
     * @param sn
     * @return
     */
    @Override
    public JSONObject getDeviceStatus(String sn) {
        if (StringUtils.isBlank(sn)) {
            throw new AppRuntimeException("通过设备sn获取设备状态>> sn不能为空");
        }
        ResponseMessage<DeviceShadowEntity> resposne = deviceShadowRemote.findBySN(sn);
        if (Objects.isNull(resposne.getResult())) {
            return null;
        }
        DeviceShadowEntity shadowEntity = resposne.getResult();
        return shadowEntity.getProperties();
    }

    /**
     * 通过设备id集合获取设备信息集合
     *
     * @param deviceIds 设备id集合
     * @return
     */
    @Override
    public List<DeviceInfoEntity> getDevicesByIds(List<String> deviceIds) {
        QueryWrapper<DeviceInfoEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(DeviceInfoEntity::getId, deviceIds);
        return deviceInfoMapper.selectList(wrapper);
    }

    /**
     * 获取设备所有sn列表
     *
     * @return
     */
    @Override
    public List<String> selectAllRobotSn() {
        QueryWrapper<DeviceInfoEntity> wrapper = new QueryWrapper<>();
        wrapper.select("sn");
        List<DeviceInfoEntity> deviceInfoEntities = deviceInfoMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(deviceInfoEntities)) {
            return new ArrayList<>();
        }
        return deviceInfoEntities.stream().map(m -> m.getSn()).collect(Collectors.toList());
    }

    @Override
    public Map<String, Long> getDeviceCountByPro(List<String> productIds) {
        Map<String, Long> map = new HashMap<>();
        List<Map<String, Long>> res = baseMapper.selectCountByProuctId(productIds);
        if (!CollectionUtils.isEmpty(res)) {
            for (Map<String, Long> resMap : res) {
                map.put(String.valueOf(resMap.get("product_id")), Long.valueOf(resMap.get("device_num").toString()));
            }
        }
        return map;
    }

    @Override
    public ResponseMessage<List<DeviceInfoEntity>> getDeviceInfosByIds(List<String> deviceIds) {
        QueryWrapper<DeviceInfoEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(DeviceInfoEntity::getId, deviceIds);
        List<DeviceInfoEntity> deviceInfos = deviceInfoMapper.selectList(wrapper);
        return ResponseMessage.buildSuccess(deviceInfos);
    }

    @Override
    public ResponseMessage<Boolean> modifyNickname(String tenantId, String deviceId, String nickname, String sn,
                                                   String mac) {
        if (StringUtils.isBlank(deviceId)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, DEVICE_ID);
        }
        if (StringUtils.isBlank(nickname)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "nickname");
        }
        if (StringUtils.isBlank(sn)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "sn");
        }
        if (StringUtils.isBlank(mac)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "mac");
        }
        // 校验设备昵称是否重复
        String userId = SystemContextUtils.getId();
        if (isNickNameRepeat(userId, deviceId, nickname)) {
            return ResponseMessage.buildFail(DeviceResponseCode.DEVICE_SAME_NICKNAME_ERROR);
        }
        UpdateWrapper<DeviceInfoEntity> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(DeviceInfoEntity::getId, deviceId)
                .set(DeviceInfoEntity::getNickname, nickname).set(DeviceInfoEntity::getUpdateTime, LocalDateTime.now())
                .set(DeviceInfoEntity::getUpdateBy, OperatorUtils.getOperateBy());
        int update = deviceInfoMapper.update(null, wrapper);
        DeviceInfoEntity deviceInfo = new DeviceInfoEntity(deviceId, sn, mac);
        deviceInfoCache.clear(tenantId, deviceInfo);
        // 新增语控关系
        icloudIntentService.insert(userId, sn);
        return ResponseMessage.buildSuccess(update > 0);
    }

    @Override
    public ResponseMessage<Map<String, String>> getDeviceNickNameByIds(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "ids列表不能为空");
        }
        List<DeviceInfoEntity> deviceInfoEntityList = this.listByIds(ids);
        Map<String, String> nickNameMap = null;
        if (CollectionUtil.isNotEmpty(deviceInfoEntityList)) {
            nickNameMap = deviceInfoEntityList.stream().collect(Collectors.toMap(DeviceInfoEntity::getId,
                    DeviceInfoEntity::getNickname));
        }
        return ResponseMessage.buildSuccess(nickNameMap);
    }

    @Override
    public List<DeviceInfoEntity> getByIds(String tenantId, List<String> ids) {
        List<DeviceInfoEntity> devices = new ArrayList<>();
        if (CollectionUtils.isEmpty(ids)) {
            return devices;
        }
        for (String id : ids) {
            DeviceInfoEntity deviceInfoEntity = deviceInfoCache.getCacheById(tenantId, id);
            if (Objects.nonNull(deviceInfoEntity)) {
                devices.add(deviceInfoEntity);
            }
        }
        return devices;
    }

    @Override
    public Page<DeviceInfoEntity> getPageByCode(String productModeCode, Integer currentPage, Integer pageSize) {
        Page<DeviceInfoEntity> page = new Page<>();
        page.setCurrent(currentPage);
        page.setSize(pageSize);
        return deviceInfoMapper.getPageByCode(page, productModeCode,
                SystemContextUtils.getContextValue(ContextKey.TENANT_ID));
    }

    @Override
    public Integer getCountByCode(String productModeCode) {
        QueryWrapper<DeviceInfoEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(DeviceInfoEntity::getProductModeCode, productModeCode);
        return deviceInfoMapper.selectCount(wrapper).intValue();
    }

    @Override
    public ResponseMessage<Boolean> updateVerifyPassword(DeviceVerifyPasswordVo vo) {
        Integer verifyPassword = vo.getVerifyPassword();
        List<String> deviceIds = vo.getDeviceIds();
        if (CollectionUtils.isEmpty(deviceIds)) {
            return ResponseMessage.buildSuccess(true);
        }
        if (Objects.isNull(verifyPassword)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "verifyPassword");
        }
        UpdateWrapper<DeviceInfoEntity> wrapper = new UpdateWrapper<>();
        LambdaUpdateWrapper<DeviceInfoEntity> lambda = wrapper.lambda();
        lambda.set(DeviceInfoEntity::getVerifyPassword, verifyPassword)
                .set(DeviceInfoEntity::getUpdateBy, OperatorUtils.getOperateBy())
                .set(DeviceInfoEntity::getUpdateTime, LocalDateTime.now())
                .in(DeviceInfoEntity::getId, deviceIds);
        int update = deviceInfoMapper.update(null, wrapper);
        if (update > 0) {
            clearCache(SystemContextUtils.getContextValue(ContextKey.TENANT_ID), deviceIds);
        }
        return ResponseMessage.buildSuccess(update > 0);
    }

    private void clearCache(String tenantId, List<String> deviceIds) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return;
        }
        for (String deviceId : deviceIds) {
            DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheById(tenantId, deviceId);
            if (Objects.nonNull(deviceInfo)) {
                deviceInfoCache.clear(tenantId, deviceInfo);
            }
        }

    }

    @Override
    public ResponseMessage<Boolean> configVerify(DeviceConfigVerifyPasswordVo vo) {
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        Integer verify = vo.getVerify();
        String deviceId = vo.getDeviceId();
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "tenantId");
        }
        if (StringUtils.isBlank(deviceId)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, DEVICE_ID);
        }
        if (Objects.isNull(verify) || !DeviceVerifyEmum.check(verify)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "verify");
        }
        DeviceInfoEntity device = deviceInfoCache.getCacheById(tenantId, deviceId);
        if (Objects.isNull(device)) {
            return ResponseMessage.buildSuccess(false);
        }
        UpdateWrapper<DeviceInfoEntity> wrapper = new UpdateWrapper<>();
        LambdaUpdateWrapper<DeviceInfoEntity> lambda = wrapper.lambda();
        lambda.set(DeviceInfoEntity::getVerifyPassword, verify)
                .set(DeviceInfoEntity::getUpdateBy, OperatorUtils.getOperateBy())
                .set(DeviceInfoEntity::getUpdateTime, LocalDateTime.now())
                .eq(DeviceInfoEntity::getId, deviceId);
        int update = deviceInfoMapper.update(null, wrapper);
        if (update > 0) {
            deviceInfoCache.clear(tenantId, device);
        }
        return ResponseMessage.buildSuccess(update > 0);
    }

    @Override
    public Long getCount() {
        return deviceInfoMapper.selectCount();
    }

    @Override
    public Page<DeviceInfoEntity> getDevicePage(Integer page, Integer pageSize) {
        page = Objects.isNull(page) || page <= 0 ? 1 : page;
        pageSize = Objects.isNull(pageSize) || pageSize <= 0 ? NORMAL_PAGE_SIZE : pageSize;
        return deviceInfoMapper.getPage(new Page<>(page, pageSize));
    }

    @Override
    public ResponseMessage<Boolean> updateIotId(IotIdVo vo) {
        if (StringUtils.isBlank(vo.getDeviceId())) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, DEVICE_ID);
        }
        if (Objects.isNull(vo.getIotId())) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "iotId");
        }
        DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheById(SystemContextUtils.getTenantId(), vo.getDeviceId());
        if (Objects.isNull(deviceInfo)) {
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA, "设备不存在");
        }
        String iotId = vo.getIotId().toJSONString();
        return ResponseMessage.buildSuccess(deviceInfoMapper.updateIotId(vo.getDeviceId(), iotId) > 0);
    }

    @Override
    public ResponseMessage<Page<DeviceInfoEntity>> getDevicesByUserId(DeviceUserPageReq req) {
        List<String> deviceIds = new ArrayList<>();

        // 先查询非智能家居状态
        LambdaQueryWrapper<DeviceBindUserEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceBindUserEntity::getCreatorId, req.getUserId());
        List<DeviceBindUserEntity> bindUserEntityList = deviceBindUserMapper.selectList(lambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(bindUserEntityList)) {
            deviceIds =
                    bindUserEntityList.stream().map(DeviceBindUserEntity::getDeviceId).distinct().collect(Collectors.toList());
        }
        if (CollectionUtil.isEmpty(deviceIds)) {
            try {
                ResponseMessage<List<String>> deviceIdResponse = smartHomeServiceRemote.getBindList(req.getTenantId()
                        , ClientType.PHONE, req.getUserId());
                if (Objects.isNull(deviceIdResponse) || Objects.isNull(deviceIdResponse.getResult()) || CollectionUtils.isEmpty(deviceIdResponse.getResult())) {
                    return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA, "数据为空");
                } else {
                    deviceIds = deviceIdResponse.getResult();
                }
            } catch (Exception e) {
                LogUtils.error(e, "根据用户id获取设备id列表, exceptionMsg={}", e.getMessage());
                return ResponseMessage.buildFail(ResponseCode.SERVICE_BUSY);
            }
        }
        if (CollectionUtil.isEmpty(deviceIds)) {
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA, "数据为空");
        }
        QueryWrapper<DeviceInfoEntity> wrapper = new QueryWrapper<>();
        LambdaQueryWrapper<DeviceInfoEntity> lambda = wrapper.lambda();
        lambda.in(DeviceInfoEntity::getId, deviceIds);
        lambda.orderByDesc(DeviceInfoEntity::getUpdateTime);

        Page<DeviceInfoEntity> page = new Page<>();
        page.setCurrent(req.getPage());
        page.setSize(req.getPageSize());
        Page<DeviceInfoEntity> deviceInfo = deviceInfoMapper.selectPage(page, wrapper);

        try {
            if (Objects.nonNull(deviceInfo) && Objects.nonNull(deviceInfo.getRecords()) && !CollectionUtils.isEmpty(deviceInfo.getRecords())) {
                handleDeviceInfoStatus(deviceInfo.getRecords());
            }
        } catch (Exception e) {
            LogUtils.error(e, "getDevicesByUserId:回写设备状态异常, exceptionMsg={}", e.getMessage());
        }
        return ResponseMessage.buildSuccess(deviceInfo);
    }

    @Override
    public ResponseMessage<Boolean> resetNickname(String sn) {
        String tenantId = SystemContextUtils.getTenantId();
        DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheBySn(tenantId, sn);
        if (Objects.isNull(deviceInfo)) {
            return ResponseMessage.buildFail(ResponseCode.DUPLICATE_DATA, "设备不存在");
        }

        if(StringUtils.isNotBlank(deviceInfo.getProductModeCode())){
            ResponseMessage<ProductModeEntity> getProductMode = productServiceRemote.getByCode(tenantId, deviceInfo.getProductModeCode());
            LogUtils.info("根据产品型号代码查询产品型号:{}", JSON.toJSONString(getProductMode));
            if (!getProductMode.isSuccess()) {
                LogUtils.info("无法根据该产品型号代码查询到产品型号:{}", getProductMode);
            }
            if (getProductMode.getResult() == null) {
                LogUtils.info("无法根据该产品型号代码查询到产品型号:{}", getProductMode);
            }
            String nickname = genNickName(sn, getProductMode.getResult());
            deviceInfo.setDefaultNickname(nickname);
        }
        LogUtils.info("resetNickname-deviceInfo:{}", JSON.toJSONString(deviceInfo));
        UpdateWrapper<DeviceInfoEntity> wrapper = new UpdateWrapper<>();
        LambdaUpdateWrapper<DeviceInfoEntity> lambda = wrapper.lambda();
        lambda.eq(DeviceInfoEntity::getSn, sn).eq(DeviceInfoEntity::getTenantId, tenantId)
                .set(DeviceInfoEntity::getNickname, deviceInfo.getDefaultNickname())
                .set(DeviceInfoEntity::getDefaultNickname, deviceInfo.getDefaultNickname());
        int update = deviceInfoMapper.update(null, wrapper);
        deviceInfoCache.clear(tenantId, deviceInfo);
        return ResponseMessage.buildSuccess(update > 0);
    }

    @Override
    public ResponseMessage<Boolean> updatePhoto(UpdatePhotoVo vo) {
        if (StringUtils.isBlank(vo.getPhotoUrl())) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "产品图片不能为空");
        }
        if (StringUtils.isBlank(vo.getProductId())) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "产品id不能为空");
        }
        if (StringUtils.isBlank(vo.getTenantId())) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "企业id不能为空");
        }
        int update = deviceInfoMapper.updatePhoto(vo.getTenantId(), vo.getProductId(), vo.getPhotoUrl());
        return ResponseMessage.buildSuccess(update > 0);
    }

    @Override
    public ResponseMessage<DeviceInfoEntity> getDeviceInfoById(String id) {
        DeviceInfoEntity deviceInfoEntity = deviceInfoMapper.selectById(id);
        return ResponseMessage.buildSuccess(deviceInfoEntity);
    }

    @Override
    public List<String> getActiveDeviceSNByCode(DeviceInfoReq deviceInfoReq) {
        TenantIdHandleUtil.handleTenantId(deviceInfoReq.getTenantId(), false);
        // 根据产品型号代码和租户获取设备信息总数
        Long total = this.baseMapper.getCountByCode(deviceInfoReq.getProductModeCode(), deviceInfoReq.getTenantId());
        int queryCount = total.intValue() * deviceInfoReq.getPercent() / 100;

        LambdaQueryWrapper<DeviceInfoEntity> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.select(DeviceInfoEntity::getSn);
        queryWrapper.eq(DeviceInfoEntity::getProductModeCode, deviceInfoReq.getProductModeCode())
                .eq(DeviceInfoEntity::getTenantId, deviceInfoReq.getTenantId());
        // 在线时间降序排序
        queryWrapper.orderByDesc(DeviceInfoEntity::getOnlineTime);
        queryWrapper.last("limit " + queryCount);

        List<DeviceInfoEntity> deviceInfoEntities = baseMapper.selectList(queryWrapper);
        return deviceInfoEntities.stream()
                .map(DeviceInfoEntity::getSn)
                .collect(Collectors.toList());
    }

    @Override
    public List<DeviceInfoVo> exportDeviceInfo(DeviceInfoPageGetReq pageGetReq) {
        QueryWrapper<DeviceInfoEntity> wrapper = new QueryWrapper<>();
        LambdaQueryWrapper<DeviceInfoEntity> lambda = wrapper.lambda();

        String id = pageGetReq.getId();
        String productModeCode = pageGetReq.getProductModeCode();
        String sn = pageGetReq.getSn();
        String mac = pageGetReq.getMac();
        String nickname = pageGetReq.getNickname();
        Long beginTime = pageGetReq.getBeginTime();
        Long endTime = pageGetReq.getEndTime();

        // 模糊搜索
        if (CommonEnum.LIKE_SEARCH.getCode().equals(pageGetReq.getSearchType())) {
            this.likeSearch(lambda, pageGetReq);
        } else {
            this.exactSearch(lambda, pageGetReq);
        }
        if (StringUtils.isNotBlank(pageGetReq.getProductId())) {
            lambda.eq(DeviceInfoEntity::getProductId, pageGetReq.getProductId());
        }
        if (null != beginTime && beginTime > 0) {
            LocalDateTime startTimeTimeMilli = LocalDateTimeUtils.milliToLocalDateTime(beginTime);
            lambda.gt(DeviceInfoEntity::getCreateTime, startTimeTimeMilli);
        }
        if (null != endTime && endTime > 0) {
            LocalDateTime endTimeMilli = LocalDateTimeUtils.milliToLocalDateTime(endTime);
            lambda.lt(DeviceInfoEntity::getCreateTime, endTimeMilli);
        }
        // 对packageType固件包类型 进行搜索
        if (StringUtils.isNotBlank(pageGetReq.getPackageType())) {
            applyJSONSearch(wrapper, "versions", "packageType", "'" + pageGetReq.getPackageType() + "'");
        }
        // 补充versions中的version的搜索
        // 正则表达式判断是否为数字，
        String regex = "^\\d+(\\.\\d+)?$";
        if (StringUtils.isNotBlank(pageGetReq.getVersion())) {
            if (pageGetReq.getVersion().matches(regex)) {
                applyJSONSearch(wrapper, "versions", "version", pageGetReq.getVersion());
            } else {
                applyJSONSearch(wrapper, "versions", "version", "'" + pageGetReq.getVersion() + "'");
            }
        }
        // 补充city中的country的搜索
        if (StringUtils.isNotBlank(pageGetReq.getCountry())) {
            applyJSONSearch(wrapper, "city", "country", "'" + pageGetReq.getCountry() + "'");
        }
        // 补充city中的city的搜索
        if (StringUtils.isNotBlank(pageGetReq.getCity())) {
            applyJSONSearch(wrapper, "city", "city", "'" + pageGetReq.getCity() + "'");
        }

        lambda.orderByDesc(DeviceInfoEntity::getCreateTime).orderByDesc(DeviceInfoEntity::getUpdateTime);
        List<DeviceInfoEntity> deviceInfoEntities = deviceInfoMapper.selectList(wrapper);
        try {
            if (!CollectionUtils.isEmpty(deviceInfoEntities)) {
                handleDeviceInfoStatus(deviceInfoEntities);
            }
        } catch (Exception e) {
            LogUtils.error(e, "回写设备状态异常, exceptionMsg={}", e.getMessage());
        }
        // TODO:导出的字段？
        List<DeviceInfoVo> result = deviceInfoEntities.stream()
                .map(e -> {
                    DeviceInfoVo vo = new DeviceInfoVo();
                    BeanUtils.copyProperties(e, vo);
                    return vo;
                })
                .collect(Collectors.toList());
        return result;
    }

    @Override
    public void exportSearchInfo(List<DeviceInfoVo> vo, HttpServletResponse response) {
        try {
            final String fileName = "deviceInfo-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM" +
                            "-dd HH:mm:ss"))
                    .replace(" ", "-").replace(":", "-") + ".csv";

            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(fileName,
                    StandardCharsets.UTF_8.name()));
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM.toString());

            String[] headers = new String[]{"设备名称", "设备sn", "产品ID", "产品型号代码", "设备ID", "软件版本号", "状态", "mac地址", "所在地",
                    "最近登陆IP", "注册时间"};
            List<Object[]> dataList = new ArrayList<>(vo.size());

            ServletOutputStream outputStream = response.getOutputStream();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            for (DeviceInfoVo entity : vo) {
                String[] data = new String[headers.length];
                int i = 0;
                data[i] = entity.getNickname();
                i++;
                data[i] = entity.getSn();
                i++;
                data[i] = entity.getProductId();
                i++;
                data[i] = entity.getProductModeCode();
                i++;
                data[i] = entity.getId();
                i++;
                data[i] = entity.getVersions();
                i++;
                data[i] = entity.isStatus() ? "在线" : "离线";
                i++;
                data[i] = entity.getMac();
                i++;
                data[i] = entity.getCity();
                i++;
                data[i] = entity.getIp();
                i++;
                data[i] = Objects.nonNull(entity.getCreateTime()) ? entity.getCreateTime().format(formatter) : "";
                dataList.add(data);
            }

            LogUtils.info("导出数据 {}", vo.size());
            outputStream.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});
            CSVUtils.writeCsv(headers, dataList, outputStream);

            response.getOutputStream().flush();
            response.getOutputStream().close();
        } catch (Exception e) {
            LogUtils.error(e, "exportSearchSn Exception");
            throw new AppRuntimeException(INNER_SERVER_ERROR);
        }

    }

    public String genNickName(String sn, ProductModeEntity productModeEntity) {
        String aliasPrefix = productModeEntity.getAliasPrefix();
        Integer snSuffixBit = productModeEntity.getSnSuffixBit();
        int snLength = sn.length();
        return aliasPrefix + (snLength <= snSuffixBit ? sn : (sn.substring(snLength - snSuffixBit)));
    }

    private void handleDeviceInfoStatus(List<DeviceInfoEntity> devices) {
        if (CollectionUtils.isEmpty(devices)) {
            return;
        }
        List<String> sns = devices.stream().map(m -> m.getSn()).distinct().collect(Collectors.toList());
        ResponseMessage<List<DeviceShadowEntity>> responseMessage = deviceShadowRemote.findAllBySN(sns);
        if (Objects.isNull(responseMessage) || !responseMessage.isSuccess()) {
            return;
        }
        List<DeviceShadowEntity> result = responseMessage.getResult();
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        Map<String, Boolean> snAndStatus =
                result.stream().filter(n -> Objects.nonNull(n.getOnlineStatus())).collect(Collectors.toMap(n -> n.getId(), m -> m.getOnlineStatus(), (o, n) -> n));
        devices.forEach((DeviceInfoEntity device) -> {
            if (snAndStatus.containsKey(device.getSn())) {
                device.setStatus(snAndStatus.get(device.getSn()));
            }
        });
        Map<String, Long> collect =
                result.stream().filter(t -> Objects.nonNull(t.getTimestamp())).collect(Collectors.toMap(DeviceShadowEntity::getId, v -> v.getTimestamp(), (o, n) -> n));
        devices.forEach((DeviceInfoEntity device) -> {
            if (collect.containsKey(device.getSn())) {
                device.setTimeStamp(collect.get(device.getSn()));
            }
        });
    }

    public LoginEntityVO newLoginEntity(DeviceInfoEntity info, String tenantId, CountryCity countryCity) {
        LoginEntityVO loginEntity = new LoginEntityVO();
        loginEntity.setId(info.getId());
        loginEntity.setClientTypeEnum(ClientType.ROBOT);
        loginEntity.putContextValue(ContextKey.TENANT_ID, tenantId);
        loginEntity.putContextValue(ContextKey.ROBOT_TYPE, applicationType);

        UserToken userToken = new UserToken();
        userToken.setValue(JsonUtils.toJSON(loginEntity));
        String token = TokenUtils.generateToken(userToken);

        loginEntity.putContextValue(ContextKey.SN, info.getSn());
        loginEntity.putContextValue(ContextKey.USERNAME, info.getSn());
        loginEntity.putContextValue(ContextKey.MAC, info.getMac());
        loginEntity.putContextValue(ContextKey.CONNECTION_TYPE, applicationType);
        loginEntity.putContextValue(ContextKey.PRODUCT_MODE_CODE, info.getProductModeCode());
        loginEntity.putContextValue(ContextKey.AUTH, token);
        loginEntity.putContextValue(ContextKey.EMQ_TOKEN, emqJwtUtil.generalToken(info.getSn(), info.getSn()));
        if (countryCity != null) {
            loginEntity.putContextValue(ContextKey.COUNTRY_CITY, countryCity);
        }
        return loginEntity;
    }

    /**
     * 待修改的设备昵称是否与其绑定用户下的设备昵称重复
     *
     * @param userId   用户id
     * @param deviceId 设备id
     * @param nickName 设备昵称
     * @return 是否重复
     */
    private boolean isNickNameRepeat(String userId, String deviceId, String nickName) {
        List<BindIdVo> bindIdVoList = deviceBindUserMapper.getBindListByUserIdList(SystemContextUtils.getTenantId(),Collections.singletonList(userId));
        //过滤掉当前修改的设备
        List<String> deviceIds = bindIdVoList.stream()
                .map(BindIdVo::getDeviceId)
                .filter(id -> !deviceId.equalsIgnoreCase(id))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deviceIds)) {
            return false;
        }
        ResponseMessage<List<DeviceInfoEntity>> deviceInfos = getDeviceInfosByIds(deviceIds);
        List<DeviceInfoEntity> deviceInfoEntityList = deviceInfos.getResult();
        if (CollectionUtils.isEmpty(deviceInfoEntityList)) {
            return false;
        }
        return deviceInfoEntityList.stream().anyMatch(t -> nickName.equalsIgnoreCase(t.getNickname()));
    }


    @Override
    public List<DeviceInfoCityVo> getCityBySns(String tenantId, List<String> sns) {
        List<DeviceInfoCityVo> deviceInfoCityVos = new ArrayList<>();
        try {
            InterceptorIgnoreHelper.handle(IgnoreStrategy.builder().tenantLine(true).build());
            LambdaQueryWrapper<DeviceInfoEntity> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.select(DeviceInfoEntity::getCity,DeviceInfoEntity::getSn,DeviceInfoEntity::getIp);
            queryWrapper.in(DeviceInfoEntity::getSn, sns);
            if(StringUtils.isNotBlank(tenantId)){
                queryWrapper.eq(DeviceInfoEntity::getTenantId,tenantId);
            }
            queryWrapper.orderByDesc((DeviceInfoEntity::getUpdateTime));
            List<DeviceInfoEntity> deviceInfoEntities = baseMapper.selectList(queryWrapper);

            if(CollectionUtils.isEmpty(deviceInfoEntities)){
                return List.of();
            }

            for(DeviceInfoEntity deviceInfo : deviceInfoEntities){
                DeviceInfoCityVo deviceInfoCityVo = new DeviceInfoCityVo();
                deviceInfoCityVo.setSn(deviceInfo.getSn());
                deviceInfoCityVo.setCity(deviceInfo.getCity());
                deviceInfoCityVo.setIp(deviceInfo.getIp());
                deviceInfoCityVos.add(deviceInfoCityVo);
            }
        }finally {
            InterceptorIgnoreHelper.clearIgnoreStrategy();
        }

        return deviceInfoCityVos;
    }
}