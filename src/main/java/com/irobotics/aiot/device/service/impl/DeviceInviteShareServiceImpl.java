package com.irobotics.aiot.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.Constants;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.basic.exception.AppRuntimeException;
import com.irobotics.aiot.bravo.commons.current.ThreadUtils;
import com.irobotics.aiot.bravo.commons.util.JsonUtils;
import com.irobotics.aiot.bravo.commons.util.SnowflakeUtil;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.bravo.web.util.OperatorUtils;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.common.DeviceResponseCode;
import com.irobotics.aiot.device.common.InviteShareEnum;
import com.irobotics.aiot.device.common.NumberEnum;
import com.irobotics.aiot.device.entity.*;
import com.irobotics.aiot.device.mapper.DeviceBindUserMapper;
import com.irobotics.aiot.device.mapper.DeviceInfoMapper;
import com.irobotics.aiot.device.mapper.DeviceInviteShareMapper;
import com.irobotics.aiot.device.remote.ProductServiceRemote;
import com.irobotics.aiot.device.remote.SmartHomeServiceRemote;
import com.irobotics.aiot.device.remote.TAuthUserRemote;
import com.irobotics.aiot.device.remote.model.ProductInfoEntity;
import com.irobotics.aiot.device.remote.model.TAuthClientUserExt;
import com.irobotics.aiot.device.service.CommonService;
import com.irobotics.aiot.device.service.IDeviceBindUserService;
import com.irobotics.aiot.device.service.IDeviceInfoService;
import com.irobotics.aiot.device.service.IDeviceInviteShareService;
import com.irobotics.aiot.device.vo.*;
import com.irobotics.aiot.device.vo.app.BindDeviceInfoVo;
import com.irobotics.aiot.encryption.util.AESUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.*;

/**
 * <p>
 * 设备分享邀请信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@SuppressWarnings("ALL")
@Service
public class DeviceInviteShareServiceImpl extends ServiceImpl<DeviceInviteShareMapper, DeviceInviteShareEntity> implements IDeviceInviteShareService {


    @Autowired
    private IDeviceInfoService deviceInfoService;

    @Autowired
    private DeviceInviteShareMapper deviceInviteShareMapper;

    @Autowired
    private DeviceInfoMapper deviceInfoMapper;

    @Autowired
    private DeviceBindUserMapper deviceBindUserMapper;

    @Autowired
    private TAuthUserRemote userRemote;

    @Autowired
    private ProductServiceRemote productServiceRemote;

    @Autowired
    private SmartHomeServiceRemote smartHomeServiceRemote;

    @Autowired
    private DeviceInfoCache deviceInfoCache;

    @Autowired
    private CommonService commonService;

    @Autowired
    private IDeviceBindUserService deviceBindUserService;


    /**
     * 分享设备给指定用户
     *
     * @param beInvited 被邀请者
     * @param inviterId 邀请者
     * @param inviter   邀请者
     * @param targetIds 设备ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseMessage<Boolean> inviteUserShareRobot(String beInvited, String inviterId, String inviter,
                                                         List<String> targetIds) {
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        // 通过被邀请者用户名获取用户信息
        ResponseMessage<TAuthClientUserInfo> beInvitedUserResponse = userRemote.getByUsername(tenantId, beInvited);
        if (!beInvitedUserResponse.isSuccess()) {
            LogUtils.error("远程调用通过用户名获取用户信息失败:{}，beInvited={}", beInvited, beInvitedUserResponse.getMsg());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, beInvitedUserResponse.getMsg());
        }
        TAuthClientUserInfo beInvitedUser = beInvitedUserResponse.getResult();
        if (beInvitedUser == null) {
            LogUtils.info("分享设备给指定用户，被分享的用户不存在，beInvited={}, tenantId={}", beInvited, tenantId);
            return ResponseMessage.buildFail(ResponseCode.USER_NOT_EXISTS, "用户不存在");
        }
        if (Objects.equals(inviterId, beInvitedUser.getId())) {
            return ResponseMessage.buildFail(DeviceResponseCode.SHARE_SELF, "自己不可共享自己");
        }
        // ==== region ====
        // 2024.6 处理卡赫新旧APP且不同场景同时存在，分享场景只能智能家居对智能家居，非智能家居对非智能家居；
        ResponseMessage<TAuthClientUserExt> beInviterUserExtRes = userRemote.getUserExtByUserId(tenantId,
                beInvitedUser.getId());
        if (!beInviterUserExtRes.isSuccess()) {
            LogUtils.error("远程调用通过用户ID获取用户信息EXT失败:{}，beInvited={}", beInvited, beInviterUserExtRes.getMsg());
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, beInviterUserExtRes.getMsg());
        }
        TAuthClientUserExt beInviterExt = beInviterUserExtRes.getResult();
        String sharerEnableSmartHome = SystemContextUtils.getContextValue(ContextKey.ENABLE_SMART_HOME);
        //1、被分享者从未登录不可以分享 2、被分享与分享者是否启用智能家居场景，不一致的，则不可分享
        if (beInviterExt == null || !String.valueOf(beInviterExt.getEnableSmartHome()).equals(sharerEnableSmartHome)) {
            return ResponseMessage.buildFail(NOT_FOUND_DATA, "分享失败，无效的被分享者");
        }
        // ==== endregion ====
        List<DeviceInfoEntity> deviceInfos = deviceInfoService.getByIds(tenantId, targetIds);
        if (CollectionUtils.isEmpty(deviceInfos)) {
            LogUtils.error("分享的设备都不存在，targetIds={},tenantId={},beInvited={},inviter={}", JsonUtils.toJSON(targetIds),
                    tenantId, beInvitedUser.getUsername(), inviter);
            return ResponseMessage.buildFail(NOT_FOUND_DATA, "分享的设备不存在");
        }
        List<String> collect = deviceInfos.stream().map(m -> m.getId()).collect(Collectors.toList());
        for (String deviceId : targetIds) {
            if (!collect.contains(deviceId)) {
                LogUtils.error("分享的设备不存在，deviceId={},tenantId={},beInvited={},inviter={}", deviceId, tenantId,
                        beInvitedUser.getUsername(), inviter);
                return ResponseMessage.buildFail(NOT_FOUND_DATA, "分享的设备中，存在非法设备");
            }
        }
        List<DeviceInviteShareEntity> insertList = new ArrayList<>(deviceInfos.size());
        List<PushInfo> list = new ArrayList<>(deviceInfos.size());
        for (DeviceInfoEntity deviceInfo : deviceInfos) {
            String shareId = SnowflakeUtil.snowflakeId();
            String nickName = StringUtils.isBlank(deviceInfo.getNickname()) ? deviceInfo.getSn() :
                    deviceInfo.getNickname();
            DeviceInviteShareEntity deviceInviteShareEntity =
                    new DeviceInviteShareEntity(shareId, inviterId, beInvitedUser.getId(),
                            deviceInfo.getId(), InviteShareEnum.DEVICE.getStatus(), InviteShareEnum.ENABLE.getStatus(),
                            OperatorUtils.getOperateBy(), OperatorUtils.getOperateBy());
            if (count(deviceInviteShareEntity, InviteShareEnum.DEVICE.getStatus(),
                    InviteShareEnum.REJECTED.getStatus(), true) > 0) {
                LogUtils.error("设备【" + nickName + "】已共享给【" + (StringUtils.isNotBlank(beInvitedUser.getNickname()) ?
                        beInvitedUser.getNickname() : beInvitedUser.getUsername()) + "】，不可重复共享");
                return ResponseMessage.buildFail(DeviceResponseCode.DEVICE_ALREADY_SHARED);
            } else {
                // 整合需要放进极光推送的额外字段里的信息
                PushInfo info = new PushInfo(deviceInfo.getId(), shareId);
                info.setSn(deviceInfo.getSn());
                // 补充产品图片
                ResponseMessage<List<ProductInfoEntity>> res =
                        productServiceRemote.getListByProductIds(List.of(deviceInfo.getProductId()));
                if (res.isSuccess() && !CollectionUtils.isEmpty(res.getResult())) {
                    info.setDevicePhoto(res.getResult().get(0).getPhotoUrl());
                }
                list.add(info);
                insertList.add(deviceInviteShareEntity);
            }
        }
        if (!CollectionUtils.isEmpty(insertList)) {
            this.saveBatch(insertList);
        }
        // 发送极光推送
        JPushShareReq jPushShareReq = new JPushShareReq(InviteShareEnum.DEVICE.getMsg(), list,
                InviteShareEnum.DEVICE.getStatus(), InviteShareEnum.ENABLE.getStatus(), beInvitedUser.getId());
        commonService.pushShare(jPushShareReq);
        return ResponseMessage.buildSuccess(true);
    }


    /**
     * 判断的邀请信息是否存在
     *
     * @param shareEntity
     * @param type        类型，设备或家庭
     * @param status      状态，拒绝，正常、已接受
     * @param ne          是否不等于 not equals
     * @return
     */
    private int count(DeviceInviteShareEntity shareEntity, int type, int status, boolean ne) {
        QueryWrapper<DeviceInviteShareEntity> wrapper = new QueryWrapper<>();
        LambdaQueryWrapper<DeviceInviteShareEntity> lambda = wrapper.lambda();
        lambda.eq(DeviceInviteShareEntity::getInviteId, shareEntity.getInviteId())
                .eq(DeviceInviteShareEntity::getBeInviteId, shareEntity.getBeInviteId())
                .eq(DeviceInviteShareEntity::getTargetId, shareEntity.getTargetId())
                .eq(DeviceInviteShareEntity::getType, type);
        if (ne) {
            lambda.ne(DeviceInviteShareEntity::getStatus, status);
        } else {
            lambda.eq(DeviceInviteShareEntity::getStatus, status);
        }
        return deviceInviteShareMapper.selectCount(wrapper).intValue();
    }

    /**
     * 回应用户的分享
     *
     * @param type            类型，0-共享设备，1-共享家庭
     * @param beInvitedUserId 被邀请的用户id
     * @param inviterId       邀请者的用户id
     * @param targetId        设备id
     * @param dealType        回应类型 1-同意，2-拒绝
     * @return
     */
    @Override
    public boolean replyShared(int type, String familyId, String familyName, String beInvitedUserId, String inviterId
            , String targetId, int dealType) {
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        DeviceInfoEntity deviceInfo = null;
        // 提前判断设备是否存在
        // 如果分享之后，在用户回应前。那么用户分享到回应之间的时间段，存在设备被初始化的情况（几率极小），因此先判断设备是否存在
        if (InviteShareEnum.DEVICE.getStatus() == type) {
            deviceInfo = deviceInfoCache.getCacheById(tenantId, targetId);
            if (Objects.isNull(deviceInfo)) {
                LogUtils.error("回应分享时，设备不存在, type={}, beInvitedUserId={}, inviterId={}, targetId={}, delType={}",
                        type, beInvitedUserId, inviterId, targetId, dealType);
                throw new AppRuntimeException(NOT_FOUND_DATA, "设备不存在");
            }
        }
        QueryWrapper<DeviceInviteShareEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(DeviceInviteShareEntity::getInviteId, inviterId)
                .eq(DeviceInviteShareEntity::getBeInviteId, beInvitedUserId)
                .eq(DeviceInviteShareEntity::getTargetId, targetId)
                .eq(DeviceInviteShareEntity::getType, type)
                .eq(DeviceInviteShareEntity::getStatus, InviteShareEnum.ENABLE.getStatus());
        DeviceInviteShareEntity inviteShare = deviceInviteShareMapper.selectOne(wrapper);
        if (inviteShare == null) {
            return false;
        }
        int status = 1;
        switch (dealType) {
            case 1: // 同意
                status = InviteShareEnum.AGREED.getStatus();
                // 共享设备
                if (InviteShareEnum.DEVICE.getStatus() == type) {
                    // 检测设备是否还属于共享者
                    List<BindDeviceInfoVo> deviceInfoAndBindByUserId =
                            deviceBindUserMapper.getDeviceInfoAndBindByUserId(inviterId);
                    if(deviceInfoAndBindByUserId.stream()
                            .noneMatch(b->targetId.equals(b.getDeviceId()))){
                        // 更新分享记录，否则用户该条分享记录一直显示为可接受
                        DeviceInviteShareEntity update = new DeviceInviteShareEntity();
                        update.setId(inviteShare.getId());
                        update.setStatus(status);
                        deviceInviteShareMapper.updateById(update);
                        // 抛出该异常处理主要为了APP不用发版可以共用异常通知
                        // FIXME: 【业务流程优化】当前方法暂未启用事务，如有事务，且未过滤该异常，此处抛出异常会回滚导致邀请记录状态更新失败。
                        throw new AppRuntimeException(NOT_FOUND_DATA, "设备不存在");
                    }
                    doBindRoom(familyId, beInvitedUserId, targetId, inviterId, deviceInfo.getNickname(),
                            deviceInfo.getSn(), deviceInfo.getProductId());
                } else if (InviteShareEnum.FAMILY.getStatus() == type) {
                    doBindFamily(dealType, targetId, inviterId, beInvitedUserId);
                } else {
                    throw new IllegalArgumentException("type:" + type);
                }
                break;
            case 2: // 拒绝
                inviteShare.setStatus(InviteShareEnum.REJECTED.getStatus());
                status = InviteShareEnum.REJECTED.getStatus();
                doBindFamily(dealType, targetId, inviterId, beInvitedUserId);
                break;
            default:
                throw new IllegalArgumentException("dealType:" + dealType);
        }

        // 更新分享记录
        UpdateWrapper<DeviceInviteShareEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(DeviceInviteShareEntity::getId, inviteShare.getId())
                .set(DeviceInviteShareEntity::getStatus, status);
        try {
            deviceInviteShareMapper.update(null, updateWrapper);
        } catch (Exception e) {
            LogUtils.error(e, "更新分享记录失败，beInvitedUserId={}, inviterId={}, targetId={}, delType={}, tenantId={}, " +
                            "exceptionMsg={]",
                    beInvitedUserId, inviterId, targetId, dealType,
                    SystemContextUtils.getContextValue(ContextKey.TENANT_ID), e.getMessage());
        }
        // 整合极光推送的额外信息
        PushInfo info = new PushInfo(targetId, inviteShare.getId());
        if (InviteShareEnum.DEVICE.getStatus() == type) {
            info.setSn(deviceInfo.getSn());
        } else if (InviteShareEnum.FAMILY.getStatus() == type) {
            info.setFamilyName(familyName);
        }
        // 发送极光消息
        List<PushInfo> list = new ArrayList<>();
        list.add(info);
        // 被邀请者呼应分享，则发送极光消息给邀请者
        JPushShareReq req = new JPushShareReq(InviteShareEnum.DEVICE.getStatus() == type ?
                InviteShareEnum.DEVICE.getMsg() : InviteShareEnum.FAMILY.getMsg(),
                list, type, status, inviterId);
        // 会更新极光推送的消息状态
        commonService.pushShare(req);
        return true;
    }

    /**
     * 通过用户名获取分享信息
     *
     * @param userId
     * @param inviter
     * @return
     */
    @Override
    public Page<DeviceShareVo> getAllSharedList(GetUserSharedReq req) {
        String userId = SystemContextUtils.getId();
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        String targetId = req.getTargetId();
        boolean inviter = req.isInviter();
        Page page = new Page(req.getPage(), req.getPageSize());
        Page<DeviceShareVo> hisPage = null;
        if (inviter) {
            // 分享者调用获取分享信息列表
            // 当分享者获取分享列表时，获取的是被分享者的用户id和用户名
            hisPage = deviceInviteShareMapper.getListByInviter(page, userId, targetId);
        } else {
            // 接受分享方获取分享列表
            // 获取分享者的用户id和用户名
            hisPage = deviceInviteShareMapper.getListByBeInviter(page, userId, targetId);
        }
        if (Objects.isNull(hisPage) || CollectionUtils.isEmpty(hisPage.getRecords())) {
            return hisPage;
        }
        handleShare(hisPage.getRecords(), userId, inviter, tenantId);
        return hisPage;
    }

    private void handleShare(List<DeviceShareVo> list, String userId, boolean inviter, String tenantId) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 指定三个子线程
        CountDownLatch countDownLatch = new CountDownLatch(NumberEnum.THREE.getNumber());
        // 当分享者获取分享列表时，获取的是被分享者的用户id和用户名
        ThreadUtils.execute(() -> {
            handleUserInfo(list, tenantId, userId, inviter);
            countDownLatch.countDown();
        });

        // 获取产品型号名称label
        ThreadUtils.execute(() -> {
            handleProductInfo(list, tenantId, userId, inviter);
            countDownLatch.countDown();
        });
        // 补充家庭信息
        // 通过家庭id列表获取家庭列表
        ThreadUtils.execute(() -> {
            List<String> familyIds =
                    list.stream().filter(f -> f.getType() == InviteShareEnum.FAMILY.getStatus()).map(m -> m.getTargetId()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(familyIds)) {
                countDownLatch.countDown();
                return;
            }
            ResponseMessage<List<FamilyInfoEntity>> response = smartHomeServiceRemote.getFamilyInfoByIds(familyIds);
            if (!response.isSuccess()) {
                LogUtils.error("获取分享信息历史，远程调用获取家庭信息失败, tenantId={}, familyIds={}, exceptionMsg={}",
                        SystemContextUtils.getContextValue(ContextKey.TENANT_ID), familyIds, response.getMsg());
            }
            List<FamilyInfoEntity> result = response.getResult();
            if (CollectionUtils.isEmpty(result)) {
                countDownLatch.countDown();
                return;
            }
            Map<String, String> idAndName = result.stream().collect(Collectors.toMap(n -> n.getId(),
                    m -> m.getFamilyName()));
            list.forEach((DeviceShareVo item) -> {
                if (idAndName.containsKey(item.getTargetId()) && InviteShareEnum.FAMILY.getStatus() == item.getType()) {
                    item.setName(idAndName.get(item.getTargetId()));
                }
            });
            countDownLatch.countDown();
        });
        try {
            // 等待
            countDownLatch.await();
        } catch (InterruptedException e) {
            LogUtils.error("分享历史信息补充异常, exceptionMsg={}", e.getMessage());
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 删除分享信息
     *
     * @param shareId 主键id
     * @param userId  用户id
     * @return
     */
    @Override
    public boolean cancelShare(String shareId, String userId) {
        QueryWrapper<DeviceInviteShareEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DeviceInviteShareEntity::getId, shareId);
        DeviceInviteShareEntity inviteShare = deviceInviteShareMapper.selectOne(wrapper);
        LogUtils.info("获取的inviteShare:{}", inviteShare);
        if (inviteShare == null) {
            throw new AppRuntimeException(NOT_FOUND_DATA);
        }
        boolean owner = false;
        if (Objects.equals(userId, inviteShare.getInviteId())) {
            owner = true;
        }
        int type = inviteShare.getType();
        int status = inviteShare.getStatus();
        int del = deviceInviteShareMapper.deleteById(shareId);
        LogUtils.info("删除分享信息结果del:{},shareId:{}", del, shareId);
        // 设备共享的，已经删除成功，并且不是拒绝状态的设备共享，才解除设备与房间的绑定
        if ((type == InviteShareEnum.DEVICE.getStatus()) && del > 0 && InviteShareEnum.REJECTED.getStatus() != inviteShare.getStatus()) {
            boolean res = unShareRoom(inviteShare.getTargetId(), inviteShare.getInviteId(),
                    inviteShare.getBeInviteId(), owner);
            if (owner) {
                commonService.shareInvalidate(inviteShare.getTargetId(), inviteShare.getBeInviteId(), res);
            }
            return res;
        }
        // 只有（正常或者同意）的并且是共享家庭的，才进行远程调用
        if ((type == InviteShareEnum.FAMILY.getStatus()) && del > 0
                && (status == InviteShareEnum.ENABLE.getStatus() || status == InviteShareEnum.AGREED.getStatus())) {
            RevokeDto revokeDto = new RevokeDto(inviteShare.getBeInviteId(), inviteShare.getTargetId(),
                    inviteShare.getInviteId());
            ResponseMessage<Boolean> revoke = smartHomeServiceRemote.revoke(revokeDto);
            if (!revoke.isSuccess()) {
                LogUtils.error("共享家庭撤销失败，beinviteId={}, familyId={}, inviteId={}, exceptionMsg={}",
                        inviteShare.getBeInviteId(), inviteShare.getTargetId(), inviteShare.getInviteId(),
                        revoke.getMsg());
                return false;
            }
            // 撤销家庭分享，需要把极光推送的消息删除
            // ResponseMessage<Boolean> deleteMessage = commonService.delByShareId(inviteShare.getTenantId(),
            //         inviteShare.getId());
            // if (!deleteMessage.isSuccess()) {
            //     LogUtils.error("删除极光推送记录失败");
            //     return false;
            // }
            // owner推送消息给所有正在使用的被分享者
            if (owner) {
                // 使分享失效，
                commonService.shareInvalidate(inviteShare.getTargetId(), inviteShare.getBeInviteId(),
                        revoke.getResult());
            }
            return true;
        }
        LogUtils.error("删除分享异常，更新异常, shareId={}, userId={}", shareId, userId);
        return false;
    }

    @Override
    public ResponseMessage<Boolean> delShare(DelShareReq req) {
        if (Objects.isNull(req)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL);
        }
        String userId = SystemContextUtils.getId();
        List<String> deviceIds = req.getDeviceId();
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "tenantId");
        }
        boolean inviter = req.isInviter();
        if (CollectionUtils.isEmpty(deviceIds)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "deviceId");
        }
        UpdateWrapper<DeviceInviteShareEntity> wrapper = new UpdateWrapper<>();
        LambdaUpdateWrapper<DeviceInviteShareEntity> lambda = wrapper.lambda();
        if (inviter) {
            lambda.eq(DeviceInviteShareEntity::getInviteId, userId);
            lambda.set(DeviceInviteShareEntity::getRemoved, InviteShareEnum.DISABLE.getStatus());
        } else {
            lambda.eq(DeviceInviteShareEntity::getBeInviteId, userId);
            lambda.set(DeviceInviteShareEntity::getRemoved, InviteShareEnum.REMOVED.getStatus());
        }
        lambda.in(DeviceInviteShareEntity::getTargetId, deviceIds);
        int delete = deviceInviteShareMapper.delete(wrapper);
        return ResponseMessage.buildSuccess(delete > 0);
    }

    @Override
    public ResponseMessage<Page<DeviceShareHisVo>> getShareHis(UserSharedHisReq req) {
        String userId = SystemContextUtils.getId();
        String tenantId = SystemContextUtils.getTenantId();

        Page<DeviceShareHisVo> hisPage = deviceInviteShareMapper.getDeviceShareHis(new Page<>(req.getPage(),
                req.getPageSize()), userId, req.getTargetId(), req.getType());
        if (Objects.isNull(hisPage) || CollectionUtils.isEmpty(hisPage.getRecords())) {
            return ResponseMessage.buildSuccess(hisPage);
        }
        List<DeviceShareHisVo> his = hisPage.getRecords();

        handleShareHis(his, userId, tenantId);
        return ResponseMessage.buildSuccess(hisPage);
    }

    @Override
    public ResponseMessage<List<String>> doShareFamily(ShareFamilyVo vo) {
        if (Objects.isNull(vo)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL);
        }
        String inviterId = SystemContextUtils.getId();
        String targetId = vo.getTargetId();
        List<String> beInvitedIds = vo.getBeInviteIds();
        String familyName = vo.getFamilyName();
        if (StringUtils.isBlank(targetId)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "targetId");
        }
        if (CollectionUtils.isEmpty(beInvitedIds)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "未选中成员");
        }
        List<DeviceInviteShareEntity> insertList = new ArrayList<>(beInvitedIds.size());
        List<Map<String, String>> list = new ArrayList<>(beInvitedIds.size());
        List<String> shareIdList = new ArrayList<>(beInvitedIds.size());
        for (String beInviteId : beInvitedIds) {
            String id = SnowflakeUtil.snowflakeId();
            DeviceInviteShareEntity deviceInviteShareEntity =
                    new DeviceInviteShareEntity(id, inviterId, beInviteId,
                            targetId, InviteShareEnum.FAMILY.getStatus(), InviteShareEnum.ENABLE.getStatus(),
                            OperatorUtils.getOperateBy(), OperatorUtils.getOperateBy());
            // 检查是否已经邀请了，并且是正常状态
            if (count(deviceInviteShareEntity, InviteShareEnum.FAMILY.getStatus(), InviteShareEnum.ENABLE.getStatus()
                    , false) > 0) {
                continue;
            } else {
                Map<String, String> map = new HashMap<>(NumberEnum.TWO.getNumber());
                map.put("to", beInviteId);
                map.put("shareId", id);
                list.add(map);
                // 分享id
                shareIdList.add(deviceInviteShareEntity.getId());
                insertList.add(deviceInviteShareEntity);
            }
        }

        if (!CollectionUtils.isEmpty(insertList)) {
            this.saveBatch(insertList);
            // 发送极光消息
            JPushFamilyShareReq jPushFamilyShareReq = new JPushFamilyShareReq(InviteShareEnum.FAMILY.getMsg(),
                    InviteShareEnum.FAMILY.getStatus(), InviteShareEnum.ENABLE.getStatus(),
                    targetId, familyName, list);
            commonService.pushFamilyShare(jPushFamilyShareReq);
            return ResponseMessage.buildSuccess(shareIdList);
        } else {
            return ResponseMessage.buildSuccess(shareIdList);
        }
    }

    @Override
    public boolean changeStatus(String familyName, String familyId, String shareId, Integer status) {
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        QueryWrapper<DeviceInviteShareEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DeviceInviteShareEntity::getId, shareId);
        DeviceInviteShareEntity inviteShareEntity = deviceInviteShareMapper.selectOne(wrapper);
        if (Objects.isNull(inviteShareEntity)) {
            return false;
        }
        // 如果分享状态为同意或拒绝，直接返回false
        if (Objects.equals(inviteShareEntity.getStatus(), InviteShareEnum.REJECTED.getStatus()) ||
                Objects.equals(inviteShareEntity.getStatus(), InviteShareEnum.AGREED.getStatus())) {
            return false;
        }
        int type = inviteShareEntity.getType();
        String beinvitedId = inviteShareEntity.getBeInviteId();
        String inviterId = inviteShareEntity.getInviteId();
        String targetId = inviteShareEntity.getTargetId();

        DeviceInfoEntity deviceInfo = null;
        // 断设备是否存在
        // 如果分享之后，在用户回应钱。那么用户分享到回应之间的时间段，存在设备被初始化的情况（几率极小），因此先判断设备是否存在
        if (InviteShareEnum.DEVICE.getStatus() == type) {
            deviceInfo = deviceInfoCache.getCacheById(tenantId, targetId);
            if (Objects.isNull(deviceInfo)) {
                LogUtils.error("通过分享消息同意或拒绝分享时，设备不存在, type={}, beInvitedUserId={}, inviterId={}, targetId={}, " +
                                "status={}",
                        type, beinvitedId, inviterId, targetId, status);
                throw new AppRuntimeException(NOT_FOUND_DATA, "设备不存在");
            }
        }

        switch (status) {
            // 同意
            case 1:
                // 设备的分享
                if (InviteShareEnum.DEVICE.getStatus() == type) {
                    // 检测设备是否还属于共享者
                    List<BindDeviceInfoVo> deviceInfoAndBindByUserId =
                            deviceBindUserMapper.getDeviceInfoAndBindByUserId(inviterId);
                    if(deviceInfoAndBindByUserId.stream()
                            .noneMatch(b->targetId.equals(b.getDeviceId()))){
                        // 更新分享记录，否则用户看到该条分享记录一直显示为可接受
                        DeviceInviteShareEntity update = new DeviceInviteShareEntity();
                        update.setId(shareId);
                        update.setStatus(status);
                        deviceInviteShareMapper.updateById(update);
                        // 抛出该异常处理主要为了APP不用发版可以共用异常通知
                        // FIXME: 【业务流程优化】当前方法暂未启用事务，如有事务，且未过滤该异常，此处抛出异常会回滚导致邀请记录状态更新失败。
                        throw new AppRuntimeException(NOT_FOUND_DATA, "设备不存在");
                    }
                    // 智能家居开关
                    doBindRoom(familyId, beinvitedId, targetId, inviterId, deviceInfo.getNickname(),
                            deviceInfo.getSn(), deviceInfo.getProductId());
                    // 家庭的分享
                } else if (InviteShareEnum.FAMILY.getStatus() == type) {
                    doBindFamily(status, targetId, inviterId, beinvitedId);
                } else {
                    throw new IllegalArgumentException("type:" + type);
                }
                break;
            // 拒绝
            case 2:
                inviteShareEntity.setStatus(InviteShareEnum.REJECTED.getStatus());
                doBindFamily(status, targetId, inviterId, beinvitedId);
                break;
            default:
                throw new IllegalArgumentException("status: " + status);
        }
        // 更新分享记录
        UpdateWrapper<DeviceInviteShareEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(DeviceInviteShareEntity::getId, inviteShareEntity.getId())
                .set(DeviceInviteShareEntity::getStatus, status);
        try {
            deviceInviteShareMapper.update(null, updateWrapper);
        } catch (Exception e) {
            LogUtils.error(e, "更新分享记录失败，beInvitedId={}, inviterId={}, targetId={}, status={}, tenantId={}, " +
                            "exceptionMsg={]",
                    beinvitedId, inviterId, targetId, status,
                    SystemContextUtils.getContextValue(ContextKey.TENANT_ID), e.getMessage());
        }
        // 整合推送的额外信息
        PushInfo info = new PushInfo(targetId, shareId);
        if (InviteShareEnum.DEVICE.getStatus() == type) {
            info.setSn(deviceInfo.getSn());
        } else if (InviteShareEnum.FAMILY.getStatus() == type) {
            info.setFamilyName(familyName);
        }
        // 发送极光消息
        List<PushInfo> list = new ArrayList<>();
        list.add(info);
        // 被分享者
        JPushShareReq req = new JPushShareReq(InviteShareEnum.DEVICE.getStatus() == type ?
                InviteShareEnum.DEVICE.getMsg() : InviteShareEnum.FAMILY.getMsg(),
                list, type, status, inviterId);
        commonService.pushShare(req);
        return true;
    }

    @Override
    public boolean delByDeviceId(String deviceId) {
        return deviceInviteShareMapper.deleteByDeviceId(deviceId) > 0;
    }

    /**
     * 根据用户id和企业id删除分享记录，用于用户注销
     *
     * @param tenantId
     * @param userId
     * @return
     */
    @Override
    public boolean delByUserId(String tenantId, String userId) {
        int delete = deviceInviteShareMapper.deleteByUserId(tenantId, userId);
        return delete > 0;
    }

    @Override
    public ResponseMessage<Boolean> changeShareRecordStatus(ShareRecordChangeStatusReq req) {
        String shareId = req.getShareId();
        String recordId = req.getRecordId();
        Integer status = req.getStatus();
        String familyId = req.getFamilyId();
        String familyName = req.getFamilyName();
        if (StringUtils.isBlank(shareId)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "shareId");
        }
        if (StringUtils.isBlank(recordId)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "recordId");
        }
        ResponseMessage<NoticeShareRecordEntity> responseMessage = commonService.checkShareRecord(recordId);
        if (!responseMessage.isSuccess()) {
            LogUtils.error("通过分享记录id获取分享记录信息失败，recordId={}", recordId);
            return ResponseMessage.buildFail(INNER_SERVER_ERROR);
        }
        NoticeShareRecordEntity one = responseMessage.getResult();
        if (Objects.isNull(one)) {
            return ResponseMessage.buildSuccess(false);
        }
        if (Objects.isNull(status) || (!Objects.equals(status, InviteShareEnum.AGREED.getStatus()) && !Objects.equals(InviteShareEnum.REJECTED.getStatus(), status))) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "status 非法");
        }
        /*if (Objects.equals(InviteShareEnum.DEVICE.getStatus(), one.getType()) && StringUtils.isBlank(familyId)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "familyId");
        }*/
        if (Objects.equals(InviteShareEnum.FAMILY.getStatus(), one.getType()) && StringUtils.isBlank(familyName)) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "familyName");
        }
        boolean result = changeStatus(familyName, familyId, shareId, status);
        return ResponseMessage.buildSuccess(result);
    }

    @Override
    public void delByInvitee(String userId, String deviceId, InviteShareEnum inviteShareEnum) {
        super.remove(lambdaQuery().eq(DeviceInviteShareEntity::getBeInviteId, userId)
                .eq(DeviceInviteShareEntity::getTargetId, deviceId)
                .eq(DeviceInviteShareEntity::getType, inviteShareEnum.getStatus()).getWrapper());
    }

    /**
     * 处理分享历史信息，把用户信息补充、图片补充、家庭信息补充
     *
     * @param his      分享历史信息
     * @param userId   当前用户的用户id
     * @param tenantId 企业用户id
     */
    private void handleShareHis(List<DeviceShareHisVo> his, String userId, String tenantId) {
        if (CollectionUtils.isEmpty(his)) {
            return;
        }
        // 用户信息补充
        Set<String> set = new HashSet<>();
        for (DeviceShareHisVo vo : his) {
            set.add(vo.getInviteId());
            set.add(vo.getBeInviteId());
        }
        CountDownLatch countDownLatch = new CountDownLatch(NumberEnum.THREE.getNumber());
        ThreadUtils.execute(() -> {
            List<String> userIds = set.stream().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(userIds)) {
                ResponseMessage<List<TAuthClientUserInfo>> response = userRemote.getListByIds(tenantId, userIds);
                if (!response.isSuccess()) {
                    LogUtils.error("获取分享信息历史，远程调用获取用户信息失败, tenantId={}, userIds={}, exceptionMsg={}", tenantId,
                            userIds, response.getMsg());
                } else {
                    List<TAuthClientUserInfo> userInfos = response.getResult();
                    if (!CollectionUtils.isEmpty(userInfos)) {
                        Map<String, String> userIdAndUserNameAndUrl =
                                userInfos.stream().collect(Collectors.toMap(m -> m.getId(),
                                        n -> n.getUsernameEnc() + "," + (StringUtils.isBlank(n.getAvatarUrl()) ? "" :
                                                n.getAvatarUrl())));
                        his.forEach((DeviceShareHisVo item) -> {
                            // 被邀请者，则显示邀请者的用户名与头像
                            if (Objects.equals(userId, item.getBeInviteId())) {
                                String usernameAndUrl = userIdAndUserNameAndUrl.get(item.getInviteId());
                                if (StringUtils.isNotBlank(usernameAndUrl)) {
                                    String[] split = usernameAndUrl.split(",");
                                    // 密文的用户名
                                    item.setUsername(split[0]);
                                    if (split.length >= NumberEnum.TWO.getNumber() && StringUtils.isNotBlank(split[1]) && item.getType() == InviteShareEnum.FAMILY.getStatus()) {
                                        // 密文的头像信息
                                        if (StringUtils.isNotBlank(split[1])) {
                                            try {
                                                item.setPhotoUrl(AESUtil.Encrypt(split[1], tenantId));
                                            } catch (Exception e) {
                                                LogUtils.error(e, "aes加密失败, user avatarUrl={}, exceptionMsg={}",
                                                        split[1], e.getMessage());
                                            }
                                        } else {
                                            item.setPhotoUrl(split[1]);
                                        }
                                    }
                                }
                            }
                            // 邀请者，则显示被邀者的用户名与头像
                            if (Objects.equals(userId, item.getInviteId())) {
                                String usernameAndUrl = userIdAndUserNameAndUrl.get(item.getBeInviteId());
                                if (StringUtils.isNotBlank(usernameAndUrl)) {
                                    String[] split = usernameAndUrl.split(",");
                                    // 密文的用户名
                                    item.setUsername(split[0]);
                                    if (split.length >= NumberEnum.TWO.getNumber() && item.getType() == InviteShareEnum.FAMILY.getStatus()) {
                                        // 密文的头像信息
                                        if (StringUtils.isNotBlank(split[1])) {
                                            try {
                                                item.setPhotoUrl(AESUtil.Encrypt(split[1], tenantId));
                                            } catch (Exception e) {
                                                LogUtils.error(e, "aes加密失败, user avatarUrl={}, exceptionMsg={}",
                                                        split[1], e.getMessage());
                                            }
                                        } else {
                                            item.setPhotoUrl(split[1]);
                                        }
                                    }
                                }
                            }
                        });
                    }
                }
            }
            countDownLatch.countDown();
        });
        // 产品信息补充以及图片补充
        ThreadUtils.execute(() -> {
            List<String> productModeCodes =
                    his.stream().map(m -> m.getProductModeCode()).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(productModeCodes)) {
                ResponseMessage<List<ProductModeEntity>> responseMessage =
                        productServiceRemote.getListByCode(tenantId, productModeCodes);
                if (!responseMessage.isSuccess()) {
                    LogUtils.error("获取分享信息历史，远程调用获取产品型号信息失败, tenantId={}, userId={}, exceptionMsg={}", tenantId,
                            userId, responseMessage.getMsg());
                } else {
                    List<ProductModeEntity> result = responseMessage.getResult();
                    Map<String, ProductModeEntity> codeAndLabel =
                            result.stream().collect(Collectors.toMap(ProductModeEntity::getCode, Function.identity()));
                    his.forEach((DeviceShareHisVo item) -> {
                        if (codeAndLabel.containsKey(item.getProductModeCode())) {
                            ProductModeEntity productModeEntity = codeAndLabel.get(item.getProductModeCode());
                            if (Objects.nonNull(productModeEntity)) {
                                if (StringUtils.isNotBlank(productModeEntity.getLabel())) {
                                    item.setModeType(productModeEntity.getLabel());
                                }
                                if (StringUtils.isNotBlank(productModeEntity.getPhotoUrl())) {
                                    item.setPhotoUrl(productModeEntity.getPhotoUrl());
                                }
                                if (StringUtils.isNotBlank(productModeEntity.getProductInfoId())) {
                                    item.setProductId(productModeEntity.getProductInfoId());
                                }
                                item.setProductName(productModeEntity.getProductName());
                            }
                        }
                    });
                }
            }
            countDownLatch.countDown();
        });
        // 家庭信息补充
        ThreadUtils.execute(() -> {
            getFamilyShare(his);
            countDownLatch.countDown();
        });
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            LogUtils.error(e, "获取分享历史时，补充分享信息异常，exceptionMsg={}", e.getMessage());
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 分享历史补充家庭信息
     *
     * @param his
     */
    private void getFamilyShare(List<DeviceShareHisVo> his) {
        if (CollectionUtils.isEmpty(his)) {
            return;
        }
        // 通过家庭id列表获取家庭列表
        List<String> familyIds = his.stream().filter(f -> Objects.equals(InviteShareEnum.FAMILY.getStatus(),
                f.getType())).map(m -> m.getTargetId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(familyIds)) {
            return;
        }
        ResponseMessage<List<FamilyInfoEntity>> response = smartHomeServiceRemote.getFamilyInfoByIds(familyIds);
        if (!response.isSuccess()) {
            LogUtils.error("获取分享信息历史，远程调用获取家庭信息失败, tenantId={}, familyIds={}, exceptionMsg={}",
                    SystemContextUtils.getContextValue(ContextKey.TENANT_ID), familyIds, response.getMsg());
        }
        List<FamilyInfoEntity> result = response.getResult();
        if (Objects.isNull(result)) {
            return;
        }
        Map<String, String> idAndName = result.stream().collect(Collectors.toMap(n -> n.getId(),
                m -> m.getFamilyName()));
        his.forEach((DeviceShareHisVo item) -> {
            if (idAndName.containsKey(item.getTargetId()) && InviteShareEnum.FAMILY.getStatus() == item.getType()) {
                item.setName(idAndName.get(item.getTargetId()));
            }
        });
    }


    /**
     * 解除设备与房间的绑定
     *
     * @param deviceId  设备id
     * @param inviter   邀请者
     * @param beInviter 被邀请者
     * @return
     */
    @SuppressWarnings("Duplicates")
    private boolean unShareRoom(String deviceId, String inviter, String beInviter, boolean owner) {
        // region 202406 涉及智能家居与非智能家居转换场景，此处取消分享，应该确认被分享者当前所处的场景
        // 并且以被分享者的场景判断走智能家居 取消分享 还是 非智能家居 取消分享
        ResponseMessage<TAuthClientUserExt> beInviterUserExtRes =
                userRemote.getUserExtByUserId(SystemContextUtils.getTenantId(), beInviter);
        if (!beInviterUserExtRes.isSuccess()) {
            LogUtils.error("远程调用通过用户ID获取用户信息EXT失败:{}，beInvited={}",
                    beInviter, beInviterUserExtRes.getMsg());
            return false;
        }
        // endregion
        // 智能家居开关
        if (Constants.openSmartHome.equals(String.valueOf(beInviterUserExtRes.getResult().getEnableSmartHome()))) {
            // 远程调用，删除对应的绑定关系
            ResponseMessage<Boolean> responseMessage =
                    smartHomeServiceRemote.userUntieDevice(new UserUniteShareDeviceReq(deviceId, inviter, beInviter,
                            owner));
            if (!responseMessage.isSuccess()) {
                LogUtils.error("删除共享消息时，删除对应的绑定关系失败，deviceId={}, inviter={}, owner={}, exceptionMsg={}",
                        deviceId, inviter, beInviter, owner, responseMessage.getMsg());
                return false;
            }
            return true;
        }
        return deviceBindUserService.untieShareBind(deviceId, beInviter);
    }

    private boolean doBindRoom(String familyId, String beInvitedUserId, String deviceId, String owner,
                               String nickname, String sn, String productInfoId) {
        // 智能家居开关
        if (Constants.openSmartHome.equals(SystemContextUtils.getContextValue(ContextKey.ENABLE_SMART_HOME))) {
            if (StringUtils.isBlank(familyId)) {
                throw new AppRuntimeException(PARAM_VALIDATE_FAIL, "familyId");
            }
            // 远程调用smart-home-service服务
            String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
            ResponseMessage<Boolean> responseMessage =
                    smartHomeServiceRemote.bindShareDevice(new RoomBindShareDeviceReq(beInvitedUserId, familyId,
                            deviceId, nickname, sn, owner, productInfoId));
            if (!responseMessage.isSuccess()) {
                LogUtils.error("接受分享时，绑定设备异常，tenantId={}, familyId={}, beInviterId={}, deviceId={}, owner={}, " +
                                "nickname={}, msg={}",
                        tenantId, familyId, beInvitedUserId, deviceId, owner, nickname, responseMessage.getMsg());
                throw new AppRuntimeException(INNER_SERVER_ERROR, "设备分享异常");
            }
            return responseMessage.getResult();
        } else {
            return deviceBindUserService.shareBind(deviceId, nickname, sn, productInfoId, owner);
        }
    }

    private boolean doBindFamily(int status, String targetId, String inviteId, String beInviteId) {
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        ShareFamilyReplyReq shareFamilyReply = new ShareFamilyReplyReq(targetId, inviteId, beInviteId, status,
                tenantId);
        ResponseMessage<Boolean> confirm = smartHomeServiceRemote.confirm(shareFamilyReply);
        if (!confirm.isSuccess()) {
            LogUtils.error("接受分享时，家庭成员绑定异常，tenantId={}, familyId={}, beInviteId={}, inviteId={}, status={}, msg={}",
                    tenantId, targetId, beInviteId, inviteId, status, confirm.getMsg());
            throw new AppRuntimeException(INNER_SERVER_ERROR, "家庭分享异常");
        }
        // 如果智能家居服务返回false，目前只有管理员撤销分享这种情况，当同意加入时需提示用户家庭已撤销
        if (status == InviteShareEnum.AGREED.getStatus() && !confirm.getResult()) {
            LogUtils.info("接受分享时，管理员已撤销分享，tenantId={}, familyId={}, beInviteId={}, inviteId={}, status={}, msg={}",
                    tenantId, targetId, beInviteId, inviteId, status, confirm.getMsg());
            throw new AppRuntimeException(ILLEGAL_STATE.getCode(), "管理员已撤销分享,无法加入家庭");
        }
        return confirm.getResult();
    }

    /**
     * 处理用户相关信息
     *
     * @param list
     * @param tenantId
     * @param userId
     * @param inviter
     */
    private void handleUserInfo(List<DeviceShareVo> list, String tenantId, String userId, Boolean inviter) {
        List<String> beInviterIds = list.stream().map(l -> l.getUserId()).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(beInviterIds)) {
            ResponseMessage<List<TAuthClientUserInfo>> userResponse = userRemote.getListByIds(tenantId, beInviterIds);
            if (!userResponse.isSuccess()) {
                LogUtils.error("远程调用，通过用户id列表获取用户信息列表失败，tenantId={}, useId={}, is inviter={}, beInviterIds={}, msg={}",
                        tenantId, userId, inviter, beInviterIds, userResponse.getMsg());
            } else {
                List<TAuthClientUserInfo> userInfos = userResponse.getResult();
                if (!CollectionUtils.isEmpty(userInfos)) {
                    Map<String, String> idAndUsernameAndUrl =
                            userInfos.stream().collect(Collectors.toMap(m -> m.getId(),
                                    n -> n.getUsernameEnc() + "," + (StringUtils.isBlank(n.getAvatarUrl()) ? "" :
                                            n.getAvatarUrl())));
                    list.forEach((DeviceShareVo item) -> {
                        if (idAndUsernameAndUrl.containsKey(item.getUserId())) {
                            String usernameAndUrl = idAndUsernameAndUrl.get(item.getUserId());
                            if (StringUtils.isNotBlank(usernameAndUrl)) {
                                String[] split = usernameAndUrl.split(",");
                                // 密文的用户名
                                item.setUsername(split[0]);
                                if (split.length >= NumberEnum.TWO.getNumber() && StringUtils.isNotBlank(split[1]) && item.getType() == InviteShareEnum.FAMILY.getStatus()) {
                                    // 密文的头像信息
                                    if (StringUtils.isNotBlank(split[1])) {
                                        try {
                                            item.setPhotoUrl(AESUtil.Encrypt(split[1], tenantId));
                                        } catch (Exception e) {
                                            LogUtils.error(e, "aes加密失败, user avatarUrl={}, exceptionMsg={}", split[1]
                                                    , e.getMessage());
                                        }
                                    } else {
                                        item.setPhotoUrl(split[1]);
                                    }
                                }
                            }
                        }
                    });
                }
            }
        }
    }

    /**
     * 处理产品相关信息
     *
     * @param list
     * @param tenantId
     * @param userId
     * @param inviter
     */
    private void handleProductInfo(List<DeviceShareVo> list, String tenantId, String userId, Boolean inviter) {
        List<String> productModeCodes =
                list.stream().map(m -> m.getProductModeCode()).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(productModeCodes)) {
            ResponseMessage<List<ProductModeEntity>> response = productServiceRemote.getListByCode(tenantId,
                    productModeCodes);
            if (!response.isSuccess()) {
                LogUtils.error("通过工程类型列表获工程类型信息列表失败，tenantId={}, userId={}, productModeCodes={}, is inviter={}, " +
                                "responseMsg={}",
                        tenantId, userId, productModeCodes, inviter, response.getMsg());
            } else {
                List<ProductModeEntity> productModeEntities = response.getResult();
                if (!CollectionUtils.isEmpty(productModeEntities)) {
                    Map<String, String> codeAndLabel =
                            productModeEntities.stream().collect(Collectors.toMap(m -> m.getCode(),
                                    n -> n.getLabel() + "," + (StringUtils.isBlank(n.getPhotoUrl()) ? "" :
                                            n.getPhotoUrl())));
                    list.forEach((DeviceShareVo item) -> {
                        if (codeAndLabel.containsKey(item.getProductModeCode())) {
                            String labelAndUrl = codeAndLabel.get(item.getProductModeCode());
                            String[] split = labelAndUrl.split(",");
                            item.setModeType(split[0]);
                            if (split.length > 1 && StringUtils.isNotBlank(split[1]) && item.getType() == InviteShareEnum.DEVICE.getStatus()) {
                                item.setPhotoUrl(split[1]);
                            }
                        }
                    });
                }

            }
        }
    }
}
