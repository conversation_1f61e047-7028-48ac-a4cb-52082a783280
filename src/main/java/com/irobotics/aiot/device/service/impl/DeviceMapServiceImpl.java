package com.irobotics.aiot.device.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.commons.util.SnowflakeUtil;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.device.entity.DeviceMapEntity;
import com.irobotics.aiot.device.mapper.DeviceMapMapper;
import com.irobotics.aiot.device.service.IDeviceMapService;
import com.irobotics.aiot.device.vo.DeviceCurMapParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 扫地机设备地图表 服务实现类
 * </p>
 */
@Service
public class DeviceMapServiceImpl extends ServiceImpl<DeviceMapMapper, DeviceMapEntity> implements IDeviceMapService {


    @Override
    public ResponseMessage<DeviceMapEntity> getCurMap(String sn) {
        if (StringUtils.isBlank(sn)) {
            return ResponseMessage.buildSuccess(null);
        }
        LambdaQueryWrapper<DeviceMapEntity> lambda = new QueryWrapper<DeviceMapEntity>().lambda();
        lambda.eq(DeviceMapEntity::getSn, sn);
        List<DeviceMapEntity> deviceMapList = this.list(lambda);
        DeviceMapEntity deviceMapEntity = CollectionUtils.isNotEmpty(deviceMapList) ? deviceMapList.get(0) : null;
        return ResponseMessage.buildSuccess(deviceMapEntity);
    }

    @Override
    public ResponseMessage<Boolean> uploadCurMap(DeviceCurMapParam curMapParam) {
        String sn = SystemContextUtils.getContextValue(ContextKey.SN);
        String tenantId = SystemContextUtils.getTenantId();
        LambdaQueryWrapper<DeviceMapEntity> lambda = new QueryWrapper<DeviceMapEntity>().lambda();
        lambda.eq(DeviceMapEntity::getSn, sn);

        DeviceMapEntity deviceMapEntity = new DeviceMapEntity();
        List<DeviceMapEntity> list = this.list(lambda);
        if (CollectionUtils.isNotEmpty(list)) {
            deviceMapEntity = list.get(0);
        } else {
            deviceMapEntity.setId(SnowflakeUtil.snowflakeId());
            deviceMapEntity.setSn(sn);
            deviceMapEntity.setCreateTime(LocalDateTime.now());
        }

        deviceMapEntity.setCurMapId(curMapParam.getCurMapId());
        deviceMapEntity.setCurMapRoom(JSONArray.parseArray(JSON.toJSONString(curMapParam.getMapRoomList())));
        deviceMapEntity.setTenantId(tenantId);
        deviceMapEntity.setUpdateTime(LocalDateTime.now());

        return ResponseMessage.buildSuccess(this.saveOrUpdate(deviceMapEntity));
    }

    @Override
    public ResponseMessage<Boolean> delCurMap(String sn) {
        return ResponseMessage.buildSuccess(this.remove(new QueryWrapper<DeviceMapEntity>().lambda().eq(DeviceMapEntity::getSn, sn)));
    }
}
