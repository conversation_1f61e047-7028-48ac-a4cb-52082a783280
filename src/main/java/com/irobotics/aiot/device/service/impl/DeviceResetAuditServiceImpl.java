package com.irobotics.aiot.device.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.commons.util.SnowflakeUtil;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.bravo.web.util.OperatorUtils;
import com.irobotics.aiot.device.common.AuditStatus;
import com.irobotics.aiot.device.config.HaiErBigdataConfig;
import com.irobotics.aiot.device.entity.DeviceResetAuditEntity;
import com.irobotics.aiot.device.mapper.DeviceResetAuditMapper;
import com.irobotics.aiot.device.service.DeviceResetAuditService;
import com.irobotics.aiot.device.service.DeviceResetService;
import com.irobotics.aiot.device.service.HaiErDeviceResetService;
import com.irobotics.aiot.device.utils.LocalDateTimeUtils;
import com.irobotics.aiot.device.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 设备出厂初始化服务实现
 * @since 2023/5/18
 */
@Service
public class DeviceResetAuditServiceImpl extends ServiceImpl<DeviceResetAuditMapper, DeviceResetAuditEntity> implements DeviceResetAuditService {

    @Resource
    HaiErDeviceResetService haiErDeviceResetService;

    @Resource
    DeviceResetService deviceResetService;

    @Resource
    HaiErBigdataConfig haiErBigdataConfig;

    @Override
    public ResponseMessage<PageResp<IdSnUsernameResp>> getIdSnByIdAndPage(DeviceResetAuditPageParam param) {
        DeviceResetAuditEntity entity = this.getById(param.getRecordId());
        if (entity == null) {
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA, "该数据不存在");
        }
        //申请SN列表
        List<IdSnUsernameResp> idSnUsernameRespList = JSONUtil.toList(entity.getApplySnJson(), IdSnUsernameResp.class);
        /** 兼容部分审核逻辑
         if (StringUtils.isNotBlank(entity.getDelSnJson())) {
         //已删除SN列表
         List<IdSnUsernameResp> deletedList = JSONUtil.toList(entity.getDelSnJson(), IdSnUsernameResp.class);
         List<String> deletedSnLst = deletedList.stream()
         .map(IdSnUsernameResp::getSn)
         .collect(Collectors.toList());
         //过滤出未审核删除的SN列表
         idSnUsernameRespList = idSnUsernameRespList.stream()
         .filter(t -> !deletedSnLst.contains(t.getSn()))
         .collect(Collectors.toList());
         }
         */
        return ResponseMessage.buildSuccess(new PageResp<>(param.getPage(), param.getPageSize(), idSnUsernameRespList));
    }

    @Override
    public Page<DeviceResetAuditEntity> getList(DeviceResetAuditRecordParam param) {
        LambdaQueryWrapper<DeviceResetAuditEntity> lambda = new QueryWrapper<DeviceResetAuditEntity>().lambda();
        if (StringUtils.isNotBlank(param.getTenantId())) {
            lambda.eq(DeviceResetAuditEntity::getTenantId, param.getTenantId());
        }
        if (StringUtils.isNotBlank(param.getApplyBy())) {
            lambda.like(DeviceResetAuditEntity::getApplyBy, param.getApplyBy());
        }
        if (param.getApplyBeginTime() > 0) {
            lambda.ge(DeviceResetAuditEntity::getApplyTime, LocalDateTimeUtils.milliToLocalDateTime(param.getApplyBeginTime()));
        }
        if (param.getApplyEndTime() > 0) {
            lambda.le(DeviceResetAuditEntity::getApplyTime, LocalDateTimeUtils.milliToLocalDateTime(param.getApplyEndTime()));
        }
        if (StringUtils.isNotBlank(param.getAuditBy())) {
            lambda.like(DeviceResetAuditEntity::getAuditBy, param.getAuditBy());
        }
        if (param.getAuditBeginTime() > 0) {
            lambda.ge(DeviceResetAuditEntity::getAuditTime, LocalDateTimeUtils.milliToLocalDateTime(param.getAuditBeginTime()));
        }
        if (param.getAuditEndTime() > 0) {
            lambda.le(DeviceResetAuditEntity::getAuditTime, LocalDateTimeUtils.milliToLocalDateTime(param.getAuditEndTime()));
        }
        if (param.getAuditStatus() != null) {
            lambda.eq(DeviceResetAuditEntity::getAuditStatus, param.getAuditStatus());
        }
        lambda.orderByDesc(DeviceResetAuditEntity::getApplyTime, DeviceResetAuditEntity::getId);
        return baseMapper.selectPage(new Page<>(param.getPage(), param.getPageSize()), lambda);
    }

    @Override
    public ResponseMessage<DeviceResetAuditEntity> apply(ApplyDelSnVo param) {
        if (CollectionUtils.isEmpty(param.getIdSnUsernameRespList())) {
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA, "申请初始化的SN列表为空");
        }
        DeviceResetAuditEntity auditEntity = new DeviceResetAuditEntity();
        auditEntity.setId(SnowflakeUtil.snowflakeId());
        auditEntity.setTenantId(param.getTenantId());
        auditEntity.setApplySnJson(JSONUtil.toJsonStr(param.getIdSnUsernameRespList()));
        auditEntity.setApplyBy(OperatorUtils.getOperateBy());
        auditEntity.setApplyTime(LocalDateTime.now());
        auditEntity.setAuditStatus(AuditStatus.NotAudit.getCode());
        this.save(auditEntity);
        return ResponseMessage.buildSuccess(auditEntity);
    }

    @Override
    public ResponseMessage<DeviceResetAuditEntity> audit(String token, String taskId, DeviceResetAuditParam param) {
        DeviceResetAuditEntity newEntity = this.getById(param.getId());
        if (newEntity == null) {
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA, "该数据不存在");
        }
        if (AuditStatus.NotAudit.getCode().compareTo(newEntity.getAuditStatus()) < 0) {
            return ResponseMessage.buildFail(ResponseCode.ILLEGAL_STATE, "该数据已审核，请勿重复操作");
        }
        Integer billStatus = param.getAuditStatus();
        if (AuditStatus.AuditFailed.getCode().equals(billStatus) && StringUtils.isBlank(param.getAuditDesc())) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "审核不通过时，请务必录入审核说明");
        }
        /**兼容部分审核逻辑   2023.5.26 简化部分操作，改为全量操作
         //审核通过，执行删除
         if (AuditStatus.Audited.getCode().equals(param.getAuditStatus())) {
         //申请需要删除的SN
         List<IdSnUsernameResp> needDeleteSnList;
         //申请审核删除的SN
         List<IdSnUsernameResp> applySnList = JSONUtil.toList(newEntity.getApplySnJson(), IdSnUsernameResp.class);
         try {
         needDeleteSnList = JSONUtil.toList(param.getDelSnJson(), IdSnUsernameResp.class);
         } catch (Exception ex) {
         LogUtils.error("申请需要删除的SN参数{} 格式错误", param.getDelSnJson());
         return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "delSnJson");
         }
         String deleteJson;
         //一键通过 全部删除
         if (CollectionUtils.isEmpty(needDeleteSnList)) {
         deleteJson = newEntity.getApplySnJson();
         billStatus = AuditStatus.Audited.getCode();
         } else {
         //部分通过，部分删除
         deleteJson = param.getDelSnJson();
         billStatus = AuditStatus.AuditPartly.getCode();
         }
         needDeleteSnList = JSONUtil.toList(deleteJson, IdSnUsernameResp.class);
         //校验提交的SN是否存在申请审核SN中
         List<String> applySns = applySnList.stream()
         .map(IdSnUsernameResp::getSn)
         .collect(Collectors.toList());
         needDeleteSnList = needDeleteSnList.stream()
         .filter(t -> applySns.contains(t.getSn()))
         .collect(Collectors.toList());
         if (needDeleteSnList.isEmpty()) {
         return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA, "当前申请审核的SN列表校验不通过，与申请的不一致");
         }

         //排除掉历史审核的SN数据
         List<IdSnUsernameResp> histDeleteSnList = new ArrayList<>();
         if (StringUtils.isNotBlank(newEntity.getDelSnJson())) {
         histDeleteSnList = JSONUtil.toList(newEntity.getDelSnJson(), IdSnUsernameResp.class);
         List<String> hisDeleteList = histDeleteSnList.stream()
         .map(IdSnUsernameResp::getSn)
         .collect(Collectors.toList());
         needDeleteSnList = needDeleteSnList.stream()
         .filter(t -> !hisDeleteList.contains(t.getSn()))
         .collect(Collectors.toList());
         }

         Set<String> sns = needDeleteSnList.stream()
         .map(IdSnUsernameResp::getSn)
         .filter(StringUtils::isNotBlank)
         .collect(Collectors.toSet());
         if (CollectionUtils.isEmpty(sns)) {
         return ResponseMessage.buildFail(ResponseCode.DUPLICATE_DATA, "SN列表为空或者已在当前审核记录初始化过");
         }
         //海尔
         if (param.getTenantId().equalsIgnoreCase(haiErBigdataConfig.getTenantId())) {
         haiErDeviceResetService.deleteHaiErDeviceRecord(token, param.getTenantId(), taskId, new ArrayList<>(sns));
         } else {
         //常规
         deviceResetService.deleteRecordBySns(token, taskId, param.getTenantId(), new ArrayList<>(sns));
         }
         String curDeleteJson = deleteJson;
         //前面状态未部分审核或历史需要删除SN不为空，则补充历史删除SN
         if (AuditStatus.AuditPartly.getCode().equals(billStatus)) {
         histDeleteSnList.addAll(needDeleteSnList);
         curDeleteJson = JSONUtil.toJsonStr(histDeleteSnList);

         //全部审核则修改状态为审核通过
         List<String> curSns = histDeleteSnList.stream()
         .map(IdSnUsernameResp::getSn)
         .filter(StringUtils::isNotBlank)
         .collect(Collectors.toList());
         if (applySnList.stream().allMatch(t -> curSns.contains(t.getSn()))) {
         billStatus = AuditStatus.Audited.getCode();
         }
         }

         newEntity.setDelSnJson(curDeleteJson);
         }
         */

        if (AuditStatus.Audited.getCode().equals(billStatus)) {
            List<IdSnUsernameResp> needDeleteSnList = JSONUtil.toList(newEntity.getApplySnJson(), IdSnUsernameResp.class);
            Set<String> sns = needDeleteSnList.stream()
                    .map(IdSnUsernameResp::getSn)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(sns)) {
                return ResponseMessage.buildFail(ResponseCode.DUPLICATE_DATA, "SN列表为空或者当前记录的SN列表已初始化过");
            }
            //海尔
            if (param.getTenantId().equalsIgnoreCase(haiErBigdataConfig.getTenantId())) {
                haiErDeviceResetService.deleteHaiErDeviceRecord(token, param.getTenantId(), taskId, new ArrayList<>(sns));
            } else {
                //常规
                deviceResetService.deleteRecordBySns(token, taskId, param.getTenantId(), new ArrayList<>(sns));
            }
            newEntity.setDelSnJson(newEntity.getApplySnJson());
        } else {
            newEntity.setNoDelSnJson(newEntity.getApplySnJson());
        }

        //更改审核状态
        newEntity.setAuditStatus(billStatus);
        newEntity.setAuditBy(OperatorUtils.getOperateBy());

        StringBuilder builder = new StringBuilder();
        if (StringUtils.isNotBlank(newEntity.getAuditDesc())) {
            builder.append(newEntity.getAuditDesc());
            builder.append(";");
        }
        builder.append(StringUtils.isNotBlank(param.getAuditDesc()) ? param.getAuditDesc() : "");

        newEntity.setAuditDesc(builder.toString());
        newEntity.setAuditTime(LocalDateTime.now());
        this.updateById(newEntity);
        //减少传输
        newEntity.setApplySnJson(null);
        newEntity.setNoDelSnJson(null);
        newEntity.setDelSnJson(null);
        return ResponseMessage.buildSuccess(newEntity);
    }
}
