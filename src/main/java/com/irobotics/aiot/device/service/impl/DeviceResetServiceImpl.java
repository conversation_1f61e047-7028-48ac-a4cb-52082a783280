package com.irobotics.aiot.device.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.commons.util.JsonUtils;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.cache.DeleteRecordCache;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.cache.UploadResultCache;
import com.irobotics.aiot.device.common.NumberEnum;
import com.irobotics.aiot.device.common.UserClientAgentEnum;
import com.irobotics.aiot.device.entity.*;
import com.irobotics.aiot.device.mapper.DeviceBindBaseMapper;
import com.irobotics.aiot.device.mapper.DeviceBindUserMapper;
import com.irobotics.aiot.device.mapper.DeviceInfoMapper;
import com.irobotics.aiot.device.mapper.DeviceInviteShareMapper;
import com.irobotics.aiot.device.remote.*;
import com.irobotics.aiot.device.remote.model.BindIdVo;
import com.irobotics.aiot.device.remote.model.ClearFlowParam;
import com.irobotics.aiot.device.repository.DeviceResetRecordRepository;
import com.irobotics.aiot.device.service.*;
import com.irobotics.aiot.device.utils.JWTService;
import com.irobotics.aiot.device.utils.PageHelper;
import com.irobotics.aiot.device.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @since 2022/1/13
 */
@Service
public class DeviceResetServiceImpl implements DeviceResetService {

    private static final int QUERY_STEP = 50;

    private static final int DELETE_STEP = 100;

    private static final int ALL_DELETE_STEP = 7;

    private static final Integer ONE_HUNDRED = 100;
    private static final Integer ONE_THOUSAND = 1000;

    @Resource
    private UploadResultCache uploadResultCache;

    @Resource
    private DeleteRecordCache deleteRecordCache;

    @Resource
    private DeviceInfoMapper deviceInfoMapper;

    @Resource
    private DeviceInviteShareMapper deviceInviteShareMapper;

    @Resource
    private SmartHomeServiceRemote smartHomeServiceRemote;

    @Resource
    private TAuthUserRemote tAuthUserRemote;

    @Resource
    private LogServiceRemote logServiceRemote;

    @Resource
    private ProductServiceRemote productServiceRemote;

    @Resource
    private DeviceResetRecordRepository repository;

    @Resource
    private JWTService jwtService;

    @Resource
    private DeleteRecordByIdService deleteRecordByIdService;

    @Autowired
    private DeviceInfoCache deviceInfoCache;

    @Resource
    private IDeviceInviteShareService deviceInviteShareService;

    @Resource
    private IDeviceInfoService deviceInfoService;

    @Autowired
    private DeviceBindUserMapper deviceBindUserMapper;

    @Autowired
    private IDeviceBindUserService deviceBindUserService;

    @Autowired
    private PetServiceRemote petServiceRemote;

    @Autowired
    private DeviceBindBaseMapper deviceBindBaseMapper;

    @Autowired
    private IProductSnDetailActiveZoneService productSnDetailActiveZoneService;

    @Resource
    private IMongoService mongoService;
    @Resource
    private IcloudIntentService icloudIntentService;
    @Resource
    private CommonService commonService;
    @Resource
    private DeviceShadowRemote dataStatisticsServiceRemote;

    @Autowired
    private IConversationService conversationService;

    @Override
    @Async
    public void listBySns(String taskId, String tenantId, List<String> snList) {
        uploadResultCache.putFinishPercent(0, taskId);
        if (CollectionUtils.isEmpty(snList)) {
            uploadResultCache.putFinishPercent(-1, taskId);
            return;
        }
        List<IdSnUsernameResp> resultAllList = new ArrayList<>();
        for (int cur = 0; cur * QUERY_STEP < snList.size(); cur++) {
            List<String> subList = snList.subList(cur * QUERY_STEP, Math.min((cur + 1) * QUERY_STEP, snList.size()));
            ResponseMessage<List<String>> activeSnResult = productServiceRemote.getActiveSnList(subList);
            if (!activeSnResult.isSuccess()) {
                LogUtils.error("远程调用根据sn列表返回激活sn列表失败:{}", activeSnResult.getMsg());
                continue;
            } else {
                subList = activeSnResult.getResult();
            }
            if (CollectionUtils.isEmpty(subList)) {
                continue;
            }
            QueryWrapper<DeviceInfoEntity> deviceInfoEntityQueryWrapper = new QueryWrapper<>();
            deviceInfoEntityQueryWrapper.lambda().in(DeviceInfoEntity::getSn, subList);
            List<DeviceInfoEntity> infoList = deviceInfoMapper.selectList(deviceInfoEntityQueryWrapper);
            Map<String, IdSnUsernameResp> resultMap = new HashMap<>();
            List<String> deviceIdList = new ArrayList<>();
            infoList.forEach((DeviceInfoEntity deviceInfo) -> {
                deviceIdList.add(deviceInfo.getId());
                IdSnUsernameResp resp = new IdSnUsernameResp();
                resp.setDeviceId(deviceInfo.getId());
                resp.setSn(deviceInfo.getSn());
                resultMap.put(deviceInfo.getId(), resp);
            });
            if (CollectionUtils.isEmpty(deviceIdList)) {
                continue;
            }
            // 主要分为2种设备
            // 1、设备和用户有绑定关系的设备，需要查询绑定关系
            // 2、设备只激活，没有绑定关系
            List<BindIdVo> bindIdVoList;
            // 走智能家居
            ResponseMessage<List<BindIdVo>> bindIdVoResult =
                    smartHomeServiceRemote.getBindListByDeviceIdList(deviceIdList);
            if (!bindIdVoResult.isSuccess()) {
                LogUtils.error("远程调用根据设备id列表获取绑定用户id列表失败:{}", bindIdVoResult.getMsg());
                continue;
            }
            bindIdVoList = bindIdVoResult.getResult();
            if (CollectionUtils.isEmpty(bindIdVoList)) {
                bindIdVoList = deviceBindUserMapper.getBindListByDeviceIdList(tenantId, deviceIdList);
            }
            if (CollectionUtils.isNotEmpty(bindIdVoList)) {
                List<String> userIdList = bindIdVoList.stream().map(BindIdVo::getUserId).collect(Collectors.toList());
                ResponseMessage<List<TAuthClientUserInfo>> userInfoResult = tAuthUserRemote.getListByIds(tenantId,
                        userIdList);
                if (!userInfoResult.isSuccess()) {
                    LogUtils.error("远程调用根据用户id列表获取用户信息失败:{}", userInfoResult.getMsg());
                } else {
                    List<TAuthClientUserInfo> userInfoList = userInfoResult.getResult();
                    if (CollectionUtils.isNotEmpty(userInfoList)) {
                        Map<String, String> userIdNameMap =
                                userInfoList.stream().collect(Collectors.toMap(TAuthClientUserInfo::getId,
                                        TAuthClientUserInfo::getUsername));
                        bindIdVoList.forEach((BindIdVo bindIdVo) -> {
                            if (resultMap.containsKey(bindIdVo.getDeviceId())) {
                                IdSnUsernameResp resp = resultMap.get(bindIdVo.getDeviceId());
                                if (userIdNameMap.containsKey(bindIdVo.getUserId())) {
                                    resp.getUsernameList().add(userIdNameMap.get(bindIdVo.getUserId()));
                                }
                            }
                        });
                    }
                }
            }
            List<IdSnUsernameResp> resultList = new ArrayList<>(resultMap.values());
            int percent = 0;
            percent = (cur + 1) * QUERY_STEP * ONE_HUNDRED / snList.size();
            if (percent > ONE_HUNDRED - 1) {
                percent = ONE_HUNDRED - 1;
            }
            uploadResultCache.putFinishPercent(percent, taskId);
            resultAllList.addAll(resultList);
        }
        uploadResultCache.putResult(taskId, resultAllList);
        uploadResultCache.putFinishPercent(ONE_HUNDRED, taskId);
    }

    @Override
    @Async
    public void listByUsernames(String taskId, String tenantId, List<String> usernameList) {
        uploadResultCache.putFinishPercent(0, taskId);
        if (CollectionUtils.isEmpty(usernameList)) {
            uploadResultCache.putFinishPercent(-1, taskId);
            return;
        }
        List<IdSnUsernameResp> resultAllList = new ArrayList<>();
        for (int cur = 0; cur * QUERY_STEP < usernameList.size(); cur++) {
            List<String> subList = usernameList.subList(cur * QUERY_STEP, Math.min((cur + 1) * QUERY_STEP,
                    usernameList.size()));
            ResponseMessage<List<TAuthClientUserInfo>> userInfoResult = tAuthUserRemote.getListByUsernameList(subList);
            if (!userInfoResult.isSuccess()) {
                LogUtils.error("远程调用通过用户名列表获取用户信息列表失败:{}", userInfoResult.getMsg());
                continue;
            }
            List<TAuthClientUserInfo> userInfoList = userInfoResult.getResult();
            List<String> userIdList =
                    userInfoList.stream().map(TAuthClientUserInfo::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userIdList)) {
                continue;
            }
            // 智能家居开关
            List<BindIdVo> bindIdVoList;
            ResponseMessage<List<BindIdVo>> bindVoResult = smartHomeServiceRemote.getBindListByUserIdList(userIdList);
            if (!bindVoResult.isSuccess()) {
                LogUtils.error("远程调用通过用户id列表获取绑定设备id列表失败:{}", userInfoResult.getMsg());
                continue;
            }
            bindIdVoList = bindVoResult.getResult();
            if (CollectionUtils.isEmpty(bindIdVoList)) {
                bindIdVoList = deviceBindUserMapper.getBindListByUserIdList(tenantId, userIdList);
            }
            if (CollectionUtils.isEmpty(bindIdVoList)) {
                continue;
            }
            List<String> deviceIdList = bindIdVoList.stream().map(BindIdVo::getDeviceId).collect(Collectors.toList());
            Map<String, IdSnUsernameResp> resultMap = new HashMap<>();
            Map<String, String> userIdNameMap =
                    userInfoList.stream().collect(Collectors.toMap(TAuthClientUserInfo::getId,
                            TAuthClientUserInfo::getUsername));
            bindIdVoList.forEach((BindIdVo bindIdVo) -> {
                IdSnUsernameResp resp = new IdSnUsernameResp();
                if (resultMap.containsKey(bindIdVo.getDeviceId())) {
                    resp = resultMap.get(bindIdVo.getDeviceId());
                }
                resp.setDeviceId(bindIdVo.getDeviceId());
                resp.getUsernameList().add(userIdNameMap.get(bindIdVo.getUserId()));
                resultMap.put(resp.getDeviceId(), resp);
            });
            QueryWrapper<DeviceInfoEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(DeviceInfoEntity::getId, deviceIdList).eq(DeviceInfoEntity::getTenantId, tenantId);
            List<DeviceInfoEntity> deviceInfoList = deviceInfoMapper.selectList(queryWrapper);
            Map<String, String> deviceIdSnMap =
                    deviceInfoList.stream().collect(Collectors.toMap(DeviceInfoEntity::getId, DeviceInfoEntity::getSn));
            resultMap.forEach((String s, IdSnUsernameResp idSnUsernameResp) -> {
                if (deviceIdSnMap.containsKey(s)) {
                    idSnUsernameResp.setSn(deviceIdSnMap.get(s));
                }
            });
            List<IdSnUsernameResp> resultList = new ArrayList<>(resultMap.values());
            List<String> snList = resultList.stream().map(IdSnUsernameResp::getSn).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(snList)) {
                continue;
            }
            ResponseMessage<List<String>> activeSnResult = productServiceRemote.getActiveSnList(snList);
            List<String> activeSnList;
            if (!activeSnResult.isSuccess()) {
                LogUtils.error("远程调用根据sn列表返回激活sn列表失败:{}", activeSnResult.getMsg());
            } else {
                activeSnList = activeSnResult.getResult();
                Set<String> activeSnSet = new HashSet<>(activeSnList);
                resultList =
                        resultList.stream().filter(idSnUsernameResp -> activeSnSet.contains(idSnUsernameResp.getSn())).collect(Collectors.toList());
            }
            int percent = 0;
            percent = (cur + 1) * QUERY_STEP * ONE_HUNDRED / usernameList.size();
            if (percent > ONE_HUNDRED - 1) {
                percent = ONE_HUNDRED - 1;
            }
            uploadResultCache.putFinishPercent(percent, taskId);
            resultAllList.addAll(resultList);
        }
        uploadResultCache.putResult(taskId, resultAllList);
        uploadResultCache.putFinishPercent(ONE_HUNDRED, taskId);
    }

    @Override
    @Async
    public void deleteRecordByIds(String token, String taskId, String tenantId, List<String> deviceIdList) {
        if (deviceIdList.size() > ONE_THOUSAND) {
            deviceIdList = deviceIdList.subList(0, ONE_THOUSAND);
        }

        List<DeleteResp> resultList = new ArrayList<>();

        deleteRecordCache.putFinishPercent(0, taskId);

        for (int cur = 0; cur * DELETE_STEP < deviceIdList.size(); cur++) {
            List<String> deviceIdSubList = deviceIdList.subList(cur * DELETE_STEP, Math.min((cur + 1) * DELETE_STEP,
                    deviceIdList.size()));

            try {
                // 获取设备sn列表
                QueryWrapper<DeviceInfoEntity> deviceInfoQueryWrapper = new QueryWrapper<>();
                deviceInfoQueryWrapper.lambda().in(DeviceInfoEntity::getId, deviceIdSubList).eq(DeviceInfoEntity::getTenantId, tenantId);
                List<DeviceInfoEntity> deviceInfoList = deviceInfoMapper.selectList(deviceInfoQueryWrapper);
                List<String> snList =
                        deviceInfoList.stream().map(DeviceInfoEntity::getSn).filter(Objects::nonNull).collect(Collectors.toList());
                LogUtils.info("删除sn集合 snList:{}", snList);

                deleteRecordCache.putFinishPercent(getFinishPercent(cur, 1, deviceIdList.size()), taskId);

                // 删除设备分享记录
                QueryWrapper<DeviceInviteShareEntity> deviceInviteShareEntityQueryWrapper = new QueryWrapper<>();
                deviceInviteShareEntityQueryWrapper.lambda().in(DeviceInviteShareEntity::getTargetId,
                        deviceIdSubList).eq(DeviceInviteShareEntity::getTenantId, tenantId);
                deviceInviteShareMapper.delete(deviceInviteShareEntityQueryWrapper);

                deleteRecordCache.putFinishPercent(getFinishPercent(cur, NumberEnum.TWO.getNumber(),
                        deviceIdList.size()), taskId);
                // 删除非智能家居下的绑定关系
                deviceBindUserService.deleteBindByDeviceIds(deviceIdSubList);
                // 删除智能家居下设备房间绑定关系
                ResponseMessage<Void> deleteRoomBindResult =
                        smartHomeServiceRemote.deleteRoomBindByDeviceIds(deviceIdSubList);
                if (!deleteRoomBindResult.isSuccess()) {
                    deviceIdSubList.stream().parallel().forEach(s -> deleteRecordByIdService.deleteRecordById(taskId,
                            tenantId, s, resultList));
                    continue;
                }
                deleteRecordCache.putFinishPercent(getFinishPercent(cur, NumberEnum.THREE.getNumber(),
                        deviceIdList.size()), taskId);

                // 删除app日志
                if (CollectionUtils.isNotEmpty(snList)) {
                    ResponseMessage<Void> deleteAppLogResult = logServiceRemote.deleteAppLogBySns(snList);
                    if (!deleteAppLogResult.isSuccess()) {
                        deviceIdSubList.stream().parallel().forEach(s -> deleteRecordByIdService.deleteRecordById(taskId, tenantId, s, resultList));
                        continue;
                    }
                }

                deleteRecordCache.putFinishPercent(getFinishPercent(cur, NumberEnum.FOUR.getNumber(),
                        deviceIdList.size()), taskId);

                // 删除device日志
                if (CollectionUtils.isNotEmpty(snList)) {
                    ResponseMessage<Void> deleteDeviceLogResult = logServiceRemote.deleteDeviceLogBySns(snList);
                    if (!deleteDeviceLogResult.isSuccess()) {
                        deviceIdSubList.stream().parallel().forEach(s -> deleteRecordByIdService.deleteRecordById(taskId, tenantId, s, resultList));
                        continue;
                    }
                }

                deleteRecordCache.putFinishPercent(getFinishPercent(cur, NumberEnum.FIVE.getNumber(),
                        deviceIdList.size()), taskId);


                // 将已激活sn信息删除
                if (CollectionUtils.isNotEmpty(snList)) {
                    ResponseMessage<Boolean> delSnResult = productSnDetailActiveZoneService.delBySns(snList);
                    if (!delSnResult.isSuccess()) {
                        deviceIdSubList.stream().parallel().forEach(s -> deleteRecordByIdService.deleteRecordById(taskId, tenantId, s, resultList));
                        continue;
                    }
                }

                deleteRecordCache.putFinishPercent(getFinishPercent(cur, NumberEnum.SIX.getNumber(),
                        deviceIdList.size()), taskId);

                // 删除设备信息
                QueryWrapper<DeviceInfoEntity> deleteDeviceInfoWrapper = new QueryWrapper<>();
                deleteDeviceInfoWrapper.lambda().in(DeviceInfoEntity::getId, deviceIdSubList);
                deviceInfoMapper.delete(deleteDeviceInfoWrapper);
                // 删除缓存
                deviceInfoCache.clearMore(deviceInfoList);

                deleteRecordCache.putFinishPercent(getFinishPercent(cur, NumberEnum.SEVEN.getNumber(),
                        deviceIdList.size()), taskId);
                // 如果是猫砂仓,删除猫咪信息
                petServiceRemote.deleteBySns(snList);
                // 删除基站与设备的绑定关系
                LambdaQueryWrapper<DeviceBindBaseEntity> baseEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
                baseEntityLambdaQueryWrapper.in(DeviceBindBaseEntity::getSn, snList);
                deviceBindBaseMapper.delete(baseEntityLambdaQueryWrapper);
                LogUtils.info("删除数据完成:snList{}", snList);
                Map<String, String> idSnMap =
                        deviceInfoList.stream().collect(Collectors.toMap(DeviceInfoEntity::getId,
                                DeviceInfoEntity::getSn));
                deviceIdSubList.forEach((String deviceId) -> {
                    String sn = idSnMap.get(deviceId);
                    DeleteResp resp = new DeleteResp(deviceId, true, sn);
                    resultList.add(resp);
                });
            } catch (Exception e) {
                LogUtils.info("删除数据出现异常:{}", e);
                deviceIdSubList.stream().parallel().forEach(s -> deleteRecordByIdService.deleteRecordById(taskId,
                        tenantId, s, resultList));
            }
        }
        deleteRecordCache.putResult(taskId, resultList);
        deleteRecordCache.putFinishPercent(ONE_HUNDRED, taskId);
        saveDeleteRecord(token, tenantId, taskId, resultList);
    }

    @Override
    public ResponseMessage<String> deleteRecordBySns(String token, String taskId, String tenantId, List<String> sns) {
        if (CollectionUtils.isEmpty(sns)) {
            LogUtils.info("deleteRecordBySns SN列表为空 {}", JsonUtils.toJSON(sns));
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA, "SN列表为空");
        }
        int total = sns.size();
        int page = total / DELETE_STEP;
        if (total % DELETE_STEP > 0) {
            page += 1;
        }
        List<String> ids = new ArrayList<>();
        // 分批查询获取设备id列表
        for (int i = 0; i < page; i++) {
            QueryWrapper<DeviceInfoEntity> deviceInfoQueryWrapper = new QueryWrapper<>();
            SystemContextUtils.setContextValue(ContextKey.TENANT_ID, tenantId);
            deviceInfoQueryWrapper.lambda().in(DeviceInfoEntity::getSn, sns).eq(DeviceInfoEntity::getTenantId,
                    tenantId);
            List<DeviceInfoEntity> deviceInfoList = deviceInfoMapper.selectList(deviceInfoQueryWrapper);
            ids.addAll(deviceInfoList.stream().map(DeviceInfoEntity::getId).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(ids)) {
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA, "没有可删除的设备记录");
        }
        deleteRecordByIds(token, taskId, tenantId, ids);
        return ResponseMessage.buildSuccess(taskId);
    }

    @Override
    public ResponseMessage<PageHelper<DeviceResetRecordEntity>> findRecordPage(DeviceResetRecordPageReq pageReq) {
        return ResponseMessage.buildSuccess(repository.findPage(pageReq));
    }

    @Override
    public void saveRecordToMongo(String token, String tenantId, String taskId, List<DeleteResp> resultList) {
        saveDeleteRecord(token, tenantId, taskId, resultList);
    }

    // 重置设备，清空设备所有相关数据
    @Transactional
    @Override
    public void reset(String deviceId) {
        ResponseMessage<DeviceInfoEntity> res = deviceInfoService.getDeviceInfoById(deviceId);
        if (res.getResult() == null) {
            return;
        }
        String id = SystemContextUtils.getId();
        // 通过机器上的按钮操作重置设备比较deviceId  是否  等于  请求主体ID
        boolean machineRest = deviceId.equals(id);
        String sn = res.getResult().getSn();
        if (machineRest){
            // 删除流水信息
            ClearFlowParam param = new ClearFlowParam();
            param.setTenantId(SystemContextUtils.getTenantId());
            param.setSnList(List.of(sn));
            dataStatisticsServiceRemote.flow(param);
            // 删除语音会话记录
            conversationService.deleteBySn(sn);
        }
        //检测设备是否有绑定用户
        Wrapper<DeviceBindUserEntity> queryWrapper =
                Wrappers.<DeviceBindUserEntity>lambdaQuery().eq(DeviceBindUserEntity::getDeviceId, deviceId)
                        .eq(DeviceBindUserEntity::getUserAgent, UserClientAgentEnum.NON_BASE.getType());
        List<DeviceBindUserEntity> deviceBindList = deviceBindUserService.list(queryWrapper);
        if (CollectionUtil.isEmpty(deviceBindList)) {
            return;
        }
        // 检查当前操作是否合法，
        // 用户操作比较owner  是否  等于 请求主体ID
        DeviceBindUserEntity deviceBindUserEntity = deviceBindList.get(0);
        if (!deviceBindUserEntity.getOwner().equals(id)) {
            // 不是设备拥有者 不能 重置设备，
            // 非法操作
            return;
        }
        // 删除设备绑定
        deviceBindUserService.remove(queryWrapper);
        // 重置基站用户为基站owner
        ((DeviceBindUserMapper) deviceBindUserService.getBaseMapper()).resetOwner(deviceId);
        // 如果是通过机器设备按钮重置，则开始就已经清除过流水，不需要重新清楚
        if (!machineRest){
            // 删除流水信息
            ClearFlowParam param = new ClearFlowParam();
            param.setTenantId(SystemContextUtils.getTenantId());
            param.setSnList(List.of(sn));
            dataStatisticsServiceRemote.flow(param);
            // 删除语音会话记录
            conversationService.deleteBySn(sn);
        }
        // 删除分享记录
        DelShareReq shareReq = new DelShareReq();
        shareReq.setInviter(true);
        shareReq.setDeviceId(List.of(deviceId));
        deviceInviteShareService.delShare(shareReq);
        // 重置昵称
        deviceInfoService.resetNickname(sn);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(deviceBindList)) {
            // UID去重
            Set<String> userIdset = new HashSet<>(deviceBindList.size());
            // 使分享失效，
            deviceBindList.forEach(data -> {
                userIdset.add(data.getCreatorId());
                commonService.shareInvalidate(data.getDeviceId(), data.getCreatorId(), true);
            });
            List<String> userIds = userIdset.stream().toList();
            // 移除数据
            mongoService.batchRemoveByUserIds(userIds, sn);
            // 移除语控
            icloudIntentService.batchRemoveByUserIds(userIds, sn);
        }


    }

    private void saveDeleteRecord(String token, String tenantId, String taskId, List<DeleteResp> resultList) {
        DeviceResetRecordEntity deviceResetRecordEntity = new DeviceResetRecordEntity();

        LogUtils.info("saveDeleteRecord before ,token:{},taskId:{},results:{}", token, taskId,
                JsonUtils.toJSON(resultList));
        if (StringUtils.isNotBlank(token)) {
            try {
                deviceResetRecordEntity.setUserId(jwtService.getUserIdFromToken(token));
                deviceResetRecordEntity.setUserName(jwtService.getUserNameFromToken(token));
                deviceResetRecordEntity.setTenantId(tenantId);
            } catch (Exception e) {
                LogUtils.error("get userId、userName and tenantId from token:{}", e.getMessage());
            }
            deviceResetRecordEntity.setTaskId(taskId);
            resultList.forEach((DeleteResp deleteResp) -> {
                DeviceResetRecordEntity entity = new DeviceResetRecordEntity();
                BeanUtil.copyProperties(deviceResetRecordEntity, entity);
                entity.setDeviceId(deleteResp.getDeviceId());
                entity.setFailMessages(deleteResp.getFailMessages());
                entity.setIsSuccess(deleteResp.getIsSuccess());
                entity.setSn(deleteResp.getSn());
                entity.setTime(System.currentTimeMillis());
                repository.save(entity);
                LogUtils.info("saveDeleteRecord save ,token:{},taskId:{},result:{}", token, taskId,
                        JsonUtils.toJSON(entity.getSn()));
            });
        }
    }

    private static int getFinishPercent(int cur, int step, int size) {
        int percent = (cur + 1) * DELETE_STEP * step * ONE_HUNDRED / (ALL_DELETE_STEP * size);
        if (percent > ONE_HUNDRED - 1) {
            percent = ONE_HUNDRED - 1;
        }
        return percent;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean ResetNickname(ResetNicknameReq req) {
        DelShareReq shareReq = new DelShareReq();
        shareReq.setInviter(req.isInviter());
        ArrayList<String> idList = new ArrayList<>(16);
        idList.add(req.getDeviceId());
        shareReq.setDeviceId(idList);
        // 删除分享记录
        ResponseMessage<Boolean> del = deviceInviteShareService.delShare(shareReq);
        // 重置昵称
        ResponseMessage<Boolean> reset = deviceInfoService.resetNickname(req.getSn());
        if (del.getResult() && reset.getResult()) {
            return true;
        }
        return false;
    }
}
