package com.irobotics.aiot.device.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.common.Constant;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.remote.model.PackageInfoVo;
import com.irobotics.aiot.device.service.IDeviceInfoService;
import com.irobotics.aiot.device.service.IDeviceUpgradeService;
import com.irobotics.aiot.device.utils.ThreadPoolUtil;
import com.irobotics.aiot.device.vo.KafkaData;
import com.irobotics.aiot.device.vo.UpgradeSetModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @description:
 * @author: pengfeng
 * @time: 2022/4/27 18:19
 */
@Service
@Slf4j
public class DeviceUpgradeServiceImpl implements IDeviceUpgradeService {

    /**
     * 线程每次获取的设备数量
     */
    @Value("${upgrade.publishNum:2000}")
    private int publishNum;

    /**
     * 开的线程数量
     */
    @Value("${upgrade.threadNum:4}")
    private int threadNum;

    @Resource
    @Lazy
    private IDeviceInfoService deviceInfoService;

    @Resource
    private KafkaTemplate kafkaTemplate;

    @Resource
    private RedisTemplate redisTemplate;

    @Override
    @Async
    public void publishDevices(PackageInfoVo info) {
        String productModelCode = info.getProductModelCode();
        Integer count = deviceInfoService.getCountByCode(productModelCode);
        if (null != count && count > 0) {

            int pageCount = (count + publishNum - 1) / publishNum;
            log.info(" {} 条设备 分 {} 每次 {} 推送kafka升级消息！", count, pageCount, threadNum);
            ThreadPoolExecutor threadPool = ThreadPoolUtil.getThreadPool(threadNum);
            for (int i = 1; i <= pageCount; i++) {
                int finalI = i;
                threadPool.execute(() -> {
                    Page<DeviceInfoEntity> device = deviceInfoService.getPageByCode(productModelCode, finalI, publishNum);
                    if (null == device || device.getRecords().isEmpty()) {
                        log.error("{} 固件升级包没有找到对应工程类型为 {} 的设备", info.getId(), info.getProductModelCode());
                        return;
                    }

                    UpgradeSetModel model = new UpgradeSetModel(Integer.valueOf(info.getPackageSize()), info.getProductModelCode(), info.getPackageType(),
                            info.getVersionName(), info.getVersionCode(), info.getPackageUrl(), info.getMd5(), false, info.getSilence().equals(1));
                    String body = JSONObject.toJSONString(model);
                    List<DeviceInfoEntity> deviceInfos = Collections.synchronizedList(new ArrayList<DeviceInfoEntity>());
                    deviceInfos = device.getRecords();

                    for (DeviceInfoEntity deviceInfo : deviceInfos) {
                        try {
                            KafkaData data = new KafkaData();
                            data.setData(body);
                            data.setTimestamp(System.currentTimeMillis());
                            data.setMessageId(data.getTimestamp());
                            data.setProductKey(deviceInfo.getProductModeCode());
                            data.setTenantId(deviceInfo.getTenantId());
                            data.setTopic(Constant.MQTT_BASE_TOPIC + deviceInfo.getProductModeCode() + Constant.BASE_PREF + deviceInfo.getSn() + Constant.MOTHOD_UPGRADE_SET);
                            String sn = deviceInfo.getSn();
                            data.setSn(sn);
                            data.setVersion(info.getVersionCode());
                            String kafkaData = JSONObject.toJSONString(data);
                            LogUtils.info("kafka推送设备OTA升级消息,topic为:{},内容为:{}", Constant.UPGRADE_SET_TOPIC, kafkaData);
                            kafkaTemplate.send(Constant.UPGRADE_SET_TOPIC, kafkaData);
                            redisTemplate.opsForList().rightPushAll("upgrade:" + productModelCode + ":" + info.getVersionCode(), sn);
                        } catch (Exception e) {
                            log.error("{} 设备推送kafka升级消息失败: {} ", deviceInfo.getSn(), e.getMessage());
                            redisTemplate.opsForList().rightPushAll("errorUpgrade:" + productModelCode + ":" + info.getVersionCode(), deviceInfo.getSn());
                        }
                    }
                });
            }
        }
    }

    @Override
    @Async
    public void createPushUpgradeTask(PackageInfoVo info) {
        int pageSize = 500;
        Page<DeviceInfoEntity> pageResult = deviceInfoService.getPageByCode(info.getProductModelCode(), 1, pageSize);
        List<DeviceInfoEntity> deviceList = pageResult.getRecords();
        long total = pageResult.getTotal();
        long pages = pageResult.getPages();
        if (total > 0 && CollectionUtils.isNotEmpty(deviceList)) {
            JSONObject object = new JSONObject();
            object.put("taskId",info.getPushTaskId());
            object.put("device",deviceList);
            kafkaTemplate.send(Constant.UPGRADE_DEVICE_LIST,object.toJSONString());
            if (pages > 1) {
                for (long i = 2; i <= pages; i++) {
                    Page<DeviceInfoEntity> result = deviceInfoService.getPageByCode(info.getProductModelCode(), (int) i, pageSize);
                    JSONObject json = new JSONObject();
                    json.put("taskId",info.getPushTaskId());
                    json.put("device",result.getRecords());
                    kafkaTemplate.send(Constant.UPGRADE_DEVICE_LIST,json.toJSONString());
                }
            }
        } else {
            LogUtils.error("OTA 主动下发升级任务详情新增失败，productModelCode: {} 下没有找到设备", info.getProductModelCode());
        }
    }

    @Override
    public void checkDeviceUpgrade(DeviceInfoEntity device) {
        if (null == device || StringUtils.isBlank(device.getId()) || StringUtils.isBlank(device.getVersions())) {
            log.error("当前设备没有version信息，不检查是否需OTA升级：",device);
            return;
        }
        try {
            log.info("开始发送kafka检测设备是否需要OTA主动升级消息 设备：{}",device);
            kafkaTemplate.send(Constant.UPGRADE_DEVICE_CHECK,JSONObject.toJSONString(device));
            log.info("发送kafka检测设备是否需要OTA主动升级消息成功!!!");
        } catch (Exception e) {
            LogUtils.error("发送kafka检测设备是否需要OTA主动升级消息失败: " + e.getMessage());
        }
    }
}
