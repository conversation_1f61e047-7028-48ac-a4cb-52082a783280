package com.irobotics.aiot.device.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.irobotics.aiot.device.common.RobotVersion;
import com.irobotics.aiot.device.entity.DeviceVersionLog;
import com.irobotics.aiot.device.service.IDeviceVersionLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class DeviceVersionLogServiceImpl implements IDeviceVersionLogService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Async
    @Override
    public void save(String sn, List<RobotVersion> packageVersions) {
        if (CollectionUtil.isEmpty(packageVersions)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        List<DeviceVersionLog.DevicePackage> packages = new ArrayList<>();
        for (RobotVersion version : packageVersions) {
            DeviceVersionLog.DevicePackage pack = new DeviceVersionLog.DevicePackage();
            pack.setVersion(version.getVersion());
            pack.setCtrlVersion(version.getCtrlVersion());
            pack.setVersionName(version.getVersionName());
            pack.setPackageType(version.getPackageType());
            packages.add(pack);
        }
        Collections.sort(packages);
        // SN + hash(packageVersions)
        String id = sn + Arrays.hashCode(packages.toArray());
        Query query = new Query(Criteria.where("id").is(id));
        boolean isExist = mongoTemplate.exists(query, DeviceVersionLog.class);
        if (isExist) {
            // 如果存在就修改最近登录时间
            Update update = new Update().set("lastLoginTime", now);
            mongoTemplate.updateFirst(query, update, DeviceVersionLog.class);
            return;
        }
        //不存在就新增一条
        DeviceVersionLog log = new DeviceVersionLog();
        log.setId(id);
        log.setPackageVersions(packages);
        log.setCreateTime(now);
        log.setLastLoginTime(now);
        mongoTemplate.save(log);
    }
}
