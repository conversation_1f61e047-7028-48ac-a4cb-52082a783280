package com.irobotics.aiot.device.service.impl;

import cn.hutool.log.Log;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.IResponseCode;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.commons.util.SnowflakeUtil;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.bravo.web.util.OperatorUtils;
import com.irobotics.aiot.device.common.CommonEnum;
import com.irobotics.aiot.device.common.ProviderEnum;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.entity.DeviceVideoInfoEntity;
import com.irobotics.aiot.device.mapper.DeviceVideoInfoMapper;
import com.irobotics.aiot.device.service.IDeviceVideoInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.irobotics.aiot.device.utils.LocalDateTimeUtils;
import com.irobotics.aiot.device.vo.admin.AdminDeviceVideoInfoReq;
import com.irobotics.aiot.device.vo.app.VideoInfoUploadVo;
import com.irobotics.aiot.device.vo.app.VideoInfoVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-25
 */
@Service
public class DeviceVideoInfoServiceImpl extends ServiceImpl<DeviceVideoInfoMapper, DeviceVideoInfoEntity> implements IDeviceVideoInfoService {


    public final static String ERROR = "导入失败deviceName:";

    @Override
    public ResponseMessage<VideoInfoVo> getKey(String sn) {
        LambdaQueryWrapper<DeviceVideoInfoEntity>  queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceVideoInfoEntity::getSn,sn);
        queryWrapper.last(" LIMIT 1");
        DeviceVideoInfoEntity infoEntity = this.baseMapper.selectOne(queryWrapper);
        if (Objects.isNull(infoEntity)){
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA);
        }
        VideoInfoVo result = new VideoInfoVo();
        result.setDeviceName(infoEntity.getDeviceName());
        result.setDeviceSecret(infoEntity.getDeviceSecret());
        result.setProductKey(infoEntity.getProductKey());
        return ResponseMessage.buildSuccess(result);
    }


    @Override
    public ResponseMessage<Boolean> update(DeviceVideoInfoEntity deviceVideoInfoEntity) {
        DeviceVideoInfoEntity infoEntity = this.baseMapper.selectById(deviceVideoInfoEntity.getId());
        if (Objects.isNull(infoEntity)){
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA);
        }
        infoEntity.setUpdateBy(OperatorUtils.getOperateBy());
        infoEntity.setUpdateTime(LocalDateTime.now());
        return ResponseMessage.buildSuccess(this.baseMapper.updateById(deviceVideoInfoEntity) > 0);
    }


    @Override
    public ResponseMessage<Boolean> delete(List<String> idList) {
        LambdaQueryWrapper<DeviceVideoInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(DeviceVideoInfoEntity::getId,idList);
        return ResponseMessage.buildSuccess(this.baseMapper.delete(lambdaQueryWrapper) > 0);
    }

    @Override
    public ResponseMessage<Page<DeviceVideoInfoEntity>> page(AdminDeviceVideoInfoReq req) {

        LambdaQueryWrapper<DeviceVideoInfoEntity> lambda = new LambdaQueryWrapper<>();
        Page<DeviceVideoInfoEntity> page = new Page<>();
        page.setCurrent(req.getPage());
        page.setSize(req.getPageSize());
        if (null != req.getBeginTime() && req.getBeginTime() > 0) {
            LocalDateTime startTimeTimeMilli = LocalDateTimeUtils.milliToLocalDateTime(req.getBeginTime());
            lambda.gt(DeviceVideoInfoEntity::getCreateTime, startTimeTimeMilli);
        }
        if (null != req.getEndTime() && req.getEndTime() > 0) {
            LocalDateTime endTimeMilli = LocalDateTimeUtils.milliToLocalDateTime(req.getEndTime());
            lambda.lt(DeviceVideoInfoEntity::getCreateTime, endTimeMilli);
        }
        if (StringUtils.isNotBlank(req.getProductId())){
            lambda.eq(DeviceVideoInfoEntity::getProductId,req.getProductId());
        }
        if (StringUtils.isNotBlank(req.getDeviceName())){
            lambda.eq(DeviceVideoInfoEntity::getDeviceName,req.getDeviceName());
        }
        if (StringUtils.isNotBlank(req.getProductKey())){
            lambda.eq(DeviceVideoInfoEntity::getProductKey,req.getProductKey());
        }
        if (StringUtils.isNotBlank(req.getSn())){
            lambda.eq(DeviceVideoInfoEntity::getSn,req.getSn());
        }
        lambda.orderByDesc(DeviceVideoInfoEntity::getCreateTime);
        Page<DeviceVideoInfoEntity> deviceInfoPage = this.baseMapper.selectPage(page, lambda);

        return ResponseMessage.buildSuccess(deviceInfoPage);
    }

    @Override
    public ResponseMessage<String> upload(MultipartFile file, String paramTenantId) {
        List<VideoInfoUploadVo>  addList = new ArrayList<>();
        StringBuffer sb = new StringBuffer(ERROR);
        try {
            EasyExcel.read(file.getInputStream())
                    // 读取Excel文件
                    .sheet(0)
                    // 读取哪个sheet，索引从0开始
                    .head(VideoInfoUploadVo.class)
                    // 设置映射对象
                    .headRowNumber(1)
                    // 设置1，因为头值占了一行。如果多行头，就设置几行。索引从1开始
                    .registerReadListener(new AnalysisEventListener<VideoInfoUploadVo>() {
                        /**
                         * 每解析一行excel数据，就会被调用一次
                         * @param vo
                         * @param analysisContext
                         */
                        @Override
                        public void invoke(VideoInfoUploadVo vo, AnalysisContext analysisContext) {
                            LambdaQueryWrapper<DeviceVideoInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
                            queryWrapper.eq(DeviceVideoInfoEntity::getDeviceName,vo.getDeviceName());
                            queryWrapper.last(" LIMIT 1");
                            DeviceVideoInfoEntity infoEntity = baseMapper.selectOne(queryWrapper);
                            if (Objects.isNull(infoEntity) && StringUtils.isNotBlank(vo.getProductId())
                                    && StringUtils.isNotBlank(vo.getDeviceName())
                                    && StringUtils.isNotBlank(vo.getProductKey())
                                    && StringUtils.isNotBlank(vo.getDeviceSecret())
                                    && StringUtils.isNotBlank(vo.getSn())){
                                vo.setProductId(vo.getProductId().trim());
                                vo.setDeviceName(vo.getDeviceName().trim());
                                vo.setProductKey(vo.getProductKey().trim());
                                vo.setDeviceSecret(vo.getDeviceSecret().trim());
                                vo.setSn(vo.getSn().trim());
                                addList.add(vo);
                            }else if (Objects.nonNull(infoEntity)){
                                sb.append(vo.getDeviceName()+";");
                            }

                        }

                        /**
                         * 全部解析完被调用
                         * @param analysisContext
                         */
                        @Override
                        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                            LogUtils.info("解析完成");
                        }
                    })
                    .doRead();

        }catch (Exception e){
            e.printStackTrace();
            LogUtils.error("解析数据异常");
            return ResponseMessage.buildFail(ResponseCode.ILLEGAL_PROTOCOL,"数据格式解析异常");
        }
        if (CollectionUtils.isEmpty(addList)){
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA,sb.toString());
        }
        List<DeviceVideoInfoEntity> entityList = new ArrayList<>();
        addList.forEach(e->{
            DeviceVideoInfoEntity videoInfo = new DeviceVideoInfoEntity(SnowflakeUtil.snowflakeId(),
                    e.getProductId(),e.getSn(),e.getDeviceName(),e.getProductKey(),e.getDeviceSecret());
            if (ProviderEnum.ALIYUN.getMsg().equals(e.getProvider())){
                videoInfo.setProvider(ProviderEnum.ALIYUN.getCode());
            }
            entityList.add(videoInfo);
        });
        this.saveBatch(entityList);
        if (ERROR.equals(sb.toString())){
            return ResponseMessage.buildSuccess();
        }
        return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA,sb.toString());
    }
}
