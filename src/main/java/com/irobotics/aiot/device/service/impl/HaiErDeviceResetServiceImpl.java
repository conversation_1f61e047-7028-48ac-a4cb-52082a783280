package com.irobotics.aiot.device.service.impl;

import cn.hutool.core.lang.Tuple;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.commons.util.JsonUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.cache.DeleteRecordCache;
import com.irobotics.aiot.device.cache.UploadResultCache;
import com.irobotics.aiot.device.remote.HaiErBigDataRemote;
import com.irobotics.aiot.device.service.DeviceResetService;
import com.irobotics.aiot.device.service.HaiErDeviceResetService;
import com.irobotics.aiot.device.vo.DeleteResp;
import com.irobotics.aiot.device.vo.IdSnUsernameResp;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 海尔设备出厂初始化服务实现
 * @since 2023/5/18
 */
@Service
public class HaiErDeviceResetServiceImpl implements HaiErDeviceResetService {

    private final static int pageSize = 100;
    private static final Integer ONE_HUNDRED = 100;

    @Resource
    HaiErBigDataRemote haiErBigDataRemote;

    @Resource
    UploadResultCache uploadResultCache;

    @Resource
    DeviceResetService deviceResetService;

    @Resource
    DeleteRecordCache deleteRecordCache;

    @Override
    @Async
    public void deleteHaiErDeviceRecord(String token, String tenantId, String taskId, List<String> sns) {
        if (CollectionUtils.isEmpty(sns)) {
            LogUtils.info("deleteHaiErDeviceRecord sn集合为空 {}", JsonUtils.toJSON(sns));
            return;
        }
        int total = sns.size();
        int page = total / pageSize;
        if (total % pageSize > 0) {
            page += 1;
        }
        int stepPercent = ONE_HUNDRED / page;
        deleteRecordCache.putFinishPercent(0, taskId);

        String result = "";
        List<String> successSns = new ArrayList<>();
        List<String> failSns = new ArrayList<>();
        Tuple innerResult = null;
        for (int i = 0; i < page; i++) {
            List<String> subSns = sns.subList(i * pageSize, Math.min((i + 1) * pageSize, total));
            try {
                innerResult = innerDeleteSweepRecord(taskId, subSns, stepPercent, i);
                LogUtils.info("远程调用海尔大数据接口完成 {} ", JsonUtils.toJSON(subSns));
            } catch (Exception ex) {
                LogUtils.error("远程调用海尔大数据接口[HaiErBigDataRemote#deleteSweepRecord]出现异常 {}", ex.getMessage(), ex);
                //远程调用出现问题，重试3次，每次等待500ms
                int retry = 1;
                while (retry <= 3) {
                    try {
                        Thread.sleep(500);
                        LogUtils.info("远程调用海尔大数据接口[HaiErBigDataRemote#deleteSweepRecord]重试第 {} 次", retry);
                        innerResult = innerDeleteSweepRecord(taskId, subSns, stepPercent, i);
                        if (innerResult.get(0)) {
                            break;
                        }
                    } catch (InterruptedException ignored) {
                    } finally {
                        retry++;
                    }
                }
            } finally {
                if (ObjectUtils.isNotEmpty(innerResult) && (boolean) innerResult.get(0)) {
                    successSns.addAll(subSns);
                } else {
                    failSns.addAll(subSns);
                }
            }
        }

        List<DeleteResp> resultList = new ArrayList<>();
        successSns.forEach(sn -> {
            DeleteResp resp = new DeleteResp("0", true, sn);
            resultList.add(resp);
        });
        failSns.forEach(sn -> {
            DeleteResp resp = new DeleteResp("0", false, sn);
            resultList.add(resp);
        });

        deviceResetService.saveRecordToMongo(token, tenantId, taskId, resultList);
        deleteRecordCache.putResult(taskId, resultList);
        deleteRecordCache.putFinishPercent(ONE_HUNDRED, taskId);
        //return ResponseMessage.buildSuccess(result);
    }

    private Tuple innerDeleteSweepRecord(String taskId, List<String> subSns, int stepPercent, int i) {
        Tuple result = haiErBigDataRemote.deleteSweepRecord(subSns);
        deleteRecordCache.putFinishPercent((stepPercent * (i + 1)) - 1, taskId);
        return result;
    }

    @Override
    public void saveSnToCache(String taskId, List<String> snList) {
        if (CollectionUtils.isEmpty(snList)) {
            return;
        }
        List<IdSnUsernameResp> idSnUsernameRespList = new ArrayList<>();
        snList.stream().distinct().forEach(t -> {
            IdSnUsernameResp idSnUsernameResp = new IdSnUsernameResp();
            idSnUsernameResp.setSn(t);
            idSnUsernameRespList.add(idSnUsernameResp);
        });
        uploadResultCache.putFinishPercent(ONE_HUNDRED, taskId);
        uploadResultCache.putResult(taskId, idSnUsernameRespList);
    }
}
