package com.irobotics.aiot.device.service.impl;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.entity.DeviceBindBaseEntity;
import com.irobotics.aiot.device.remote.DeviceShadowRemote;
import com.irobotics.aiot.device.remote.model.ClearFlowParam;
import com.irobotics.aiot.device.service.IBaseAuthService;
import com.irobotics.aiot.device.service.IDeviceBindBaseService;
import com.irobotics.aiot.device.vo.base.BaseUnAuthDeviceReq;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class IBaseAuthServiceImpl implements IBaseAuthService {

    @Resource
    private DeviceShadowRemote dataStatisticsServiceRemote;

    @Autowired
    private IDeviceBindBaseService deviceBindBaseService;

    @Override
    public ResponseMessage<Boolean> baseUnAuth(BaseUnAuthDeviceReq baseUnAuthDeviceReq) {
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(baseUnAuthDeviceReq.getSn())) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "sn");
        }
        String tenantId = SystemContextUtils.getTenantId();
        String userId = SystemContextUtils.getId();
        String sn = baseUnAuthDeviceReq.getSn();

        ResponseMessage<List<DeviceBindBaseEntity>> baseListResp = deviceBindBaseService.getBaseBind(userId, null, null, sn);
        if(!baseListResp.isSuccess()){
            LogUtils.error("unAuthByBase error ,baseListResp:{}", baseListResp);
            return ResponseMessage.buildSuccess(false);
        }

        List<DeviceBindBaseEntity> baseList = baseListResp.getResult();
        if (CollectionUtils.isEmpty(baseList)) {
            return ResponseMessage.buildFail(ResponseCode.ILLEGAL_OPERATE, "【baseInfo、deviceInfo、roomInfo】not match");
        }
        //存在基站和主机的绑定关系
        List<String> sns = new ArrayList<>(1);
        sns.add(sn);
        ClearFlowParam clearFlowParam = new ClearFlowParam().snList(sns).tenantId(tenantId);
        ResponseMessage<Boolean> dataResponseMessage = dataStatisticsServiceRemote.flow(clearFlowParam);
        if (!dataResponseMessage.isSuccess()) {
            LogUtils.error("通过sn批量清除设备流水失败，msg={}", dataResponseMessage.getMsg());
            return ResponseMessage.buildSuccess(false);
        }
        return ResponseMessage.buildSuccess(true);
    }
}
