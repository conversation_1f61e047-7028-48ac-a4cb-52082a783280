package com.irobotics.aiot.device.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.device.entity.DeviceBindBaseEntity;
import com.irobotics.aiot.device.mapper.DeviceBindBaseMapper;
import com.irobotics.aiot.device.service.IBaseBindService;
import com.irobotics.aiot.device.vo.base.BaseBindVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class IBaseBindServiceImpl extends ServiceImpl<DeviceBindBaseMapper, DeviceBindBaseEntity> implements IBaseBindService {

    @Override
    public ResponseMessage<BaseBindVo> baseBind(String baseId) {
        String tenantId = SystemContextUtils.getTenantId();
        List<BaseBindVo> resultList = this.baseMapper.getBindListByBaseId(baseId,tenantId);
        if (CollectionUtils.isEmpty(resultList)){
            return ResponseMessage.buildSuccess(null);
        }
        BaseBindVo baseBindVo = resultList.get(0);
        if(StringUtils.isBlank(baseBindVo.getOwner())){
            baseBindVo.setOwner(baseId);
        }
        return ResponseMessage.buildSuccess(baseBindVo);
    }
}
