package com.irobotics.aiot.device.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.remote.IcloudIntentServiceRemote;
import com.irobotics.aiot.device.remote.model.SyncDeviceReportReq;
import com.irobotics.aiot.device.service.IcloudIntentService;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class IcloudIntentServiceImpl implements IcloudIntentService {

    private static final String IcloudUserKey = "IcloudUserDevice:User::AllUser_null";

    @Autowired
    private IcloudIntentServiceRemote icloudIntentServiceRemote;
    @Autowired
    RedisTemplate<String, Object> redisTemplate;

    @Async
    @Override
    public void insert(String userId, String sn) {
        List<String> userList = getIcloudUser();
        if(CollectionUtils.isEmpty(userList) || !userList.contains(userId)){
            return;
        }
        SyncDeviceReportReq req = getSyncDeviceReportReq(userId, Arrays.asList(sn), null);
        syncDevice(req);
    }

    @Async
    @Override
    public void remove(String userId, String sn) {
        List<String> userList = getIcloudUser();
        if(CollectionUtils.isEmpty(userList) || !userList.contains(userId)){
            return;
        }
        SyncDeviceReportReq req = getSyncDeviceReportReq(userId, null, Arrays.asList(sn));
        syncDevice(req);
    }

    @Async
    @Override
    public void batchInsertByUserIds(List<String> userIds, String sn) {
        if (CollectionUtil.isEmpty(userIds)) {
            return;
        }

        List<String> userList = getIcloudUser();
        if(CollectionUtils.isEmpty(userList)){
            return;
        }

        userIds.forEach(userId -> {
            if(!userList.contains(userId)){
                return;
            }
            SyncDeviceReportReq req = getSyncDeviceReportReq(userId, Arrays.asList(sn), null);
            syncDevice(req);
        });

    }

    @Async
    @Override
    public void batchRemoveByUserIds(List<String> userIds, String sn) {
        if (CollectionUtil.isEmpty(userIds)) {
            return;
        }

        List<String> userList = getIcloudUser();
        if(CollectionUtils.isEmpty(userList)){
            return;
        }

        userIds.forEach(userId -> {
            if(!userList.contains(userId)){
                return;
            }
            SyncDeviceReportReq req = getSyncDeviceReportReq(userId, null, Arrays.asList(sn));
            syncDevice(req);
        });
    }

    @Async
    @Override
    public void batchInsertBySns(String userId, List<String> sns) {
        if (CollectionUtil.isEmpty(sns)) {
            return;
        }
        List<String> userList = getIcloudUser();
        if(CollectionUtils.isEmpty(userList) || !userList.contains(userId)){
            return;
        }
        SyncDeviceReportReq req = getSyncDeviceReportReq(userId, sns, null);
        syncDevice(req);
    }

    @Async
    @Override
    public void batchRemoveBySns(String userId, List<String> sns) {
        if (CollectionUtil.isEmpty(sns)) {
            return;
        }
        List<String> userList = getIcloudUser();
        if(CollectionUtils.isEmpty(userList) || !userList.contains(userId)){
            return;
        }
        SyncDeviceReportReq req = getSyncDeviceReportReq(userId, null, sns);
        syncDevice(req);
    }

    private void syncDevice(SyncDeviceReportReq req) {
        try {
            icloudIntentServiceRemote.syncDeviceReport(req);
        }catch (Exception e){
            LogUtils.error(e, "绑定关系同步到语控异常，req：{}", JSONObject.toJSONString(req));
        }
    }

    @NotNull
    private SyncDeviceReportReq getSyncDeviceReportReq(String userId, List<String> addSnList, List<String> delSnList) {
        SyncDeviceReportReq req = new SyncDeviceReportReq();

        if(CollectionUtils.isNotEmpty(addSnList)){
            Map<String, List<String>> addUserSnMap = new HashMap<>();
            addUserSnMap.put(userId, addSnList);
            req.setAddUserSnMap(addUserSnMap);
        }

        if(CollectionUtils.isNotEmpty(delSnList)){
            Map<String, List<String>> delUserSnMap = new HashMap<>();
            delUserSnMap.put(userId, delSnList);
            req.setDelUserSnMap(delUserSnMap);
        }

        return req;
    }

    @Nullable
    private List<String> getIcloudUser() {
        try {
            List<String> userList = (List<String>) redisTemplate.opsForValue().get(IcloudUserKey);
            if(CollectionUtils.isEmpty(userList)){
                ResponseMessage<List<String>> result = icloudIntentServiceRemote.getAllUser();
                if(result.isSuccess() && CollectionUtils.isNotEmpty(result.getResult())){
                    userList = result.getResult();
                }
            }
            return userList;
        }catch (Exception e){
            LogUtils.error(e,"获取语控用户异常");
        }
        return null;
    }
}
