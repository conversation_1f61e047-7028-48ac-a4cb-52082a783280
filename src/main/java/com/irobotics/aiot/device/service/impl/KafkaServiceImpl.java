package com.irobotics.aiot.device.service.impl;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.basic.exception.AppRuntimeException;
import com.irobotics.aiot.bravo.commons.util.JsonUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.common.DevicePushTag;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.entity.DeviceShadowEntity;
import com.irobotics.aiot.device.kafka.KafkaProducer;
import com.irobotics.aiot.device.remote.DeviceShadowRemote;
import com.irobotics.aiot.device.service.KafkaService;
import com.irobotics.aiot.device.vo.KafkaData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/10/15 16:39
 */
@Service
public class KafkaServiceImpl implements KafkaService {

    /**
     * 设备影子
     */
    @Autowired
    private DeviceShadowRemote deviceShadowRemote;

    /**
     * kafka生产者
     */
    @Autowired
    private KafkaProducer kafkaProducer;

    /**
     * 设备注册主题
     */
    @Value("${kafka.bigDataService.device.register:deviceRegister}")
    private String deviceRegister;

    /**
     * kafka
     */
    @Resource
    private KafkaTemplate kafkaTemplate;

    /**
     * 设备缓存
     */
    @Autowired
    private DeviceInfoCache deviceInfoCache;

    /**
     * 生成服务调用消息内容
     *
     * @param deviceId 设备id
     * @param tenantId 厂商id
     * @param content  消息主体
     * @param pushTag  消息类型
     * @return json
     */
    @Override
    public String genServiceInvokeMsg(String deviceId, String tenantId, String content, DevicePushTag pushTag) {
        DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheById(tenantId, deviceId);

        if (Objects.isNull(deviceInfo)) {
            LogUtils.error("genServiceInvokeMsg,设备不存在,deviceId={}", deviceId);
            throw new AppRuntimeException(ResponseCode.NOT_FOUND_DATA, "设备不存在");
        }

        String sn = deviceInfo.getSn();
        String infoZone = deviceInfo.getZone();

        //使用kafka发送消息
        String tag = pushTag.getTag();
        String mqttMethod = "thing.service." + tag;

        //查询设备影子
        ResponseMessage<DeviceShadowEntity> shadowResp = deviceShadowRemote.findBySN(sn);
        if (!shadowResp.isSuccess()) {
            LogUtils.error("获取设备影子失败，msg={}", shadowResp.getMsg());
            throw new AppRuntimeException(ResponseCode.NOT_FOUND_DATA, "获取设备影子失败");
        }
        DeviceShadowEntity result = shadowResp.getResult();
        String version = result.getModelVersion();
        String productKey = result.getProductKey();
        //mqtt topic 示例: /mqtt/productKey001(设备影子里)/设备sn/thing/service_invoke/类型（reset、upgrade、online、offline）
        // /mqtt/productKey001/DF0102030405/thing/service_invoke/SetWeight
//        String mqttTopic = "/mqtt/"+productKey+"/"+sn+"/thing/service_invoke/" +tag;
        String str = "/mqtt/%s/%s/thing/service_invoke/%s";
        String mqttTopic = String.format(str, productKey, sn, tag);
        KafkaData kafkaData = new KafkaData(System.currentTimeMillis(), mqttTopic, mqttMethod, sn, tenantId, productKey, version, infoZone, System.currentTimeMillis(), content);

        //由于远程查看设备影子不具备，因此以下两行代码用于测试用
       /* String mqttTopic = "/mqtt/" + sn + "/thing/service_invoke/" + tag;
        KafkaData kafkaData1 = new KafkaData(System.currentTimeMillis(), mqttTopic, mqttMethod, sn, tenantId, "anxiao-productKey", "1.0", infoZone, System.currentTimeMillis(), content);*/

        return JsonUtils.toJSON(kafkaData);
    }

    @Override
    public void senServiceInvoke(String deviceId, String tenantId, String content, DevicePushTag pushTag, String topic) {
        String data = genServiceInvokeMsg(deviceId, tenantId, content, pushTag);
        kafkaProducer.sendMessage(topic, data);
    }

    @Override
    public void sendBatchServiceInvoke(List<String> deviceIds, String tenantId, String content, DevicePushTag pushTag, String topic) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return;
        }

        for (String deviceId : deviceIds) {
            String data = genServiceInvokeMsg(deviceId, tenantId, content, pushTag);
            kafkaProducer.sendMessage(topic, data);
        }
    }

    @Override
    @Async
    public void deviceRegister(DeviceInfoEntity infoEntity) {
        try {
            kafkaTemplate.send(deviceRegister, JsonUtils.toJSON(infoEntity));
        } catch (Exception e) {
            LogUtils.error(e, "设备统计，写入 kafka 异常", e.getMessage());
        }

    }
}
