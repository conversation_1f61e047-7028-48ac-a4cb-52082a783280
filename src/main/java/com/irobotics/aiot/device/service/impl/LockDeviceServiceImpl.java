package com.irobotics.aiot.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.irobotics.aiot.bravo.basic.base.CountryCity;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.exception.AppRuntimeException;
import com.irobotics.aiot.bravo.basic.exception.FastRuntimeException;
import com.irobotics.aiot.bravo.commons.util.JsonUtils;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.common.NumberEnum;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.entity.ProductModeEntity;
import com.irobotics.aiot.device.mapper.DeviceInfoMapper;
import com.irobotics.aiot.device.remote.ProductServiceRemote;
import com.irobotics.aiot.device.remote.SmartHomeServiceRemote;
import com.irobotics.aiot.device.service.DeviceAuthService;
import com.irobotics.aiot.device.service.KafkaService;
import com.irobotics.aiot.device.service.LockDeviceService;
import com.irobotics.aiot.device.service.LogService;
import com.irobotics.aiot.device.utils.EnDecryptionUtil;
import com.irobotics.aiot.device.utils.HKDF;
import com.irobotics.aiot.device.utils.RandomUtil;
import com.irobotics.aiot.device.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.time.LocalDateTime;
import java.util.*;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.*;

/**
 * 门锁实现类
 */
@Service
public class LockDeviceServiceImpl implements LockDeviceService {

    private static final String LOCK_PRI_KEY = "Lock::PriKey_";
    private static final String LOCK_AUTH_VAL = "Lock::AuthVal_";
    private static final String LOCK_E_SHARE_KEY = "Lock::EShareKey_";
    static final String LOCK_LTMK_KEY = "Lock::LTMK_";
    private static final String LOCK_SESSION_KEY = "Lock::SessionKey_";

    private static final String LOGIN_SALT = "sciot-login-salt";

    private static final String HKDF_LOGIN_INFO = "sciot-login-info";
    private static final String HKDF_REG_INFO = "sciot-register-info";
    private static final String PRODUCT_MODEL_CODE = "ProductModeCode";
    private static final String PRODUCT_MODEL_CODE_ERROR = "获取产品型号信息异常";

    private static Map<Integer, Integer> binaryMap = new HashMap<>();

    static {
        binaryMap.put(0, 0b0);
        binaryMap.put(1, 0b1);
        binaryMap.put(NumberEnum.TWO.getNumber(), 0b10);
        binaryMap.put(NumberEnum.THREE.getNumber(), 0b11);
        binaryMap.put(NumberEnum.FOUR.getNumber(), 0b100);
        binaryMap.put(NumberEnum.FIVE.getNumber(), 0b101);
        binaryMap.put(NumberEnum.SIX.getNumber(), 0b110);
        binaryMap.put(NumberEnum.SEVEN.getNumber(), 0b111);
        binaryMap.put(NumberEnum.EIGHT.getNumber(), 0b1000);
        binaryMap.put(NumberEnum.NIGHT.getNumber(), 0b1001);
    }

    @Autowired
    private DeviceInfoMapper deviceInfoMapper;
    @Autowired
    private ProductServiceRemote productServiceRemote;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private KafkaService kafkaService;
    @Autowired
    private DeviceAuthService deviceAuthService;
    @Autowired
    private DeviceInfoServiceImpl deviceInfoService;
    @Autowired
    private DeviceInfoCache deviceInfoCache;
    @Autowired
    private LogService logService;
    @Autowired
    private SmartHomeServiceRemote smartHomeServiceRemote;

    @Override
    public ResponseMessage<LockGetPcdRep> getPasscode(LockLoginParam param) {
        if (param == null) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "param");
        }
        String sn = param.getSn();
        if (StringUtils.isBlank(sn)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "sn");
        }
        if (StringUtils.isBlank(param.getMac())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "mac");
        }
        if (param.getPasscodeLength() == null || param.getPasscodeLength() <= 0) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "PasscodeLength");
        }
        if (StringUtils.isBlank(param.getProductModeCode())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, PRODUCT_MODEL_CODE);
        }

        ResponseMessage<ProductModeEntity> getProductMode = productServiceRemote.getByCode(SystemContextUtils.getTenantId(), param.getProductModeCode());
        if (!getProductMode.isSuccess()) {
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, PRODUCT_MODEL_CODE_ERROR);
        }
        if (getProductMode.getResult() == null) {
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, "无法根据该产品型号代码查询到产品型号");
        }

        //sn_key
        String key = null;
        DeviceInfoEntity info = deviceInfoMapper.getBySn(SystemContextUtils.getTenantId(), param.getSn());
        if (info != null) {
            if (!StringUtils.equals(info.getMac(), param.getMac())) {
                return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "mac与已注册设备信息不匹配");
            }
            key = info.getKey();
        } else {
            ResponseMessage<ProductSnDetailEntity> snDetailRemote = productServiceRemote.getSnDetail(sn);
            if (!snDetailRemote.isSuccess() || snDetailRemote.getResult() == null) {
                return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "无法获取sn信息");
            }
            ProductSnDetailEntity snDetail = snDetailRemote.getResult();
            key = snDetail.getKey();
        }

        //获取随机数
        String random = RandomUtil.getRandom(param.getPasscodeLength());
        //Generate ECC256 key pair(app_pub,app_pri)
        String iotPub = getIotPub(param.getSn());

        genAuthVal(sn, key, random);

        LockGetPcdRep rep = new LockGetPcdRep();
        rep.setIotPub(iotPub);

        byte[] randomArr = new byte[param.getPasscodeLength()];
        for (int i = 0; i < param.getPasscodeLength(); i++) {
            randomArr[i] = (byte) Integer.parseInt(String.valueOf(random.charAt(i)));
        }

        rep.setPasscode(EnDecryptionUtil.base64Encode(randomArr));

        return ResponseMessage.buildSuccess(rep);
    }

    @Override
    public ResponseMessage<LockBindRep> publicKeyExchange(LockBindReq lockBindReq) throws Exception {
        if (lockBindReq.getCode() != 0) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "code不为0");
        }
        //1、eShareKey = ECDHE(dev_pub,app_pri)
        String lockPriKey = (String) redisTemplate.opsForValue().get(LOCK_PRI_KEY + lockBindReq.getSn());
        byte[] eShareKey = EnDecryptionUtil.ecdhe(lockBindReq.getDevPub(), lockPriKey);
        redisTemplate.opsForValue().set(LOCK_E_SHARE_KEY + lockBindReq.getSn(), EnDecryptionUtil.base64Encode(eShareKey));

        //2、Generate 16 bytes random code(rand_app)
        String rand_app = RandomUtil.getRandom(NumberEnum.SIXTEEN.getNumber());

        //3、app_confirm = HMAC(eShareKey,rand_app||authValApp)
        byte[] iotrandBytes = rand_app.getBytes(StandardCharsets.UTF_8);
        byte[] hmacKey = new byte[NumberEnum.THIRTY_TWO.getNumber()];
        byte[] lockAuthVal = EnDecryptionUtil.base64Decode((String) redisTemplate.opsForValue().get(LOCK_AUTH_VAL + lockBindReq.getSn()));

        for (int i = 0; i < hmacKey.length; i++) {
            if (i < NumberEnum.SIXTEEN.getNumber()) {
                hmacKey[i] = iotrandBytes[i];
            } else {
                hmacKey[i] = lockAuthVal[i - NumberEnum.SIXTEEN.getNumber()];
            }
        }

        byte[] iotConfirm = EnDecryptionUtil.encryptHMAC(hmacKey, eShareKey);

        LogUtils.info("HMAC信息：rand_app={},hmackey={},lockAuthVal={},iotConfirm={}", rand_app, EnDecryptionUtil.base64Encode(hmacKey),
                EnDecryptionUtil.base64Encode(lockAuthVal), EnDecryptionUtil.base64Encode(iotConfirm));

        //4、LTMK = HKDF(eShareKey,salt=authValApp)
        byte[] hkdf = HKDF.deriveSecrets(eShareKey, lockAuthVal, HKDF_REG_INFO.getBytes(), NumberEnum.THIRTY_TWO.getNumber());
        String ltmk = EnDecryptionUtil.base64Encode(hkdf);
        redisTemplate.opsForValue().set(LOCK_LTMK_KEY + lockBindReq.getSn(), ltmk);

        LockBindRep lockBindRep = new LockBindRep();
        lockBindRep.setAppConfirm(EnDecryptionUtil.base64Encode(iotConfirm));
        lockBindRep.setRandCode(EnDecryptionUtil.base64Encode(rand_app.getBytes()));

        return ResponseMessage.buildSuccess(lockBindRep);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseMessage<CheckDevConfirmRep> checkDevConfirm(CheckDevConfirmParam param) throws Exception {
        CheckDevConfirmRep checkDevConfirmRep = new CheckDevConfirmRep();
        checkDevConfirmRep.setCode(0);
        String sn = param.getSn();

        if (StringUtils.isBlank(param.getFamilyId())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "家庭id");
        }
        if (StringUtils.isBlank(param.getSn())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "sn");
        }
        if (StringUtils.isBlank(param.getMac())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "mac");
        }
        if (StringUtils.isBlank(param.getRandDev())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "随机数");
        }
        if (StringUtils.isBlank(param.getDevConfirm())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "devConfirm");
        }

        if (StringUtils.isBlank(param.getProductModeCode())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, PRODUCT_MODEL_CODE);
        }
        ResponseMessage<ProductModeEntity> getProductMode = productServiceRemote.getByCode(SystemContextUtils.getTenantId(), param.getProductModeCode());
        if (!getProductMode.isSuccess()) {
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, PRODUCT_MODEL_CODE_ERROR);
        }
        if (getProductMode.getResult() == null) {
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, "无法根据该产品型号代码查询到产品型号");
        }

        ResponseMessage<ProductSnDetailEntity> snDetailRemote = productServiceRemote.getSnDetail(sn);
        if (!snDetailRemote.isSuccess() || snDetailRemote.getResult() == null) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "无法获取sn信息");
        }
        ProductSnDetailEntity snDetail = snDetailRemote.getResult();

        DeviceInfoEntity info = deviceInfoMapper.getBySn(SystemContextUtils.getTenantId(), param.getSn());
        if (info != null && !StringUtils.equals(info.getMac(), param.getMac())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "mac与已注册设备信息不匹配");
        }

        byte[] lockAuthVal = EnDecryptionUtil.base64Decode((String) redisTemplate.opsForValue().get(LOCK_AUTH_VAL + sn));
        byte[] devRandom = EnDecryptionUtil.base64Decode(param.getRandDev());
        byte[] HMACKey = new byte[NumberEnum.THIRTY_TWO.getNumber()];
        for (int i = 0; i < HMACKey.length; i++) {
            if (i < NumberEnum.SIXTEEN.getNumber()) {
                HMACKey[i] = devRandom[i];
            } else {
                HMACKey[i] = lockAuthVal[i - NumberEnum.SIXTEEN.getNumber()];
            }
        }
        byte[] eShareKey = EnDecryptionUtil.base64Decode((String) redisTemplate.opsForValue().get(LOCK_E_SHARE_KEY + sn));
        byte[] bytesIot = EnDecryptionUtil.encryptHMAC(HMACKey, eShareKey);
        String iotConfrimRes = EnDecryptionUtil.base64Encode(bytesIot);

        LogUtils.info("checkDevConfirm HMAC信息：sn={},rand_dev={},hmackey={},lockAuthVal={},iotConfirm={}", param.getSn(), param.getRandDev(), EnDecryptionUtil.base64Encode(HMACKey),
                EnDecryptionUtil.base64Encode(lockAuthVal), iotConfrimRes);
        boolean res = iotConfrimRes.equals(param.getDevConfirm());
        if (!res) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "验证不通过");
        }

        LogUtils.info("注册验证已通过");

        DeviceInfoEntity robot = null;

        String ip = SystemContextUtils.getContextValue(ContextKey.CLIENT_IP);
        CountryCity countryCity = deviceAuthService.getCountryCity(ip);
        final String countryLoc = countryCity != null ? JsonUtils.toJSON(countryCity) : null;

        ResponseMessage<List<ProductModeEntity>> modes = productServiceRemote.getModeByIds(Arrays.asList(snDetail.getProductModeId()));
        if (!modes.isSuccess() || CollectionUtils.isEmpty(modes.getResult())) {
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, "无法获取产品型号信息");
        }
        ProductModeEntity productModeEntity = modes.getResult().get(0);

        boolean isActive = info != null;

        if (!isActive) {
            robot = lockRegist(param, sn, snDetail, ip, countryLoc, productModeEntity);
        } else {
            LambdaQueryWrapper<DeviceInfoEntity> lambda = new QueryWrapper<DeviceInfoEntity>().lambda();
            lambda.eq(DeviceInfoEntity::getSn, sn);
            robot = deviceInfoMapper.selectOne(lambda);
            robot.setCity(countryLoc);
            robot.setUpdateTime(LocalDateTime.now());
            robot.setPhotoUrl(productModeEntity.getPhotoUrl());
            deviceInfoMapper.updateById(robot);
        }
        checkDevConfirmRep.setDeviceId(robot.getId());

        bindDevice(param, sn, robot);
        //删除注册流程中的缓存，只需保留ltmk
        redisTemplate.delete(LOCK_PRI_KEY + sn);
        redisTemplate.delete(LOCK_AUTH_VAL + sn);
        redisTemplate.delete(LOCK_E_SHARE_KEY + sn);
        return ResponseMessage.buildSuccess(checkDevConfirmRep);
    }

    private DeviceInfoEntity lockRegist(CheckDevConfirmParam param, String sn, ProductSnDetailEntity snDetail, String ip, String countryLoc, ProductModeEntity productModeEntity) {
        DeviceInfoEntity robot;
        int updateSnDetailResult;
        // 改变sn表激活状态，并在设备表插入新设备
        ResponseMessage<Integer> updateSnDetailResponse = productServiceRemote.updateSnDetailForActive(SystemContextUtils.getTenantId(), sn, 1);
        if (!updateSnDetailResponse.isSuccess()) {
            throw new FastRuntimeException(INNER_SERVER_ERROR, "远程调用修改sn激活状态失败,sn=" + sn);
        }
        updateSnDetailResult = updateSnDetailResponse.getResult();
        if (updateSnDetailResult <= 0) {
            throw new AppRuntimeException(INNER_SERVER_ERROR, "设备激活授权码失败,或sn已被激活，请重试" + sn);
        }

        String nickname = deviceInfoService.genNickName(sn, productModeEntity);

        robot = new DeviceInfoEntity();
        robot.setId(snDetail.getId());
        robot.setIp(ip);
        robot.setOnlineTime(LocalDateTime.now());
        robot.setSn(sn);
        robot.setKey(snDetail.getKey());
        robot.setProductModeCode(productModeEntity.getCode());
        robot.setPhotoUrl(productModeEntity.getPhotoUrl());
        robot.setMac(param.getMac());
        robot.setCreateTime(LocalDateTime.now());
        robot.setDefaultNickname(nickname);
        robot.setNickname(nickname);
        robot.setCity(countryLoc);
        robot.setVersions(null);
        robot.setCreateTime(LocalDateTime.now());
        robot.setUpdateTime(LocalDateTime.now());
        robot.setProductId(productModeEntity.getProductInfoId());
        try {
            int insertRes = deviceInfoMapper.insert(robot);
            if (insertRes <= 0) {
                //正常不会走到这里
                LogUtils.error(new IllegalStateException(), "SN={} 设备激活注册信息失败,需要重置激活状态...", sn);
                throw new AppRuntimeException(INNER_SERVER_ERROR, "设备激活注册信息失败,需要重置激活状态" + sn);
            }
            // 将激活设备进行统计
            robot.setTenantId(SystemContextUtils.getTenantId());
            LogUtils.info("统计激活设备:", robot.toString());
            kafkaService.deviceRegister(robot);

        } catch (Exception e) {
            //发生异常，回写sn的激活状态
            if (updateSnDetailResult > 0) {
                // 改变sn表激活状态
                ResponseMessage<Integer> response = productServiceRemote.updateSnDetailForActive(SystemContextUtils.getTenantId(), sn, 0);
                if (!response.isSuccess()) {
                    throw new FastRuntimeException(INNER_SERVER_ERROR, "远程调用修改sn激活状态失败,sn=" + sn);
                }
                Integer result = response.getResult();
                if (result <= 0) {
                    throw new AppRuntimeException(INNER_SERVER_ERROR, "设备激活授权码失败,请重试" + sn);
                }
            }
            LogUtils.error(e, "设备注册失败,sn={},mac={}, tenantId={},  exceptionMsg={}, ",
                    sn, param.getMac(), SystemContextUtils.getTenantId(), e.getMessage());
            throw new AppRuntimeException(INNER_SERVER_ERROR, "设备注册失败！");
        }
        return robot;
    }

    private void bindDevice(CheckDevConfirmParam param, String sn, DeviceInfoEntity robot) {
        //远程调用保存用户房间设备之间的绑定关系,覆盖保存
        ResponseMessage<Boolean> responseMessage = smartHomeServiceRemote.bindDevice(
                new RoomBindDeviceReq(param.getFamilyId(), param.getRoomId(), robot.getId(), robot.getNickname(), sn, robot.getProductId()));
        if (!responseMessage.isSuccess()) {
            String msg = "门锁绑定发生异常.userId=%s,deviceId=%s,tenantId=%s,familyId=%s, roomId=%s,responseMessage=%s";
            String format = String.format(msg, SystemContextUtils.getId(), robot.getId(), SystemContextUtils.getId(), param.getFamilyId(), param.getRoomId(), responseMessage.getMsg());
            LogUtils.error(format);
            throw new AppRuntimeException("绑定房间异常");
        }
    }

    @Override
    public ResponseMessage<LoginGetPubRep> loginGetPub(LockLoginParam param) throws Exception {
        if (StringUtils.isBlank(param.getSn())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "sn");
        }
        if (StringUtils.isBlank(param.getMac())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "mac");
        }
        DeviceInfoEntity info = deviceInfoMapper.getByMac(SystemContextUtils.getTenantId(), param.getMac());
        if (info == null) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "无法根据mac查询设备注册信息");
        }
        if (!Objects.equals(info.getSn(), param.getSn())) {
            return ResponseMessage.buildFail(ILLEGAL_OPERATE,
                    "mac已被占用,已注册的设备mac【" + info.getMac() + "】，sn【" + info.getSn() + "】；当前的mac【" + param.getMac() + "】,sn【" + param.getSn() + "】");
        }

        if (StringUtils.isBlank(param.getProductModeCode())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, PRODUCT_MODEL_CODE);
        }

        ResponseMessage<ProductModeEntity> getProductMode = productServiceRemote.getByCode(SystemContextUtils.getTenantId(), param.getProductModeCode());
        if (!getProductMode.isSuccess()) {
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, PRODUCT_MODEL_CODE_ERROR);
        }
        if (getProductMode.getResult() == null) {
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, "无法根据该产品型号代码查询到产品型号");
        }

        String iotPub = getIotPub(param.getSn());
        LoginGetPubRep loginGetPubRep = new LoginGetPubRep();
        loginGetPubRep.setIotPub(iotPub);
        return ResponseMessage.buildSuccess(loginGetPubRep);
    }

    @Override
    public ResponseMessage<LockLoginGetVerifyRep> loginGetVerifyData(LockLoginGetVerifyReq lockLoginGetVerifyReq) throws Exception {
        String privateKey = (String) redisTemplate.opsForValue().get(LOCK_PRI_KEY + lockLoginGetVerifyReq.getSn());
        String ltmk = (String) redisTemplate.opsForValue().get(LOCK_LTMK_KEY + lockLoginGetVerifyReq.getSn());
        byte[] iotShareKey = EnDecryptionUtil.ecdhe(lockLoginGetVerifyReq.getDevPub(), privateKey);
        byte[] iotBytes = EnDecryptionUtil.base64Decode(ltmk);
        int length = iotShareKey.length > iotBytes.length ? iotShareKey.length : iotBytes.length;
        byte[] iotEd = new byte[length];
        for (int i = 0; i < length; i++) {
            byte eshare = ((i + 1) > iotShareKey.length ? 0 : iotShareKey[i]);
            byte iot = ((i + 1) > iotBytes.length ? 0 : iotBytes[i]);
            iotEd[i] = (byte) (eshare ^ iot);
        }

        byte[] iotSessionKey = HKDF.deriveSecrets(iotEd, LOGIN_SALT.getBytes(), HKDF_LOGIN_INFO.getBytes(), NumberEnum.SIXTY_FOUR.getNumber());
        String iotSessionKeyStr = EnDecryptionUtil.base64Encode(iotSessionKey);
        redisTemplate.opsForValue().set(LOCK_SESSION_KEY + lockLoginGetVerifyReq.getSn(), iotSessionKeyStr);

        byte[] devPubOri = EnDecryptionUtil.base64Decode(lockLoginGetVerifyReq.getDevPub());
        byte[] devPubCrc = new byte[devPubOri.length - 1];
        for (int i = 0; i < devPubCrc.length; i++) {
            devPubCrc[i] = devPubOri[i + 1];
        }
        int crc32 = EnDecryptionUtil.crc32(devPubCrc);
        byte[] crc32Bytes = EnDecryptionUtil.intToByte(crc32);

        //hkdf输出64字节 取前16字节作为session key 后面4字节不用 再后面13字节所为IV传入
        byte[] aesccmKey = new byte[NumberEnum.SIXTEEN.getNumber()];
        byte[] iv = new byte[NumberEnum.THIRTEEN.getNumber()];
        for (int i = 0; i < NumberEnum.THIRTY_THREE.getNumber() + 1; i++) {
            if (i < NumberEnum.SIXTEEN.getNumber()) {
                aesccmKey[i] = iotSessionKey[i];
            } else if (i < NumberEnum.TWENTY.getNumber()) {

            } else {
                iv[i - NumberEnum.TWENTY.getNumber()] = iotSessionKey[i];
            }
        }

        byte[] verifyData = EnDecryptionUtil.aesccm(aesccmKey, crc32Bytes, iv);
        LogUtils.info("loginGetVerifyData,crc32:{},crc32Bytes:{},aesccmKey:{},iv:{},verifyData:{},iotSessionKey:{}",
                crc32, EnDecryptionUtil.bytesToHex(crc32Bytes), EnDecryptionUtil.bytesToHex(aesccmKey),
                EnDecryptionUtil.bytesToHex(iv), EnDecryptionUtil.bytesToHex(verifyData), iotSessionKeyStr);

        LockLoginGetVerifyRep rep = new LockLoginGetVerifyRep();
        rep.setVerifyData(EnDecryptionUtil.base64Encode(verifyData));
        return ResponseMessage.buildSuccess(rep);
    }

    @Override
    public ResponseMessage<LoginEndRep> loginEnd(LoginEndReq loginEndReq) {
        if (loginEndReq == null) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "locingEndReq");
        }
        if (loginEndReq.getCode() != 0) {
            return ResponseMessage.buildFail(ILLEGAL_STATE);
        }
        if (StringUtils.isBlank(loginEndReq.getSn())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "sn");
        }
        if (StringUtils.isBlank(loginEndReq.getMac())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "mac");
        }

        if (StringUtils.isBlank(loginEndReq.getProductModeCode())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, PRODUCT_MODEL_CODE);
        }
        ResponseMessage<ProductModeEntity> getProductMode = productServiceRemote.getByCode(SystemContextUtils.getTenantId(), loginEndReq.getProductModeCode());
        if (!getProductMode.isSuccess()) {
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, PRODUCT_MODEL_CODE_ERROR);
        }
        if (getProductMode.getResult() == null) {
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, "无法根据该产品型号代码查询到产品型号");
        }

        DeviceInfoEntity info = deviceInfoMapper.getByMac(SystemContextUtils.getTenantId(), loginEndReq.getMac());
        if (info == null) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "无法根据mac查询设备注册信息");
        }
        if (!Objects.equals(info.getSn(), loginEndReq.getSn())) {
            return ResponseMessage.buildFail(ILLEGAL_OPERATE,
                    "mac已被占用,已注册的设备mac【" + info.getMac() + "】，sn【" + info.getSn() + "】；当前的mac【" + loginEndReq.getMac() + "】,sn【" + loginEndReq.getSn() + "】");
        }

        String ip = SystemContextUtils.getContextValue(ContextKey.CLIENT_IP);
        CountryCity countryCity = deviceAuthService.getCountryCity(ip);

        final String countryLoc = countryCity != null ? JsonUtils.toJSON(countryCity) : null;
        ResponseMessage<List<ProductModeEntity>> modes = productServiceRemote.getListByCode(SystemContextUtils.getTenantId(), Arrays.asList(info.getProductModeCode()));
        if (!modes.isSuccess() || CollectionUtils.isEmpty(modes.getResult())) {
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, "无法获取产品型号信息");
        }
        ProductModeEntity productModeEntity = modes.getResult().get(0);

        info.setPhotoUrl(productModeEntity.getPhotoUrl());
        info.setCity(countryLoc);
        info.setIp(ip);
        info.setOnlineTime(LocalDateTime.now());
        info.setUpdateTime(LocalDateTime.now());
        //检查设备是否需要重置
        int resetCode = 0;
        if (info.getResetStatus() != null && Objects.equals(info.getResetStatus(), "1")) {
            info.setResetStatus("0");
            resetCode = 1;
        }

        deviceInfoMapper.updateById(info);
        logService.loginLog(info.getProductModeCode(), info.getSn(), info.getMac(), SystemContextUtils.getTenantId(), info.getKey(), info.getId(), ip, countryLoc, null);
        LoginEntityVO loginEntityVO = deviceInfoService.newLoginEntity(info, SystemContextUtils.getTenantId(), countryCity);
        //返回未重置状态给设备
        loginEntityVO.setResetCode(resetCode);
        deviceInfoCache.clear(SystemContextUtils.getTenantId(), info);

        LoginEndRep rep = new LoginEndRep();
        rep.setLoginEntityVO(loginEntityVO);
        String sessionKey = (String) redisTemplate.opsForValue().get(LOCK_SESSION_KEY + loginEndReq.getSn());
        rep.setSessionKey(sessionKey);

        //删除登录流程中的缓存
        redisTemplate.delete(LOCK_PRI_KEY + loginEndReq.getSn());
        redisTemplate.delete(LOCK_SESSION_KEY + loginEndReq.getSn());
        return ResponseMessage.buildSuccess(rep);
    }

    @Override
    public ResponseMessage<ResetLockRep> resetLock(ResetLockReq resetLockReq) {
        if (resetLockReq == null) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "locingEndReq");
        }

        String sn = resetLockReq.getSn();
        if (StringUtils.isBlank(sn)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "sn");
        }
        DeviceInfoEntity device = deviceInfoMapper.getBySn(SystemContextUtils.getTenantId(), sn);
        if (device == null) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "sn非法，无法根据sn获取设备信息");
        }

        ResponseMessage<String> ownerBySn = smartHomeServiceRemote.getOwnerBySn(SystemContextUtils.getTenantId(), sn);
        if (!ownerBySn.isSuccess()) {
            return ResponseMessage.buildFail(INNER_SERVER_ERROR, "查询设备拥有者信息异常");
        }
        if (!StringUtils.equals(ownerBySn.getResult(), SystemContextUtils.getId())) {
            return ResponseMessage.buildFail(ILLEGAL_OPERATE, "非法操作，非设备拥有者");
        }

        //删除绑定关系
        smartHomeServiceRemote.deleteRoomBindByDeviceId(device.getId());
        //删除ltmk
        redisTemplate.delete(LOCK_LTMK_KEY + sn);
        ResetLockRep rep = new ResetLockRep();
        rep.setRes(true);
        return ResponseMessage.buildSuccess(rep);
    }

    //SC_IOT根据设备的sc_key生成生成 AuthValApp(16Bytes) = (sc_key)^(passcode||zero padding)
    private void genAuthVal(String sn, String keyt, String random) {
        int authValLength = NumberEnum.SIXTEEN.getNumber();
        byte[] authVal = new byte[authValLength];
        byte[] scKeyBytes = (keyt.length() > authValLength ? keyt.substring(0, authValLength) : keyt).getBytes(StandardCharsets.UTF_8);

        //随机数补16位
        if (random.length() < authValLength) {
            int subLength = authValLength - random.length();
            long zeroAdd = (long) Math.pow(NumberEnum.TEN.getNumber(), subLength);
            random = random + (String.valueOf(zeroAdd)).substring(1);
        }

        //异或处理
        for (int i = 0; i < authValLength; i++) {
            authVal[i] = (byte) ((i > scKeyBytes.length - 1 ? binaryMap.get(0) : scKeyBytes[i]) ^ binaryMap.get(Integer.valueOf(String.valueOf(random.charAt(i)))));
        }
        redisTemplate.opsForValue().set(LOCK_AUTH_VAL + sn, EnDecryptionUtil.base64Encode(authVal));
    }


    private String getIotPub(String sn) {
        KeyPair keyPair = EnDecryptionUtil.generateECCKeyPair();
        PublicKey iotPublicKey = keyPair.getPublic();
        PrivateKey privateKey = keyPair.getPrivate();
        byte[] privateKeyEncoded = privateKey.getEncoded();
        redisTemplate.opsForValue().set(LOCK_PRI_KEY + sn, EnDecryptionUtil.base64Encode(privateKeyEncoded));
        byte[] iotPubKeyBytes = EnDecryptionUtil.ecKeyBytesFromDERKey(iotPublicKey.getEncoded());
        return EnDecryptionUtil.base64Encode(iotPubKeyBytes);
    }
}
