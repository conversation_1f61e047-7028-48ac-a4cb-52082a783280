package com.irobotics.aiot.device.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.entity.LockPasswordRecordEntity;
import com.irobotics.aiot.device.entity.UserLockRecordEntity;
import com.irobotics.aiot.device.mapper.DeviceInfoMapper;
import com.irobotics.aiot.device.remote.SmartHomeServiceRemote;
import com.irobotics.aiot.device.repository.UserLockRecordRepository;
import com.irobotics.aiot.device.service.LockService;
import com.irobotics.aiot.device.utils.EnDecryptionUtil;
import com.irobotics.aiot.device.utils.lock.LockPasswordUtil;
import com.irobotics.aiot.device.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.*;

/**
 * @author: tlh
 * @date: 2022-05-12 14:12
 * @description:
 **/
@Service
public class LockServiceImpl implements LockService {

    private static final String TENANT_ID = "tenantId";

    private static final String DEVICE_ID = "deviceId";

    @Resource
    private DeviceInfoMapper deviceInfoMapper;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private UserLockRecordRepository repository;

    @Resource
    private SmartHomeServiceRemote smartHomeServiceRemote;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;


    @Override
    public ResponseMessage<String> generatorKey(LockPasswordReq keyReq) {
        String userId = SystemContextUtils.getContextValue(ContextKey.ID);
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        LambdaQueryWrapper<DeviceInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceInfoEntity::getSn, keyReq.getSn());
        queryWrapper.eq(DeviceInfoEntity::getTenantId, tenantId);
        DeviceInfoEntity deviceInfo = deviceInfoMapper.selectOne(queryWrapper);
        if (Objects.isNull(deviceInfo)) {
            return ResponseMessage.buildFail(NOT_FOUND_DATA, "设备不存在");
        }
        if (StringUtils.isBlank(deviceInfo.getKey())) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "key缺失，无法生成一次性密码");
        }
        Long dateTime = DateUtil.currentSeconds();
        //获取绑定关系
        ResponseMessage<String> responseMessage = smartHomeServiceRemote.getOwnerBySn(tenantId, keyReq.getSn());
        if (!responseMessage.isSuccess()) {
            LogUtils.error("获取绑定关系失败, sn={},exceptionMsg={}", keyReq.getSn(), responseMessage.getMsg());
        }
        String bindUserId = responseMessage.getResult();
        if (!userId.equals(bindUserId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "不是门锁拥有者无法操作");
        }
        queryWrapper.eq(DeviceInfoEntity::getCreateBy, userId);
        // 生成一次性密码
        String ltmk = (String) redisTemplate.opsForValue().get(LockDeviceServiceImpl.LOCK_LTMK_KEY + deviceInfo.getSn());
        if (StringUtils.isBlank(ltmk)) {
            return ResponseMessage.buildFail(ILLEGAL_OPERATE, "ltmk不存在，请先注册设备");
        }
        String password = LockPasswordUtil.getPassword(EnDecryptionUtil.base64Decode(ltmk), dateTime.intValue());
        if (StringUtils.isBlank(password)) {
            return ResponseMessage.buildFail(NOT_FOUND_DATA, "生成一次性密码失败");
        }
        // 保存一次性密码
        LockPasswordRecordEntity recordEntity = new LockPasswordRecordEntity();
        recordEntity.setSn(keyReq.getSn());
        recordEntity.setUserId(userId);
        recordEntity.setTenantId(tenantId);
        recordEntity.setCreateTime(dateTime);
        //到期时间
        recordEntity.setExpireTime(dateTime + LockPasswordUtil.VALID_PERIOD);
        mongoTemplate.insert(recordEntity);
        return ResponseMessage.buildSuccess(password);
    }


    @Override
    public ResponseMessage<List<LockPasswordVo>> getPasswordHis(LockPasswordReq keyReq) {
        Criteria criteria = Criteria.where("sn").is(keyReq.getSn())
                .and("userId").is(SystemContextUtils.getContextValue(ContextKey.ID))
                .and(TENANT_ID).is(SystemContextUtils.getContextValue(ContextKey.TENANT_ID))
                .and("expireTime").gte(DateUtil.currentSeconds());
        Query query = new Query(criteria);
        List<LockPasswordVo> list = mongoTemplate.find(query, LockPasswordVo.class, "lock_password_record");
        return ResponseMessage.buildSuccess(list);
    }

    @Override
    public ResponseMessage<Boolean> batchDel(List<String> deviceUserIds) {
        String tenantId = SystemContextUtils.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        String deviceId = SystemContextUtils.getId();
        if (StringUtils.isBlank(deviceId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, DEVICE_ID);
        }
        if (CollectionUtils.isEmpty(deviceUserIds)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "deviceUserIds");
        }
        long l = repository.batchDel(tenantId, deviceId, deviceUserIds);
        return ResponseMessage.buildSuccess(l > 0);
    }

    @Override
    public ResponseMessage<List<UserLockVo>> getRecords(UserLockReq req) {
        String deviceId = req.getDeviceId();
        if (StringUtils.isBlank(deviceId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, DEVICE_ID);
        }
        String tenantId = req.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        List<UserLockVo> list = new ArrayList<>();
        List<UserLockRecordEntity> records = repository.getRecords(req);
        for (UserLockRecordEntity entity : records) {
            UserLockVo userLockVo = new UserLockVo();
            BeanUtils.copyProperties(entity, userLockVo);
            list.add(userLockVo);
        }
        return ResponseMessage.buildSuccess(list);
    }

    @Override
    public ResponseMessage<Void> syncRecord(List<UserLockSyncReq> list) {
        String tenantId = SystemContextUtils.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, TENANT_ID);
        }
        String deviceId = SystemContextUtils.getId();
        if (StringUtils.isBlank(deviceId)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, DEVICE_ID);
        }
        String userName = SystemContextUtils.getContextValue(ContextKey.USERNAME);
        Date curDate = new Date();
        for (UserLockSyncReq req : list) {
            String deviceUserId = req.getDeviceUserId();
            if (StringUtils.isBlank(deviceUserId)) {
                continue;
            }

            Query query = new Query();
            query.addCriteria(Criteria.where(TENANT_ID).is(tenantId));
            query.addCriteria(Criteria.where(DEVICE_ID).is(deviceId));
            query.addCriteria(Criteria.where("deviceUserId").is(deviceUserId));

            // 更新的字段
            Update update = new Update();
            update.set(TENANT_ID, tenantId);
            update.set(DEVICE_ID, deviceId);
            update.set("deviceUserId", deviceUserId);
            update.set("beginTime", req.getBeginTime());
            update.set("endTime", req.getEndTime());
            update.set("name", req.getName());
            update.set("role", req.getRole());
            update.set("lockCfg", req.getLockCfg());
            update.set("updateBy", userName);
            update.set("updateTime", curDate);
            update.setOnInsert("createBy", userName);
            update.setOnInsert("createdTime", curDate);
            repository.upsert(query, update);
        }
        return ResponseMessage.buildSuccess();
    }
}
