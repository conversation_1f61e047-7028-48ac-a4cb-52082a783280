package com.irobotics.aiot.device.service.impl;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.commons.util.JsonUtils;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.common.LogEnum;
import com.irobotics.aiot.device.remote.LogServiceRemote;
import com.irobotics.aiot.device.service.LogService;
import com.irobotics.aiot.device.vo.BindLogEntity;
import com.irobotics.aiot.device.vo.DeviceLoginLogEntity;
import com.irobotics.aiot.device.vo.LoginKeyReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/10/25 15:04
 */
@Service
public class LogServiceImpl implements LogService {

    /**
     * 日志
     */
    @Autowired
    private LogServiceRemote logServiceRemote;

    /**
     * 登录失败，插入一条日志
     *
     * @param req
     */
    @Override
    @Async
    public void loginFail(LoginKeyReq req, String country) {
        String productModeCode = req.getProductModeCode();
        String sn = req.getSn();
        String mac = req.getMac();
        String tenantId = req.getTenantId();
        String key = req.getKeyt();
        String version = JsonUtils.toJSON(req.getPackageVersions());

        String ip = SystemContextUtils.getContextValue(ContextKey.CLIENT_IP);
        DeviceLoginLogEntity deviceLoginLogEntity =
                new DeviceLoginLogEntity(sn, mac, key, productModeCode, version, null,
                        LogEnum.LOGIN_FAIL.getCode(), ip, country, System.currentTimeMillis(),
                        null, tenantId);
        ResponseMessage<String> responseMessage = logServiceRemote.loginLog(deviceLoginLogEntity);
        if (!responseMessage.isSuccess()) {
            LogUtils.error("插入登录日志失败， message={}", responseMessage.getMsg());
        }
    }

    /**
     * 插入一条登录日志
     *
     * @param productModeCode 工程类型
     * @param sn              sn
     * @param mac             mac
     * @param tenantId        租户id
     * @param key             登录带的key
     * @param deviceId        设备第
     * @param ip              ip
     * @param country         城市
     * @param version         版本
     */
    @Override
    @Async
    public void loginLog(String productModeCode, String sn, String mac, String tenantId, String key, String deviceId, String ip, String country, String version) {
        DeviceLoginLogEntity deviceLoginLogEntity = new DeviceLoginLogEntity(sn, mac, key, productModeCode, version, deviceId, LogEnum.LOGIN_SUCCESS.getCode(), ip, country, System.currentTimeMillis(), null, tenantId);
        ResponseMessage<String> responseMessage = logServiceRemote.loginLog(deviceLoginLogEntity);
        if (!responseMessage.isSuccess()) {
            LogUtils.error("保存登录日志失败，deviceLoginEntity={}, msg={}", JsonUtils.toJSON(deviceLoginLogEntity), responseMessage.getMsg());
        }
    }

    @Override
    @Async
    public void bindLog(String type, String operaType,String deviceId) {
        BindLogEntity bindLogEntity = new BindLogEntity(deviceId, SystemContextUtils.getId(), type, operaType, SystemContextUtils.getTenantId());
        ResponseMessage<String> responseMessage = logServiceRemote.report(bindLogEntity);
        if (!responseMessage.isSuccess()) {
            LogUtils.error("插入绑定或解绑日志失败，msg={}", responseMessage.getMsg());
        }
    }

    @Override
    @Async
    public void bindLogList(List<BindLogEntity> bindLogEntity) {
        ResponseMessage<Void> responseMessage = logServiceRemote.reportList(bindLogEntity);
        if(!responseMessage.isSuccess()){
            LogUtils.error("插入多条绑定或解绑日志失败，msg={}", responseMessage.getMsg());
        }
    }
}
