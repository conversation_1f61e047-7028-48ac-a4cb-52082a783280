package com.irobotics.aiot.device.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.irobotics.aiot.device.repository.vo.DeviceBindCollection;
import com.irobotics.aiot.device.service.IMongoService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @since 2022/10/12
 */
@RefreshScope
@Service
public class MongoServiceImpl implements IMongoService {

    @Resource
    private MongoTemplate mongoTemplate;
    @Value("${user.device.relation:false}")
    private boolean enabled;

    @Override
    public void insert(String userId, String sn) {
        if (enabled) {
            DeviceBindCollection deviceBindCollection = new DeviceBindCollection();
            deviceBindCollection.setId(getId(userId, sn));
            deviceBindCollection.setCreateTime(System.currentTimeMillis());
            mongoTemplate.save(deviceBindCollection);
        }
    }

    @Override
    public void remove(String userId, String sn) {
        if (enabled) {
            Query query = Query.query(Criteria.where("id").is(getId(userId, sn)));
            mongoTemplate.remove(query, DeviceBindCollection.class);
        }
    }

    @Override
    public void batchInsertByUserIds(List<String> userIds, String sn) {
        if (enabled && CollectionUtil.isNotEmpty(userIds)) {
            userIds.forEach(userId -> {
                DeviceBindCollection deviceBindCollection = new DeviceBindCollection();
                deviceBindCollection.setId(getId(userId, sn));
                deviceBindCollection.setCreateTime(System.currentTimeMillis());
                mongoTemplate.save(deviceBindCollection);
            });
        }
    }

    @Override
    public void batchRemoveByUserIds(List<String> userIds, String sn) {
        if (enabled && CollectionUtil.isNotEmpty(userIds)) {
            List<String> ids = new ArrayList<>(userIds.size());
            userIds.forEach(userId -> {
                ids.add(getId(userId, sn));
            });
            Query query = Query.query(Criteria.where("id").in(ids));
            mongoTemplate.remove(query, DeviceBindCollection.class);
        }
    }

    @Override
    public void batchInsertBySns(String userId, List<String> sns) {
        if (enabled && CollectionUtil.isNotEmpty(sns)) {
            sns.forEach(sn -> {
                DeviceBindCollection deviceBindCollection = new DeviceBindCollection();
                deviceBindCollection.setId(getId(userId, sn));
                deviceBindCollection.setCreateTime(System.currentTimeMillis());
                mongoTemplate.save(deviceBindCollection);
            });
        }
    }

    @Override
    public void batchRemoveBySns(String userId, List<String> sns) {
        if (enabled && CollectionUtil.isNotEmpty(sns)) {
            List<String> ids = new ArrayList<>(sns.size());
            sns.forEach(sn -> {
                ids.add(getId(userId, sn));
            });
            Query query = Query.query(Criteria.where("id").in(ids));
            mongoTemplate.remove(query, DeviceBindCollection.class);
        }
    }

    /**
     * 组成mongo ID
     *
     * @param userId
     * @param sn
     * @return
     */
    private String getId(String userId, String sn) {
        return userId + "_" + sn;
    }
}
