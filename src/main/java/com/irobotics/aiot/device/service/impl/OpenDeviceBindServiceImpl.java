package com.irobotics.aiot.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.basic.exception.NoDataFoundException;
import com.irobotics.aiot.bravo.basic.exception.ParameterValidateException;
import com.irobotics.aiot.bravo.commons.util.SnowflakeUtil;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.cache.BindKeyCache;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.common.BindUserTypeEnum;
import com.irobotics.aiot.device.common.Constant;
import com.irobotics.aiot.device.common.DeviceKey;
import com.irobotics.aiot.device.common.LogEnum;
import com.irobotics.aiot.device.config.DeviceBindConfig;
import com.irobotics.aiot.device.entity.DeviceBindUserEntity;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.mapper.DeviceBindUserMapper;
import com.irobotics.aiot.device.repository.DeviceBindRepository;
import com.irobotics.aiot.device.repository.vo.DeviceBindCollection;
import com.irobotics.aiot.device.service.CommonService;
import com.irobotics.aiot.device.service.IOpenDeviceBindService;
import com.irobotics.aiot.device.service.LogService;
import com.irobotics.aiot.device.vo.BindKey;
import com.irobotics.aiot.device.vo.BindLogEntity;
import com.irobotics.aiot.device.vo.open.BatchExecuteResult;
import com.irobotics.aiot.device.vo.open.BindDeviceVo;
import com.irobotics.aiot.device.vo.open.OpenBindDeviceInfoVo;
import com.irobotics.aiot.device.vo.open.UnBindDeviceVo;
import com.irobotics.sso.user.base.SsoResponseMessage;
import com.irobotics.sso.user.dto.request.OpenLoginReqDTO;
import com.irobotics.sso.user.dto.result.ThirdUserinfoDTO;
import com.irobotics.sso.user.remote.SsoRemote;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.NOT_FOUND_DATA;
import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.PARAM_VALIDATE_FAIL;

/**
 * 开放SDK实现类
 */
@Service
public class OpenDeviceBindServiceImpl extends ServiceImpl<DeviceBindUserMapper, DeviceBindUserEntity> implements IOpenDeviceBindService {

    @Autowired
    private DeviceInfoCache deviceInfoCache;

    @Autowired
    private DeviceBindRepository deviceBindRepository;

    @Autowired
    private DeviceBindConfig deviceBindConfig;

    @Autowired
    private CommonService commonService;

    @Autowired
    private BindKeyCache bindKeyCache;

    @Autowired
    private SsoRemote ssoRemote;

    @Autowired
    private LogService logService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseMessage<Boolean> bindDevice(BindDeviceVo vo) {
        String sn = vo.getSn();
        String tenantId = SystemContextUtils.getTenantId();
        String id = SystemContextUtils.getId();
        String openUserId = SystemContextUtils.getContextValue(ContextKey.OPEN_USER_ID);
        String collectionId = id + "_" + sn;
        String key = vo.getBindKey();
        LogUtils.info("请求参数sn:{},id:{},openUserId:{}", sn, id, openUserId);
        if (StringUtils.isBlank(sn)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "sn 不能为空");
        }

        //判断 bindKey
        if (StringUtils.isNotBlank(vo.getBindKey())) {
            BindKey bindKey = bindKeyCache.getBindKey(openUserId, tenantId, DeviceKey.USER);
            LogUtils.info("bindDevice cache key: {}", bindKey);
            if (bindKey == null) {
                throw new NoDataFoundException("缓存中无bindKey");
            }
            if (!Objects.equals(bindKey.getBindKey(), key)) {
                throw new ParameterValidateException("bindKey验证不通过,缓存中的设备的bindKey和app的bindKey不一致");
            }
            bindKeyCache.removeBindKey(openUserId, tenantId, DeviceKey.USER);
        }

        DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheBySn(tenantId, sn);
        if (Objects.isNull(deviceInfo)) {
            return ResponseMessage.buildFail(NOT_FOUND_DATA, Constant.MES_DEVICE_NOTEXIST);
        }

        DeviceBindUserEntity deviceBindEntity = new DeviceBindUserEntity(SnowflakeUtil.snowflakeId(), deviceInfo.getId(),
                deviceInfo.getSn(), id, id, BindUserTypeEnum.OPEN_API.getCode(), deviceInfo.getProductId());
        QueryWrapper<DeviceBindUserEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DeviceBindUserEntity::getSn, sn);
        List<DeviceBindUserEntity> deviceBindEntities = this.baseMapper.selectList(wrapper);
        //没有绑定关系，直接绑定即可
        if (CollectionUtils.isEmpty(deviceBindEntities)) {
            int insert = this.baseMapper.insert(deviceBindEntity);
            if (deviceBindConfig.getRelation()) {
                //维护一份绑定关系到mongo，用于mqtt
                DeviceBindCollection save = deviceBindRepository.save(new DeviceBindCollection(collectionId, System.currentTimeMillis()));
                LogUtils.info("保存绑定关系到mongo，collection={}", save);
            }
            logService.bindLog(LogEnum.BIND.getCode(), LogEnum.SELF.getCode(), deviceInfo.getId());
            return ResponseMessage.buildSuccess(insert > 0);
        }
        //有绑定关系，若是当前用户的绑定关系，则直接返回成功
        DeviceBindUserEntity bindEntity = deviceBindEntities.get(0);
        if (Objects.nonNull(bindEntity) && Objects.equals(bindEntity.getOwner(), id)) {
            if (deviceBindConfig.getRelation()) {
                //维护一份绑定关系到mongo，用于mqtt
                DeviceBindCollection save = deviceBindRepository.save(new DeviceBindCollection(collectionId, System.currentTimeMillis()));
                LogUtils.info("保存绑定关系到mongo，collection={}", save);
            }
            logService.bindLog(LogEnum.BIND.getCode(), LogEnum.SELF.getCode(), deviceInfo.getId());
            return ResponseMessage.buildSuccess(true);
        }
        //设备不属于当前用户，直接覆盖原来的绑定关系，及删除该设备的绑定关系，重新绑定
        this.baseMapper.delete(wrapper);
        int insert = this.baseMapper.insert(deviceBindEntity);
        if (deviceBindConfig.getRelation()) {
            //维护一份绑定关系到mongo，用于mqtt
            //先移除旧的绑定关系
            deviceBindRepository.remove(bindEntity.getOwner(), bindEntity.getSn());
            DeviceBindCollection save = deviceBindRepository.save(new DeviceBindCollection(collectionId, System.currentTimeMillis()));
            LogUtils.info("保存绑定关系到mongo，collection={}", save);
        }
        logService.bindLog(LogEnum.BIND.getCode(), LogEnum.SELF.getCode(), deviceInfo.getId());
        return ResponseMessage.buildSuccess(insert > 0);
    }

    @Override
    public ResponseMessage<Boolean> unBindDevice(BindDeviceVo vo) {
        String sn = vo.getSn();
        String tenantId = SystemContextUtils.getTenantId();
        String id = SystemContextUtils.getId();
        if (StringUtils.isBlank(sn)) {
            return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "sn 不能为空");
        }
        DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheBySn(tenantId, sn);
        if (Objects.isNull(deviceInfo)) {
            return ResponseMessage.buildFail(NOT_FOUND_DATA, Constant.MES_DEVICE_NOTEXIST);
        }
        QueryWrapper<DeviceBindUserEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DeviceBindUserEntity::getSn, sn);
        List<DeviceBindUserEntity> deviceBindEntities = this.baseMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(deviceBindEntities)) {
            LogUtils.info("不存在得绑定关系");
            return ResponseMessage.buildSuccess(false);
        }
        DeviceBindUserEntity bindEntity = deviceBindEntities.get(0);
        if (!Objects.equals(id, bindEntity.getOwner())) {
            return ResponseMessage.buildFail(ResponseCode.ILLEGAL_OPERATE, "只能解绑自己的设备");
        }
        int delete = this.baseMapper.delete(wrapper);
        if (deviceBindConfig.getRelation()) {
            //维护一份绑定关系到mongo，用于mqtt
            long remove = deviceBindRepository.remove(id, sn);
            LogUtils.info("mongo删除绑定关系, vo={}, remove={}", vo, remove);
        }
        logService.bindLog(LogEnum.UNBIND.getCode(), LogEnum.SELF.getCode(), bindEntity.getDeviceId());
        return ResponseMessage.buildSuccess(delete > 0);
    }

    @Override
    public ResponseMessage<List<OpenBindDeviceInfoVo>> deviceList(String userId) {
        List<OpenBindDeviceInfoVo> resultList = new ArrayList<>();
        String tenantId = SystemContextUtils.getTenantId();
        List<OpenBindDeviceInfoVo> bindEntityList = this.baseMapper.getOpenApiListByUserId(userId, tenantId);
        if (CollectionUtils.isEmpty(bindEntityList)) {
            return ResponseMessage.buildSuccess(resultList);
        }
        resultList = commonService.formatBindDeviceInfo(bindEntityList);
        return ResponseMessage.buildSuccess(resultList);
    }

    @Override
    public ResponseMessage<BatchExecuteResult> batchUnBindDevice(UnBindDeviceVo unBindDeviceVo) {
        BatchExecuteResult executeResult = new BatchExecuteResult();
        Map<String, String> failResult = new HashMap<>();
        List<String> bindSnList = new ArrayList<>();
        executeResult.setFailResult(failResult);
        try {
            String tenantId = SystemContextUtils.getTenantId();
            List<String> snList = unBindDeviceVo.getSnList();
            String userId = unBindDeviceVo.getUserId();
            if (StringUtils.isEmpty(userId)) {
                return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "userId 不能为空");
            }
            if (ObjectUtils.isEmpty(snList)) {
                return ResponseMessage.buildFail(PARAM_VALIDATE_FAIL, "snList 不能为空");
            }
            snList.forEach(sn -> {
                DeviceInfoEntity deviceInfo = deviceInfoCache.getCacheBySn(tenantId, sn);
                if (Objects.isNull(deviceInfo)) {
                    failResult.put(sn, Constant.MES_DEVICE_NOTEXIST);
                }
            });
            snList.removeAll(failResult.keySet());
            if (ObjectUtils.isEmpty(snList)) {
                return ResponseMessage.buildFail(NOT_FOUND_DATA, executeResult, Constant.MES_DEVICE_UNBIND_FAIL);
            }
            //查询这里的userId是否为开发平台的openid
            SsoResponseMessage<ThirdUserinfoDTO> thirdUserByOpenIdMessage = ssoRemote.getThirdUserByOpenId(userId,SystemContextUtils.getTenantId());
            if (thirdUserByOpenIdMessage.isSuccess() && Objects.nonNull(thirdUserByOpenIdMessage.getResult())){
                ThirdUserinfoDTO dto = thirdUserByOpenIdMessage.getResult();
                if (StringUtils.isNotBlank(dto.getSsoId())){
                    userId=dto.getSsoId();
                }
            }
            LambdaQueryWrapper<DeviceBindUserEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DeviceBindUserEntity::getOwner, userId);
            wrapper.in(DeviceBindUserEntity::getSn, snList);
            List<DeviceBindUserEntity> deviceBindEntities = this.baseMapper.selectList(wrapper);

            bindSnList = deviceBindEntities.stream().map(DeviceBindUserEntity::getSn).collect(Collectors.toList());
            snList.removeAll(bindSnList);
            snList.forEach(sn -> failResult.put(sn, "绑定关系不存在或设备的管理员不是当前用户，无法解绑"));

            if(ObjectUtils.isEmpty(bindSnList)){
                return ResponseMessage.buildFail(NOT_FOUND_DATA, executeResult, Constant.MES_DEVICE_UNBIND_FAIL);
            }
            //删除绑定关系
            int delete = this.baseMapper.delete(wrapper);
            if (deviceBindConfig.getRelation()) {
                //维护一份绑定关系到mongo，用于mqtt
                long remove = deviceBindRepository.remove(userId, bindSnList);
                LogUtils.info("mongo删除绑定关系, unBindDeviceVo={}, remove={}", unBindDeviceVo, remove);
            }
            //解绑日志
            List<BindLogEntity> bindLogEntityList = new ArrayList<>();
            for (DeviceBindUserEntity t : deviceBindEntities) {
                bindLogEntityList.add(new BindLogEntity(t.getDeviceId(), userId, LogEnum.UNBIND.getCode(), LogEnum.SELF.getCode(), tenantId));
            }
            logService.bindLogList(bindLogEntityList);
            executeResult.setSuccessResult(bindSnList);

            return delete > 0 ? ResponseMessage.buildSuccess(executeResult) : ResponseMessage.buildFail(NOT_FOUND_DATA, executeResult, Constant.MES_DEVICE_UNBIND_FAIL);
        } catch (Exception ex) {
            LogUtils.error(ex, "批量解绑设备出现异常 {}", ex.getMessage());
            return ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR, Constant.MES_DEVICE_UNBIND_FAIL);
        }
    }
}