package com.irobotics.aiot.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.device.cache.ProductSnDetailActiveCache;
import com.irobotics.aiot.device.entity.ProductSnDetailActiveZoneEntity;
import com.irobotics.aiot.device.mapper.ProductSnDetailActiveZoneMapper;
import com.irobotics.aiot.device.service.IProductSnDetailActiveZoneService;
import com.irobotics.aiot.device.utils.TenantIdHandleUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProductSnDetailActiveZoneServiceImpl extends ServiceImpl<ProductSnDetailActiveZoneMapper, ProductSnDetailActiveZoneEntity> implements IProductSnDetailActiveZoneService {

    @Autowired
    private ProductSnDetailActiveZoneMapper productSnDetailActiveZoneMapper;

    @Autowired
    private ProductSnDetailActiveCache activeCache;

    @Cacheable(value = "productSnDetailActive", key = "'productSnDetailActive_sn_'+#sn")
    @Override
    public ProductSnDetailActiveZoneEntity getBySn(String sn) {
        QueryWrapper<ProductSnDetailActiveZoneEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ProductSnDetailActiveZoneEntity::getSn, sn);
        return productSnDetailActiveZoneMapper.selectOne(wrapper);
    }

    @CacheEvict(value = "productSnDetailActive", key = "'productSnDetailActive_sn_'+#productSnDetailActiveZoneEntity.sn")
    @Override
    public ResponseMessage<Boolean> insertOne(ProductSnDetailActiveZoneEntity productSnDetailActiveZoneEntity) {
        if (StringUtils.isBlank(productSnDetailActiveZoneEntity.getSn())) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "sn");
        }
        if (StringUtils.isBlank(productSnDetailActiveZoneEntity.getKey())) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "key");
        }
        if (StringUtils.isBlank(productSnDetailActiveZoneEntity.getProductModeId())) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "productModeId");
        }
        if (StringUtils.isBlank(productSnDetailActiveZoneEntity.getId())) {
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL, "id");
        }
        productSnDetailActiveZoneEntity.setTenantId(TenantIdHandleUtil.getTenantId(productSnDetailActiveZoneEntity.getTenantId()));
        int insert = productSnDetailActiveZoneMapper.insert(productSnDetailActiveZoneEntity);
        return ResponseMessage.buildSuccess(insert > 0);
    }

    @CacheEvict(value = "productSnDetailActive", key = "'productSnDetailActive_sn_'+#sn")
    @Override
    public ResponseMessage<Boolean> delBySn(String sn) {
        QueryWrapper<ProductSnDetailActiveZoneEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ProductSnDetailActiveZoneEntity::getSn, sn);
        int delete = productSnDetailActiveZoneMapper.delete(wrapper);
        return ResponseMessage.buildSuccess(delete > 0);
    }

    @Override
    public ResponseMessage<Boolean> delBySns(List<String> sns) {
        if(CollectionUtils.isEmpty(sns)){
            return ResponseMessage.buildSuccess(false);
        }
        QueryWrapper<ProductSnDetailActiveZoneEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(ProductSnDetailActiveZoneEntity::getSn, sns);
        int delete = productSnDetailActiveZoneMapper.delete(wrapper);
        if (delete > 0) {
            activeCache.removeCacheBySns(sns);
        }
        return ResponseMessage.buildSuccess(delete > 0);
    }

    @Override
    public Long countByGroupId(String groupId) {
        LambdaQueryWrapper<ProductSnDetailActiveZoneEntity> activeWrapper = new LambdaQueryWrapper<>();
        activeWrapper.eq(ProductSnDetailActiveZoneEntity::getGroupId, groupId);
        return this.count(activeWrapper);
    }

    @Override
    public List<ProductSnDetailActiveZoneEntity> snCodes(String productModeId, String sn) {
        return baseMapper.snCodes(productModeId, sn);
    }

    @Override
    public List<ProductSnDetailActiveZoneEntity> getBySnList(List<String> snList) {
        LambdaQueryWrapper<ProductSnDetailActiveZoneEntity> lambda = new QueryWrapper<ProductSnDetailActiveZoneEntity>().lambda();
        lambda.in(ProductSnDetailActiveZoneEntity::getSn, snList);
        return this.list(lambda);
    }

}
