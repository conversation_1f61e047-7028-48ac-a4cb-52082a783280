package com.irobotics.aiot.device.service.impl;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.remote.ProductServiceRemote;
import com.irobotics.aiot.device.service.IDeviceInfoService;
import com.irobotics.aiot.device.service.RtcDeviceTokenInfoService;
import com.irobotics.aiot.device.utils.RtcRequestConfig;
import com.irobotics.aiot.device.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 * 设备RTC秘钥以及token信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@Service
public class RtcDeviceTokenInfoServiceImpl  implements RtcDeviceTokenInfoService {

    @Autowired
    private RtcRequestConfig rtcRequestConfig;

    @Autowired
    private ProductServiceRemote productRemote;

    @Autowired
    private IDeviceInfoService deviceInfoService;

    private final static Integer TOKEN_EXPIRE = 7200000;

//    @Value("${rtc.agora.region}")
//    private String region;

    @Autowired
    private StringRedisTemplate redisTemplate;



    @Override
    public ResponseMessage<RtcActivateDeviceTokenVo> activateDevice(RtcDeviceNodeInfoReq param) {
        // 获取设备Id
        DeviceInfoEntity deviceInfo = deviceInfoService.getBySnAndMac(param.getSn(), param.getMac());
        if (Objects.isNull(deviceInfo)){
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL,"设备不存在");
        }
        String deviceId = deviceInfo.getId();
        String sn = deviceInfo.getSn();
        String productId = deviceInfo.getProductId();
        //（1）获取AppId。clientKey,clientSecret信息
        // (2)获取设备绑定的秘钥信息
        NodeInfoFromDeviceParam getNodeInfoParam = new NodeInfoFromDeviceParam();
        getNodeInfoParam.setSn(sn);
        getNodeInfoParam.setProductId(productId);
        ResponseMessage<RtcDeviceNodeInfoVo> nodeInfoMessage = productRemote.getNodeInfoFromDevice(getNodeInfoParam);
        LogUtils.info("获取到的设备秘钥信息:{}",nodeInfoMessage);
        if (!nodeInfoMessage.isSuccess() || Objects.isNull(nodeInfoMessage.getResult())){
            LogUtils.error("获取不到秘钥信息，设备激活音视频失败:{}",nodeInfoMessage);
            return ResponseMessage.buildFail(ResponseCode.NOT_FOUND_DATA,nodeInfoMessage.getMsg());
        }
        RtcDeviceNodeInfoVo nodeInfoVo = nodeInfoMessage.getResult();
        //TODO: 缓存token -> A地区 - B地区 - A地区 这个时候缓存如何淘汰掉？
        // (3)获取token
        RtcActivateDeviceTokenVo vo = new RtcActivateDeviceTokenVo();
        vo.setAppId(nodeInfoVo.getRtcAppId());
        vo.setNodeId(nodeInfoVo.getNodeId());
        vo.setNodeSecret(nodeInfoVo.getNodeSecret());
        // 激活获取新token
        RtcActivateDeviceVo rtcActivateDeviceVo = rtcRequestConfig.activateDeviceRequest(nodeInfoVo);
        if (!rtcActivateDeviceVo.getSuccess() || StringUtils.isBlank(rtcActivateDeviceVo.getData())){
            LogUtils.error("激活设备授权失败：deviceId:{},message:{}",deviceId,rtcActivateDeviceVo.getMsg());
            return ResponseMessage.buildFail(ResponseCode.PARAM_VALIDATE_FAIL,rtcActivateDeviceVo.getMsg());
        }
        // (4)激活token返回
        vo.setToken(rtcActivateDeviceVo.getData());
        return ResponseMessage.buildSuccess(vo);
    }
}
