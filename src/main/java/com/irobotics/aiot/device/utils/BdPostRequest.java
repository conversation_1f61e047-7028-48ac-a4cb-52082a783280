package com.irobotics.aiot.device.utils;

import cn.hutool.core.collection.CollUtil;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@RefreshScope
public class BdPostRequest {

    @Value("${baidu.ak:ALTAKaoEwHyxdkXv7okT6aGppN}")
    private  String ACCESS_KEY;

    @Value("${baidu.sk:07037bf8db794402955082992a890fb8}")
    private  String SECRET_KEY;
    /**
     * 固定值
     */
    private static final String REGION = "us-east-1";
    /**
     * 固定值
     */
    private static final String SERVICE = "execute-api";

    @Value("${baidu.url:https://smarthome-bdvs.baidubce.com}")
    private String URL_PRE;


    public String postRequest(String url, String requestBodyStr, Map<String,Object> headersParam) throws Exception {
        url = URL_PRE + url;
        String format = getAuthDate();
        String authorizationHeader = getAuthorizationHeader(url, "POST",
                requestBodyStr, format);
        Headers headers = new Headers.Builder()
                // 注意鉴权只能⽤ application/json。 如果是 application/json;charaset=urf8;将会失败
                .add("Content-Type", "application/json")
                .add("Authorization", authorizationHeader)
                .add("X-Amz-Content-Sha256", calculateSHA256(requestBodyStr))
                .add("X-Amz-Date", format)
                .build();
        if(headersParam!=null && !CollUtil.isEmpty(headersParam.keySet())){
            Headers.Builder builder = headers.newBuilder();
            for(String headerKey:headersParam.keySet()){
                builder.add(headerKey,headersParam.get(headerKey).toString());
            }
            headers = builder.build();
        }
        OkHttpClient client = new OkHttpClient();
        MediaType mediaType = MediaType.parse("application/json");
        Request request = new Request.Builder()
                // 构建requestBody 不能使⽤字符的⽅法，可以使⽤字节数组或者流的⽅式，字符串的⽅法中
                // 会强制在请求头中增加 charaset=urf8; 导致鉴权失败
                .post(RequestBody.create(mediaType,
                        requestBodyStr.getBytes(StandardCharsets.UTF_8)))
                .url(url)
                .headers(headers)
                .build();
        try (Response execute = client.newCall(request).execute()) {
            String string = execute.body().string();
            return string;
        }
    }

    private static String getAuthDate() {
        return LocalDateTime.now(ZoneId.of("UTC")).format(DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'"));
    }
    public  String getAuthorizationHeader(String url, String method, String
            requestBody, String amzDate) throws Exception {
        URI uri;
        try {
            uri = new URI(url);
        } catch (URISyntaxException e) {
            throw new Exception("Invalid URL: " + url);
        }
        String host = uri.getAuthority();
        String canonicalUri = uri.getPath();
        String canonicalQuerystring = uri.getQuery();
        String contentType = "application/json";
        String contentHash = calculateSHA256(requestBody);
        String canonicalHeaders = "content-type:" + contentType + "\n" +
                "host:" + host + "\n" +
                "x-amz-content-sha256:" + contentHash + "\n" +
                "x-amz-date:" + amzDate + "\n";
        String signedHeaders = "content-type;host;x-amz-content-sha256;x-amz-date";
        String canonicalRequest = method + "\n" + canonicalUri + "\n" +
                (canonicalQuerystring == null ? "" : canonicalQuerystring) + "\n" +
                canonicalHeaders + "\n" + signedHeaders + "\n" + contentHash;
        String credentialScope = amzDate.substring(0, 8) + "/" + REGION + "/" +
                SERVICE + "/aws4_request";
        String stringToSign = "AWS4-HMAC-SHA256\n" + amzDate + "\n" +
                credentialScope + "\n" +
                calculateSHA256(canonicalRequest);
        byte[] signingKey = getSigningKey(amzDate.substring(0, 8));
        String signature = calculateHMAC(stringToSign, signingKey);
        return "AWS4-HMAC-SHA256 Credential=" + ACCESS_KEY + "/" + credentialScope +
                ", " +
                "SignedHeaders=" + signedHeaders + ", " + "Signature=" + signature;
    }

    private static String calculateSHA256(String text) throws
            NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(text.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(hash);
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    private  byte[] getSigningKey(String dateStamp) throws Exception {
        byte[] kSecret = ("AWS4" + SECRET_KEY).getBytes(StandardCharsets.UTF_8);
        byte[] kDate = hmacSHA256(dateStamp, kSecret);
        byte[] kRegion = hmacSHA256(REGION, kDate);
        byte[] kService = hmacSHA256(SERVICE, kRegion);
        return hmacSHA256("aws4_request", kService);
    }

    private static byte[] hmacSHA256(String data, byte[] key) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec keySpec = new SecretKeySpec(key, "HmacSHA256");
        mac.init(keySpec);
        return mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
    }

    private static String calculateHMAC(String data, byte[] key) throws Exception {
        byte[] hmacData = hmacSHA256(data, key);
        return bytesToHex(hmacData);
    }

}
