package com.irobotics.aiot.device.utils;

import com.google.common.base.Preconditions;
import com.google.common.hash.Funnel;
import com.google.common.hash.Hashing;
import com.irobotics.aiot.device.common.NumberEnum;

/**
 * 布隆过滤器
 *
 * @param <T>
 */
public class BloomFilterHelper<T> {

    /**
     * hash函数
     */
    private int numHashFunctions;

    /**
     * 位数组长度
     */
    private int bitSize;

    /**
     * funnel
     */
    private Funnel<T> funnel;

    /**
     * 构造
     *
     * @param funnel
     * @param expectedInsertions
     * @param fpp
     */
    public BloomFilterHelper(Funnel<T> funnel, long expectedInsertions, double fpp) {
        Preconditions.checkArgument(funnel != null, "funnel不能为空");
        this.funnel = funnel;
        // 计算bit数组长度
        bitSize = optimalNumOfBits(expectedInsertions, fpp);
        // 计算hash方法执行次数
        numHashFunctions = optimalNumOfHashFunctions(expectedInsertions, bitSize);
    }


    /**
     * 计算获取bit数组的下标集合
     *
     * @param value
     * @return
     */
    public int[] murmurHashOffset(T value) {
        int[] offset = new int[numHashFunctions];
        long hash64 = Hashing.murmur3_128().hashObject(value, funnel).asLong();
        int hash1 = (int) hash64;
        int hash2 = (int) (hash64 >>> NumberEnum.THIRTY_TWO.getNumber());
        for (int i = 1; i <= numHashFunctions; i++) {
            int nextHash = hash1 + i * hash2;
            if (nextHash < 0) {
                nextHash = ~nextHash;
            }
            offset[i - 1] = nextHash % bitSize;
        }
        return offset;
    }

    /**
     * 计算bit数组长度
     */
    private static int optimalNumOfBits(long n, double p) {
        if (Double.compare(p, 0.0F) == 0) {
            // 设定最小期望长度
            p = Double.MIN_VALUE;
        }
        return (int) (-n * Math.log(p) / (Math.log(NumberEnum.TWO.getNumber()) * Math.log(NumberEnum.TWO.getNumber())));
    }


    /**
     * 计算hash方法执行次数
     */
    private static int optimalNumOfHashFunctions(long n, long m) {
        return Math.max(1, (int) Math.round((double) m / n * Math.log(NumberEnum.TWO.getNumber())));
    }
}
