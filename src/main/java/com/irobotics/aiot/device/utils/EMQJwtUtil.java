package com.irobotics.aiot.device.utils;

import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTHeader;
import com.irobotics.aiot.device.config.EMQConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: EMQ jwt token工具类
 * @author: huangwa1911
 * @time: 2021/10/11 17:55
 */
@Component
public class EMQJwtUtil {

    private static final String USERNAME = "username";
    private static final String CLIENT_ID = "clientId";

    @Autowired
    private EMQConfig emqConfig;

    /**
     * 生成emq的token
     *
     * @param username
     * @param clientId
     * @return
     */
    public String generalToken(String username, String clientId) {
        String secret = emqConfig.getSecret();
        byte[] key = secret.getBytes();
        String token = JWT.create()
                .setPayload(USERNAME, username)
                .setPayload(CLIENT_ID, clientId)
                .setKey(key)
                .sign();
        return token;
    }

    /**
     * 解析emq的token
     *
     * @param token
     * @return
     */
    public Map<String, Object> parseToken(String token) {
        JWT jwt = JWT.of(token);
        Map<String, Object> map = new HashMap<>();
        Object headerType = jwt.getHeader(JWTHeader.TYPE);
        Object algorithm = jwt.getHeader(JWTHeader.ALGORITHM);
        Object username = jwt.getPayload(USERNAME);
        Object clientId = jwt.getPayload(CLIENT_ID);
        map.put("headerType", headerType);
        map.put("algorithm", algorithm);
        map.put(USERNAME, username);
        map.put(CLIENT_ID, clientId);
        return map;
    }
}
