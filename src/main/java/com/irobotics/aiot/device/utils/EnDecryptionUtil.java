package com.irobotics.aiot.device.utils;

import com.github.netricecake.hkdf.HKDF;
import com.irobotics.aiot.bravo.log.LogUtils;
import com.irobotics.aiot.device.common.NumberEnum;
import jakarta.xml.bind.DatatypeConverter;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.StringUtils;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.DERBitString;
import org.bouncycastle.asn1.DERSequence;
import org.bouncycastle.jce.ECNamedCurveTable;
import org.bouncycastle.jce.ECPointUtil;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.spec.ECNamedCurveParameterSpec;
import org.bouncycastle.jce.spec.ECNamedCurveSpec;

import javax.crypto.Cipher;
import javax.crypto.KeyAgreement;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigInteger;
import java.security.*;
import java.security.spec.*;
import java.util.zip.CRC32;

/**
 * 加解密工具类
 */
public class EnDecryptionUtil {

    /**
     * 十六进制
     */
    private static final char[] HEX_ARRAY = "0123456789ABCDEF".toCharArray();

    /**
     * base64
     */
    private static Base64 base64 = new Base64();
    /**
     * EC
     */
    private static String EC_ALGORITHM = "EC";
    /**
     * EC
     */
    private static String EC_PROVIDER = "BC";
    /**
     * MAC算法可选以下多种算法
     *
     * <pre>
     * HmacMD5
     * HmacSHA1
     * HmacSHA256
     * HmacSHA384
     * HmacSHA512
     * </pre>
     */
    public static final String KEY_MAC = "HmacSHA256";

    static {
        try {
            Security.addProvider(new BouncyCastleProvider());
        } catch (Exception e) {
            LogUtils.error(e, "exceptionMsg={}", e.getMessage());
        }
    }

    /**
     * 生成密钥对
     *
     * @return
     */
    public static KeyPair generateECCKeyPair() {
        try {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(EC_ALGORITHM, EC_PROVIDER);
            // curveName这里取值：secp256r1
            ECGenParameterSpec ecGenParameterSpec = new ECGenParameterSpec("secp256r1");
            keyPairGenerator.initialize(ecGenParameterSpec, new SecureRandom());
            return keyPairGenerator.generateKeyPair();
        } catch (Exception e) {
            LogUtils.error(e, "exceptionMsg={}", e.getMessage());
        }
        return null;
    }

    /**
     * 91 > 65
     *
     * @param ourPk
     * @return
     */
    public static byte[] ecKeyBytesFromDERKey(byte[] ourPk) {
        ASN1Sequence sequence = DERSequence.getInstance(ourPk);
        DERBitString subjectPublicKey = (DERBitString) sequence.getObjectAt(1);
        return subjectPublicKey.getBytes();
    }

    /**
     * 65 - 91
     *
     * @param keyStr
     * @return
     */
    public static byte[] getPubKeyTLV(String keyStr) {
        byte[] keyBytes = base64.decode(StringUtils.getBytesUtf8(keyStr));
        byte[] PUB_KEY_TL = new byte[NumberEnum.TWENTY_SIX.getNumber()];
        if (keyBytes.length == NumberEnum.SIXTY_FIVE.getNumber()) {
            byte[] tlv = new byte[NumberEnum.NINETY_ONE.getNumber()];
            System.arraycopy(PUB_KEY_TL, 0, tlv, 0, NumberEnum.TWENTY_SIX.getNumber());
            System.arraycopy(keyBytes, 0, tlv, NumberEnum.TWENTY_SIX.getNumber(), NumberEnum.SIXTY_FIVE.getNumber());
            return tlv;
        }
        return keyBytes;
    }

    /**
     * @param otherPublicKeyStr
     * @param myPrivateKey
     * @return
     * @throws Exception
     */
    public static byte[] ecdhe(String otherPublicKeyStr, String myPrivateKey) throws Exception {
        KeyFactory kf = KeyFactory.getInstance(EC_ALGORITHM, EC_PROVIDER);
        ECNamedCurveParameterSpec spec = ECNamedCurveTable.getParameterSpec("secp256r1");
        ECNamedCurveSpec params = new ECNamedCurveSpec("secp256r1", spec.getCurve(), spec.getG(), spec.getN());

        //第一位为4，后面32位是x坐标，再后面32位是y坐标
        byte[] bytes = base64Decode(otherPublicKeyStr);
        byte[] xByteArray = new byte[NumberEnum.THIRTY_THREE.getNumber()];
        //x坐标第一位补0
        xByteArray[0] = 0;
        byte[] yByteArray = new byte[NumberEnum.THIRTY_THREE.getNumber()];
        //y坐标第一位补0
        yByteArray[0] = 0;
        for (int i = 1; i < bytes.length; i++) {
            if (i < NumberEnum.THIRTY_THREE.getNumber()) {
                xByteArray[i] = bytes[i];
            }
            if (i >= NumberEnum.THIRTY_THREE.getNumber()) {
                yByteArray[i - NumberEnum.THIRTY_TWO.getNumber()] = bytes[i];
            }

        }

        BigInteger x = new BigInteger(xByteArray);
        BigInteger y = new BigInteger(yByteArray);

        ECPoint ecPoint = new ECPoint(x, y);
        ECPublicKeySpec pubKeySpec = new ECPublicKeySpec(ecPoint, params);
        final PublicKey devPublicKey = kf.generatePublic(pubKeySpec);

        //设备65字节数组
//        KeyFactory kf = KeyFactory.getInstance(EC_ALGORITHM, EC_PROVIDER);
//        ECNamedCurveParameterSpec spec = ECNamedCurveTable.getParameterSpec("secp256r1");
//        ECNamedCurveSpec params = new ECNamedCurveSpec("secp256r1", spec.getCurve(), spec.getG(), spec.getN());
//        ECPoint publicPoint = ECPointUtil.decodePoint(params.getCurve(), base64Decode(otherPublicKeyStr));
//        ECPublicKeySpec pubKeySpec = new ECPublicKeySpec(publicPoint, params);
//        final PublicKey devPublicKey = kf.generatePublic(pubKeySpec);
//        91字节数组用
//        final X509EncodedKeySpec deviceKeySpec = new X509EncodedKeySpec(base64Decode(otherPublicKeyStr));
//        final PublicKey devPublicKey = ec.generatePublic(deviceKeySpec);

        final PKCS8EncodedKeySpec iotKeySpec = new PKCS8EncodedKeySpec(base64Decode(myPrivateKey));
        final PrivateKey iotPrivateKey = kf.generatePrivate(iotKeySpec);

        KeyAgreement ecdh = KeyAgreement.getInstance("ECDH");
        ecdh.init(iotPrivateKey);
        ecdh.doPhase(devPublicKey, true);
        return ecdh.generateSecret();
    }

    /**
     * HMAC加密
     *
     * @param data
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] encryptHMAC(byte[] data, byte[] key) throws Exception {

        SecretKey secretKey = new SecretKeySpec(key, KEY_MAC);
        Mac mac = Mac.getInstance(secretKey.getAlgorithm());
        mac.init(secretKey);
        return mac.doFinal(data);

    }

    /**
     * @param salt
     * @param secret
     * @return
     * @throws Exception
     */
    public static String hkdf(byte[] salt, byte[] secret) throws Exception {
        //if no dynamic salt is available, a static salt is better than null

        HKDF hkdf = HKDF.fromHmacSha256();

        byte[] pseudoRandomKey = hkdf.extract(salt, secret);

        return base64Encode(pseudoRandomKey);


    }

    /**
     * @param param
     * @return
     */
    public static int crc32(byte[] param) {
        CRC32 c = new CRC32();
        //Resets CRC-32 to initial value.
        c.reset();
        //将数据丢入CRC32解码器
        c.update(param, 0, param.length);
        //获取CRC32 的值  默认返回值类型为long 用于保证返回值是一个正数
        return (int) c.getValue();
    }

    /**
     * @param sessionKey
     * @param input
     * @param iv
     * @return
     * @throws Exception
     */
    public static byte[] aesccm(byte[] sessionKey, byte[] input, byte[] iv) throws Exception {
        Security.addProvider(new BouncyCastleProvider());
        GCMParameterSpec parameterSpec = new GCMParameterSpec(NumberEnum.THIRTY_TWO.getNumber(), iv);
        Cipher cipher = Cipher.getInstance("AES/CCM/NoPadding");
        SecretKeySpec secretKeySpec = new SecretKeySpec(sessionKey, "AES");
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, parameterSpec);
        return cipher.doFinal(input);
    }

    /**
     * @param sessionKey
     * @param input
     * @return
     * @throws Exception
     */
    public static String decAesccm(byte[] sessionKey, byte[] input) throws Exception {
        Security.addProvider(new BouncyCastleProvider());
        byte[] nonce = {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l'};
        GCMParameterSpec parameterSpec = new GCMParameterSpec(NumberEnum.THIRTY_TWO.getNumber(), nonce);
        Cipher cipher = Cipher.getInstance("AES/CCM/NoPadding");
        SecretKeySpec secretKeySpec = new SecretKeySpec(sessionKey, "AES");
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, parameterSpec);
        return DatatypeConverter.printHexBinary(cipher.doFinal(input));
    }

//    public static byte[] aesccm(byte[] key, byte[] iv, byte[] text) throws Exception{
//        SecretKeySpec secretKeySpec = new SecretKeySpec(key, "AES");
//        Cipher encAESCCMcipher = Cipher.getInstance("AES/CCM/NoPadding", "BC");
//        IvParameterSpec ivSpec = new IvParameterSpec(iv);
//        encAESCCMcipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivSpec);
//
//        // Encrypt the data
//        byte[] cipherText = encAESCCMcipher.doFinal(text);
//        return cipherText;
//    }


    /**
     * @param encrypted
     * @return
     */
    public static String base64Encode(byte[] encrypted) {
        return base64.encodeToString(encrypted);
    }

    /**
     * @param encodeStr
     * @return
     */
    public static byte[] base64Decode(String encodeStr) {
        return base64.decode(encodeStr);
    }

    /**
     * @param n
     * @return
     */
    public static byte[] intToByte(int n) {
        byte[] b = new byte[NumberEnum.FOUR.getNumber()];
        b[NumberEnum.THREE.getNumber()] = (byte) (n & 0xff);
        b[NumberEnum.TWO.getNumber()] = (byte) (n >> NumberEnum.EIGHT.getNumber() & 0xff);
        b[1] = (byte) (n >> NumberEnum.SIXTEEN.getNumber() & 0xff);
        b[0] = (byte) (n >> NumberEnum.TWENTY_FOUR.getNumber() & 0xff);
        return b;
    }

    /**
     * @param ecKeyByteArray
     * @return
     * @throws NoSuchProviderException
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeySpecException
     */
    private static PublicKey publicKeyFromEC(byte[] ecKeyByteArray) throws NoSuchProviderException, NoSuchAlgorithmException, InvalidKeySpecException {
        KeyFactory kf = KeyFactory.getInstance("EC", "BC");
        ECNamedCurveParameterSpec spec = ECNamedCurveTable.getParameterSpec("secp256r1");
        ECNamedCurveSpec params = new ECNamedCurveSpec("secp256r1", spec.getCurve(), spec.getG(), spec.getN());
        ECPoint publicPoint = ECPointUtil.decodePoint(params.getCurve(), ecKeyByteArray);
        ECPublicKeySpec pubKeySpec = new ECPublicKeySpec(publicPoint, params);
        return kf.generatePublic(pubKeySpec);
    }

    /**
     * @param bytes
     * @return
     */
    public static String bytesToHex(byte[] bytes) {
        char[] hexChars = new char[bytes.length * NumberEnum.TWO.getNumber()];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * NumberEnum.TWO.getNumber()] = HEX_ARRAY[v >>> NumberEnum.FOUR.getNumber()];
            hexChars[j * NumberEnum.TWO.getNumber() + 1] = HEX_ARRAY[v & 0x0F];
        }
        return new String(hexChars);
    }

    /**
     * @param hex
     * @return
     */
    public static int hexToDecimal(String hex) {
        int outcome = 0;
        for (int i = 0; i < hex.length(); i++) {
            char hexChar = hex.charAt(i);
            outcome = outcome * NumberEnum.SIXTEEN.getNumber() + charToDecimal(hexChar);
        }
        return outcome;
    }

    /**
     * @param c
     * @return
     */
    public static int charToDecimal(char c) {
        if (c >= 'A' && c <= 'F') {
            return NumberEnum.TEN.getNumber() + c - 'A';
        } else {
            return c - '0';
        }

    }

    /**
     * s must be an even-length string.
     *
     * @param s
     * @return
     */
    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / NumberEnum.TWO.getNumber()];
        for (int i = 0; i < len; i += NumberEnum.TWO.getNumber()) {
            data[i / NumberEnum.TWO.getNumber()] = (byte) ((Character.digit(s.charAt(i), NumberEnum.SIXTEEN.getNumber()) << NumberEnum.FOUR.getNumber())
                    + Character.digit(s.charAt(i + 1), NumberEnum.SIXTEEN.getNumber()));
        }
        return data;
    }
}
