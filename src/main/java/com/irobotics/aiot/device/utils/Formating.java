package com.irobotics.aiot.device.utils;

import com.irobotics.aiot.device.common.NumberEnum;

import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

/**
 * 格式化
 */
public class Formating {

    /**
     * 16进制
     */
    protected static final char[] hexArray = "0123456789abcdef".toCharArray();

    /**
     * @param hexStr
     * @return
     */
    public static byte[] hexStr2Byte(String hexStr) {
        int len = hexStr.length();
        byte[] data = new byte[len / NumberEnum.TWO.getNumber()];
        for (int i = 0; i < len; i += NumberEnum.TWO.getNumber()) {
            data[i / NumberEnum.TWO.getNumber()] = (byte) ((Character.digit(hexStr.charAt(i), NumberEnum.SIXTEEN.getNumber()) << NumberEnum.FOUR.getNumber())
                    + Character.digit(hexStr.charAt(i + 1), NumberEnum.SIXTEEN.getNumber()));
        }
        return data;
    }

    /**
     * @param bytes
     * @return
     */
    public static String byte2HexStr(byte[] bytes) {
        char[] hexChars = new char[bytes.length * NumberEnum.TWO.getNumber()];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * NumberEnum.TWO.getNumber()] = hexArray[v >>> NumberEnum.FOUR.getNumber()];
            hexChars[j * NumberEnum.TWO.getNumber() + 1] = hexArray[v & 0x0F];
        }
        return new String(hexChars);
    }

    /**
     * @param arg
     * @return
     */
    public static String stringToHex(String arg) {
        return String.format("%02x", new BigInteger(1, arg.getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * @param plain
     * @param blockSize
     * @return
     */
    public static byte[] padWithPKCS7(byte[] plain, int blockSize) {
        int paddingAmount = blockSize - (plain.length % blockSize);
        byte[] out = new byte[plain.length + paddingAmount];

        byte[] padLookup = {0x10, 0x0f, 0x0e, 0x0d, 0x0c, 0x0b, 0x0a, 0x09, 0x08, 0x07, 0x06, 0x05, 0x04, 0x03, 0x02,
                0x01};

        for (int i = 0; i < plain.length; i++) {
            out[i] = plain[i];
        }

        for (int i = plain.length; i < out.length; i++) {
            out[i] = padLookup[plain.length % blockSize];
        }

        return out;
    }

    /**
     * @param padded
     * @param blocksize
     * @return
     */
    public static byte[] unpadPKCS7(byte[] padded, int blocksize) {
        int padLen = padded[padded.length - 1];
        byte[] out = new byte[padded.length - padLen];

        for (int i = 0; i < out.length; i++) {
            out[i] = padded[i];
        }
        return out;
    }

    /**
     * @param x
     * @return
     */
    public static byte[] longToBytes(long x) {
        ByteBuffer buffer = ByteBuffer.allocate(Long.BYTES);
        buffer.putLong(x);
        return buffer.array();
    }

    /**
     * @param bytes
     * @return
     */
    public static long bytesToLong(byte[] bytes) {
        ByteBuffer buffer = ByteBuffer.allocate(Long.BYTES);
        buffer.put(bytes);
        buffer.flip();
        return buffer.getLong();
    }

    /**
     * @param first
     * @param second
     * @return
     */
    public static byte[] concatenateByteArrays(byte[] first, byte[] second) {
        byte[] concatenated = new byte[first.length + second.length];

        System.arraycopy(first, 0, concatenated, 0, first.length);
        System.arraycopy(second, 0, concatenated, first.length, second.length);

        return concatenated;
    }
}
