package com.irobotics.aiot.device.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Base64;

/**
 * <AUTHOR>
 * @description
 * @since 2022/1/17
 */
@Component
public class JWTService implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 密钥
     */
    @Value("${jjwt.secretKey}")
    private String secretKey;

    /**
     * 解析token
     *
     * @param token
     * @return
     */
    public Claims getAllClaimsFromToken(String token) {
        return Jwts.parser().setSigningKey(Base64.getEncoder().encodeToString(secretKey.getBytes())).parseClaimsJws(token).getBody();
    }

    /**
     * 根据token获取用户ID
     *
     * @param token
     * @return
     */
    public String getUserIdFromToken(String token) {
        return getAllClaimsFromToken(token).getId();
    }

    /**
     * 根据token获取租户ID
     *
     * @param token
     * @return
     */
    public String getTenantIdFromToken(String token) {
        return getAllClaimsFromToken(token).getSubject();
    }

    /**
     * 根据token获取用户名
     *
     * @param token
     * @return
     */
    public String getUserNameFromToken(String token) {
        return getAllClaimsFromToken(token).getIssuer();
    }

}
