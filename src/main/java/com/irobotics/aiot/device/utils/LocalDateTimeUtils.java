package com.irobotics.aiot.device.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;

/**
 * 时间转换工具类
 */
public class LocalDateTimeUtils {

    /**
     * 毫秒转localdatetime
     *
     * @param ms
     * @return
     */
    public static LocalDateTime milliToLocalDateTime(long ms) {
        return Instant.ofEpochMilli(ms).atZone(ZoneOffset.systemDefault()).toLocalDateTime();
    }

    /**
     * 秒转localdatetime
     *
     * @param second
     * @return
     */
    public static LocalDateTime secondToLocalDateTime(long second) {
        return LocalDateTime.ofEpochSecond(second, 0, ZoneOffset.of(OffsetDateTime.now().getOffset().getId()));
    }
}
