package com.irobotics.aiot.device.utils;

import lombok.Data;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Data
@Component
public class MongoUtil<T> {

    /**
     * 默认每页条数
     */
    private static final Integer DEFAULT_PAGE_SIZE = 10;

    /**
     * 每页条数
     */
    public Integer pageSize;

    /**
     * 页码
     */
    private Integer currentPage;

    /**
     * 开始分页
     *
     * @param currentPage
     * @param pageSize
     * @param query
     */
    public void start(Integer currentPage, Integer pageSize, Query query) {
        pageSize = pageSize == 0 ? DEFAULT_PAGE_SIZE : pageSize;
        query.limit(pageSize);
        query.skip((currentPage - 1) * pageSize);
        this.pageSize = pageSize;
        this.currentPage = currentPage;
    }

    /**
     * 构造
     *
     * @param total
     * @param list
     * @return
     */
    public PageHelper pageHelper(long total, List<T> list) {
        return new PageHelper(this.currentPage, total, this.pageSize, list);
    }

    /**
     * 构造
     *
     * @param list
     * @return
     */
    public PageHelper pageHelper(List<T> list) {
        return new PageHelper(this.currentPage, this.pageSize, list);
    }

    /**
     * 构造
     *
     * @param currentPage
     * @param total
     * @param pageSize
     * @param list
     * @return
     */
    public PageHelper pageHelper(long currentPage, long total, long pageSize, List<T> list) {
        return new PageHelper(currentPage, total, pageSize, list);
    }

    /**
     * 构造
     *
     * @param currentPage
     * @param pageSize
     * @param list
     * @return
     */
    public PageHelper pageHelper(long currentPage, long pageSize, List<T> list) {
        return new PageHelper(currentPage, pageSize, list);
    }


    /**
     * 用于模糊查询忽略大小写
     *
     * @param string
     * @return
     */
    public Pattern getPattern(String string) {
        return Pattern.compile("^.*" + string + ".*$", Pattern.CASE_INSENSITIVE);
    }
}
