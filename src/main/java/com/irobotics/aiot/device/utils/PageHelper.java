package com.irobotics.aiot.device.utils;

import lombok.Data;

import java.util.List;

/**
 * 分页
 *
 * @param <T>
 */
@Data
public class PageHelper<T> {

    /**
     * 页码
     */
    private long currentPage;

    /**
     * 总数
     */
    private long total;

    /**
     * 每页条数
     */
    private long pageSize;

    /**
     * 集合
     */
    private List<T> list;

    /**
     * @param pageNum
     * @param total
     * @param pageSize
     * @param list
     */
    public PageHelper(long pageNum, long total, long pageSize, List<T> list) {
        this.currentPage = pageNum;
        this.total = total;
        this.pageSize = pageSize;
        this.list = list;
    }

    /**
     * @param pageNum
     * @param pageSize
     * @param list
     */
    public PageHelper(long pageNum, long pageSize, List<T> list) {
        this.currentPage = pageNum;
        this.pageSize = pageSize;
        this.list = list;
    }
}
