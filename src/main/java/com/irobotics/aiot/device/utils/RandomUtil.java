package com.irobotics.aiot.device.utils;

import com.irobotics.aiot.bravo.basic.exception.AppRuntimeException;

import java.util.Objects;
import java.util.Random;

/**
 * 随机数工具类
 */
public class RandomUtil {

    /**
     * 倍数
     */
    private static final Integer RANDOM_BOUND = 10;

    /**
     * 字母
     */
    private static final String MODEL = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

    /**
     * 获取指定位数的随机串
     *
     * @param size
     * @return
     */
    public static String getRandom(int size) {
        StringBuilder randomStr = new StringBuilder();
        for (int i = 0; i < size; i++) {
            int num = new Random().nextInt(RANDOM_BOUND);
            randomStr.append(num);
        }
        return randomStr.toString();
    }

    /**
     * 生成指定位数的随机字母bindKey
     *
     * @param size 位数
     * @return
     */
    public static String getRandomBindKey(Integer size) {
        if (Objects.isNull(size) || size < 0 || size >= MODEL.length()) {
            new AppRuntimeException("Illegal Random Size!");
        }
        StringBuilder bindKey = new StringBuilder();
        char[] chars = MODEL.toCharArray();
        for (int i = 0; i < size; i++) {
            char randomValue = chars[new Random().nextInt(chars.length)];
            bindKey.append(randomValue);
        }
        return bindKey.toString();
    }
}
