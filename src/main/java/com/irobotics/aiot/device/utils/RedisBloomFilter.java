package com.irobotics.aiot.device.utils;

import com.google.common.base.Preconditions;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 布隆过滤器
 */
@Component
public class RedisBloomFilter {

    private static final String ERROR_MSG = "bloomFilterHelper不能为空";

    @Autowired
    private RedisTemplate redisTemplate;


    /**
     * 根据给定的布隆过滤器添加值
     */
    public <T> void addByBloomFilter(BloomFilterHelper<T> bloomFilterHelper, String key, T value) {
        Preconditions.checkArgument(bloomFilterHelper != null, ERROR_MSG);
        int[] offset = bloomFilterHelper.murmurHashOffset(value);
        if (ArrayUtils.isNotEmpty(offset)) {
            redisTemplate.executePipelined(new SessionCallback<String>() {
                @Override
                public <K, V> String execute(RedisOperations<K, V> operations) throws DataAccessException {
                    ValueOperations<String, Object> valueOperations = (ValueOperations<String, Object>) operations.opsForValue();
                    Arrays.stream(offset).forEach((int o) -> {
                        valueOperations.setBit(key, o, true);
                    });
                    return null;
                }
            });
        }
    }

    /**
     * 根据给定的布隆过滤器添加多个值
     *
     * @param bloomFilterHelper
     * @param key
     * @param values
     * @param <T>
     */
    public <T> void addMoreByBloomFilter(BloomFilterHelper<T> bloomFilterHelper, String key, List<T> values) {
        Preconditions.checkArgument(bloomFilterHelper != null, ERROR_MSG);
        redisTemplate.executePipelined(new SessionCallback<String>() {
            @Override
            public <K, V> String execute(RedisOperations<K, V> redisOperations) throws DataAccessException {
                ValueOperations<String, Object> valueOperations = (ValueOperations<String, Object>) redisOperations.opsForValue();
                values.forEach((T value) -> {
                    int[] offset = bloomFilterHelper.murmurHashOffset(value);
                    Arrays.stream(offset).forEach((int o) -> {
                        valueOperations.setBit(key, o, true);
                    });
                });
                return null;
            }
        });
    }


    /**
     * 根据给定的布隆过滤器判断值是否存在
     */
    public <T> boolean includeByBloomFilter(BloomFilterHelper<T> bloomFilterHelper, String key, T value) {
        Preconditions.checkArgument(bloomFilterHelper != null, ERROR_MSG);
        int[] offset = bloomFilterHelper.murmurHashOffset(value);
        List<Boolean> list = redisTemplate.executePipelined(new SessionCallback<Boolean>() {
            @Override
            public <K, V> Boolean execute(RedisOperations<K, V> operations) throws DataAccessException {
                ValueOperations<String, Object> valueOperations = (ValueOperations<String, Object>) operations.opsForValue();
                for (int i : offset) {
                    valueOperations.getBit(key, i);
                }
                return null;
            }
        });
        if (list.contains(Boolean.FALSE)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
