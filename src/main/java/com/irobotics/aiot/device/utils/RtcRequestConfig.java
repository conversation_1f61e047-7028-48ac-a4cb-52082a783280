package com.irobotics.aiot.device.utils;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.irobotics.aiot.bravo.basic.exception.AppRuntimeException;
import com.irobotics.aiot.device.vo.RtcActivateDeviceVo;
import com.irobotics.aiot.device.vo.RtcDeviceNodeInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 声网需要用到的常量
 */
@Configuration
public class RtcRequestConfig {

    //region 决定请求处理的区域：当前支持：cn（中国大陆），ap（亚太不含中国大陆），eu（欧洲），na（北美）。
//    @Value("${rtc.agora.region}")
//    private String region;

    @Autowired
    private RestTemplate restTemplate;

    private final String APP_ID = "appId";

    private final String NODE_ID = "nodeId";

    private final String NODE_SECRET = "nodeSecret";

    private final String PRODUCT_KEY = "productKey";


    // 请求的域名（本业的所有API接口都是基于该域名，且需要在请求头添加Authorization才可访问）
    private final String BASE_URL = "https://api.sd-rtn.com/iot/link";
//    private final String BASE_URL = "https://api.sd-rtn.com/%s/iot/link";


    // POST:激活设备，获取访问授权令牌
    private final String ACTIVATE_DEVICE = "/open-api/v2/iot-core/secret-node/device/activate";

    /**
     * 请求头封装authorization字段
     * @param authInfo
     * @return
     */
    private HttpHeaders headAddAuthorization(RtcDeviceNodeInfoVo authInfo) {
        HttpHeaders httpHeaders = new HttpHeaders();
        String clientKey = authInfo.getClientKey();
        String clientSecret = authInfo.getClientSecret();
        // 拼接客户 ID 和客户密钥并使用 base64 编码
        String plainCredentials = clientKey + ":" + clientSecret;
        String base64Credentials = new String(Base64.getEncoder().encode(plainCredentials.getBytes()));
        // 创建 authorization header
        String authorizationHeader = "Basic " + base64Credentials;
        // 封装请求头
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.add("authorization", authorizationHeader);
        return httpHeaders;
    }


    /**
     * 激活设备，获取访问授权令牌
     * @param rtcDeviceNodeInfoVo
     * @return
     */
    public RtcActivateDeviceVo activateDeviceRequest(RtcDeviceNodeInfoVo rtcDeviceNodeInfoVo) {
        // （1）请求头
        HttpHeaders headers = headAddAuthorization(rtcDeviceNodeInfoVo);
        String nodeId = rtcDeviceNodeInfoVo.getNodeId();
        String nodeSecret = rtcDeviceNodeInfoVo.getNodeSecret();
        String rtcProductKey = rtcDeviceNodeInfoVo.getRtcProductKey();
        // （2）请求体
        Map<String, Object> request = new LinkedHashMap<>();
        request.put(APP_ID, rtcDeviceNodeInfoVo.getRtcAppId());
        request.put(NODE_ID, nodeId);
        request.put(NODE_SECRET, nodeSecret);
        request.put(PRODUCT_KEY,rtcProductKey);
        String requestBody = JSON.toJSONString(request);
        HttpEntity<String> httpEntity = new HttpEntity(requestBody, headers);
        // （3）请求声网
//        String url = BASE_URL.formatted(region) + ACTIVATE_DEVICE;
        String url = BASE_URL + ACTIVATE_DEVICE;
        ResponseEntity<Map> responseMessage = restTemplate.postForEntity(url, httpEntity, Map.class);
        if (responseMessage.getStatusCode().isError() || Objects.isNull(responseMessage.getBody()) || responseMessage.getBody().isEmpty()) {
            throw new AppRuntimeException("网络异常，访问声网失败");
        }
        // （4）封装响应体
        Map body = responseMessage.getBody();
        RtcActivateDeviceVo result = JSON.toJavaObject(new JSONObject(body), RtcActivateDeviceVo.class);
        return result;
    }

}
