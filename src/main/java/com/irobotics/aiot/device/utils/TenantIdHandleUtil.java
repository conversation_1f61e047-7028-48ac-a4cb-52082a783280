package com.irobotics.aiot.device.utils;

import cn.hutool.core.util.ObjectUtil;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.basic.exception.AppRuntimeException;
import com.irobotics.aiot.bravo.basic.exception.FastRuntimeException;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.device.common.Constant;
import com.irobotics.aiot.device.common.TenantEnum;
import org.apache.commons.lang3.StringUtils;

import static com.irobotics.aiot.bravo.basic.constant.ResponseCode.PARAM_VALIDATE_FAIL;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/9/11 16:56
 */
public class TenantIdHandleUtil {

    /**
     * 当参数与头部都有厂商id（租户id）时，优先使用头部的厂商id（租户id）
     * 当某一个为空时，则使用有值的一方
     *
     * @param paramsTenantId
     */
    public static void handleTenantId(String paramsTenantId) {
        //预防厂商id（租户id）传递错误的问题，有限使用头部的厂商id（租户）作为条件，其次才是参数
        String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            SystemContextUtils.setContextValue(ContextKey.TENANT_ID, paramsTenantId);
        }
    }

    /**
     * 头部优先：
     * 头部租户id为空时则使用参数的租户id；
     * <p>
     * 参数优先：
     * 参数不为空，则使用参数的租户id
     *
     * @param paramsTenantId 参数的租户id
     * @param header         是否头部参数tenantId优先
     */
    public static void handleTenantId(String paramsTenantId, boolean header) {
        if (!header && StringUtils.isNotBlank(paramsTenantId)) {
            SystemContextUtils.setContextValue(ContextKey.TENANT_ID, paramsTenantId);
        } else {
            String tenantId = SystemContextUtils.getContextValue(ContextKey.TENANT_ID);
            if (StringUtils.isBlank(tenantId)) {
                SystemContextUtils.setContextValue(ContextKey.TENANT_ID, paramsTenantId);
            }
        }
    }

    /**
     * 修改头部的企业id
     *
     * @param paramTenantId
     */
    public static void modifyHeaderTenantId(String paramTenantId) {
        if (TenantEnum.ADMIN.getId().equals(SystemContextUtils.getTenantId())) {
            if (StringUtils.isBlank(paramTenantId)) {
                throw new FastRuntimeException(ResponseCode.PARAM_VALIDATE_FAIL, "paramTenantId");
            }
            SystemContextUtils.setContextValue(ContextKey.TENANT_ID, paramTenantId);
        }
    }


    /**
     * 获取租户id
     * @param paramTenantId
     * @return
     */
    public static String getTenantId(String paramTenantId){
        String selfTenantId = SystemContextUtils.getTenantId();
        if(ObjectUtil.equal(selfTenantId, Constant.ADMIN_TENANT_ID)){
            if(StringUtils.isBlank(paramTenantId)){
                throw new AppRuntimeException(PARAM_VALIDATE_FAIL,"请输入企业id");
            }
            return paramTenantId;
        } else{
            //相当于交给多租户框架维护
            return null;
        }
    }
}
