package com.irobotics.aiot.device.utils;

import java.util.concurrent.*;

/**
 * @Description: 线程池工具类
 * @author: pengf
 * @Date: 2022/4/26
 */
public class ThreadPoolUtil {

    private static final Integer CAPACITY = 5000;
    private static final Integer KEY_ALIVE = 60;

    public static int corePoolSize;

    public static ThreadPoolExecutor threadPool;

    /**
     * 无返回值直接执行
     *
     * @param runnable
     */
    public static void execute(Runnable runnable) {
        getThreadPool(corePoolSize).execute(runnable);
    }

    /**
     * 返回值直接执行
     *
     * @param callable
     */
    public static <T> Future<T> submit(Callable<T> callable) {
        return getThreadPool(corePoolSize).submit(callable);
    }

    /**
     * 关闭线程池
     */
    public static void shutdown() {
        getThreadPool(corePoolSize).shutdown();
    }

    /**
     * dcs获取线程池
     *
     * @return 线程池对象
     */
    public static ThreadPoolExecutor getThreadPool(int corePoolSize) {
        if (threadPool != null) {
            return threadPool;
        } else {
            synchronized (ThreadPoolUtil.class) {
                if (threadPool == null) {
                    threadPool = new ThreadPoolExecutor(corePoolSize, corePoolSize, KEY_ALIVE, TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(CAPACITY), new ThreadPoolExecutor.CallerRunsPolicy());
                }
                return threadPool;
            }
        }
    }
}
