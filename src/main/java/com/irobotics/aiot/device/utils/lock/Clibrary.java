package com.irobotics.aiot.device.utils.lock;

import com.sun.jna.Library;
import com.sun.jna.Native;

/**
 * @author: tlh
 * @date: 2022-05-12 17:49
 * @description: java 调用 c
 **/
public interface Clibrary extends Library {
    /**
     * 在 window 下运行需要将 .dll 文件
     * 在 linux  下运行 .os 文件
     *
     */
    Clibrary instance = Native.load("otp", Clibrary.class);

    /**
     *
     * @param key
     * @param len
     */
    void dll_set_ltmk(byte[] key, int len);

    /**
     *
     * @param key
     * @param out_len
     */
    void dll_get_ltmk(byte[] key, int out_len);

    /**
     *
     * @param utc
     */
    void dll_set_utc(int utc);

    /**
     *
     * @return
     */
    String dll_gen_opt();
}
