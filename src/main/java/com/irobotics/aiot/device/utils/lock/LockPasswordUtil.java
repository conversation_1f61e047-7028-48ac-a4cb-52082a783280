package com.irobotics.aiot.device.utils.lock;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * @author: tlh
 * @date: 2022-05-11 09:58
 * @description: 生成一次性锁密码
 **/
public class LockPasswordUtil {


    /**
     * 有效期 600s
     */
    public static final Integer VALID_PERIOD = 600;

    private static final Clibrary lib = Clibrary.instance;

    private static final Integer SIX = 6;

    /**
     * 获取一次性密码
     *
     * @param key
     * @param time
     * @return
     */
    public static String getPassword(byte[] key, Integer time) {
        // 1.set utc time
        lib.dll_set_utc(time);

        // 2. set ltmk
        lib.dll_set_ltmk(key, key.length);

        byte[] out = new byte[key.length];
        lib.dll_get_ltmk(out, key.length);
//        LogUtils.info("一次性密码dll ltmk计算结果:{},本地计算结果:{}",ret,key);

        if (!Arrays.equals(key, out)) {
            // ltmk not match;
            return null;
        }
        // 3. generate one time passcode
        String otp = lib.dll_gen_opt();
        List<String> pwdList = new ArrayList<>();
        for (int i = 0; i < otp.length() / SIX; i++) {
            pwdList.add(otp.substring(i * SIX, i * SIX + SIX));
        }
        // 随机获取一个密码响应给用户端
        Integer index = (new Random()).nextInt(pwdList.size());
        return pwdList.get(index);
    }
}
