package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/18
 */
@Schema(name  = "申请删除参数",description = "申请删除参数")
@Data
public class ApplyDelSnVo {

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "设备绑定SN与用户信息")
    List<IdSnUsernameResp> idSnUsernameRespList;

    /**
     * 构造
     */
    public ApplyDelSnVo() {
        this.idSnUsernameRespList = new ArrayList<>();
    }
}
