package com.irobotics.aiot.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BdDelDictParam {

    @Schema(description = "fc")
    private String fc;

    @Schema(description = "pk")
    private String pk;

    @Schema(description = "ak")
    private String ak;

    @Schema(description = "0-location， 1-customTask")
    private int type;

    @Schema(description = "请求标识，毫秒级随机字符串")
    private String logId;

    @Schema(description = "归⼀化值")
    private String normValue;

    @Schema(description = "同义词/别名")
    private String synonymValue;
}
