package com.irobotics.aiot.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BdListDictParam {

    @Schema(description = "fc")
    private String fc;

    @Schema(description = "pk")
    private String pk;

    @Schema(description = "ak")
    private String ak;

    @Schema(description = "请求标识，毫秒级随机字符串")
    private String logId;
}
