package com.irobotics.aiot.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BdSkillParam {

    @Schema(description = "日志 id")
    private String logId;

    @Schema(description = "百度三元组数据")
    private BdDevice device;

    @Schema(description = "ASR识别结果")
    private String query;

    @Schema(description = "请求语义")
    private String nluInfos;

    @Schema(description = "拓展信息")
    private ExtInfo extInfo;

    @Schema(description = "端上自定义信息")
    private String custom;


    @Schema(description = "会话id",hidden = true)
    private String conversationId;

}
