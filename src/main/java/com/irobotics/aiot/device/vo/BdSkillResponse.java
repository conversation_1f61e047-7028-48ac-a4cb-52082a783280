package com.irobotics.aiot.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BdSkillResponse {

    @Schema(description = "日志 ID")
    private String logId;

    @Schema(description = "错误码，0为成功，其他为失败")
    private int errCode;

    @Schema(description = "错误信息")
    private String errMsg;

    @Schema(description = "播报信息")
    private Tts tts;

    @Schema(description = "响应语义")
    private String nluInfos;

    @Schema(description = "控制参数")
    private CtrlParams ctrlParams;

    @Schema(description = "客户自定义,透传到端上")
    private String custom;
}
