package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备绑定app实体
 */
@Data
@ApiModel
public class BindAppReq {

    /**
     * 用户id
     */
    @Schema(description = "用户ID", required = true)
    private String userId;

    /**
     * 绑定密钥
     */
    @Schema(description = "绑定密钥", required = true)
    private String key;
}
