package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/11/10 9:15
 */
@Data
@ApiModel
public class BindConfirmVo {

    /**
     * 绑定的key
     */
    @Schema(description = "绑定的key", required = true)
    private String bindKey;

    /**
     *家庭ID
     */
    @Schema(description = "家庭ID,非智能家居状态下可以不传", required = true)
    private String familyId;

    /**
     *房间id
     */
    @Schema(description = "房间id")
    private String roomId;
}
