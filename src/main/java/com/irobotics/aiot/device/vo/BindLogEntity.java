package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description: 绑定或解绑日志
 * @author: huangwa1911
 * @time: 2021/10/22 17:03
 */
@Data
public class BindLogEntity {

    /**
     * 日志id
     */
    @Schema(description = "日志id, 雪花算法生成")
    private String id;

    /**
     * 设备id
     */
    @Schema(description = "设备id")
    private String deviceId;

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private String userId;

    /**
     * 绑定类型
     */
    @Schema(description = "绑定类型，1=绑定;2=解绑")
    private String type;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型，1=自身；2=管理后台")
    private String operateType;

    /**
     * 设备日志发生时间
     */
    @Schema(description = "设备日志发生时间")
    private Long time;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Long createTime;

    /**
     * 地区
     */
    @Schema(description = "地区")
    private String zone;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private String tenantId;

    /**
     *
     */
    public BindLogEntity() {
    }

    public BindLogEntity(String deviceId, String userId, String type, String operateType, String tenantId) {
        this.time = System.currentTimeMillis();
        this.deviceId = deviceId;
        this.userId = userId;
        this.type = type;
        this.operateType = operateType;
        this.tenantId = tenantId;
    }
}
