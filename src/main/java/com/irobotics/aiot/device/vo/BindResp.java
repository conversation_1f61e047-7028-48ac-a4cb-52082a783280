package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import lombok.Data;

/**
 * 绑定设备返回的结果
 */
@Data
@ApiModel
public class BindResp {

    /**
     * 是否初次绑定的机器
     */
    @ApiParam("是否初次绑定的机器")
    private boolean isNew;

    /**
     * 绑定的机器ID
     */
    @ApiParam("绑定的机器ID")
    private String deviceId;

    /**
     * 绑定的机器SN
     */
    @ApiParam("绑定的机器SN")
    private String sn;

    /**
     * 房间id
     */
    @ApiParam("房间id")
    private String roomId;
}
