package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 绑定房间
 */
@Data
@Schema(name  = "绑定房间")
public class BindRoomVo {

    /**
     * 设备id
     */
    @Schema(description = "设备id", required = true)
    private String deviceId;

    /**
     * 家庭id
     */
    @Schema(description = "家庭id", required = true)
    private String familyId;

    /**
     * 房间id
     */
    @Schema(description = "房间id，非必填，若不为空，则绑定到对应的房间里")
    private String roomId;
}
