package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 验证设备bindkey的返回结果
 */
@Data
@Schema(name  = "验证设备bindkey的返回结果")
public class BindVerifyResp {

    /**
     * 设备id
     */
    @Schema(description = "设备id")
    private String deviceId;

    /**
     * 无参构造
     */
    public BindVerifyResp() {
    }

    /**
     * 有参构造
     *
     * @param deviceId
     */
    public BindVerifyResp(String deviceId) {
        this.deviceId = deviceId;
    }

}
