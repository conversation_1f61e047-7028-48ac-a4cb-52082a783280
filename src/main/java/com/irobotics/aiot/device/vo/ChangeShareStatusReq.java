package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2022/1/5 10:45
 */
@Data
@Schema(name  = "改变分享状态实体")
public class ChangeShareStatusReq {

    /**
     * 状态
     */
    @Schema(description = "状态，同意或拒绝")
    private Integer status;

    /**
     * 分享id
     */
    @Schema(description = "分享id，主键id")
    private String shareId;

    /**
     * 家庭id
     */
    @Schema(description = "家庭id")
    private String familyId;

    /**
     * 家庭名称
     */
    @Schema(description = "家庭名称")
    private String familyName;
}
