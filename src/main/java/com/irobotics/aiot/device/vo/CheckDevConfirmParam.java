package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 注册检查门锁 请求参数
 */
@Data
public class CheckDevConfirmParam {

    /**
     * 状态码
     */
    @Schema(description = "状态码", required = true)
    private int code;

    /**
     * 设备验证数据
     */
    @Schema(description = "设备验证数据（base64加密）", required = true)
    private String devConfirm;

    /**
     * 随机数
     */
    @Schema(description = "随机数", required = true)
    private String randDev;

    /**
     * sn
     */
    @Schema(description = "sn", required = true)
    private String sn;

    /**
     * mac
     */
    @Schema(description = "mac", required = true)
    private String mac;

    /**
     * 家庭id
     */
    @Schema(description = "家庭id", required = true)
    private String familyId;

    /**
     * 房间id
     */
    @Schema(description = "房间id")
    private String roomId;

    /**
     * 产品型号代码
     */
    @Schema(description = "产品型号代码", required = true)
    private String productModeCode;


}
