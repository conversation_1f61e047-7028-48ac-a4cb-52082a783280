package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/12/21 17:18
 */
@Data
@Schema(name  = "删除分享记录封装实体")
public class DelShareReq {

    /**
     * 设备id
     */
    @Schema(description = "设备id", required = true)
    private List<String> deviceId;

    /**
     * 是否是邀请者
     */
    @Schema(description = "是否是邀请者", required = true)
    private boolean inviter;
}
