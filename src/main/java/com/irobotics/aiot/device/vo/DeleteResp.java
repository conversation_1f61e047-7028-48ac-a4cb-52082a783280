package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @since 2022/1/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name  = "DeleteResp", description = "删除设备id相关记录返回参数")
public class DeleteResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 失败信息
     */
    @Schema(description = "失败信息")
    private List<String> failMessages;

    /**
     * 设备id
     */
    @Schema(description = "设备id")
    private String deviceId;

    /**
     * sn
     */
    @Schema(description = "sn")
    private String sn;

    /**
     * 是否成功
     */
    @Schema(description = "是否成功")
    private Boolean isSuccess;

    /**
     * 有参构造
     */
    public DeleteResp(String deviceId, Boolean isSuccess, String sn) {
        this.sn = sn;
        this.deviceId = deviceId;
        this.isSuccess = isSuccess;
        this.failMessages = new ArrayList<>();
    }
}
