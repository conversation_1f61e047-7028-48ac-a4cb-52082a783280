package com.irobotics.aiot.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class DeviceAesDecryptParam {
    @Schema(description = "被租户ID加密的 sn+ts 密文（采用的是AES对称加密算法，和用户名加密算法一致）")
    @NotEmpty(message = "src不可以为空")
    private String src;

    @Schema(description = "sn")
    @NotEmpty(message = "sn不可以为空")
    private String sn;

    @Schema(description = "ts")
    @NotEmpty(message = "ts不可以为空")
    private String ts;

    @Schema(description = "时间戳类型,0:纳秒,1:微妙,2:毫秒,3:秒")
    @NotNull(message = "timeType不可为空")
    @Min(value = 0,message = "timeType最小为0")
    @Max(value = 3,message = "timeType最大为3")
    private Integer timeType;
/*
    @Schema(description = "src加密的秘钥")
    @NotEmpty(message = "tenantId不可以为空")
    private String tenantId;
*/
}
