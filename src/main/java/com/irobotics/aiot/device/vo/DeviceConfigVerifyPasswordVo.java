package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2022/5/9 14:53
 */
@Data
@Schema(name  = "设备配置是否验证密码的参数实体")
public class DeviceConfigVerifyPasswordVo {

    /**
     * 设备id
     */
    @Schema(description = "设备id", required = true)
    private String deviceId;

    /**
     *是否开启
     */
    @Schema(description = "是否开启，0-不开启(关闭),1-开启", required = true)
    private Integer verify;
}
