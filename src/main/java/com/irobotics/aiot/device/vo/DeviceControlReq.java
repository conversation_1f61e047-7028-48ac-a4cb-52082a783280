package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description: 控制设备请求实体
 * @author: huangwa1911
 * @time: 2021/10/18 13:53
 */
@Data
@ApiModel
public class DeviceControlReq {

    /**
     * 设备id
     */
    @Schema(description = "设备id", required = true)
    private String deviceId;

    /**
     * 指令内容
     */
    @Schema(description = "指令内容", required = true)
    private String content;
}
