package com.irobotics.aiot.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 设备上传当前地图参数
 */
@Data
public class DeviceCurMapParam {

    /**
     * 地图id
     */
    @NotNull(message = "地图id不能为空")
    @Schema(description = "当前地图id")
    private Long curMapId;

    /**
     * 地图房间列表
     */
    @NotNull(message = "地图房间列表不能为空")
    @Schema(description = "地图房间列表")
    private List<MapRoom> mapRoomList;

    /**
     * 地图房间
     */
    @Data
    static class MapRoom {
        /**
         * 房间id
         */
        @NotNull(message = "房间id不能为空")
        @Schema(description = "房间id")
        private Long roomId;

        /**
         * 房间名称
         */
        @NotNull(message = "房间名称不能为空")
        @Schema(description = "房间名称")
        private String roomName;
    }
}
