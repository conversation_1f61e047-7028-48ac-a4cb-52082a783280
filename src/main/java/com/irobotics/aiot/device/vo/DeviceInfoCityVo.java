package com.irobotics.aiot.device.vo;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备信息城市信息
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
public class DeviceInfoCityVo extends Model {

    private static final long serialVersionUID = 1L;

    /**
     * sn
     */
    @Schema(description = "sn")
    private String sn;

    /**
     * 最近登录的城市
     */
    @Schema(description = "最近登录的城市")
    private String city;

    /**
     * 最近登录的城市
     */
    @Schema(description = "ip")
    private String ip;

}
