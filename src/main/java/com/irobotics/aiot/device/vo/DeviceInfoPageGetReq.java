package com.irobotics.aiot.device.vo;

import com.irobotics.aiot.device.common.PageGetReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/9/16 10:45
 */
@Data
@ApiModel("设备信息get请求条件实体")
public class DeviceInfoPageGetReq extends PageGetReq {

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private String tenantId;

    /**
     * 设备id
     */
    @Schema(description = "设备id")
    private String id;

    /**
     * 产品型号代码
     */
    @Schema(description = "产品型号代码")
    private String productModeCode;

    /**
     * 产品的Id
     */
    @Schema(description = "产品的Id")
    private String productId;

    /**
     * 设备sn
     */
    @Schema(description = "设备sn")
    private String sn;

    /**
     * 设备sn
     */
    @Schema(description = "设备sn列表,适用于批量查询设备信息")
    private List<String> snList;

    /**
     * 设备昵称
     */
    @Schema(description = "设备昵称")
    private String nickname;

    @Schema(description = "固件包类型")
    private String packageType;

    @Schema(description = "version版本号")
    private String version;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "城市")
    private String city;

    /**
     * 设备mac
     */
    @Schema(description = "设备mac")
    private String mac;

    /**
     * 创建时间：开始的时间戳
     */
    @Schema(description = "开始的时间戳")
    private Long beginTime;

    /**
     * 创建时间：结束时间戳
     */
    @Schema(description = "结束时间戳")
    private Long endTime;

    @Schema(description = "搜索方式 1:模糊搜索，2:精准搜索")
    private Integer searchType;
}
