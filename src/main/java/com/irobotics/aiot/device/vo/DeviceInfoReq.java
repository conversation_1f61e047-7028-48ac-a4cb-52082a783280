package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description: 活跃设备查询请求参数
 * @date 2023/9/14 13:59
 */
@Data
@ApiModel("活跃设备查询请求参数")
public class DeviceInfoReq {

    @NotNull(message = "租户id不能为空")
    @Schema(description = "租户id")
    private String tenantId;

    @NotNull(message = "产品型号代码不能为空")
    @Schema(description = "产品型号代码")
    private String productModeCode;

    @NotNull(message = "前percent百分比 个设备不能为空")
    @Min(value = 1, message = "percent最小为1")
    @Max(value = 100, message = "percent最大为100")
    @Schema(description = "前percent百分比 个设备")
    private Integer percent;
}