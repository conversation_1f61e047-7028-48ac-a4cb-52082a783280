package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description: 设备登录或登出日志
 * @author: huangwa1911
 * @time: 2021/10/22 16:54
 */
@Data
public class DeviceLoginLogEntity {

    /**
     * 日志id
     */
    @Schema(description = "日志id, 雪花算法生成")
    private String id;

    /**
     * 设备sn
     */
    @Schema(description = "设备sn")
    private String sn;

    /**
     * 设备mac
     */
    @Schema(description = "设备mac")
    private String mac;

    /**
     * 设备sn对应的key
     */
    @Schema(description = "设备sn对应的key")
    private String key;

    /**
     * 产品型号代码
     */
    @Schema(description = "产品型号代码（由 企业英文缩写.产品型号名称 组成）(功能相当于原工程类型）")
    private String productModeCode;

    /**
     * 软件版本
     */
    @Schema(description = "软件版本")
    private String version;

    /**
     * 设备id
     */
    @Schema(description = "设备id")
    private String deviceId;

    /**
     * 1-登录，2-登出
     */
    @Schema(description = "类型，登录或登出")
    private String type;

    /**
     * 登录结果
     */
    @Schema(description = "登录结果，0为成功，1为失败。登出时忽略")
    private String success;

    /**
     * ip地址
     */
    @Schema(description = "ip地址")
    private String ip;

    /**
     * 城市
     */
    @Schema(description = "城市")
    private String country;

    /**
     * 设备日志发生时间
     */
    @Schema(description = "设备日志发生时间")
    private Long time;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Long createTime;

    /**
     * 地区
     */
    @Schema(description = "地区")
    private String zone;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private String tenantId;

    /**
     * 无参构造
     */
    public DeviceLoginLogEntity() {
    }

    /**
     * 有参构造
     *
     * @param sn
     * @param mac
     * @param key
     * @param productModeCode
     * @param version
     * @param deviceId
     * @param ip
     * @param country
     * @param time
     * @param zone
     * @param tenantId
     */
    public DeviceLoginLogEntity(String sn, String mac, String key, String productModeCode, String version, String deviceId, String ip, String country, Long time, String zone, String tenantId) {
        this.sn = sn;
        this.mac = mac;
        this.key = key;
        this.productModeCode = productModeCode;
        this.version = version;
        this.deviceId = deviceId;
        this.ip = ip;
        this.country = country;
        this.time = time;
        this.zone = zone;
        this.tenantId = tenantId;
    }

    /**
     * 有参构造
     *
     * @param sn
     * @param mac
     * @param key
     * @param productModeCode
     * @param version
     * @param deviceId
     * @param success
     * @param ip
     * @param country
     * @param time
     * @param zone
     * @param tenantId
     */
    public DeviceLoginLogEntity(String sn, String mac, String key, String productModeCode, String version, String deviceId, String success, String ip, String country, Long time, String zone, String tenantId) {
        this.sn = sn;
        this.mac = mac;
        this.key = key;
        this.productModeCode = productModeCode;
        this.version = version;
        this.deviceId = deviceId;
        this.success = success;
        this.ip = ip;
        this.country = country;
        this.time = time;
        this.zone = zone;
        this.tenantId = tenantId;
    }
}
