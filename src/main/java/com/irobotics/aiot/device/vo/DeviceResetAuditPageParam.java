package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/19
 */
@Data
@Schema(name  = "DeviceResetAuditPageParam对象", description = "")
public class DeviceResetAuditPageParam extends PageReq {

    @Schema(description = "记录id")
    private String recordId;

    @Schema(description = "租户id")
    private String tenantId;
}
