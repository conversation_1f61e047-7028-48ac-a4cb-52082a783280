package com.irobotics.aiot.device.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @since 2023/5/24
 */
@Data
@Schema(name  = "DeviceResetAuditParam对象", description = "设备出厂初始化审核参数对象")
public class DeviceResetAuditParam {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;
    /**
     * 租户id
     */
    @Schema(description = "租户id", required = true)
    private String tenantId;
    /**
     * 审核状态
     */
    @Schema(description = "审核状态（0：未审核，1：审核通过，2：审核不通过，3：部分审核通过）")
    private Integer auditStatus;

    /**
     * 需要删除SN
     */
    @Schema(description = "确认需要删除SN")
    private String delSnJson;

    /**
     * 审核说明
     */
    @Schema(description = "审核说明(审核不通过时必填)")
    private String auditDesc;
}
