package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 设备出厂初始化审核参数
 * @since 2023/5/18
 */
@Data
@Schema(name ="DeviceResetAuditRecordParam对象", description="设备出厂初始化审核参数")
public class DeviceResetAuditRecordParam {

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private String tenantId;
    /**
     * 审核状态
     */
    @Schema(description = "审核状态（0：未审核，1：审核通过，2：审核不通过，3：部分审核通过）")
    private Integer auditStatus;

    /**
     * 申请人
     */
    @Schema(description = "申请人")
    private String applyBy;

    /**
     * 审核人
     */
    @Schema(description = "审核人")
    private String auditBy;

    /**
     * 申请开始时间
     */
    @Schema(description = "申请开始时间")
    private long applyBeginTime;
    /**
     * 申请结束时间
     */
    @Schema(description = "申请结束时间")
    private long applyEndTime;

    /**
     * 审核开始时间
     */
    @Schema(description = "审核开始时间")
    private long auditBeginTime;
    /**
     * 审核结束时间
     */
    @Schema(description = "审核结束时间")
    private long auditEndTime;
    /**
     * 页数
     */
    private int page = 1;
    /**
     * 每页大小
     */
    private int pageSize = 15;
}
