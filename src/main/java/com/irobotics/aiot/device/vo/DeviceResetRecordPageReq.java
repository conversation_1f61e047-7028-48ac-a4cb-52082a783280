package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description
 * @since 2022/1/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(name  = "DeviceResetRecordPageReq", description = "设备出厂初始化日志分页查询返回实体")
public class DeviceResetRecordPageReq extends PageReq {

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private String userId;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private String tenantId;

    /**
     * 任务id
     */
    @Schema(description = "任务id")
    private String taskId;

    /**
     * 设备id
     */
    @Schema(description = "设备id")
    private String deviceId;

    /**
     * sn
     */
    @Schema(description = "sn")
    private String sn;

    /**
     * 初始化是否成功
     */
    @Schema(description = "初始化是否成功")
    private Boolean isSuccess;

    /**
     * 日志发生时间
     */
    @Schema(description = "日志发生时间，包含")
    private Long beginTime;

    /**
     * 日志结束时间
     */
    @Schema(description = "日志结束时间，包含")
    private Long endTime;
}
