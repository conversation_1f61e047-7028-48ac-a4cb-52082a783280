package com.irobotics.aiot.device.vo;

import com.irobotics.aiot.device.entity.DeviceInviteShareEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/12/22 9:40
 */
@Data
@Schema(name  = "获取设备分享历史")
public class DeviceShareHisVo extends DeviceInviteShareEntity {

    /**
     * 用户名
     */
    @Schema(description = "用户名,当前用户为分享者，则用户名为接受分享者的用户名(分享给:xxx)；当前用户为接受分享者，则用户名为分享者的用户名（来自:xxx）,密文，app端自己解密")
    private String username;

    /**
     * 设备sn
     */
    @Schema(description = "设备sn")
    private String sn;

    /**
     * 设备mac
     */
    @Schema(description = "设备mac")
    private String mac;

    /**
     * 设备虚拟id
     */
    @Schema(description = "设备虚拟id")
    private String iotId;

    /**
     * 设备昵称或家庭名称
     */
    @Schema(description = "设备昵称或家庭名称")
    private String name;

    /**
     * 产品型号代码
     */
    @Schema(description = "产品型号代码")
    private String productModeCode;

    /**
     * 产品型号名称
     */
    @Schema(description = "产品型号名称")
    private String modeType;

    /**
     * 图片地址
     */
    @Schema(description = "图片地址,共享家庭则为共享人图片（图片地址会加密），共享设备则为设备的图片（图片地址不会加密）")
    private String photoUrl;

    @Schema(description = "产品id")
    private String productId;

    @Schema(description = "产品名称")
    private String productName;
}
