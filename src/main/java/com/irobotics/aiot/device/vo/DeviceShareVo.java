package com.irobotics.aiot.device.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/12/3 10:39
 */
@Data
public class DeviceShareVo {

    /**
     * 分享ID
     */
    @Schema(description = "分享ID")
    private String id;

    /**
     * 分享ID
     */
    @Schema(description = "分享ID，分享者获取分享列表时，则为接受分享者的用户id；接受分享者获取分享列表时，则为分享者的用户id")
    private String userId;

    /**
     * 用户名
     */
    @Schema(description = "用户名，分享者获取分享列表时，则为接受分享者的用户名；接受分享者获取分享列表时，则为分享者的用户名, 密文，AES加密")
    private String username;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    private String targetId;

    /**
     * 设备sn
     */
    @Schema(description = "设备sn")
    private String sn;

    /**
     * 设备mac
     */
    @Schema(description = "设备mac")
    private String mac;

    /**
     * 设备的虚拟id
     */
    @Schema(description = "设备的虚拟id")
    private String iotId;

    /**
     * 状态
     */
    @Schema(description = "状态；0-正常；1-同意；2-拒绝")
    private int status;

    /**
     * 共享类型
     */
    @Schema(description = "0-共享设备；1-共享家庭")
    private int type;

    /**
     * 状态
     */
    @Schema(description = "状态；0-正常；1-邀请方删除的；2-被邀请方删除的")
    private Integer removed;

    /**
     * 设备昵称或家庭名称
     */
    @Schema(description = "设备昵称或家庭名称")
    private String name;

    /**
     * 产品型号代码
     */
    @Schema(description = "产品型号代码")
    private String productModeCode;

    /**
     * 产品型号名称
     */
    @Schema(description = "产品型号名称")
    private String modeType;

    /**
     * 图片地址
     */
    @Schema(description = "图片地址(用户的图片会加密，设备的图片不会加密)")
    private String photoUrl;

    /**
     * 邀请时间
     */
    @Schema(description = "邀请时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;
}
