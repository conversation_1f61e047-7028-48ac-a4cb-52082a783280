package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2022/5/13 14:52
 */
@Data
@Schema(name  = "设备sn和租户id实体")
public class DeviceSnAndTenantReq {

    /**
     * 设备sn
     */
    @Schema(description = "设备sn")
    private String sn;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private String tenantId;
}
