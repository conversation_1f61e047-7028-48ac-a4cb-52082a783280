package com.irobotics.aiot.device.vo;

import com.irobotics.aiot.device.common.PageGetReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @projectName: device-service
 * @package: com.irobotics.aiot.device.vo
 * @className: DeviceUpgradePageReq
 * @author: pengfeng
 * @description: TODO
 * @date: 2022/11/5 18:13
 * @version: 1.0
 */
@Data
@ApiModel("设备升级分页信息get请求条件实体")
public class DeviceUpgradePageReq extends PageGetReq {

    @Schema(description = "下发任务Id",required = true)
    private String taskId;

    @Schema(description = "设备sn")
    private String sn;

    @Schema(description = "升级状态：0待升级；1已升级")
    private Integer upgradeStatus;
}
