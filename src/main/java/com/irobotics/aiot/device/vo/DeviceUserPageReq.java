package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * @projectName: device-service
 * @package: com.irobotics.aiot.device.vo
 * @className: DeviceUserVo
 * @author: pengfeng
 * @description: TODO
 * @date: 2022/9/20 15:19
 * @version: 1.0
 */
@Data
@Schema(name  = "app用户设备分页请求实体")
public class DeviceUserPageReq extends PageReq{

    @Schema(description = "租户id",required = true)
    @NotBlank(message = "租户Id不能为空")
    private String tenantId;

    @Schema(description = "app用户Id",required = true)
    @NotBlank(message = "app用户Id不能为空")
    private String userId;
}
