package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2022/5/9 11:45
 */
@Data
@Schema(name  = "更新app是否验证密码")
public class DeviceVerifyPasswordVo {

    /**
     *app是否开启密码验证
     */
    @Schema(description = "app是否开启密码验证，默认为否(0:否，1:是)")
    private Integer verifyPassword;

    /**
     *设备id集合
     */
    @Schema(description = "设备id集合")
    private List<String> deviceIds;
}
