package com.irobotics.aiot.device.vo;

import com.irobotics.aiot.device.common.PageGetReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/12/28 10:05
 */
@Data
@Schema(name  = "用户获取指定目标的设备分享记录")
public class GetUserSharedReq extends PageGetReq {

    /**
     * 是否是邀请者
     */
    @Schema(description = "是否是邀请者", required = true)
    private boolean inviter;

    /**
     * 设备id
     */
    @Schema(description = "设备id", required = true)
    private String targetId;
}
