package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @since 2022/1/10
 */
@Data
@Schema(name  = "SNIdResp", description = "sn批量返回设备id放回参数实体")
public class IdSnUsernameResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备sn
     */
    @Schema(description = "设备sn")
    private String sn;

    /**
     * 设备id
     */
    @Schema(description = "设备id")
    private String deviceId;

    /**
     * 用户名列表
     */
    @Schema(description = "用户名列表")
    private List<String> usernameList;

    /**
     * 构造
     */
    public IdSnUsernameResp() {
        this.usernameList = new ArrayList<>();
    }
}
