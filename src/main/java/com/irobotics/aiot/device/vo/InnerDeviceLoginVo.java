package com.irobotics.aiot.device.vo;

import com.irobotics.aiot.device.entity.ProductModeEntity;
import com.irobotics.aiot.device.entity.ProductSnDetailActiveZoneEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2022/3/15 18:06
 */
@Data
@Schema(name  = "设备登录查询信息实体")
public class InnerDeviceLoginVo {

    /**
     * 产品信息
     */
    @Schema(description = "产品信息")
    private ProductModeEntity productModeEntity;

    /**
     * sn信息
     */
    @Schema(description = "sn信息")
    private ProductSnDetailEntity snDetailEntity;

    /**
     * 已激活的sn信息
     */
    @Schema(description = "已激活的sn信息")
    private ProductSnDetailActiveZoneEntity productSnDetailActiveZoneEntity;
}
