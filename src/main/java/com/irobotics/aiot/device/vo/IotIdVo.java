package com.irobotics.aiot.device.vo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * iotId上传的实体封装
 */
@Data
@Schema(name  = "iotId上传的实体封装")
public class IotIdVo {

    /**
     * 设备id
     */
    @Schema(description = "设备id", required = true)
    private String deviceId;

    /**
     * iotId
     */
    @Schema(description = "iotId", required = true)
    private JSONObject iotId;

    /**
     * 重写
     */
    @Override
    public String toString() {
        return "IotIdVo{" +
                "deviceId='" + deviceId + '\'' +
                ", iotId=" + iotId +
                '}';
    }
}
