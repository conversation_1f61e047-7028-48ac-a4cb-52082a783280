package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2022/1/5 16:32
 */
@Data
@Schema(name  = "共享家庭请求参数")
public class JPushFamilyShareReq {

    /**
     * 类型
     */
    @Schema(description = "类型", required = true)
    private Integer type;

    /**
     * 状态
     */
    @Schema(description = "状态", required = true)
    private Integer status;

    /**
     * 标题
     */
    @Schema(description = "标题", required = true)
    private String title;

    /**
     * key为to
     */
    @Schema(description = "key为to，即为极光消息接收者，value为shareId", required = true)
    private List<Map<String, String>> list;

    /**
     * 目标
     */
    @Schema(description = "目标", required = true)
    private String targetId;

    /**
     * 家庭名称
     */
    @Schema(description = "家庭名称", required = true)
    private String familyName;

    /**
     * 无参构造
     */
    public JPushFamilyShareReq() {
    }

    /**
     * 有参构造
     *
     * @param title
     * @param type
     * @param status
     * @param targetId
     * @param familyName
     * @param list
     */
    public JPushFamilyShareReq(String title, Integer type, Integer status, String targetId, String familyName, List<Map<String, String>> list) {
        this.title = title;
        this.type = type;
        this.status = status;
        this.targetId = targetId;
        this.familyName = familyName;
        this.list = list;
    }
}
