package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/12/30 16:41
 */
@Data
@Schema(name  = "极光分享推送消息请求实体")
public class JPushShareReq {

    /**
     * 消息接收者
     */
    @Schema(description = "消息接收者", required = true)
    private String to;

    /**
     * 共享类型
     */
    @Schema(description = "共享类型，0-设备，1-家庭", required = true)
    private Integer type;

    /**
     * 状态
     */
    @Schema(description = "状态，0-正常；1-已同意；2-已拒绝；", required = true)
    private Integer status;

    /**
     * 消息标题
     */
    @Schema(description = "消息标题", required = true)
    private String title;

    /**
     * 额外的信息
     */
    @Schema(description = "额外的信息", required = true)
    private List<PushInfo> list;

    /**
     * 无参构造
     */
    public JPushShareReq() {
    }

    /**
     * 有参构造
     *
     * @param title
     * @param list
     * @param type
     * @param status
     * @param to
     */
    public JPushShareReq(String title, List<PushInfo> list, Integer type, Integer status, String to) {
        this.to = to;
        this.title = title;
        this.list = list;
        this.type = type;
        this.status = status;
    }

}
