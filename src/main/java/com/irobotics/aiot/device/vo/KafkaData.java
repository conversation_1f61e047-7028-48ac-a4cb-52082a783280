package com.irobotics.aiot.device.vo;

import lombok.Data;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/10/15 16:28
 */
@Data
public class KafkaData {

    /**
     * 消息id，时间戳
     */
    private Long messageId;

    /**
     * mqtt topic
     */
    private String topic;

    /**
     * mqtt method
     * 示例：thing.service.指令类型（reset、upgrade、online、offline）
     */
    private String method;

    /**
     * 设备sn
     */
    private String sn;

    /**
     * 厂商id
     */
    private String tenantId;

    /**
     * 产品key，相当与id
     */
    private String productKey;

    /**
     * 物模型版本
     */
    private String version;

    /**
     * 地区
     */
    private String zone;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 发送的内容
     */
    private String data;

    /**
     * 无参构造
     */
    public KafkaData() {
    }

    /**
     * 有参构造
     *
     * @param messageId
     * @param topic
     * @param method
     * @param sn
     * @param tenantId
     * @param productKey
     * @param version
     * @param zone
     * @param timestamp
     * @param data
     */
    public KafkaData(Long messageId, String topic, String method, String sn, String tenantId, String productKey, String version, String zone, Long timestamp, String data) {
        this.messageId = messageId;
        this.topic = topic;
        this.method = method;
        this.sn = sn;
        this.tenantId = tenantId;
        this.productKey = productKey;
        this.version = version;
        this.zone = zone;
        this.timestamp = timestamp;
        this.data = data;
    }
}
