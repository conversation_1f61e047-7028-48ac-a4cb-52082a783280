package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 注册交换门锁公钥 请求参数
 */
@Data
public class LockBindRep {

    /**
     * base64加密
     */
    @Schema(description = "base64加密")
    private String appConfirm;

    /**
     * 随机数
     */
    @Schema(description = "随机数（base64加密）")
    private String randCode;
}
