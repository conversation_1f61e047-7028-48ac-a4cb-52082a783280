package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 注册交换门锁公钥 请求参数
 */
@Data
public class LockBindReq {

    /**
     * 状态码
     */
    @Schema(description = "状态码，0成功，非0失败", required = true)
    private int code;

    /**
     * 设备公钥
     */
    @Schema(description = "设备公钥", required = true)
    private String devPub;

    /**
     * sn
     */
    @Schema(description = "sn", required = true)
    private String sn;


}
