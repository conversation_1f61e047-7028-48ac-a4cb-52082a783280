package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 门锁登录前获取验证信息 请求参数
 */
@Data
public class LockLoginGetVerifyReq {

    /**
     * sn
     */
    @Schema(description = "sn", required = true)
    private String sn;

    /**
     * 设备公钥
     */
    @Schema(description = "设备公钥", required = true)
    private String devPub;
}
