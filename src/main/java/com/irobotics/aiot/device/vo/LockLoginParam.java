package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 注册获取passcode 请求参数
 */
@Data
public class LockLoginParam {

    /**
     * 设备唯一序列号
     */
    @Schema(description = "设备唯一序列号", required = true)
    private String sn;

    /**
     * mac
     */
    @Schema(description = "mac", required = true)
    private String mac;

    /**
     * passcode长度
     */
    @Schema(description = "passcode长度,注册请求passcode时必填，登录时不需要")
    private Integer passcodeLength;

    /**
     * 产品型号代码
     */
    @Schema(description = "产品型号代码", required = true)
    private String productModeCode;
}
