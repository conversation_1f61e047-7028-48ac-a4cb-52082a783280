package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: tlh
 * @date: 2022-05-12 14:04
 * @description: 门锁获取一次密码请求参数
 **/

@Data
@ApiModel("门锁获取一次密码请求参数")
public class LockPasswordReq {

    /**
     *设备sn
     */
    @Schema(description = "设备sn",required = true)
    private String sn;

}
