package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: tlh
 * @date: 2022-05-13 09:16
 * @description: 门锁获取一次性有效密码记录
 **/
@Data
@ApiModel("门锁获取一次密码请求参数")
public class LockPasswordVo {

    /**
     * 设备sn
     */
    @Schema(description = "设备sn")
    private String sn;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Long createTime;

    /**
     * 到期时间
     */
    @Schema(description = "到期时间")
    private Long expireTime;
}
