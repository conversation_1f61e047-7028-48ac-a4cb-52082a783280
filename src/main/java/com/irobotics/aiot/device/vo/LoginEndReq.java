package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 门锁登录 请求参数
 */
@Data
public class LoginEndReq {

    /**
     * 状态码
     */
    @Schema(description = "状态码", required = true)
    private int code;

    /**
     * sn
     */
    @Schema(description = "sn", required = true)
    private String sn;

    /**
     * mac
     */
    @Schema(description = "mac", required = true)
    private String mac;

    /**
     * 产品型号代码
     */
    @Schema(description = "产品型号代码", required = true)
    private String productModeCode;
}
