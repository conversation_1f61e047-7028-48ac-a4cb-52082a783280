package com.irobotics.aiot.device.vo;

import com.irobotics.aiot.bravo.basic.base.LoginEntity;

/**
 * 登录请求参数
 */
public class LoginEntityVO extends LoginEntity {

    /**
     * 0：不需要重置，1：需要重置
     */
    private int resetCode;

    /**
     * 最大升级时间
     */
    private int maxUpgradeTime;

    /**
     * getter
     */
    public int getResetCode() {
        return resetCode;
    }

    public int getMaxUpgradeTime() {
        return maxUpgradeTime;
    }

    public void setMaxUpgradeTime(int maxUpgradeTime) {
        this.maxUpgradeTime = maxUpgradeTime;
    }

    /**
     * setter
     *
     * @param resetCode
     */
    public void setResetCode(int resetCode) {
        this.resetCode = resetCode;
    }
}
