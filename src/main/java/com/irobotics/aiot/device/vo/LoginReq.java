package com.irobotics.aiot.device.vo;

import com.irobotics.aiot.device.common.RobotVersion;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 设备的登录通用参数
 */
@ApiModel
@Data
public class LoginReq {

    /**
     * 厂商ID
     */
    @Schema(description = "厂商ID", required = true)
    private String tenantId;

    /**
     * 产品型号代码
     */
    @Schema(description = "产品型号代码", required = true)
    private String productModeCode = "";

    /**
     * 设备唯一序列号
     */
    @Schema(description = "设备唯一序列号", required = true)
    private String sn;

    /**
     * 设备mac地址
     */
    @Schema(description = "设备mac地址", required = true)
    private String mac;

    /**
     * 当前包信息
     */
    @Schema(description = "当前包信息,所有模块都要发上来", required = true)
    private List<RobotVersion> packageVersions;
}
