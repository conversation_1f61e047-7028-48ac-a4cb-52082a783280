package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/12/31 16:55
 */
@Data
@Schema(name  = "修改设备昵称")
public class ModifyDeviceNicknameReq {

    /**
     *设备id
     */
    @Schema(description = "设备id", required = true)
    private String deviceId;

    /**
     *昵称
     */
    @Schema(description = "昵称", required = true)
    private String nickname;

    /**
     *设备sn
     */
    @Schema(description = "设备sn", required = true)
    private String sn;

    /**
     *设备mac
     */
    @Schema(description = "设备mac", required = true)
    private String mac;
}
