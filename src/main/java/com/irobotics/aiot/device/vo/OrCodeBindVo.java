package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 二维码绑定
 */
@Data
@Schema(name  = "二维码绑定")
public class OrCodeBindVo {

    /**
     * 设备id
     */
    @Schema(description = "设备id")
    private String deviceId;

    /**
     * 绑定的key
     */
    @Schema(description = "绑定的key", required = true)
    private String bindKey;

    /**
     * 家庭ID
     */
    @Schema(description = "家庭ID", required = true)
    private String familyId;

    /**
     * 房间id
     */
    @Schema(description = "房间id，可以指定设备放到哪个房间，若不指定，则放到家庭的默认房间（未分配的房间）下")
    private String roomId;
}
