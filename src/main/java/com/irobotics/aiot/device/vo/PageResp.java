package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @since 2022/2/16
 */
@Data
@NoArgsConstructor
@Schema(name  = "SNIdResp", description = "sn批量返回设备id分页实体")
public class PageResp<T> {

    /**
     * 每页条数
     */
    @Schema(description = "每页条数")
    private Integer pageSize;

    /**
     * 页数
     */
    @Schema(description = "页数")
    private Integer pageNum;

    /**
     * 总页数
     */
    @Schema(description = "总页数")
    private Integer pages;

    /**
     * 总条数
     */
    @Schema(description = "总条数")
    private Integer size;

    /**
     * 记录
     */
    @Schema(description = "记录")
    private List<T> record;

    /**
     * 当前页
     */
    @Schema(description = "当前页")
    private Integer current;

    /**
     * 构造
     */
    public PageResp(Integer pageNum, Integer pageSize, List<T> record) {
        this.size = record.size();
        this.pages = this.size / pageSize + 1;
        if (pageNum > pages) {
            pageNum = pages;
        }
        pageNum = pageNum < 1 ? 1 : pageNum;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.record = record.subList((pageNum - 1) * pageSize, Math.min(pageNum * pageSize, record.size()));
    }

    /**
     * 是否有前一页
     */
    public boolean hasPrevious() {
        return this.current > 1;
    }

    /**
     * 是否存在下一页
     *
     * @return true / false
     */
    public boolean hasNext() {
        return this.current < this.getPages();
    }
}
