package com.irobotics.aiot.device.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import com.irobotics.aiot.device.entity.ProductModeSellRegionEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Data
@Schema(name  = "ProductSnDetailEntity对象", description = "")
public class ProductSnDetailEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private String id;

    /**
     * sn号
     */
    @Schema(description = "sn号")
    private String sn;

    /**
     * sn组id
     */
    @Schema(description = "sn组id")
    private String groupId;

    /**
     * 密钥
     */
    @Schema(description = "密钥")
    private String key;

    /**
     * 激活时间
     */
    @Schema(description = "激活时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime activeTime;

    /**
     * 产品型号
     */
    @Schema(description = "产品型号")
    private String productModeId;

    /**
     * 区域
     */
    @Schema(description = "区域")
    private String zone;

    /**
     * 创建者
     */
    @Schema(description = "创建者 用户ID")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 修改者
     */
    @Schema(description = "修改者 用户ID")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private String tenantId;

    /**
     * 删除标志
     */
    @Schema(description = "0：正常；低于0的数字：已删除")
    private Integer deleteFlag;


    /**
     *是否开启防串货功能  0 不开启  1  开启
     */
    @Schema(description = "是否禁用非销售地区 0 不禁用   1  禁用")
    private Integer disableNotSellRegion;


    @Schema(description = "sellRegion=1时-销售地区-具体地区")
    @TableField(exist = false)
    private List<ProductModeSellRegionEntity> productModeSellRegions;


}
