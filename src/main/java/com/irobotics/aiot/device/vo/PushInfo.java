package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 极光推送的信息
 */
@Data
@Schema(name  = "极光推送的信息")
public class PushInfo {

    /**
     * 目标id
     */
    @Schema(description = "目标id，分享类型是设备（0）时，则为设备id，分享类型为家庭（1）时，则为家庭id")
    private String targetId;

    /**
     * 分享记录的id
     */
    @Schema(description = "分享记录的id")
    private String shareId;

    /**
     * 家庭名称
     */
    @Schema(description = "家庭名称，分享家庭时，有值")
    private String familyName;

    /**
     * 设备sn
     */
    @Schema(description = "设备sn，当分享设备时，有值")
    private String sn;

    /**
     * 设备产品图片
     */
    private String devicePhoto;

    /**
     * 有参构造
     *
     * @param targetId
     * @param shareId
     */
    public PushInfo(String targetId, String shareId) {
        this.targetId = targetId;
        this.shareId = shareId;
    }

    /**
     * 无参构造
     */
    public PushInfo() {
    }
}
