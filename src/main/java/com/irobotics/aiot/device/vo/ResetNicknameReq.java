package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @author: tlh
 * @date: 2022-10-31 14:54
 * @description: 设备重置
 **/
@Data
@Schema(name  = "ResetNicknameReq对象", description = "重置设备昵称参数实体")
public class ResetNicknameReq {

    @Schema(description = "设备id", required = true)
    private String deviceId;

    @Schema(description = "是否是邀请者", required = true)
    private boolean inviter;

    @Schema(description = "设置sn", required = true)
    private String sn;
}
