package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RevokeDto
 * @Description
 * @createTime 2021/11/30 13:53
 */
@Data
@Schema(name  = "管理员撤销实体类")
public class RevokeDto {

    /**
     * 被邀请人的用户id
     */
    @Schema(description = "被邀请人的用户id")
    private String beInviteId;

    /**
     * 家庭id
     */
    @Schema(description = "家庭id")
    private String familyId;

    /**
     * 共享状态
     */
    @Schema(description = "共享状态")
    private String inviteId;

    /**
     * 无参构造
     */
    public RevokeDto() {
    }

    /**
     * 有参构造
     *
     * @param beInviteId
     * @param familyId
     * @param inviteId
     */
    public RevokeDto(String beInviteId, String familyId, String inviteId) {
        this.beInviteId = beInviteId;
        this.familyId = familyId;
        this.inviteId = inviteId;
    }

}
