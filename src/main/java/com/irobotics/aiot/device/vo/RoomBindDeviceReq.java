package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @since 2021/11/27
 */
@Data
@Schema(name  = "RoomBindDeviceReq对象", description = "家庭房间绑定设备参数实体")
public class RoomBindDeviceReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 家庭ID
     */
    @Schema(description = "家庭ID", required = true)
    private String familyId;

    /**
     * 房间ID
     */
    @Schema(description = "房间ID", required = true)
    private String roomId;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID", required = true)
    private String deviceId;

    /**
     * 设备昵称
     */
    @Schema(description = "设备昵称")
    private String nickname;

    /**
     * 设备sn
     */
    @Schema(description = "设备sn")
    private String sn;

    /**
     * 产品信息id
     */
    @Schema(description = "产品信息id")
    private String productId;

    /**
     * 无参构造
     */
    public RoomBindDeviceReq() {
    }

    /**
     * 有参构造
     *
     * @param familyId
     * @param roomId
     * @param deviceId
     * @param nickname
     * @param sn
     * @param productId
     */
    public RoomBindDeviceReq(String familyId, String roomId, String deviceId, String nickname, String sn, String productId) {
        this.familyId = familyId;
        this.roomId = roomId;
        this.deviceId = deviceId;
        this.nickname = nickname;
        this.sn = sn;
        this.productId = productId;
    }
}
