package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @since 2021/11/27
 */
@Data
@Schema(name  = "RoomBindDeviceReq对象", description = "家庭房间绑定设备参数实体")
public class RoomBindShareDeviceReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 家庭ID
     */
    @Schema(description = "家庭ID", required = true)
    private String familyId;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID", required = true)
    private String deviceId;

    /**
     * 设备昵称
     */
    @Schema(description = "设备昵称")
    private String nickname;

    /**
     * 设备sn
     */
    @Schema(description = "设备sn", required = true)
    private String sn;

    /**
     * 被邀请者用户id
     */
    @Schema(description = "被邀请者用户id", required = true)
    private String userId;

    /**
     * 设备拥有者用户id
     */
    @Schema(description = "设备拥有者用户id", required = true)
    private String owner;

    /**
     * 产品信息id
     */
    @Schema(description = "产品信息id")
    private String productId;

    /**
     * 无参构造
     */
    public RoomBindShareDeviceReq() {
    }

    /**
     * 有参构造
     *
     * @param userId
     * @param familyId
     * @param deviceId
     * @param nickname
     * @param sn
     * @param owner
     * @param productId
     */
    public RoomBindShareDeviceReq(String userId, String familyId, String deviceId, String nickname, String sn, String owner, String productId) {
        this.userId = userId;
        this.familyId = familyId;
        this.deviceId = deviceId;
        this.nickname = nickname;
        this.sn = sn;
        this.owner = owner;
        this.productId = productId;
    }
}
