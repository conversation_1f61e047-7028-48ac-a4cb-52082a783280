package com.irobotics.aiot.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RtcDeviceNodeInfoVo {

    @Schema(description = "声网的APP ID，这里是设备初次绑定的APPId，并不一定是当前激活的RTC客户端APPId")
    private String rtcAppId;

    @Schema(description = "RESTful API的key（在声网平台开发配置中的key）")
    private String clientKey;

    @Schema(description = "RESTful API的secret（在声网平台开发配置中的secret）")
    private String clientSecret;

    @Schema(description = "设备 node 唯一标识")
    private String nodeId;

    @Schema(description = "设备 node 密钥")
    private String nodeSecret;

    @Schema(description = "服务区域")
    private String region;

    //TODO: 新增声网配置的productKey
    @Schema(description = "新增声网配置的productKey")
    private String RtcProductKey;
}
