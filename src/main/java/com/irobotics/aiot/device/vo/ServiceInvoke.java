package com.irobotics.aiot.device.vo;

import lombok.Data;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/10/18 11:23
 */
@Data
public class ServiceInvoke {

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 企业ide
     */
    private String tenantId;

    /**
     * 无参构造
     */
    public ServiceInvoke() {
    }

    /**
     * 有参构造
     *
     * @param deviceId
     * @param tenantId
     */
    public ServiceInvoke(String deviceId, String tenantId) {
        this.deviceId = deviceId;
        this.tenantId = tenantId;
    }
}
