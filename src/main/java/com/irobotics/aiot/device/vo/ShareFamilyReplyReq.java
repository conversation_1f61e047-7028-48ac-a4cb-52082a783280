package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/12/23 14:39
 */
@Data
@Schema(name  = "回应共享家庭实体")
public class ShareFamilyReplyReq {


    /**
     * 家庭id
     */
    @Schema(description = "家庭id")
    private String familyId;

    /**
     * 邀请者id
     */
    @Schema(description = "邀请者id")
    private String inviteId;

    /**
     * 被邀请者id
     */
    @Schema(description = "被邀请者id")
    private String beInviteId;

    /**
     * 状态
     */
    @Schema(description = "1-同意，2-拒绝")
    private int status;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private String tenantId;

    /**
     * 无参构造
     */
    public ShareFamilyReplyReq() {
    }

    /**
     * 有参构造
     *
     * @param familyId
     * @param invitedId
     * @param beInvitedId
     * @param status
     * @param tenantId
     */
    public ShareFamilyReplyReq(String familyId, String invitedId, String beInvitedId, int status, String tenantId) {
        this.familyId = familyId;
        this.inviteId = invitedId;
        this.beInviteId = beInvitedId;
        this.status = status;
        this.tenantId = tenantId;
    }
}
