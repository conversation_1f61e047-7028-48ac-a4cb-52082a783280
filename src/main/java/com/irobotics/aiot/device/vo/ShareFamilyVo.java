package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @description: 家庭分享请求参数
 * @author: huangwa1911
 * @time: 2021/12/24 9:09
 */
@Data
public class ShareFamilyVo {

    /**
     * 被邀请的用户id列表
     */
    @Schema(description = "被邀请的用户id列表", required = true)
    private List<String> beInviteIds;

    /**
     * 被分享的家庭ID
     */
    @Schema(description = "被分享的家庭ID", required = true)
    private String targetId;

    /**
     * 被分享的家庭名称
     */
    @Schema(description = "被分享的家庭名称", required = true)
    private String familyName;
}
