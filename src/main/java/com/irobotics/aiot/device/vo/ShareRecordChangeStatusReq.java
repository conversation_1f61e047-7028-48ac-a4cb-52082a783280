package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/12/31 11:04
 */
@Data
@Schema(name  = "同意或拒绝分享实体封装")
public class ShareRecordChangeStatusReq {

    /**
     * 消息分享记录id
     */
    @Schema(description = "消息分享记录id", required = true)
    private String shareId;

    /**
     * 消息记录id
     */
    @Schema(description = "消息记录id", required = true)
    private String recordId;

    /**
     * 状态
     */
    @Schema(description = "状态，同意或拒绝", required = true)
    private Integer status;

    /**
     * 家庭id
     */
    @Schema(description = "家庭id，回应的分享是设备分享时，必填")
    private String familyId;

    /**
     * 家庭名称
     */
    @Schema(description = "家庭名称, 回应的分享是家庭分享时必填")
    private String familyName;
}
