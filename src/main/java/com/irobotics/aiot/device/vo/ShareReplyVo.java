package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description: 回应分享请求参数
 * @author: huangwa1911
 * @time: 2021/11/10 9:22
 */
@ApiModel
@Data
public class ShareReplyVo {

    /**
     * 邀请者用户ID
     */
    @Schema(description = "邀请者用户ID", required = true)
    private String inviterId;

    /**
     * 被分享的目标ID
     */
    @Schema(description = "被分享的目标ID", required = true)
    private String targetId;

    /**
     * 回应类型
     */
    @Schema(description = "1-同意，2-拒绝", required = true)
    private int dealType;

    /**
     * 共享类型
     */
    @Schema(description = "共享类型，0-设备共享，1-家庭共享", required = true)
    private int type;

    /**
     * 家庭id
     */
    @Schema(description = "家庭id，共享设备时必填")
    private String familyId;

    /**
     * 家庭名称
     */
    @Schema(description = "家庭名称，共享家庭时必填")
    private String familyName;
}
