package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @description: 设备分享请求参数
 * @author: huangwa1911
 * @time: 2021/11/10 9:19
 */
@ApiModel
@Data
public class ShareVo {

    /**
     * 被邀请的用户名
     */
    @Schema(description = "被邀请的用户名，密文，AES加密", required = true)
    private String beInvited;

    /**
     * 被分享的设备ID列表
     */
    @Schema(description = "被分享的设备ID列表", required = true)
    private List<String> targetIds;
}
