package com.irobotics.aiot.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SyncBindReq {

    @Schema(description = "tenantId")
    private String tenantId;

    @Schema(description = "deviceId")
    private String deviceId;

    @Schema(description = "userId")
    private String userId;

    public SyncBindReq(String tenantId, String deviceId, String userId) {
        this.tenantId = tenantId;
        this.deviceId = deviceId;
        this.userId = userId;
    }
}
