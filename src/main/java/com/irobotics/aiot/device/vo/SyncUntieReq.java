package com.irobotics.aiot.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SyncUntieReq {

    @Schema(description = "tenantId")
    private String tenantId;

    @Schema(description = "deviceId")
    private String deviceId;

    public SyncUntieReq(String tenantId, String deviceId) {
        this.tenantId = tenantId;
        this.deviceId = deviceId;
    }
}
