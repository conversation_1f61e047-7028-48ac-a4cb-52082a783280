package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 更新产品下对应的设备的产品图片信息
 * <AUTHOR>
 * @date 2022年11月7日
 */
@Data
@Schema(name  = "更新产品下对应的设备的产品图片信息")
public class UpdatePhotoVo {

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private String productId;

    /**
     *产品图片地址
     */
    @Schema(description = "产品图片地址")
    private String photoUrl;

    /**
     *企业id
     */
    @Schema(description = "企业id")
    private String tenantId;
}
