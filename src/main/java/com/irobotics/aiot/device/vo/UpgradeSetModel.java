package com.irobotics.aiot.device.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UpgradeSetModel
 * @Description OTA 升级set发送kafka消息数据model
 * @createTime 2022/04/21 14:57
 */
@Data
@AllArgsConstructor
public class UpgradeSetModel {

    /**
     * 升级包大小
     */
    private Integer packageSize;

    /**
     * 产品型号代码（原工程类型）
     */
    private String productModelCode;

    /**
     * 固件类型
     */
    private String packageType;

    /**
     * 升级包版本名称
     */
    private String versionName;

    /**
     * 升级包版本
     */
    private String versionCode;

    /**
     * 升级包下载地址
     */
    private String packageUrl;

    /**
     * 升级包MD5值
     */
    private String md5;

    /**
     * 签名
     */
    private boolean signed;

    /**
     * 是否静默升级
     */
    private boolean silence;

}
