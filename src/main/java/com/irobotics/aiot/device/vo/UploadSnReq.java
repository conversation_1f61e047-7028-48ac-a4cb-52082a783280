package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @author: tlh
 * @date: 2022-12-23 10:54
 * @description:
 **/
@Schema(name  = "sn初始化上传sn",description = "")
@Data
public class UploadSnReq {

    @Schema(description = "企业id")
    private String paramTenantId;

    @Schema(description = "sn 集合")
    private List<String> snList;
}
