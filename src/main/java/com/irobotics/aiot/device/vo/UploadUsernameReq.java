package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @author: tlh
 * @date: 2022-12-23 11:37
 * @description:
 **/
@Schema(name  = "用户初始化上传username",description = "")
@Data
public class UploadUsernameReq {

    @Schema(description = "企业id")
    private String paramTenantId;

    @Schema(description = "usernameList 集合")
    private List<String> usernameList;
}
