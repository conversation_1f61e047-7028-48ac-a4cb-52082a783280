package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 拉取用户门锁记录 请求参数
 */
@Data
@ApiModel
public class UserLockReq {

    /**
     * 设备id
     */
    @Schema(description = "设备id", required = true)
    private String deviceId;

    /**
     * 租户id
     */
    @Schema(description = "租户id", required = true)
    private String tenantId;

}


