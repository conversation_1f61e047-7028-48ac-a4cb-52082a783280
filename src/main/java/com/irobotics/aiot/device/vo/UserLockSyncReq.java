package com.irobotics.aiot.device.vo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description: 用户门锁同步数据信息
 * @author: qwei
 * @time: 2022/5/6 18:19
 */
@Data
@ApiModel
public class UserLockSyncReq {

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 角色
     */
    @Schema(description = "角色（owner：我，admin：管理员，customer：常访客）")
    private String role;

    /**
     * 设备用户id
     */
    @Schema(description = "设备用户id", required = true)
    private String deviceUserId;

    /**
     * 起始时间
     */
    @Schema(description = "起始时间")
    private Long beginTime;

    /**
     * 失效时间
     */
    @Schema(description = "失效时间")
    private Long endTime;

    /**
     * 凭据列表信息
     */
    @Schema(description = "凭据列表信息")
    private JSONObject lockCfg;

}


