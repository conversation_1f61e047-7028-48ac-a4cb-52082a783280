package com.irobotics.aiot.device.vo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户门锁信息
 */
@Data
@Schema(name  = "用户门锁信息")
public class UserLockVo {

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 角色
     */
    @Schema(description = "角色（owner：我，admin：管理员，customer：常访客）")
    private String role;

    /**
     * 设备用户id
     */
    @Schema(description = "设备用户id")
    private String deviceUserId;

    /**
     * 起始时间
     */
    @Schema(description = "起始时间")
    private Long beginTime;

    /**
     * 失效时间
     */
    @Schema(description = "失效时间")
    private Long endTime;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    private String deviceId;

    /**
     * 配置信息
     */
    @Schema(description = "配置信息")
    private JSONObject lockCfg;

}



