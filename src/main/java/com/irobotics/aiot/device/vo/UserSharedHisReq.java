package com.irobotics.aiot.device.vo;

import com.irobotics.aiot.device.common.PageGetReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description: 用户获取分享历史
 * @author: huangwa1911
 * @time: 2021/12/28 10:51
 */
@Data
@Schema(name  = "用户获取分享历史")
public class UserSharedHisReq extends PageGetReq {

    /**
     * 家庭id或设备id
     */
    @Schema(description = "家庭id或设备id, 当查询家庭分享记录的时候必填")
    private String targetId;

    /**
     * 共享类型
     */
    @Schema(description = "共享类型,0-共享设备，1-共享家庭，暂时忽略")
    private int type = 1;
}
