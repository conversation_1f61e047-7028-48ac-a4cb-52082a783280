package com.irobotics.aiot.device.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @since 2021/11/27
 */
@Data
@Schema(name  = "RoomUniteShareDeviceReq对象", description = "家庭房间解绑分享设备参数实体")
public class UserUniteShareDeviceReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    @Schema(description = "设备id")
    private String deviceId;

    /**
     * 邀请者
     */
    @Schema(description = "邀请者", required = true)
    private String inviter;

    /**
     * 被邀请者
     */
    @Schema(description = "被邀请者", required = true)
    private String beInviter;

    /**
     * 是否时拥有者
     */
    @Schema(description = "是否时拥有者", required = true)
    private boolean owner;

    /**
     * 无参构造
     */
    public UserUniteShareDeviceReq() {
    }

    /**
     * 有参构造
     *
     * @param deviceId
     * @param inviter
     * @param beInviter
     * @param owner
     */
    public UserUniteShareDeviceReq(String deviceId, String inviter, String beInviter, boolean owner) {
        this.deviceId = deviceId;
        this.inviter = inviter;
        this.beInviter = beInviter;
        this.owner = owner;
    }

}
