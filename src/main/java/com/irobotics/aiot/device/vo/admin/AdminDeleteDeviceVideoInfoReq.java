package com.irobotics.aiot.device.vo.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @author:tlh
 * @create: 2023-09-26 11:32:47
 * @Description:
 */
@Data
@Schema(name  = "AdminDeleteDeviceVideoInfoReq", description = "删除三元组信息")
public class AdminDeleteDeviceVideoInfoReq {

    @Schema(description = "idList")
    private List<String> idList;

    @Schema(description = "租户ID")
    private String tenantId;
}
