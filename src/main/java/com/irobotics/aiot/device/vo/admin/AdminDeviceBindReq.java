package com.irobotics.aiot.device.vo.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author:tlh
 * @create: 2023-07-06 11:32:11
 * @Description:
 */
@Data
@Schema(name  = "AdminDeviceBindReq对象", description = "设备绑定列表查询参数")
public class AdminDeviceBindReq extends AdminPageReq{

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "设备SN")
    private String sn;

    /**
     * 创建时间：开始的时间戳
     */
    @Schema(description = "开始的时间戳")
    private Long beginTime;

    /**
     * 创建时间：结束时间戳
     */
    @Schema(description = "结束时间戳")
    private Long endTime;
}
