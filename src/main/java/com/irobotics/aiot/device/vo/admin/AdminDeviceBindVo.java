package com.irobotics.aiot.device.vo.admin;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author:tlh
 * @create: 2023-07-06 11:28:06
 * @Description: 非智能家居状态下
 */
@Data
@Schema(name  = "AdminDeviceBindVo对象", description = "非智能家居状态下-设备绑定列表")
public class AdminDeviceBindVo {

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "设备昵称")
    private String deviceNickname;

    @Schema(description = "设备sn")
    private String sn;

    @Schema(description = "mac地址")
    private String mac;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "设备拥有者")
    private String owner;

    @Schema(description = "用户类型: 1:自有账号，2:第三方授权账号;默认为值为 1")
    private String bindUserType;

    @Schema(description = "绑定时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime bindTime;
}
