package com.irobotics.aiot.device.vo.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author:tlh
 * @create: 2023-09-25 19:54:57
 * @Description:
 */
@Data
@Schema(name  = "AdminDeviceVideoInfoReq对象", description = "")
public class AdminDeviceVideoInfoReq extends AdminPageReq{

    @Schema(description = "aiot云平台产品id")
    private String productId;

    @Schema(description = "sn")
    private String sn;

    @Schema(description = "deviceName")
    private String deviceName;

    @Schema(description = "productKey")
    private String productKey;

    /**
     * 创建时间：开始的时间戳
     */
    @Schema(description = "开始的时间戳")
    private Long beginTime;

    /**
     * 创建时间：结束时间戳
     */
    @Schema(description = "结束时间戳")
    private Long endTime;
}
