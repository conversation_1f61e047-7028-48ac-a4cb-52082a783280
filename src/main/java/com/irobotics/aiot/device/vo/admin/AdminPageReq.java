package com.irobotics.aiot.device.vo.admin;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author:tlh
 * @create: 2023-07-06 11:31:07
 * @Description:
 */
@Data
public class AdminPageReq {

    @Schema(description = "页数", required = true)
    int page = 1;

    @Schema(description = "每页显示条数", required = true)
    int pageSize = 15;

    @Schema(description = "租户ID")
    private String tenantId;
}
