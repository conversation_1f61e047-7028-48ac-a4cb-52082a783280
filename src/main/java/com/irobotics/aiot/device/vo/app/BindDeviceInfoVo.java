package com.irobotics.aiot.device.vo.app;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @since 2021/11/30
 */
@Data
@Schema(name  = "BindDeviceInfoVo对象", description = "设备绑定信息对象")
public class BindDeviceInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户")
    private String userId;

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "设备sn")
    private String sn;

    @Schema(description = "mac地址")
    private String mac;

    @Schema(description = "拥有者")
    private String owner;

    @Schema(description = "设备昵称")
    private String nickname;

    @Schema(description = "软件版本号")
    private String versions;

    @Schema(description = "设备状态")
    private int status;
    /**
     * 开放平台 open-api 对应设备在线状态
     */
    @Schema(description = "设备在线状态：离线、在线",hidden = true)
    private Boolean onlineStatus;

    private boolean isDefault;

    @Schema(description = "最近在线时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime onlineTime;

    @Schema(description = "最近离线时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime offlineTime;

    @Schema(description = "设备头像")
    private String photoUrl;

    @Schema(description = "产品ID")
    private String productId;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "产品型号代码，相当与原工程类型")
    private String productModeCode;

    @Schema(description = "绑定时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime bindTime;

    @Schema(description = "虚拟设备id，json结构")
    private String iotId;
}
