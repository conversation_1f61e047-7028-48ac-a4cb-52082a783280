package com.irobotics.aiot.device.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @author:tlh
 * @create: 2023-07-19 16:49:07
 * @Description:
 */
@Data
@Schema(name  = "MoveDeviceReq", description = "移动设备")
public class MoveDeviceReq {

    @Schema(description = "设备id列表")
    private List<String> idList;

}
