package com.irobotics.aiot.device.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author:tlh
 * @create: 2023-07-06 14:39:51
 * @Description:
 */
@Data
@Schema(name  = "UntieDeviceReq对象", description = "解绑设备参数实体")
public class UntieDeviceReq {

    @Schema(description = "设备ID", required = true)
    private String deviceId;

    /**
     * 兼容APP-SDK-OPENAPI的20240601前已发布版本的接口调用
     * 设备sn
     */
    @Schema(description = "设备sn",hidden = true)
    private String sn;

}
