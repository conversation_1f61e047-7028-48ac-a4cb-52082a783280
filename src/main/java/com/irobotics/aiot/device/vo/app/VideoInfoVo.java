package com.irobotics.aiot.device.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author:tlh
 * @create: 2023-09-25 16:23:28
 * @Description:
 */
@Data
@Schema(name  = "VideoInfoVo", description = "视频流返回")
public class VideoInfoVo {

    @Schema(description = "sn")
    private String deviceName;

    @Schema(description = "产品key")
    private String productKey;

    @Schema(description = "阿里云设备秘钥")
    private String deviceSecret;
}
