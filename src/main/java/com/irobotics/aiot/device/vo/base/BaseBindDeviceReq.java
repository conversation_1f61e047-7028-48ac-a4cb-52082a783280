package com.irobotics.aiot.device.vo.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @since 2021/11/27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(name  = "BaseBindDeviceReq对象", description = "基站绑定设备参数实体")
public class BaseBindDeviceReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", required = true)
    private String userId;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID", required = true)
    private String deviceId;

    /**
     * 基站sn
     */
    @Schema(description = "基站sn(用户名)", required = true)
    private String baseSn;

    /**
     * 设备sn
     */
    @Schema(description = "设备sn", required = true)
    private String sn;

    /**
     * 产品ID
     */
    @Schema(description = "产品ID", required = true)
    private String productId;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID", required = true)
    private String tenantId;

    /**
     * 设备昵称
     */
    private String nickname;
}
