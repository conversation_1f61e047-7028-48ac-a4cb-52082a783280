package com.irobotics.aiot.device.vo.base;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class BaseBindVo {


    @Schema(description = "基站id")
    private String baseId;

    @Schema(description = "设备sn")
    private String sn;

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "拥有者ID")
    private String owner;

    @Schema(description = "mac")
    private String mac;

    @Schema(description = "nickname")
    private String nickname;


}
