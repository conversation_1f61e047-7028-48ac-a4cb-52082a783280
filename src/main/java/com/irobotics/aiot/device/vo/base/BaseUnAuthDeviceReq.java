package com.irobotics.aiot.device.vo.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @since 2021/12/24
 */
@Data
@Schema(name  = "BaseUnAuthDeviceReq对象", description = "基站取消授权对象")
public class BaseUnAuthDeviceReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "设备SN", required = true)
    private String sn;

}
