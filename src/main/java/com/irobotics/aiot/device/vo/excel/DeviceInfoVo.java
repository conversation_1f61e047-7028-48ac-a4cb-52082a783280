package com.irobotics.aiot.device.vo.excel;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ExcelIgnoreUnannotated
public class DeviceInfoVo {


    /**
     * 主键id
     */
    @Schema(description = "主键id（t_sn_detail的主键id）")
    @ExcelProperty(value = "设备ID",index = 0)
    private String id;

    /**
     * 产品id
     */
    @Schema(description = "产品id")
    @ExcelProperty(value = "产品id",index = 1)
    private String productId;

    /**
     * 说明，设备里的媒体设备，存在一个自己的设备id，相当于设备与这个媒体设备进行一对一的关联关系
     */
    @Schema(description = "虚拟设备id，json结构")
    @ExcelProperty(value = "虚拟设备id，json结构",index = 2)
    private String iotId;

    /**
     * 虚拟设备id
     */
    @Schema(description = "虚拟设备id，相当与原工程类型")
    @ExcelProperty(value = "虚拟设备id，相当与原工程类型",index = 3)
    private String productModeCode;

    /**
     * mac地址
     */
    @Schema(description = "mac地址")
    @ExcelProperty(value = "mac地址",index = 4)
    private String mac;

    /**
     * 设备sn
     */
    @Schema(description = "设备sn")
    @ExcelProperty(value = "设备sn",index = 5)
    private String sn;

    /**
     * 密钥
     */
    @Schema(description = "密钥")
    @ExcelProperty(value = "密钥",index = 6)
    private String key;

    /**
     * 软件版本号
     */
    @Schema(description = "软件版本号")
    @ExcelProperty(value = "软件版本号",index = 7)
    private String versions;

    /**
     * 设备昵称
     */
    @Schema(description = "设备昵称")
    @ExcelProperty(value = "设备昵称",index = 8)
    private String nickname;

    /**
     * 设备默认昵称
     */
    @Schema(description = "设备默认昵称")
    @ExcelProperty(value = "设备默认昵称",index = 9)
    private String defaultNickname;

    /**
     * 测试设备是否已重置
     */
    @Schema(description = "测试设备是否已重置：0已重置，1未重置")
    @ExcelProperty(value = "测试设备是否已重置",index = 10)
    private String resetStatus;

    /**
     * 最近在线时间
     */
    @Schema(description = "最近在线时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    @ExcelProperty(value = "最近在线时间",index = 11)
    private LocalDateTime onlineTime;

    /**
     * 最近离线时间
     */
    @Schema(description = "最近离线时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    @ExcelProperty(value = "最近离线时间",index = 12)
    private LocalDateTime offlineTime;

    /**
     * 登录IP
     */
    @Schema(description = "登录IP")
    @ExcelProperty(value = "登录IP",index = 13)
    private String ip;

    /**
     * 最近登录的城市
     */
    @Schema(description = "最近登录的城市")
    @ExcelProperty(value = "最近登录的城市",index = 14)
    private String city;

    /**
     * 产品的图片
     */
    @Schema(description = "产品的图片，冗余字段")
    @ExcelProperty(value = "产品的图片",index = 15)
    private String photoUrl;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    @ExcelProperty(value = "创建时间",index = 16)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    @ExcelProperty(value = "修改时间",index = 17)
    private LocalDateTime updateTime;


    /**
     * 租户id
     */
    @Schema(description = "租户id")
    @ExcelProperty(value = "租户id",index = 18)
    private String tenantId;

    /**
     * 区域
     */
    @Schema(description = "区域")
    @ExcelProperty(value = "区域",index = 19)
    private String zone;

    /**
     * 设备状态
     */
    @Schema(description = "设备状态")
    @ExcelProperty(value = "设备状态",index = 20)
    private boolean status;

    /**
     * 设备影子最后一次更新的时间
     */
    @Schema(description = "设备影子最后一次更新的时间")
    @ExcelProperty(value = "设备影子最后一次更新的时间",index = 21)
    private Long timeStamp;

    /**
     * app是否开启密码验证
     */
    @Schema(description = "app是否开启密码验证，默认为否(0:否，1:是)")
    @ExcelProperty(value = "app是否开启密码验证",index = 22)
    private Integer verifyPassword;

}
