package com.irobotics.aiot.device.vo.inner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author:tlh
 * @create: 2023-10-16 20:26:41
 * @Description:
 */
@Data
@Schema(name  = "BindBaseListReq对象")
public class BindBaseListReq implements Serializable {

    private static final long serialVersionUID = 6498632101454287156L;

    @Schema(description = "sn类型,如果是基站sn 为true，否则为false",required = true)
    private Boolean flag;

    @Schema(description = "sn列表",required = true)
    private List<String> snList;

    /**
     * 搜索方式 1：模糊搜索；2：精确搜索
     */
    @Schema(description = "搜索方式 1：模糊搜索；2：精确搜索")
    private Integer searchType;
}
