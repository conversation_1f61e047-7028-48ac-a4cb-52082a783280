package com.irobotics.aiot.device.vo.inner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @author: tlh
 * @date: 2022-05-13 16:49
 * @description: 获取设备和用户的关系参数对象
 **/
@Data
@Schema(name  = "OwnerListParam对象")
public class OwnerListParam {

    @Schema(description = "设备id")
    private List<String> ids;

    @Schema(description = "true:表示只查设备拥有者，false:表示查绑定关系")
    private Boolean flag;
}
