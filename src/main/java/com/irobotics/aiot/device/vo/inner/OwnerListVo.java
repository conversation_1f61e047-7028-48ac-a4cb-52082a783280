package com.irobotics.aiot.device.vo.inner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Set;

/**
 * @author: tlh
 * @date: 2022-05-13 15:31
 * @description: 设备id 和 设备拥有者id集合
 **/
@Data
@Schema(name  = "OwnerListVo对象", description = "OwnerListVo对象")
public class OwnerListVo {

    @Schema(description = "设备id")
    private String deviceId;

    @Schema(description = "用户id")
    private Set<String> userIdList;

    @Schema(description = "租户ID")
    private String tenantId;
}
