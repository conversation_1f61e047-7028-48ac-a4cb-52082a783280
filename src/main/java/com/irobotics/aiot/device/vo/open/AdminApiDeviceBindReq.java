package com.irobotics.aiot.device.vo.open;

import com.irobotics.aiot.device.vo.admin.AdminDeviceBindReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author:tlh
 * @create: 2023-08-08 09:33:43
 * @Description:
 */
@Data
@Schema(name  = "AdminApiDeviceBindReq对象", description = "设备绑定列表查询参数")
public class AdminApiDeviceBindReq extends AdminDeviceBindReq {

    @Schema(description = "用户id")
    private String userId;
}
