package com.irobotics.aiot.device.vo.open;

import com.irobotics.aiot.device.common.ExecuteResultEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/20 15:28
 * @Description: 批量处理结果 成功与失败
 */
@Data
@ApiModel(value = "开放平台-批量处理结果")
public class BatchExecuteResult {

    @ApiModelProperty(value = "成功的设备SN")
    private List<String> successResult;

    @ApiModelProperty(value = "失败设备SN及其原因，比如,SN:xxx")
    private Map<String, String> failResult;

    @ApiModelProperty(value = "执行结果 1:成功，2:部分成功，0：失败")
    private int executeResult;

    public int getExecuteResult() {
        if (ObjectUtils.isEmpty(failResult) && ObjectUtils.isNotEmpty(successResult)) {
            executeResult = ExecuteResultEnum.SUCCESS.getCode();
        } else if (ObjectUtils.isNotEmpty(failResult) && ObjectUtils.isEmpty(successResult)) {
            executeResult = ExecuteResultEnum.FAIL.getCode();
        } else {
            executeResult = ExecuteResultEnum.PARTIAL_SUCCESS.getCode();
        }
        return executeResult;
    }
}
