package com.irobotics.aiot.device.vo.open;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 绑定设备
 */
@Data
@Schema(name  = "绑定设备")
public class BindDeviceVo {

    /**
     * 设备sn
     */
    @Schema(description = "设备sn")
    private String sn;

    @Schema(description = "bindKey")
    private String bindKey;

    /**
     * 重写
     *
     * @return
     */
    @Override
    public String toString() {
        return "BindDeviceVo{" +
                "sn='" + sn + '\'' +
                '}';
    }
}
