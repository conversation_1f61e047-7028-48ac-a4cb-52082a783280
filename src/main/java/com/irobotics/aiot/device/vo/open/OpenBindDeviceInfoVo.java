package com.irobotics.aiot.device.vo.open;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.irobotics.aiot.device.config.LocalDateTimeSerializerConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 绑定设备列表
 */
@Data
@Schema(name  = "绑定设备列表")
public class OpenBindDeviceInfoVo {

    @Schema(description = "用户")
    private String userId;

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "设备sn")
    private String sn;


    @Schema(description = "拥有者")
    private String owner;

    @Schema(description = "设备昵称")
    private String nickname;

    @Schema(description = "软件版本号")
    private String versions;

    /**
     *设备在线状态
     */
    @Schema(description = "设备在线状态：离线、在线")
    private Boolean onlineStatus;

    @Schema(description = "最近在线时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime onlineTime;

    @Schema(description = "最近离线时间")
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime offlineTime;

    @Schema(description = "设备头像")
    private String photoUrl;

    @Schema(description = "产品ID")
    private String productId;

    @Schema(description = "产品型号代码，相当与原工程类型")
    private String productModeCode;

    @Schema(description = "mac地址")
    private String mac;

    /**
     *设备影子属性内容
     */
    @Schema(description = "设备影子属性内容")
    private JSONObject properties;
}
