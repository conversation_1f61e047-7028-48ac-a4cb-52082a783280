package com.irobotics.aiot.device.vo.open;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/20 14:18
 * @Description: 开放平台-设备解绑参数
 */
@Data
@ApiModel(value = "开放平台-设备解绑参数")
public class UnBindDeviceVo {

    @ApiModelProperty(value = "用户id,设备拥有者", required = true)
    private String userId;

    @ApiModelProperty(value = "解绑的设备SN集合", required = true)
    private List<String> snList;
}