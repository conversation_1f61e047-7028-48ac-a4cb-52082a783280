nacos:
  host: ************
  port: 8858
spring:

#  redis:
#    cluster:
#      nodes: ***********:7000,***********:7001,***********:7002,***********:7003,***********:7004,***********:7005
  kafka:
    bootstrap-servers: ***********:9092,***********:9092,***********:9092
    producer: #生产者配置
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: 1

  redis:
    password: testrdpd
    cluster:
      enabled: true
      nodes:
        - ************:7000
        - ************:7001
        - ************:7002
        - ************:7003
        - ************:7004
        - ************:7005
      max-redirects: 3  # 获取失败 最大重定向次数
    lettuce:
      pool:
        max-active: 1000  #连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
#    host: ************
#    port: 6379


  #数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************
    username: aiot
    password: O8DOhaGcaO6R6CrB
    hikari:
      poolName: HikariPool
      maximumPoolSize: 10
      minimumIdle: 2
      idleTimeout: 15000
      maxLifetime: 500000
      autoCommit: true

#分页条数限制，默认50000条
mybatis-plus:
  page:
    limit: 50000

#租户id配置
tenant:
  enbale: true
  tablePre: t_device_
  idColumn: tenant_id
  idCamel: tenantId

#雪花算法配置
snowflake:
  work_id: 2
  datacenter_id: 1

# 接口 localdatetime 类型字段序列化转换
timeTransform:
  enable: true

jjwt:
  secretKey: irobotics-gateway
  expiration: 72000

emq:
  auth:
    jwt:
      secret: 123456
