server:
  port: 8304

application.type: device

service:
  test:
    secret: As35nskDfj$KJj*qppmZq#12

spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  profiles:
    active: @profiles.active@
  application:
    name: device-service

  #nacos注册与发现配置
  cloud:
    openfeign:
      circuitbreaker:
        enabled: true
        alphanumeric-ids:
          enabled: true
      okhttp:
        enabled: true
      httpclient:
        enabled: false
    nacos:
      discovery:
        server-addr: ${nacos.host}:${nacos.port:8848}
        namespace: ${nacos.namespace}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${nacos.namespace}
        file-extension: yml
        group: DEFAULT_GROUP
        # 用于共享的配置文件
        shared-configs:
          - data-id: application-${spring.profiles.active}.yml
            group: DEFAULT_GROUP
            refresh: true

# mybatis- plus配置
mybatis-plus:
  # xml扫描，多个目录用逗号或者分号隔开隔开
  mapper-locations: classpath:mapper/*.xml
  # 以下配置均有默认值,可以不设置
  #  global-config:
  #    db-config:
  #      #主键类型 AUTO:"数据库ID自增" INPUT:"用户输入ID",ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
  #      id-type: input
  configuration:
    # 是否开启自动驼峰命名规则映射:从数据库列名到Java属性驼峰命名的类似映射
    map-underscore-to-camel-case: true
    # 返回map时true:当查询数据为空时字段返回为null,false:不加这个查询数据为空时，字段将被隐藏
    call-setters-on-nulls: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

#租户id配置
tenant:
  enbale: true
  tablePre: t_device_
  idColumn: tenant_id
  idCamel: tenantId

feign:
  sentinel:
    enabled: true

ribbon:
  ReadTimeout: 1000
  ConnectTimeout: 500
  MaxAutoRetries: 1
  MaxAutoRetriesNextServer: 1
  OkToRetryOnAllOperations: false  #防止破坏幂等性
  ServerListRefreshInterval: 10000
