<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.irobotics.aiot.device.mapper.DeviceBindUserMapper">

    <update id="updateOwnerByDeviceId">
        UPDATE t_device_bind_user
        SET owner = #{owner}
        WHERE device_id = #{deviceId}
          AND user_agent = '1'
    </update>

    <update id="resetOwner">
        UPDATE t_device_bind_user
        SET `owner` = creator_id
        WHERE device_id = #{deviceId}
          AND user_agent = '1'
    </update>

    <select id="getBindListByDeviceIdList" resultType="com.irobotics.aiot.device.remote.model.BindIdVo">
        SELECT DISTINCT tdbu.device_id AS deviceId, tdbu.creator_id AS userId
        FROM t_device_bind_user tdbu
        WHERE tdbu.tenant_id = #{tenantId}
        AND tdbu.device_id IN
        <foreach collection="deviceIdList" item="deviceId" index="index"
                 open="(" close=")" separator=",">
            #{deviceId}
        </foreach>
    </select>

    <select id="getBindListByUserIdList" resultType="com.irobotics.aiot.device.remote.model.BindIdVo">
        SELECT DISTINCT tdbu.device_id AS device_id, tdbu.creator_id AS user_id
        FROM t_device_bind_user tdbu
        WHERE tdbu.tenant_id = #{tenantId}
        AND tdbu.creator_id IN
        <foreach collection="userIdList" item="userId" index="index"
                 open="(" close=")" separator=",">
            #{userId}
        </foreach>
    </select>

    <select id="getBindList" resultType="com.irobotics.aiot.device.vo.admin.AdminDeviceBindVo">
        SELECT tdbu.device_id deviceId,tdbu.sn,tdbu.owner,tdbu.creator_id userId,
               tdbu.bind_user_type bindUserType,tdi.mac,tdi.nickname deviceNickname,tdbu.bind_time bindTime
        FROM t_device_bind_user tdbu INNER JOIN t_device_info tdi ON tdi.id = tdbu.device_id
        WHERE
        tdbu.tenant_id = #{tenantId}
        <if test="sn!=null and sn!=''">
            AND tdbu.sn = #{sn}
        </if>
        <if test="userId!=null and userId!=''">
            AND tdbu.creator_id = #{userId}
        </if>
        <if test="beginTime!=null">
            AND tdbu.create_time <![CDATA[>=]]> #{beginTime}
        </if>
        <if test="endTime!=null">
            AND tdbu.create_time <![CDATA[<=]]> #{endTime}
        </if>
        Order by tdbu.create_time DESC
    </select>

    <select id="getListByUserId" resultType="com.irobotics.aiot.device.entity.DeviceBindUserEntity">
        SELECT tdbu.*,tdi.nickname
        FROM t_device_bind_user tdbu INNER JOIN t_device_info tdi ON tdi.id = tdbu.device_id
        WHERE tdbu.creator_id = #{userId} and tdbu.tenant_id = #{tenantId}
    </select>

    <resultMap id="bindDeviceInfoVoMap" type="com.irobotics.aiot.device.vo.app.BindDeviceInfoVo">
        <result column="creator_id" property="userId"/>
        <result column="device_id" property="deviceId"/>
        <result column="owner" property="owner"/>
        <result column="sn" property="sn"/>
        <result column="mac" property="mac"/>
        <result column="create_time" property="bindTime"/>
        <result column="nickname" property="nickname"/>
        <result column="versions" property="versions"/>
        <result column="online_time" property="onlineTime"/>
        <result column="offline_time" property="offlineTime"/>
        <result column="product_id" property="productId"/>
        <result column="photo_url" property="photoUrl"/>
        <result column="product_mode_code" property="productModeCode"/>
        <result column="iot_id" property="iotId"/>
    </resultMap>
    <sql id="bindDeviceColumn">
        tdbu.creator_id,tdbu.device_id,tdbu.owner,tdbu.sn,tdbu.create_time,
               tdi.mac,tdi.nickname,tdi.versions,tdi.online_time,tdi.offline_time,
               tdi.product_id,tdi.photo_url,tdi.product_mode_code,tdi.iot_id
    </sql>

    <select id="getDeviceInfoAndBindByUserId" resultMap="bindDeviceInfoVoMap">
        SELECT <include refid="bindDeviceColumn"/>
        FROM t_device_bind_user tdbu INNER JOIN t_device_info tdi ON tdi.id = tdbu.device_id
        WHERE tdbu.creator_id = #{userId} and tdi.disable_city = 0
        ORDER BY tdbu.device_sort_weight DESC, tdbu.id DESC
    </select>

    <!--主要是为了兼容开放平台-->
    <resultMap id="BindDeviceInfoVoMap" type="com.irobotics.aiot.device.vo.open.OpenBindDeviceInfoVo">
        <result column="creator_id" property="userId"/>
        <result column="device_id" property="deviceId"/>
        <result column="sn" property="sn"/>
        <result column="owner" property="owner"/>
        <result column="nickname" property="nickname"/>
        <result column="versions" property="versions"/>
        <result column="online_time" property="onlineTime"/>
        <result column="offline_time" property="offlineTime"/>
        <result column="photo_url" property="photoUrl"/>
        <result column="product_id" property="productId"/>
        <result column="mac" property="mac"/>
        <result column="product_mode_code" property="productModeCode"/>
    </resultMap>

    <select id="getOpenApiListByUserId" resultMap="BindDeviceInfoVoMap">
        SELECT tdb.creator_id,tdb.device_id,tdb.sn,tdb.owner,tdi.nickname,tdi.versions,tdi.mac,
               tdi.online_time,tdi.offline_time,tdi.photo_url,tdi.product_id,tdi.product_mode_code
        FROM t_device_bind_user tdb ,t_device_info tdi WHERE tdb.sn = tdi.sn AND tdb.creator_id = #{userId}
                                                    AND tdb.tenant_id = #{tenantId}
        ORDER BY tdb.bind_time DESC
    </select>

</mapper>
