<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.irobotics.aiot.device.mapper.DeviceInviteShareMapper">
    <resultMap id="BaseResultMap" type="com.irobotics.aiot.device.entity.DeviceInviteShareEntity">
        <!--
        WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="invite_id" jdbcType="VARCHAR" property="inviteId"/>
        <result column="be_invite_id" jdbcType="VARCHAR" property="beInviteId"/>
        <result column="target_id" jdbcType="VARCHAR" property="targetId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="removed" jdbcType="INTEGER" property="removed"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="zone" jdbcType="VARCHAR" property="zone"/>
    </resultMap>

    <select id="getDeviceShareHis" resultType="com.irobotics.aiot.device.vo.DeviceShareHisVo">
        select dis.*, info.sn, info.mac, info.nickname as name, info.product_mode_code from t_device_invite_share dis
        left join t_device_info info on info.id=dis.target_id
        <where>
            <if test="userId != null and userId != ''">
                and (dis.invite_id=#{userId} or dis.be_invite_id=#{userId})
            </if>
            <if test="targetId != null and targetId != ''">
                and dis.target_id=#{targetId} and dis.type = #{type}
            </if>
        </where>

        order by dis.create_time desc
    </select>
</mapper>