MZ�       ��  �       @                                   �   � �	�!�L�!This program cannot be run in DOS mode.

$       PE  d� ���b .   � & ! @   b     P       h                             ��                                         �  �    �  x           p  l           �  d                           �a  (                   ��  `                          .text   �?      @                 ` P`.data   �    P      F              @ P�.rdata  p   `      H              @ `@.pdata  l   p      P              @ 0@.xdata  �   �      T              @ 0@.bss    @   �                      � `�.edata  �    �      X              @ 0@.idata  x   �      Z              @ 0�.CRT    X    �      b              @ @�.tls        �      d              @ @�.reloc  d    �      f              @ 0B/4      0   �      h              @ PB/19     �#     $  l              @ B/31     �!   0  "   �             @ B/45     �.   `  0   �             @ B/57     h	   �  
   �             @ @B/70     c   �     �             @ B/81     �4   �  6   �             @ B/92     @   �     (             @ B                                                                                                                                                                                                                                                                                                                                                                                                H�
�  ��<  @ AUATUWVSH��(I��M�Ņ�uz��  1���~^��H��S  E1�   ��  H�-ߡ  �D  ��  ��L���H�;H��H��u�H�=�S  �����   �   �=<  �   H��([^_]A\A]� ����   eH�%0   H�GS  H�p1�H�-j�  ��     H9���   ��  ��H���H�3H��u�1�H�5S  �����   �����   �����   ����   H��R  H� H��t
M��   L���Ѓ�~  �   H��([^_]A\A]�D  �   H��([^_]A\A]�fD  H�
�~  �d<  �    H�3�   ������   �U���fD  1�H��t���fD  H��R  H�
�R  �   ��:  �=���f�H�YR  H�
BR  ��:  �   �(���f��   ��:  �����AVAUATVSH�� H�5�Q  I�͉A��L�Å�u^��}  ��t5�/,  I��1�L����  I��D��L���:  I��D��L��A��������uE1�D�������H�� [^A\A]A^�fD  ��+  A�D$�I��D��L���wp�s�����t�I��D��L���9  A�ƅ�tjA��up�O&  I�غ   L���0  A�ƅ�u�I��1�L���  I��1�L���^9  I��1�L�������`���@ ��   A��A���J��������A���7�����I�غ   L����   A��� ���ff.�     �H��HH�Q  �     ��t
H��H�����L�D$8�T$4H�L$(�%  �/  L�D$8�T$4H�L$(H��H�a����H��H�
V|  �Q9  �H�
	   �����@ Ð��������������UH��H�� A�@   L�L  H�L  �    H��  �АH�� ]�UH��H�M�UL�E �}t�}w�}t�}w�} t�}�������   ]�UH��H�� �   �   �!  �H�� ]�UH��H�� H�M�U�E��H�M�!  �H�� ]�UH��H�� H�M�U�E��H�M��!  �H�� ]�UH��H�� �M�M�+"  �H�� ]�UH��H�� �*"  H�� ]Ð����������UH��H��H�MH�UH�EH�E��H�E�H�PH�U��  H�EH�P�H�UH��u���H��]�UH��H�� H�MA�l   �    H�M�N7  �H�� ]�UH��H�� H�MH�} t�l   H�M�v�����H�� ]�USH��H�MH�U H�EH�U H�
H�ZH�H�XH�JH�ZH�HH�XH�J H�Z(H�H H�X(H�J0H�Z8H�H0H�X8H�J@H�ZHH�H@H�XHH�JPH�ZXH�HPH�XXH�J`H�H`�Rh�Ph�[]�UH��H�M�UH�E�     H�E�@    �} uZH�E�@g�	jH�E�@��g�H�E�@r�n<H�E�@:�O�H�E�@RQH�E�@�h�H�E�@ �كH�E�@$��[�XH�E�@؞�H�E�@�|6H�E�@�p0H�E�@9Y�H�E�@1��H�E�@XhH�E�@ ���dH�E�@$�O��H�E�U�Ph�]�UH��0  H��$�   H���   H���   ǅ�       �"H���   ���   �T����   �T�����   ���   v�ǅ�       �   ���   ����H���   H�� �������   ������H���   H�� ����	��   ������H���   H�� ����	��   ������H���   H�� ��	��   �T�����   ���   �\���ǅ�       �]  �E��U������U���1ыU���1�D��E��U�D�E��M�D1�!�1�A����   H��    H��G  �����   �D��Љ��   �E����E���
1E���
1�A�ЋU��E�!�A�ыE��M��U�	�!�D	�D����   �U����   ЉE����   ���   ЉE��E��U������U���1ыU���1�D��E��U�D�E��M�D1�!�1�A����   ����H��    H�G  �����   �����D��Љ��   �E����E���
1E���
1�A�ЋU��E�!�A�ыE��M��U�	�!�D	�D����   �U����   ЉE����   ���   ЉE��E��U������U���1ыU���1�D��E��U�D�E��M�D1�!�1�A����   ����H��    H�MF  �����   �����D��Љ��   �E����E���
1E���
1�A�ЋU��E�!�A�ыE��M��U�	�!�D	�D����   �U����   ЉE����   ���   ЉE��E��U������U���1ыU���1�D��E��U�D�E��M�D1�!�1�A����   ����H��    H��E  �����   �����D��Љ��   �E����E���
1E���
1�A�ЋU��E�!�A�ыE��M��U�	�!�D	�D����   �U����   ЉE����   ���   ЉE��E��U������U���1ыU���1�D��E��U�D�E��M�D1�!�1�A����   ����H��    H��D  �����   �����D��Љ��   �E����E���
1E���
1�A�ЋU��E�!�A�ыE��M��U�	�!�D	�D����   �U����   ЉE����   ���   ЉE��E��U������U���1ыU���1�D��E��U�D�E��M�D1�!�1�A����   ����H��    H��C  �����   �����D��Љ��   �E����E���
1E���
1�A�ЋU��E�!�A�ыE��M��U�	�!�D	�D����   �U����   ЉE����   ���   ЉE��E��U������U���1ыU���1�D��E��U�D�E��M�D1�!�1�A����   ����H��    H�C  �����   �����D��Љ��   �E����E���
1E���
1�A�ЋU��E�!�A�ыE��M��U�	�!�D	�D����   �U����   ЉE����   ���   ЉE��E��U������U���1ыU���1�D��E��U�D�E��M�D1�!�1�A����   ����H��    H�QB  �����   �����D��Љ��   �E����E���
1E���
1�A�ЋU��E�!�A�ыE��M��U�	�!�D	�D����   �U����   ЉE����   ���   ЉE����   ���   �����ǅ�      �  �E��U������U���1ыU���1�D��E��U�D�E��M�D1�!�1�A����   H��    H�gA  �����   �����D�������   �����D����
1��   �����D����
1��   �����D����   �����D����A�����   �����D����A1����   �����D����D1���   �����D����   �T�����   �D��ȉ��   �E����E���
1E���
1�A�ЋU��E�!�A�ыE��M��U�	�!�D	�D����   �U����   ЉE����   ���   ЉE��E��U������U���1ыU���1�D��E��U�D�E��M�D1�!�1�A����   ����H��    H��?  �����   �����D�������   �����D����
1��   �����D����
1��   �����D����   �����D����A�����   �����D����A1����   �����D����D1�D����   �����D�����   D�JD�D�ʉD��D�ȋD��ȉ��   �E����E���
1E���
1�A�ЋU��E�!�A�ыE��M��U�	�!�D	�D����   �U����   ЉE����   ���   ЉE��E��U������U���1ыU���1�D��E��U�D�E��M�D1�!�1�A����   ����H��    H��>  �����   �D�������   �D����
1��   �D����
1��   �����D����   ��
���D����A�����   ��
���D����A1����   ��
���D����D1�D����   �����D�����   D�JD�D�ʉD��D�ȋD��ȉ��   �E����E���
1E���
1�A�ЋU��E�!�A�ыE��M��U�	�!�D	�D����   �U����   ЉE����   ���   ЉE��E��U������U���1ыU���1�D��E��U�D�E��M�D1�!�1�A����   ����H��    H�=  �����   �����D�������   �����D����
1��   �����D����
1��   �����D����   �����D����A�����   �����D����A1����   �����D����D1�D����   ��
���D�����   D�JD�D�ʉD��D�ȋD��ȉ��   �E����E���
1E���
1�A�ЋU��E�!�A�ыE��M��U�	�!�D	�D����   �U����   ЉE����   ���   ЉE��E��U������U���1ыU���1�D��E��U�D�E��M�D1�!�1�A����   ����H��    H��;  �����   �����D�������   �����D����
1��   �����D����
1��   �����D����   �����D����A�����   �����D����A1����   �����D����D1�D����   �����D�����   D�JD�D�ʉD��D�ȋD��ȉ��   �E����E���
1E���
1�A�ЋU��E�!�A�ыE��M��U�	�!�D	�D����   �U����   ЉE����   ���   ЉE��E��U������U���1ыU���1�D��E��U�D�E��M�D1�!�1�A����   ����H��    H�/:  �����   �����D�������   �����D����
1��   �����D����
1��   �����D����   ��
���D����A�����   ��
���D����A1����   ��
���D����D1�D����   �����D�����   D�JD�D�ʉD��D�ȋD��ȉ��   �E����E���
1E���
1�A�ЋU��E�!�A�ыE��M��U�	�!�D	�D����   �U����   ЉE����   ���   ЉE��E��U������U���1ыU���1�D��E��U�D�E��M�D1�!�1�A����   ����H��    H��8  �����   �����D�������   �����D����
1��   �����D����
1��   �����D����   ��	���D����A�����   ��	���D����A1����   ��	���D����D1�D����   ��
���D�����   D�JD�D�ʉD��D�ȋD��ȉ��   �E����E���
1E���
1�A�ЋU��E�!�A�ыE��M��U�	�!�D	�D����   �U����   ЉE����   ���   ЉE��E��U������U���1ыU���1�D��E��U�D�E��M�D1�!�1�A����   ����H��    H�C7  �����   �����D�������   �����D����
1��   �����D����
1��   �D����   �����D����A�����   �����D����A1����   �����D����D1�D����   ��	���D�����   D�JD�D�ʉD��D�ȋD��ȉ��   �E����E���
1E���
1�A�ЋU��E�!�A�ыE��M��U�	�!�D	�D����   �U����   ЉE����   ���   ЉE����   ���   ?�\���ǅ�       �6H���   ���   �T����   �D���H���   ���   �L����   ���   v���H��0  ]�UH��H��0H�MH�UL�E H�}  �  H�E� ��?�E��@   +E���H�E�H�E� H�U �H�E�H�E�H�E�H�E� H�U 9�sH�E�@�PH�E�P�}� tsH�E H;E�riH�EH�P(�E�H�H�U�H�EI��H���!  H�EH��(H��H�M����H�E�HEH�E�H)E �E�    �H�EH��H�M����H�E@H�m @H�} ?w�H�}  t%H�EH�P(�E�H�H�U H�EI��H���!  ��H��0]�UH��H��@H�MH�UH�E� ����H�E�@��	ЉE�H�E� ���E��E����E�E����E�E����E�E��E�E����E�E����E�E����E�E��E�H�E� ��?�E�}�7w
�8   +E���x   +E�E��E�I��H��4  H�M�#���H�E�A�   H��H�M�
���H�E�@����H�E�H�E�@����H�EH���H�E�@����H�EH���H�E�PH�EH���H�E�@����H�EH���H�E�@����H�EH���H�E�@����H�EH���H�E�PH�EH���H�E�@����H�EH���H�E�@����H�EH��	�H�E�@����H�EH��
�H�E�PH�EH���H�E�@����H�EH���H�E�@����H�EH��
�H�E�@����H�EH���H�E�PH�EH���H�E�@����H�EH���H�E�@����H�EH���H�E�@����H�EH���H�E�PH�EH���H�E�@����H�EH���H�E�@����H�EH���H�E�@����H�EH���H�E�PH�EH���H�E�@ ����H�EH���H�E�@ ����H�EH���H�E�@ ����H�EH���H�E�P H�EH���H�E�@h��uSH�E�@$����H�EH���H�E�@$����H�EH���H�E�@$����H�EH���H�E�P$H�EH����H��@]�UH��H��   H�MH�UL�E D�M(H�E�H��������U(H�E�H�������H�UH�E�I��H�UH������H�U H�E�H���2���H�E�H��������H�Đ   ]�UH��H�� H�MA��   �    H�M��  �H�� ]�UH��H�� H�MH�EH� H�������H�� ]�UH��H�M�U�    ]�UH��H��PH�MH�UL�E H�} @vQH�EH� �    H�������H�EH� H�M H�UI��H���P���H�EH� H�U�H���e���H�E     H�E�H�EH�EH��A�@   �6   H���;  H�EH��HA�@   �\   H���   H�E�    �kH�UH�E�H�H���H�UH�E�H�� 1�H�UH�E�H�H���H�UH�E�H�H��H�H�UH�E�H�� 1�H�UH�E�H�H��H�H�E�H�E�H;E r�H�Eк    H�������H�EH� �    H�������H�EH�PH�EH� A�@   H���6����    H��P]�UH��H�� H�MH�UL�E H�EH� H�M H�UI��H��������    H�� ]�UH��H��@H�MH�UH�EH� H�U�H�������H�EH� �    H���L���H�EH�PHH�EH� A�@   H������H�EH� H�U�A�    H������H�EH� H�UH�������    H��@]�UH��H�� H�MH�EH� �    H�������H�EH�PH�EH� A�@   H���*����    H�� ]�UH��   H��$�   H���   H���   L���   L���   H�EH�������H�E�A�l   �    H����  H�E�H�EH�E�   H���������   ���    t���   �RH���   H�EI��H���   H�������H���   H���   H�EI��H���;���H���   H�EH���a����    H��   ]�UAUATWVSH��   H��$�   H�Mp�UxL���   D���   H��H��H�E�    H�E�    H�E     H�E    H���    u&�MxH�E�H�U�H�T$ I��L�Ep�    H�������)D�Ex���   H���   H�M�H�L$ M��L�EpH���{������   ��!��H��H�U ��I��A�    ��H�ֿ    ��H��H��H���  H)�H�D$0H�� H�E���   ��!��H�EI�к    H���Y  ���   �����E�E,    ��   �}, u	�E(    �*H�MH�E�H�U�H�H�QH�E�H�U�H�AH�Q�E(    D���   H�U�E(H�H���   H����  ���   E(�E,�HH�U�E(��E(D�E(H�MH�E�H�U�H�T$ M��I�Ⱥ    H���W����    ���    F��   A���E,��Hc�H���   H�H�E�H���q  ���    �E,�E,9E�����    H��H�e8[^_A\A]]Ð������������UH��H��0�M�U�U�EЉE��E���H�
�+  ��  �H��0]Ð��������������UH��H��H�M��D�E �EH�} u�    �j�E�E��W�M �ʸ����H��H�� ����������)��ʉыE�H�H�P�H�EHЍQ0��E �¸����H��H�� ���E �m��}� ��EH��]�UH��H�� H�M�UH�} tK�E=   wAA�   �    H�
�Y  �&  �EH�I��H�UH�
�Y  �  �E��Z  �[  ��H�� ]�UH��H�� H�M�U�oZ  ��9EtH�
�*  �  ��EH�I��H�JY  H�M�  �H�� ]�UH��M�E��Z  �]�UH��H���   ��Z  ��Z  )�=W  wH�Z  �!  ��Y  ���D$8    H�U�H�T$0�D$(   H�=*  H�T$ A�   L�*  ��H�
�X  �����5Z  ��Hi���NH�� ���E̋Ẽ��E�H�M�H�E�H�U�H�T$ A�   I�Ⱥ    H������H�M�H�E�H�U�H�� H�T$ A�   I�Ⱥ    H���c����E� �A�E�H��L���E�Hc�H��H�H�H�H�1Y  H�A�Ⱥ   H���~����E����E��}�v��E�i�X  �gY  H��X  H���   ]Ð��������������H��(H��  H� H��t"D  ��H��  H�PH�@H��  H��u�H��(�fD  VSH��(H��*  H������t9��t �ȃ�H��H)�H�t��@ �H��H9�u�H�
~���H��([^���� 1�fD  D�@��J�<� L��u��fD  �*Y  ��t�D  �Y     �q����ATUWVSH��0H�/  H�2��-�+  H�D$     H9�tH��H�  H��0[^_]A\�f�H�L$ �ex  H�t$ �Bx  ���Bx  ���Rx  H�L$(A���\x  H3t$(��H�������  H1���H1�D��H1�H!�H9�t%H��H��H��  H��  H��0[^_]A\�fD  H��] �f���H�3��-�+  ��f.�     UVSH��H��pH��H�
<X  ��w  H�5'Y  H�U�E1�H����w  I��H����   H�E�H�U�I��1�H�D$0H�E�H�D$(H��W  H�D$8    H�D$ ��w  H��X  1�H�MX  H��\  H�	 �   H��\  H��  H�E�H��  H�E��aw  H�
�&  �tw  ��v  �	 �H���Pw  ��  H�EH�`X  H�EH��W  �{�����������H��(��t��t�   H��(�f�     �  �   H��(ÐVSH��(H�C(  �8t�    ��t��tN�   H��([^�f�H�ل  H�5҄  H9�t�D  H�H��t��H��H9�u�   H��([^�f�     �  �   H��([^�ff.�     @ 1�Ð������������ATSH��8I��H�D$X�   H�T$XL�D$`L�L$hH�D$(�S  A�   �   H�
&  I���y  H�\$(�   �*  L��H��I���  �o  �fD  ATUWVSH��PHc=�[  I��H��L�Å���  H��[  1�H���H�I9�rL�@E�@L�I9���   ��H��(9�u�L���  H��H���w  H�F[  H�<�H��H�H�h �     �
  �MH�T$ A�0   H�H�[  H�L8�Yu  H���9  �D$D�P���t�������   ��Z  ��s,����   ��t�A�$����   H��P[^_]A\�@ H�I�L$H���I�$��H�T�I�T�I)�D�L)�����ră��1���L�L�9�r�H��P[^_]A\�f.�     H�L$ H�T$8A�@   H=AZ  H�OI��H�W�xt  ���@�����s  H�
�$  �������@ 1���������A�$�D�A�D��3���f.�     ���D�fA�D�����L��H�
$  ����H��Y  �UH�
$  L�D8����� UAWAVAUATWVSH��8H��$�   �5�Y  ��tH�e�[^_A\A]A^A_]�D  �^Y     �9  H�H��H��   H����R
  L�%�$  H��$  �.Y      H)�H�D$ H�#Y  L��H)�H��~��H���K  ����  �C����  �S���  H��L�}�L�-�$  I�    ����L9�r?�C����D�L��M��I�� ���E��MH�I)�L��M�L�E�A�   �����H��L9�sw��KD�CL�L�L�
A�� �
  ��   A��t�A���u  D�L��M��I��  ��fE��MH�H��I)�L��M�L�E�A�   �o���L9�r�f.�     �X  �������L�%Kr  1�f�     H��W  H�D� E��tH�PH�HI��A�ԃ�H��(;5�W  |��>���@ ����   �C��{������SH������fD  A��@��   H�A�   L��H)�L��L�H�E���������� �L��I��L	�E��II�A�   H)�L��L�H�E����������L9������I��L�-�"  H�}�I)�I��N�d�@ �K�A�   H��H��L��E��9���L9�u������D��H�
�!  H�E�    ����H�
�!  ����������������H��(�=�  ��/  =�  �vXs��?��	w:H��!  Hc�H���1ҹ   ��
  H����   �   �   ��
  �T
  E1�D��H��(�f�     =  ���   vFE1�=  �t�=  ���   1ҹ   �
  H����   H����   �   ��E1�D��H��(�E1�=  �A��D��H��(�1ҹ   �E
  H����   A�   H���c����   ��E1��T���D  1ҹ   �
  H��t~H��t)�   ��E1��)���f.�     A�   ����D  A�   ����D  �   �   �	  E1������f�     �   �   �	  E1������f�     �   �   �y	  E1�����f�     ATUWVSH�� �a  H�Ƌ U  ��u%H��t H�
    ��T     �i  H��t�   H�� [^_]A\�@ H��U  �0   E1�H��T  H��H�-�����H��    H��H)��H�H���7f.�     �	I��H���o�P�S�PH��H��H)��S��C�I�� t#L���;  H��u�M���i���D���
�     �    I��H�
AU  �n  �B���fD  ATH�� H��I�̉������ ��CCG ��   =�  ���   =�  �vTs��?��	w:H�  Hc�H���1ҹ   �  H����   �   �   ��  �x  �����H�� A\�@ =  ���   v;=  �t�=  �u41ҹ   �  H����   H��t�   �и�����@ =  �t�H�2S  H��tL��H�� A\H����B�(����y����1�H�� A\�1ҹ   �S  H��teH��t��   �и�����L����    1ҹ   �$  H��tOH��t��   �и���������   �   ��  �������f�     �   �   ��  �����������   �   ��  ��������ATWVSH��(H�
U  ��k  H��T  H��t2H�=Wl  H�5�k  ���I���օ�uM��t	H�CL����H�[H��u�H�
�T  H��([^_A\H�%�k  D  WVSH�� ��T  ��H�օ�u
H�� [^_�f��   �   �Q  H��H��t<�8H�
pT  H�p�>k  H�?T  H�
XT  H�1T  H�C�_k  1�H�� [^_Ã����     SH�� �
T  �˅�u1�H�� [��    H�
	T  ��j  H�
�S  H��t*1�� H��H��tH���9�H�Au�H��t&H�B�  H�
�S  ��j  1�H�� [��     H��S  ���    SH�� ����   w(��tL�nS  ��t2�`S     �   H�� [�D  ��u�ES  ��t��4�����f�H�
IS  �Sj  뿐�"S  ��t�����S  ��u�H��R  H��tf.�     H��H�[��  H��u�H�
�R  H��R      ��R      ��i  �`����     ��  �   H�� [�HcA<H�1��:PE  u1�f�z���f�f�9MZu	���    1��ff.�     f�HcA<H��AH�D�I��t-��H��L�L�(@ D�@L��I9�wHH9�wH��(L9�u�1���     ATVSH�� H���  H��wzH�
  E1�f�9MZuW�D�����tNHcA<H��AL�d�A��tH��H��I�t�(�f.�     I��(I9�t'A�   H��L���>  ��u�L��H�� [^A\�fD  E1�L��H�� [^A\ÐH��(L��  E1�fA�:MZI��u`L��������tTIcB<L��L)�I�A�BM�DA�B��t1��H��M�L�(�    A�PH��H9�r	A@H9�rI��(M9�u�E1�L��H��(�ff.�     @ H��(H�
�  1�f�9MZu�'�����t	HcA<�DH��(�D  H��(E1�I��H�
�  f�9MZuL�������tCHcA<H��AL�D�A��t(��H��I�D�( A�@' t	M��tI��I��(I9�u�E1�L��H��(�@ H��(H�
U  E1�f�9MZu������LE�L��H��(��     H��(L�
%  1�fA�9MZI��uWL���P�����tKIcA<L��L)�I�A�AA�QI�D��t)��H��L�L�(D�@L��L9�rPH9�rH��(L9�u�1�H��(�@ �@$����H��(�ff.�     H��(L��  E1�fA�;MZA��u_L��������tSIcK<Lً��   ��tB�QH�T�I��t1��H��L�T�(D  D�JL��L9�rJH9�rH��(L9�u�E1�L��H��(�L�� A��H���H��u�P��t�E���D�HM�L��H��(Ð���������������Ð������������QPH=   H�L$rH��   H�	 H-   H=   w�H)�H�	 XYÐ��������������   Ð����������%�f  ���%zf  ���%jf  ���%Zf  ���%Bf  ���%2f  ���%"f  ���%f  ���%f  ���%�e  ���%�e  ���%�e  ���%�e  ���%�e  ��H��t1�H�A    H�A    H�    ø�����ff.�     UWVSH��(H��H��H����   �   �o  H�; tiH�CH�SH9�t$H�P�   H�SH�8�?  1�H��([^_]�@ H�H)�H��H��H��H��H���  H��H��tFH�H�H�0H�S�@ �   �    ����H�H��tH��   H�CH�S�t��������뉹   �   ����w���ff.�     �ATVSH�� H�ι   �   L�&H�^H�F    H�F    �   H�    �k   M��t$H��I9�wH�H��t���H��I9�v�L���b���1�H�� [^A\Ð��������������SH�� ���,   ��H�IH��H�H�� [Ð�%Jd  ���%�c  ���%�c  ���%�c  ���%Zd  ���     �%�c  ���%�c  ���%�c  ���%rc  ���%bc  ���%Rc  ���%Bc  ���%2c  ���%"c  ���%c  ���%c  ���%�b  ���%�b  ���%�b  ���%�b  ���%�b  ���%�b  ���%�b  ���%�b  ���%rb  ���%bb  ���%Rb  ���K����������������������`Oh            ��������                                                                                                                               �Oh            ��������                       �Mh     Mh    �Lh            `Nh            2��-�+          �] �f���                                                                                                                                                                                                                                                                                                                                                                                        Hi Hello World from DLL!
       �/�B�D7q�����۵�[�V9��Y��?��^����[���1$�}Ut]�r��ހ�ܛt���i��G��Ɲ�̡$o,�-��tJܩ�\ڈ�vRQ>�m�1��'��Y����G���Qc�g))�
�'8!.�m,M
8STs
e�
jv.��,r��迢Kf�p�K£Ql���$�օ5�p�j��l7LwH'���4�9J��NOʜ[�o.htoc�xxȄǌ�����lP������xqƀ                                                               a + b is %d
    length not match! sc-otp-salt sc-otp-info       @�h    `�h                    @;h                             �h    �h    �h    0�h                                    Mingw-w64 runtime failure:
     Address %p has no image-section   VirtualQuery failed for %d bytes at address %p          VirtualProtect failed with code 0x%x    Unknown pseudo relocation protocol version %d.
         Unknown pseudo relocation bit size %d.
               R���R���R���R���R���|���R�������|�������.pdata  ��������������������(�����������(���S���        0Ph            pOh            pgh            pgh            �ah              h            $Ph            8�h            0�h             �h            �h            �h             �h            �h            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0            GCC: (tdm64-1) 9.2.0                                                                                                                                                                  �    �  �     D  �  P  �  (�  �  �  0�  �  �  4�  �  �  8�  �    <�    B  H�  B  `  P�  `  �  \�  �  �  h�  �  �  t�  �  �  ��  �  "  ��  "  I  ��  I  s  ��  s  �  ��  �  �  ��  �  g*  Ā  g*  �+  Ѐ  �+   /  ܀   /  s/  �  s/  �/  �  �/  �/   �  �/  �/  �  �/  <1  �  <1  u1   �  u1   2  ,�   2  H2  8�  H2  "3  D�  "3  S5  P�  `5  �5  h�  �5  36  t�  36  �6  ��  �6  �6  ��  �6  �6  ��  �6  Q8  ��  `8  �8  ��  �8  
9  ��  9  /9  ��  09  :  ā  :  ;  ԁ  ;  ?;  �  @;  �;  �  �;  �;  ��  �;  J<  ��  P<  ]>  �  `>  EA  �  PA  C  0�  C  
D  8�  D  �E  H�  �E  +F  P�  0F  �F  `�  �F  9G  l�  @G   H  t�   H  >H  |�  @H  SH  ��  `H  �H  ��  �H  OI  ��  PI  �I  ��  �I  J  ��  J  |J  ��  �J  �J  ��  �J  5K  ��  @K  �K  ��   L  L  Ă  PL  VL  Ȃ  �L  �L  ̂   M  �M  Ђ  �M  QN  ��  `N  N  �  `O  eO  �                                                                                                                                                          B0`pP��   20`��� �           2P  P2P  2P  2P  2P  2P  P  2P  2P  0P  P�& PRP  rP   P2P  2P  P�P  2P  rP  2P  �$ P	� 0`p��P  RP  P  2P  2P  P P B   B0`     
 
R0`pP�

�0`P   B   B0`      b0�  
 
�0`pP�
�b0`
p	����P B  
 
20`pP� 2�	 	B0`p�   20`p 20 20          20`� B   B   B   B   B   B            B0`pP   20`� 20                                                                                                                                                                                                                                                                               ���b    d�           (�  @�  X�  �  �  �  `  �  B  l�  w�  ��  ��  ��  ��         otp.dll HelloWorld dll_gen_opt dll_get_ltmk dll_set_ltmk dll_set_utc testdll                                                                                                                                                                                                                                                                                                                                                P�          �  ��  �          \�  h�  ��          l�   �                      �      (�      @�      T�      j�      ��      ��      ��      ��      ֳ      �      �      �      2�      L�      `�      ~�      ��      ��      ��      Ĵ      ִ              �      ��      �      �      �       �      (�      2�      :�      D�      N�      X�      b�      j�      t�      ~�      ��      ��              ��              �      (�      @�      T�      j�      ��      ��      ��      ��      ֳ      �      �      �      2�      L�      `�      ~�      ��      ��      ��      Ĵ      ִ              �      ��      �      �      �       �      (�      2�      :�      D�      N�      X�      b�      j�      t�      ~�      ��      ��              ��              DeleteCriticalSection ?EnterCriticalSection  (GetCurrentProcess )GetCurrentProcessId -GetCurrentThreadId  vGetLastError  GetSystemTimeAsFileTime GetTickCount  |InitializeCriticalSection �LeaveCriticalSection  kQueryPerformanceCounter �RtlAddFunctionTable �RtlCaptureContext �RtlLookupFunctionEntry  �RtlVirtualUnwind  rSetUnhandledExceptionFilter �Sleep �TerminateProcess  �TlsGetValue �UnhandledExceptionFilter  �VirtualProtect  �VirtualQuery  T __iob_func  { _amsg_exit  M_initterm �_lock 4_unlock 
abort  calloc  Gfree  Sfwrite  �memcpy  �memset  �printf  �puts  �realloc �signal  �strlen  �strncmp �vfprintf  eMessageBoxA  �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �  KERNEL32.dll    �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  msvcrt.dll  (�  USER32.dll                                                                                                                                                                                                                                                                                                                                                                                                                                   h                    @;h    ;h                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     @     x�   P     �@�H�P�`�   `  4   �������������������У�� �� �0�@�P�`�   �     �0�8�                                                                                                                                                                                                                                                                                                                                                                                                                              ,              h    �                      ,    �i       `8h    �                           ��                       ,    G
      09h    �                      ,    �:      ;h    �                           /_                          a                      ,    b      �;h    e                      ,    ��      PAh    p                      ,    d$      �Eh    `                          mK                          _L                      ,    �M       Hh    �                      ,    �       Lh                           ,    �      Lh    2                           L�                      ,    ��      PLh                           ,    F      �Lh    �                      ,    �       `Nh                                                                                                                                                                                                                                           �i       GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt/crtdll.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt  h    �          char size_t (,�   long long unsigned int long long int intptr_t C#  uintptr_t P,�   wchar_t gT  short unsigned int T  int long int pthreadlocinfo �(�  �  threadlocaleinfostruct `�J  �   �o   	lc_codepage ��  	lc_collate_cp ��  	lc_handle ��  	lc_id �	�  $	lc_category ��  H
lc_clike �o  
mb_cur_max �o  
lconv_intl_refcount ��  
lconv_num_refcount ��  
lconv_mon_refcount ��   
lconv ��  (
ctype1_refcount ��  0
ctype1 ��  8
pctype ��  @
pclmap ��  H
pcumap ��  P
lc_time_curr ��  X pthreadmbcinfo �%b  h  threadmbcinfostruct localeinfo_struct ��  	locinfo ��   	mbcinfo �J   _locale_tstruct �}  tagLC_ID �&  	wLanguage �T   	wCountry �T  	wCodePage �T   LC_ID ��  
 ��  	locale ��   	wlocale ��  �   �
�  	wrefcount �
�   �   D  o  unsigned int �  �  �  �    long unsigned int &  �  �    5  �  �    lconv X-
�  decimal_point .�   thousands_sep /�  grouping 0�  int_curr_symbol 1�  currency_symbol 2�   mon_decimal_point 3�  (mon_thousands_sep 4�  0mon_grouping 5�  8positive_sign 6�  @negative_sign 7�  Hint_frac_digits 8
�   Pfrac_digits 9
�   Qp_cs_precedes :
�   Rp_sep_by_space ;
�   Sn_cs_precedes <
�   Tn_sep_by_space =
�   Up_sign_posn >
�   Vn_sign_posn ?
�   W �  T  j  �  unsigned char �  __lc_time_data �  _PHNDLR B�  �    o   _XCPT_ACTION D
T  XcptNum E�   SigNum F	o  XcptAction G
�     _   _XcptActTab JT  _XcptActTabCount Ko  _XcptActTabSize Lo  _First_FPE_Indx Mo  _Num_FPE No  �  _EXCEPTION_RECORD ��
�  	ExceptionCode �

�   	ExceptionFlags �

�  	ExceptionRecord �
!�  	ExceptionAddress �

�
  	NumberParameters �

�  	ExceptionInformation �
{    �  �  _CONTEXT ��%�  	P1Home �
r
   	P2Home �
r
  	P3Home �
r
  	P4Home �
r
  	P5Home �
r
   	P6Home �
r
  (	ContextFlags ��  0	MxCsr ��  4	SegCs �
�  8	SegDs �
�  :	SegEs �
�  <	SegFs �
�  >	SegGs �
�  @	SegSs �
�  B	EFlags ��  D	Dr0 �
r
  H	Dr1 �
r
  P	Dr2 �
r
  X	Dr3 �
r
  `	Dr6 �
r
  h	Dr7 �
r
  p	Rax �
r
  x	Rcx �
r
  �	Rdx �
r
  �	Rbx �
r
  �	Rsp �
r
  �	Rbp �
r
  �	Rsi �
r
  �	Rdi �
r
  �	R8 �
r
  �	R9 �
r
  �	R10 �
r
  �	R11 �
r
  �	R12 �
r
  �	R13 �
r
  �	R14 �
r
  �	R15 �
r
  �	Rip �
r
  �1   VectorRegister �j   
VectorControl �
r
  �
DebugControl �
r
  �
LastBranchToRip �
r
  �
LastBranchFromRip �
r
  �
LastExceptionToRip �
r
  �
LastExceptionFromRip �
r
  � WINBOOL 
o  BYTE ��  WORD �T  DWORD ��  float LPVOID ��  �  __imp__pctype +'  �  __imp__wctype ;'  __imp__pwctype G'  �  e   Z  __newclmap Pe  __newcumap Qe  __ptlocinfo R�  __ptmbcinfo SJ  __globallocalestatus To  __locale_changed Uo  __initiallocinfo V(�  __initiallocalestructinfo W�  __imp___mb_cur_max ��  signed char short int ULONG_PTR 	1.�   DWORD64 	�.�   PVOID �  LONG v  HANDLE ��  LONGLONG �%  ULONGLONG �.�   _LIST_ENTRY d  	Flink e   	Blink f   �
  LIST_ENTRY g�
  _GUID 
q  Data1 
�   Data2 
T  Data3 
T  Data4 
q   �  �  �    GUID 
%  �  IID 
X�  �  CLSID 
`�  �  FMTID 
g�  �  EXCEPTION_ROUTINE �)�  o    �  �
  �  �
   PEXCEPTION_ROUTINE �   �  _M128A j(U  	Low k�
   	High l�
   M128A m%  U  v  �    U  �  �    �  �  �   _ _onexit_t 2�  �  o  double long double �   �  �  �     _sys_errlist �&�  _sys_nerr �$o  !__imp___argc �  !__imp___argv 7  =  �  !__imp___wargv "Z  `  �  !__imp__environ B7  !__imp__wenviron GZ  !__imp__pgmptr N=  !__imp__wpgmptr S`  !__imp__osplatform X  !__imp__osver ]  !__imp__winver b  !__imp__winmajor g  !__imp__winminor l  _amblksiz 5�  _XMM_SAVE_AREA32  ��  	ControlWord �
�   	StatusWord �
�  	TagWord �
�  	Reserved1 �
�  	ErrorOpcode �
�  	ErrorOffset ��  	ErrorSelector �
�  	Reserved2 �
�  	DataOffset ��  	DataSelector �
�  	Reserved3 �
�  	MxCsr ��  	MxCsr_Mask ��  "FloatRegisters �e   "XmmRegisters �v  �
Reserved4 �
�  � XMM_SAVE_AREA32 �R  #��   "Header �    "Legacy �e   "Xmm0 �U  �"Xmm1 �U  �"Xmm2 �U  �"Xmm3 �U  �"Xmm4 �U  �"Xmm5 �U  �Xmm6 �U   Xmm7 �U  Xmm8 �U   Xmm9 �U  0Xmm10 �U  @Xmm11 �U  PXmm12 �U  `Xmm13 �U  pXmm14 �U  �Xmm15 �U  � U  1  �    $ �j  %FltSave ��  %FloatSave ��  &�   U  {  �    `
  �  �    '�  (Next 0�  (prev 0�   _EXCEPTION_REGISTRATION_RECORD �  )�   )�   �  '
  (Handler   (handler    'J  (FiberData �
  (Version �   _NT_TIB 8#�  	ExceptionList .�   	StackBase 
�
  	StackLimit 
�
  	SubSystemTib 
�
  )   	ArbitraryUserPointer !
�
  (	Self "�  0 J  NT_TIB #J  PNT_TIB $  �  !GUID_MAX_POWER_SAVINGS �  !GUID_MIN_POWER_SAVINGS �  !GUID_TYPICAL_POWER_SAVINGS �  !NO_SUBGROUP_GUID �  !ALL_POWERSCHEMES_GUID  �  !GUID_POWERSCHEME_PERSONALITY !�  !GUID_ACTIVE_POWERSCHEME "�  !GUID_IDLE_RESILIENCY_SUBGROUP #�  !GUID_IDLE_RESILIENCY_PERIOD $�  !GUID_DISK_COALESCING_POWERDOWN_TIMEOUT %�  !GUID_EXECUTION_REQUIRED_REQUEST_TIMEOUT &�  !GUID_VIDEO_SUBGROUP '�  !GUID_VIDEO_POWERDOWN_TIMEOUT (�  !GUID_VIDEO_ANNOYANCE_TIMEOUT )�  !GUID_VIDEO_ADAPTIVE_PERCENT_INCREASE *�  !GUID_VIDEO_DIM_TIMEOUT +�  !GUID_VIDEO_ADAPTIVE_POWERDOWN ,�  !GUID_MONITOR_POWER_ON -�  !GUID_DEVICE_POWER_POLICY_VIDEO_BRIGHTNESS .�  !GUID_DEVICE_POWER_POLICY_VIDEO_DIM_BRIGHTNESS /�  !GUID_VIDEO_CURRENT_MONITOR_BRIGHTNESS 0�  !GUID_VIDEO_ADAPTIVE_DISPLAY_BRIGHTNESS 1�  !GUID_CONSOLE_DISPLAY_STATE 2�  !GUID_ALLOW_DISPLAY_REQUIRED 3�  !GUID_VIDEO_CONSOLE_LOCK_TIMEOUT 4�  !GUID_ADAPTIVE_POWER_BEHAVIOR_SUBGROUP 5�  !GUID_NON_ADAPTIVE_INPUT_TIMEOUT 6�  !GUID_DISK_SUBGROUP 7�  !GUID_DISK_POWERDOWN_TIMEOUT 8�  !GUID_DISK_IDLE_TIMEOUT 9�  !GUID_DISK_BURST_IGNORE_THRESHOLD :�  !GUID_DISK_ADAPTIVE_POWERDOWN ;�  !GUID_SLEEP_SUBGROUP <�  !GUID_SLEEP_IDLE_THRESHOLD =�  !GUID_STANDBY_TIMEOUT >�  !GUID_UNATTEND_SLEEP_TIMEOUT ?�  !GUID_HIBERNATE_TIMEOUT @�  !GUID_HIBERNATE_FASTS4_POLICY A�  !GUID_CRITICAL_POWER_TRANSITION B�  !GUID_SYSTEM_AWAYMODE C�  !GUID_ALLOW_AWAYMODE D�  !GUID_ALLOW_STANDBY_STATES E�  !GUID_ALLOW_RTC_WAKE F�  !GUID_ALLOW_SYSTEM_REQUIRED G�  !GUID_SYSTEM_BUTTON_SUBGROUP H�  !GUID_POWERBUTTON_ACTION I�  !GUID_SLEEPBUTTON_ACTION J�  !GUID_USERINTERFACEBUTTON_ACTION K�  !GUID_LIDCLOSE_ACTION L�  !GUID_LIDOPEN_POWERSTATE M�  !GUID_BATTERY_SUBGROUP N�  !GUID_BATTERY_DISCHARGE_ACTION_0 O�  !GUID_BATTERY_DISCHARGE_LEVEL_0 P�  !GUID_BATTERY_DISCHARGE_FLAGS_0 Q�  !GUID_BATTERY_DISCHARGE_ACTION_1 R�  !GUID_BATTERY_DISCHARGE_LEVEL_1 S�  !GUID_BATTERY_DISCHARGE_FLAGS_1 T�  !GUID_BATTERY_DISCHARGE_ACTION_2 U�  !GUID_BATTERY_DISCHARGE_LEVEL_2 V�  !GUID_BATTERY_DISCHARGE_FLAGS_2 W�  !GUID_BATTERY_DISCHARGE_ACTION_3 X�  !GUID_BATTERY_DISCHARGE_LEVEL_3 Y�  !GUID_BATTERY_DISCHARGE_FLAGS_3 Z�  !GUID_PROCESSOR_SETTINGS_SUBGROUP [�  !GUID_PROCESSOR_THROTTLE_POLICY \�  !GUID_PROCESSOR_THROTTLE_MAXIMUM ]�  !GUID_PROCESSOR_THROTTLE_MINIMUM ^�  !GUID_PROCESSOR_ALLOW_THROTTLING _�  !GUID_PROCESSOR_IDLESTATE_POLICY `�  !GUID_PROCESSOR_PERFSTATE_POLICY a�  !GUID_PROCESSOR_PERF_INCREASE_THRESHOLD b�  !GUID_PROCESSOR_PERF_DECREASE_THRESHOLD c�  !GUID_PROCESSOR_PERF_INCREASE_POLICY d�  !GUID_PROCESSOR_PERF_DECREASE_POLICY e�  !GUID_PROCESSOR_PERF_INCREASE_TIME f�  !GUID_PROCESSOR_PERF_DECREASE_TIME g�  !GUID_PROCESSOR_PERF_TIME_CHECK h�  !GUID_PROCESSOR_PERF_BOOST_POLICY i�  !GUID_PROCESSOR_PERF_BOOST_MODE j�  !GUID_PROCESSOR_IDLE_ALLOW_SCALING k�  !GUID_PROCESSOR_IDLE_DISABLE l�  !GUID_PROCESSOR_IDLE_STATE_MAXIMUM m�  !GUID_PROCESSOR_IDLE_TIME_CHECK n�  !GUID_PROCESSOR_IDLE_DEMOTE_THRESHOLD o�  !GUID_PROCESSOR_IDLE_PROMOTE_THRESHOLD p�  !GUID_PROCESSOR_CORE_PARKING_INCREASE_THRESHOLD q�  !GUID_PROCESSOR_CORE_PARKING_DECREASE_THRESHOLD r�  !GUID_PROCESSOR_CORE_PARKING_INCREASE_POLICY s�  !GUID_PROCESSOR_CORE_PARKING_DECREASE_POLICY t�  !GUID_PROCESSOR_CORE_PARKING_MAX_CORES u�  !GUID_PROCESSOR_CORE_PARKING_MIN_CORES v�  !GUID_PROCESSOR_CORE_PARKING_INCREASE_TIME w�  !GUID_PROCESSOR_CORE_PARKING_DECREASE_TIME x�  !GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_DECREASE_FACTOR y�  !GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_THRESHOLD z�  !GUID_PROCESSOR_CORE_PARKING_AFFINITY_WEIGHTING {�  !GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_DECREASE_FACTOR |�  !GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_THRESHOLD }�  !GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_WEIGHTING ~�  !GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_THRESHOLD �  !GUID_PROCESSOR_PARKING_CORE_OVERRIDE ��  !GUID_PROCESSOR_PARKING_PERF_STATE ��  !GUID_PROCESSOR_PARKING_CONCURRENCY_THRESHOLD ��  !GUID_PROCESSOR_PARKING_HEADROOM_THRESHOLD ��  !GUID_PROCESSOR_PERF_HISTORY ��  !GUID_PROCESSOR_PERF_LATENCY_HINT ��  !GUID_PROCESSOR_DISTRIBUTE_UTILITY ��  !GUID_SYSTEM_COOLING_POLICY ��  !GUID_LOCK_CONSOLE_ON_WAKE ��  !GUID_DEVICE_IDLE_POLICY ��  !GUID_ACDC_POWER_SOURCE ��  !GUID_LIDSWITCH_STATE_CHANGE ��  !GUID_BATTERY_PERCENTAGE_REMAINING ��  !GUID_GLOBAL_USER_PRESENCE ��  !GUID_SESSION_DISPLAY_STATUS ��  !GUID_SESSION_USER_PRESENCE ��  !GUID_IDLE_BACKGROUND_TASK ��  !GUID_BACKGROUND_TASK_NOTIFICATION ��  !GUID_APPLAUNCH_BUTTON ��  !GUID_PCIEXPRESS_SETTINGS_SUBGROUP ��  !GUID_PCIEXPRESS_ASPM_POLICY ��  !GUID_ENABLE_SWITCH_FORCED_SHUTDOWN ��  !PPM_PERFSTATE_CHANGE_GUID ��  !PPM_PERFSTATE_DOMAIN_CHANGE_GUID ��  !PPM_IDLESTATE_CHANGE_GUID ��  !PPM_PERFSTATES_DATA_GUID ��  !PPM_IDLESTATES_DATA_GUID ��  !PPM_IDLE_ACCOUNTING_GUID ��  !PPM_IDLE_ACCOUNTING_EX_GUID ��  !PPM_THERMALCONSTRAINT_GUID ��  !PPM_PERFMON_PERFSTATE_GUID ��  !PPM_THERMAL_POLICY_CHANGE_GUID ��  PIMAGE_TLS_CALLBACK �[+  9+  a+  v+  �
  �  �
   _RTL_CRITICAL_SECTION_DEBUG 05x,  	Type 6�   	CreatorBackTraceIndex 7�  	CriticalSection 8%-  	ProcessLocksList 9  	EntryCount :
�   	ContentionCount ;
�  $	Flags <
�  (	CreatorBackTraceIndexHigh =�  ,	SpareWORD >�  . _RTL_CRITICAL_SECTION (P-  	DebugInfo Q##-   	LockCount R�
  	RecursionCount S�
  	OwningThread T�
  	LockSemaphore U�
  	SpinCount V`
    x,  PRTL_CRITICAL_SECTION_DEBUG ?#H-  v+  RTL_CRITICAL_SECTION Wx,  CRITICAL_SECTION 
� N-  !VIRTUAL_STORAGE_TYPE_VENDOR_UNKNOWN �  !VIRTUAL_STORAGE_TYPE_VENDOR_MICROSOFT �  RPC_IF_HANDLE B�  *tagCOINITBASE �  �/.  +COINITBASE_MULTITHREADED   IWinTypesBase_v0_1_c_ifspec *�-  IWinTypesBase_v0_1_s_ifspec +�-  IID_IUnknown a�  IID_AsyncIUnknown ��  !IID_IClassFactory 8�  !IID_IMarshal �  !IID_INoMarshal ��  !IID_IAgileObject ��  !IID_IAgileReference >�  !IID_IMarshal2 ��  !IID_IMalloc �  !IID_IStdMarshalInfo ��  !IID_IExternalConnection ��  !IID_IMultiQI b�  !IID_AsyncIMultiQI ��  !IID_IInternalUnknown �  !IID_IEnumUnknown b�  !IID_IEnumString ��  !IID_ISequentialStream p�  !IID_IStream �  !IID_IRpcChannelBuffer %	�  !IID_IRpcChannelBuffer2 �	�  !IID_IAsyncRpcChannelBuffer !
�  !IID_IRpcChannelBuffer3 �
�  !IID_IRpcSyntaxNegotiate ��  !IID_IRpcProxyBuffer ��  !IID_IRpcStubBuffer I�  !IID_IPSFactoryBuffer ��  !IID_IChannelHook H
�  !IID_IClientSecurity (�  !IID_IServerSecurity ��  !IID_IRpcOptions 0�  !IID_IGlobalOptions ��  !IID_ISurrogate �  !IID_IGlobalInterfaceTable t�  !IID_ISynchronize ��  !IID_ISynchronizeHandle F�  !IID_ISynchronizeEvent ��  !IID_ISynchronizeContainer ��  !IID_ISynchronizeMutex J�  !IID_ICancelMethodCalls ��  !IID_IAsyncManager �  !IID_ICallFactory w�  !IID_IRpcHelper ��  !IID_IReleaseMarshalBuffers &�  !IID_IWaitMultiple x�  !IID_IAddrTrackingControl ��  !IID_IAddrExclusionControl ,�  !IID_IPipeByte ��  !IID_IPipeLong ��  !IID_IPipeDouble E�  !IID_IComThreadingInfo ��  !IID_IProcessInitControl 1�  !IID_IFastRundown �  !IID_IMarshalingStream ��  !IID_ICallbackWithNoReentrancyToApplicationSTA z�  GUID_NULL 
�  CATID_MARSHALER �  IID_IRpcChannel �  IID_IRpcStub �  IID_IStubManager �  IID_IRpcProxy �  IID_IProxyManager �  IID_IPSFactory �  IID_IInternalMoniker �  IID_IDfReserved1 �  IID_IDfReserved2 �  IID_IDfReserved3 �  CLSID_StdMarshal �  CLSID_AggStdMarshal �  CLSID_StdAsyncActManager �  IID_IStub �  IID_IProxy �  IID_IEnumGeneric �  IID_IEnumHolder �  IID_IEnumCallback  �  IID_IOleManager !�  IID_IOlePresObj "�  IID_IDebug #�  IID_IDebugStream $�  CLSID_PSGenObject %�  CLSID_PSClientSite &�  CLSID_PSClassObject '�  CLSID_PSInPlaceActive (�  CLSID_PSInPlaceFrame )�  CLSID_PSDragDrop *�  CLSID_PSBindCtx +�  CLSID_PSEnumerators ,�  CLSID_StaticMetafile -�  CLSID_StaticDib .�  CID_CDfsVolume /�  CLSID_DCOMAccessControl 0�  CLSID_GlobalOptions 1�  CLSID_StdGlobalInterfaceTable 2�  CLSID_ComBinding 3�  CLSID_StdEvent 4�  CLSID_ManualResetEvent 5�  CLSID_SynchronizeContainer 6�  CLSID_AddrControl 7�  CLSID_CCDFormKrnl 8�  CLSID_CCDPropertyPage 9�  CLSID_CCDFormDialog :�  CLSID_CCDCommandButton ;�  CLSID_CCDComboBox <�  CLSID_CCDTextBox =�  CLSID_CCDCheckBox >�  CLSID_CCDLabel ?�  CLSID_CCDOptionButton @�  CLSID_CCDListBox A�  CLSID_CCDScrollBar B�  CLSID_CCDGroupBox C�  CLSID_CCDGeneralPropertyPage D�  CLSID_CCDGenericPropertyPage E�  CLSID_CCDFontPropertyPage F�  CLSID_CCDColorPropertyPage G�  CLSID_CCDLabelPropertyPage H�  CLSID_CCDCheckBoxPropertyPage I�  CLSID_CCDTextBoxPropertyPage J�  CLSID_CCDOptionButtonPropertyPage K�  CLSID_CCDListBoxPropertyPage L�  CLSID_CCDCommandButtonPropertyPage M�  CLSID_CCDComboBoxPropertyPage N�  CLSID_CCDScrollBarPropertyPage O�  CLSID_CCDGroupBoxPropertyPage P�  CLSID_CCDXObjectPropertyPage Q�  CLSID_CStdPropertyFrame R�  CLSID_CFormPropertyPage S�  CLSID_CGridPropertyPage T�  CLSID_CWSJArticlePage U�  CLSID_CSystemPage V�  CLSID_IdentityUnmarshal W�  CLSID_InProcFreeMarshaler X�  CLSID_Picture_Metafile Y�  CLSID_Picture_EnhMetafile Z�  CLSID_Picture_Dib [�  GUID_TRISTATE \�  IWinTypes_v0_1_c_ifspec )�-  IWinTypes_v0_1_s_ifspec *�-  ,VARENUM �  E@  +VT_EMPTY  +VT_NULL +VT_I2 +VT_I4 +VT_R4 +VT_R8 +VT_CY +VT_DATE +VT_BSTR +VT_DISPATCH 	+VT_ERROR 
+VT_BOOL +VT_VARIANT +VT_UNKNOWN 
+VT_DECIMAL +VT_I1 +VT_UI1 +VT_UI2 +VT_UI4 +VT_I8 +VT_UI8 +VT_INT +VT_UINT +VT_VOID +VT_HRESULT +VT_PTR +VT_SAFEARRAY +VT_CARRAY +VT_USERDEFINED +VT_LPSTR +VT_LPWSTR +VT_RECORD $+VT_INT_PTR %+VT_UINT_PTR &+VT_FILETIME @+VT_BLOB A+VT_STREAM B+VT_STORAGE C+VT_STREAMED_OBJECT D+VT_STORED_OBJECT E+VT_BLOB_OBJECT F+VT_CF G+VT_CLSID H+VT_VERSIONED_STREAM I-VT_BSTR_BLOB �-VT_VECTOR  -VT_ARRAY   -VT_BYREF  @-VT_RESERVED  �-VT_ILLEGAL ��-VT_ILLEGALMASKED �-VT_TYPEMASK � !IID_IMallocSpy ��  !IID_IBindCtx :�  !IID_IEnumMoniker J �  !IID_IRunnableObject � �  !IID_IRunningObjectTable �!�  !IID_IPersist i"�  !IID_IPersistStream �"�  !IID_IMoniker j#�  !IID_IROTData X%�  !IID_IEnumSTATSTG �%�  !IID_IStorage X&�  !IID_IPersistFile A(�  !IID_IPersistStorage �(�  !IID_ILockBytes �)�  !IID_IEnumFORMATETC �*�  !IID_IEnumSTATDATA l+�  !IID_IRootStorage ,�  !IID_IAdviseSink �,�  !IID_AsyncIAdviseSink s-�  !IID_IAdviseSink2 �.�  !IID_AsyncIAdviseSink2 ./�  !IID_IDataObject �/�  !IID_IDataAdviseHolder 1�  !IID_IMessageFilter �1�  !FMTID_SummaryInformation ]2�  !FMTID_DocSummaryInformation _2�  !FMTID_UserDefinedProperties a2�  !FMTID_DiscardableInformation c2�  !FMTID_ImageSummaryInformation e2�  !FMTID_AudioSummaryInformation g2�  !FMTID_VideoSummaryInformation i2�  !FMTID_MediaFileSummaryInformation k2�  !IID_IClassActivator s2�  !IID_IFillLockBytes �2�  !IID_IProgressNotify �3�  !IID_ILayoutStorage �3�  !IID_IBlockingLock �4�  !IID_ITimeAndNoticeControl �4�  !IID_IOplockStorage N5�  !IID_IDirectWriterLock �5�  !IID_IUrlMon M6�  !IID_IForegroundTransfer �6�  !IID_IThumbnailExtractor 7�  !IID_IDummyHICONIncluder �7�  !IID_IProcessLock �7�  !IID_ISurrogateService H8�  !IID_IInitializeSpy �8�  !IID_IApartmentShutdown �9�  IID_IOleAdviseHolder ��  !IID_IOleCache ��  !IID_IOleCache2 �  !IID_IOleCacheControl ��  !IID_IParseDisplayName �  !IID_IOleContainer p�  !IID_IOleClientSite ��  !IID_IOleObject ��  !IOLETypes_v0_0_c_ifspec ��-  !IOLETypes_v0_0_s_ifspec ��-  !IID_IOleWindow �  !IID_IOleLink k�  !IID_IOleItemContainer 9�  !IID_IOleInPlaceUIWindow ��  !IID_IOleInPlaceActiveObject X�  !IID_IOleInPlaceFrame 	�  !IID_IOleInPlaceObject �	�  !IID_IOleInPlaceSite f
�  !IID_IContinue ,�  !IID_IViewObject {�  !IID_IViewObject2 ��  !IID_IDropSource 9
�  !IID_IDropTarget �
�  !IID_IDropSourceNotify 0�  !IID_IEnumOLEVERB ��  IID_IServiceProvider T�  !IOleAutomationTypes_v1_0_c_ifspec d�-  !IOleAutomationTypes_v1_0_s_ifspec e�-  !IID_ICreateTypeInfo ��  !IID_ICreateTypeInfo2 �  !IID_ICreateTypeLib ��  !IID_ICreateTypeLib2 ��  !IID_IDispatch l�  !IID_IEnumVARIANT  	�  !IID_ITypeComp �	�  !IID_ITypeInfo ]
�  !IID_ITypeInfo2 ��  !IID_ITypeLib ��  !IID_ITypeLib2 ��  !IID_ITypeChangeEvents �  !IID_IErrorInfo l�  !IID_ICreateErrorInfo ��  !IID_ISupportErrorInfo d�  !IID_ITypeFactory ��  !IID_ITypeMarshal �  !IID_IRecordInfo ��  !IID_IErrorLog ��  !IID_IPropertyBag ��  __MIDL_itf_msxml_0000_v0_0_c_ifspec ��-  __MIDL_itf_msxml_0000_v0_0_s_ifspec ��-  LIBID_MSXML ��  !IID_IXMLDOMImplementation �  !IID_IXMLDOMNode (�  !IID_IXMLDOMDocumentFragment ��  !IID_IXMLDOMDocument g�  !IID_IXMLDOMNodeList v�  !IID_IXMLDOMNamedNodeMap ��  !IID_IXMLDOMCharacterData �  !IID_IXMLDOMAttribute ��  !IID_IXMLDOMElement �  !IID_IXMLDOMText ��  !IID_IXMLDOMComment &�  !IID_IXMLDOMProcessingInstruction ��  !IID_IXMLDOMCDATASection �  !IID_IXMLDOMDocumentType ��  !IID_IXMLDOMNotation �  !IID_IXMLDOMEntity ��  !IID_IXMLDOMEntityReference ��  !IID_IXMLDOMParseError b	�  !IID_IXTLRuntime �	�  !DIID_XMLDOMDocumentEvents >
�  !CLSID_DOMDocument ]
�  !CLSID_DOMFreeThreadedDocument a
�  !IID_IXMLHttpRequest h
�  !CLSID_XMLHTTPRequest �
�  !IID_IXMLDSOControl �
�  !CLSID_XMLDSOControl �  !IID_IXMLElementCollection �  !IID_IXMLDocument K�  !IID_IXMLDocument2 ��  !IID_IXMLElement %�  !IID_IXMLElement2 ��  !IID_IXMLAttribute ��  !IID_IXMLError 
�  !CLSID_XMLDocument /
�  !CLSID_SBS_StdURLMoniker K�  !CLSID_SBS_HttpProtocol L�  !CLSID_SBS_FtpProtocol M�  !CLSID_SBS_GopherProtocol N�  !CLSID_SBS_HttpSProtocol O�  !CLSID_SBS_FileProtocol P�  !CLSID_SBS_MkProtocol Q�  !CLSID_SBS_UrlMkBindCtx R�  !CLSID_SBS_SoftDistExt S�  !CLSID_SBS_CdlProtocol T�  !CLSID_SBS_ClassInstallFilter U�  !CLSID_SBS_InternetSecurityManager V�  !CLSID_SBS_InternetZoneManager W�  !IID_IAsyncMoniker `�  !CLSID_StdURLMoniker a�  !CLSID_HttpProtocol b�  !CLSID_FtpProtocol c�  !CLSID_GopherProtocol d�  !CLSID_HttpSProtocol e�  !CLSID_FileProtocol f�  !CLSID_MkProtocol g�  !CLSID_StdURLProtocol h�  !CLSID_UrlMkBindCtx i�  !CLSID_CdlProtocol j�  !CLSID_ClassInstallFilter k�  !IID_IAsyncBindCtx l�  !IID_IPersistMoniker �  !IID_IMonikerProp ��  !IID_IBindProtocol �  !IID_IBinding h�  !IID_IBindStatusCallback ��  !IID_IBindStatusCallbackEx ��  !IID_IAuthenticate ��  !IID_IAuthenticateEx ��  !IID_IHttpNegotiate ^�  !IID_IHttpNegotiate2 ��  !IID_IHttpNegotiate3 :	�  !IID_IWinInetFileStream �	�  !IID_IWindowForBindingUI 
�  !IID_ICodeInstall z
�  !IID_IUri �  !IID_IUriContainer ��  !IID_IUriBuilder ��  !IID_IUriBuilderFactory ^�  !IID_IWinInetInfo ��  !IID_IHttpSecurity 6�  !IID_IWinInetHttpInfo ��  !IID_IWinInetHttpTimeouts �  !IID_IWinInetCacheHints o�  !IID_IWinInetCacheHints2 ��  !SID_BindHost 4�  !IID_IBindHost >�  !IID_IInternet `�  !IID_IInternetBindInfo ��  !IID_IInternetBindInfoEx '�  !IID_IInternetProtocolRoot ��  !IID_IInternetProtocol I�  !IID_IInternetProtocolEx ��  !IID_IInternetProtocolSink ��  !IID_IInternetProtocolSinkStackable 1�  !IID_IInternetSession ��  !IID_IInternetThreadSwitch \�  !IID_IInternetPriority ��  !IID_IInternetProtocolInfo F�  !CLSID_InternetSecurityManager w�  !CLSID_InternetZoneManager x�  !CLSID_PersistentZoneIdentifier {�  !IID_IInternetSecurityMgrSite ��  !IID_IInternetSecurityManager �  !IID_IInternetSecurityManagerEx ��  !IID_IInternetSecurityManagerEx2 ��  !IID_IZoneIdentifier ��  !IID_IInternetHostSecurityManager �  !GUID_CUSTOM_LOCALMACHINEZONEUNLOCKED X�  !IID_IInternetZoneManager ��  !IID_IInternetZoneManagerEx ��  !IID_IInternetZoneManagerEx2 � �  !CLSID_SoftDistExt �!�  !IID_ISoftDistExt �!�  !IID_ICatalogFileInfo x"�  !IID_IDataFilter �"�  !IID_IEncodingFilterFactory p#�  !GUID_CUSTOM_CONFIRMOBJECTSAFETY �#�  !IID_IWrappedProtocol �#�  !IID_IGetBindHandle R$�  !IID_IBindCallbackRedirect �$�  !IID_IPropertyStorage ��  !IID_IPropertySetStorage ��  !IID_IEnumSTATPROPSTG *�  !IID_IEnumSTATPROPSETSTG ��  IID_StdOle �  GUID_DEVINTERFACE_DISK �  GUID_DEVINTERFACE_CDROM 
�  GUID_DEVINTERFACE_PARTITION �  GUID_DEVINTERFACE_TAPE �  GUID_DEVINTERFACE_WRITEONCEDISK �  GUID_DEVINTERFACE_VOLUME �  GUID_DEVINTERFACE_MEDIUMCHANGER �  GUID_DEVINTERFACE_FLOPPY �  GUID_DEVINTERFACE_CDCHANGER �  GUID_DEVINTERFACE_STORAGEPORT �  GUID_DEVINTERFACE_COMPORT �  GUID_DEVINTERFACE_SERENUM_BUS_ENUMERATOR �  _SCARD_IO_REQUEST ��\  dwProtocol ��   cbPciLength ��   SCARD_IO_REQUEST �D\  �\  g_rgSCardT0Pci  %.�\  g_rgSCardT1Pci  %=�\  g_rgSCardRawPci  %L�\  IID_IPrintDialogCallback !�  IID_IPrintDialogServices !�  _commode "$o  .@"%�]  osfhnd "&!   osfile "'
�   pipech "(
�   	lockinitflag ")	o  lock "*l-  /textmode "+
�   8/unicode ",
�    8pipech2 "-
�]  9 �   �]  �    ioinfo ".@]  ^  ^   ^  �]  __imp___badioinfo "D�]  __imp___pioinfo "I�]  _dowildcard "fo  _newmode "go  __imp___winitenv "jZ  __imp___initenv "o7  0S   �  "��^  +__uninitialized  +__initializing +__initialized  1S   "��^  �^  __native_startup_state "�+�^  __native_startup_lock "�*_  0_  2__native_dllmain_reason "� �  __native_vcclrit_reason "� �  _PVFV #�  _PIFV #�  3t   #�_  _first #�_   _last #�_  _end #�_   q_  1t   #�_  __security_cookie $�2  �  
`  �
  �  �   _pRawDllMain $�!(`  �_  "`  _  8`   __xi_a *$-`  __xi_z +$-`  q_  a`   __xc_a ,$V`  __xc_z -$V`  __dyn_tls_init_callback 7"V+  4__proc_attached 9o  	�h    4atexit_table ;�_  	 �h    mingw_app_type =o  5pcinit E_  	�h    5__mingw_module_is_dll ��   	 Ph    6atexit �o  �h           ��a  7func �q_         8�h    6i  9R	 �h    9Q�R  :__DllMainCRTStartup ��   h    D      ��c  ;    ��
  d   R   ;k   �/�  4  "  ;�   �@�  �  �  <retcode ��  y  Y  =i__leave �dh    >1h    Bi  ?>h    Ni  ab  9R} 9Q09Xs  ?Lh    bi  �b  9R} 9Q| 9Xs  ?]h    �d  �b  9R} 9Q| 9Xs  >�h    Bi  ?�h    �d  �b  9R} 9Q| 9Xs  ?�h    bi  �b  9R} 9Q| 9Xs  >�h    ni  ?�h    Ni  .c  9R} 9Q19Xs  ?�h    Ni  Qc  9R} 9Q09Xs  ?�h    bi  tc  9R} 9Q09Xs  ?�h    �d  �c  9R} 9Q09Xs  >
h    Ni  @<h    Ni  9R} 9Q29Xs   ADllMainCRTStartup ��  Ph    O       ��d  ;    ��
  �  �  ;k   �-�  -  %  ;�   �>�  �  �  Boh    �a  Wd  9R�R9Q�Q9X�X >�h    �i  >�h    �i  8�h    �a  9R�X9Q�d�9X�h  C_CRT_INIT M�  \e  D    M"�
  Dk   M4�  D�   ME�  Ee  FI   X
�  Gfiberid Y
�  Gnested Zo   HFI   �
�  ?_h    �i  (e  9R
� ?�h    �i  ?e  9RO @|h    �i  9R	 �h       :pre_c_init Ho   h           ��e  8h    �i  9R	 �h      _TEB INtCurrentTeb �"�e  �e  J_InterlockedExchangePointer .�  f  KTarget .3f  KValue .@�   �  J_InterlockedCompareExchangePointer #�  �f  KDestination #:f  KExChange #M�  KComperand #]�   J__readgsqword �   �f  KOffset �  Lret �    M�d  h    �      �6i  N�d    �  N�d  �  �  N�d  ]  M  Oe      xg  Pe      Qf  _h    P   �Og  Nlf  �  �  NZf  �  �  REf   S�e  �h    �   �N�e  �  �  R�e    S�d  �h     �   MN�d      N�d  N  H  N�d  �  �  T�d  �   P�d  �  �  P�d  O	  K	  P�d  �	  �	  U�e  �h    �h           Y!Gh  V�f  �h    �h           �"IN�f  �	  �	  P�f  
  
    Qf  �h    �   \�h  Nlf  9
  7
  NZf  _
  ]
  REf   U�e  �h    �h           w�h  N�e  �
  �
  R�e   ?�h    �i  �h  9R
� W:h    �h  9R| 9Q29X}  ?�h    �i  i  XRXQ ?�h    �i   i  XRXQ @�h    �i  9RO    Y�   �   #!Y      )
ZDllMain DllMain ?Y%   %   AZ__main __main (
Y�   �   ${Y3   3   �ZSleep Sleep %Y�   �   "sY�   �   #"Y�   �   # Y    ' �O   T  GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt/gccmain.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt `8h    �       �  char long long unsigned int long long int ptrdiff_t ]#  wchar_t g5  short unsigned int 5  int long int pthreadlocinfo �({  �  threadlocaleinfostruct `�+  
  �P   	lc_codepage �x  	lc_collate_cp �x  	lc_handle ��  	lc_id �	�  $	lc_category ��  H
lc_clike �P  
mb_cur_max �P  
lconv_intl_refcount �r  
lconv_num_refcount �r  
lconv_mon_refcount �r   
lconv ��  (
ctype1_refcount �r  0
ctype1 ��  8
pctype ��  @
pclmap ��  H
pcumap ��  P
lc_time_curr �  X pthreadmbcinfo �%C  I  threadmbcinfostruct localeinfo_struct ��  	locinfo �c   	mbcinfo �+   _locale_tstruct �^  tagLC_ID �  	wLanguage �5   	wCountry �5  	wCodePage �5   LC_ID ��  
 �f  	locale �f   	wlocale �l  
  �
r  	wrefcount �
r   �   %  P  unsigned int �  �  �    long unsigned int   �  �      �  �    lconv �  5  K  �  unsigned char �  __lc_time_data   _PHNDLR B(  .  9  P   _XCPT_ACTION D
�  XcptNum E�   SigNum F	P  XcptAction G
   9  �   _XcptActTab J�  _XcptActTabCount KP  _XcptActTabSize LP  _First_FPE_Indx MP  _Num_FPE NP  DWORD ��  float x  __imp__pctype +3  �  __imp__wctype ;3  __imp__pwctype G3  �  q   f  __newclmap Pq  __newcumap Qq  __ptlocinfo Rc  __ptmbcinfo S+  __globallocalestatus TP  __locale_changed UP  __initiallocinfo V(�  __initiallocalestructinfo W�  __imp___mb_cur_max �r  signed char short int _GUID �  Data1 �   Data2 5  Data3 5  Data4 �   �  �  �    GUID l  �  IID X�  �  CLSID `�  �  FMTID g�  �  double long double 0  f  A  �     _sys_errlist �&1  _sys_nerr �$P  __imp___argc r  __imp___argv �  �  f  __imp___wargv "�  �  l  __imp__environ B�  __imp__wenviron G�  __imp__pgmptr N�  __imp__wpgmptr S�  __imp__osplatform X  __imp__osver ]  __imp__winver b  __imp__winmajor g  __imp__winminor l  _amblksiz 5x  GUID_MAX_POWER_SAVINGS 	�  GUID_MIN_POWER_SAVINGS 	�  GUID_TYPICAL_POWER_SAVINGS 	�  NO_SUBGROUP_GUID 	�  ALL_POWERSCHEMES_GUID 	 �  GUID_POWERSCHEME_PERSONALITY 	!�  GUID_ACTIVE_POWERSCHEME 	"�  GUID_IDLE_RESILIENCY_SUBGROUP 	#�  GUID_IDLE_RESILIENCY_PERIOD 	$�  GUID_DISK_COALESCING_POWERDOWN_TIMEOUT 	%�  GUID_EXECUTION_REQUIRED_REQUEST_TIMEOUT 	&�  GUID_VIDEO_SUBGROUP 	'�  GUID_VIDEO_POWERDOWN_TIMEOUT 	(�  GUID_VIDEO_ANNOYANCE_TIMEOUT 	)�  GUID_VIDEO_ADAPTIVE_PERCENT_INCREASE 	*�  GUID_VIDEO_DIM_TIMEOUT 	+�  GUID_VIDEO_ADAPTIVE_POWERDOWN 	,�  GUID_MONITOR_POWER_ON 	-�  GUID_DEVICE_POWER_POLICY_VIDEO_BRIGHTNESS 	.�  GUID_DEVICE_POWER_POLICY_VIDEO_DIM_BRIGHTNESS 	/�  GUID_VIDEO_CURRENT_MONITOR_BRIGHTNESS 	0�  GUID_VIDEO_ADAPTIVE_DISPLAY_BRIGHTNESS 	1�  GUID_CONSOLE_DISPLAY_STATE 	2�  GUID_ALLOW_DISPLAY_REQUIRED 	3�  GUID_VIDEO_CONSOLE_LOCK_TIMEOUT 	4�  GUID_ADAPTIVE_POWER_BEHAVIOR_SUBGROUP 	5�  GUID_NON_ADAPTIVE_INPUT_TIMEOUT 	6�  GUID_DISK_SUBGROUP 	7�  GUID_DISK_POWERDOWN_TIMEOUT 	8�  GUID_DISK_IDLE_TIMEOUT 	9�  GUID_DISK_BURST_IGNORE_THRESHOLD 	:�  GUID_DISK_ADAPTIVE_POWERDOWN 	;�  GUID_SLEEP_SUBGROUP 	<�  GUID_SLEEP_IDLE_THRESHOLD 	=�  GUID_STANDBY_TIMEOUT 	>�  GUID_UNATTEND_SLEEP_TIMEOUT 	?�  GUID_HIBERNATE_TIMEOUT 	@�  GUID_HIBERNATE_FASTS4_POLICY 	A�  GUID_CRITICAL_POWER_TRANSITION 	B�  GUID_SYSTEM_AWAYMODE 	C�  GUID_ALLOW_AWAYMODE 	D�  GUID_ALLOW_STANDBY_STATES 	E�  GUID_ALLOW_RTC_WAKE 	F�  GUID_ALLOW_SYSTEM_REQUIRED 	G�  GUID_SYSTEM_BUTTON_SUBGROUP 	H�  GUID_POWERBUTTON_ACTION 	I�  GUID_SLEEPBUTTON_ACTION 	J�  GUID_USERINTERFACEBUTTON_ACTION 	K�  GUID_LIDCLOSE_ACTION 	L�  GUID_LIDOPEN_POWERSTATE 	M�  GUID_BATTERY_SUBGROUP 	N�  GUID_BATTERY_DISCHARGE_ACTION_0 	O�  GUID_BATTERY_DISCHARGE_LEVEL_0 	P�  GUID_BATTERY_DISCHARGE_FLAGS_0 	Q�  GUID_BATTERY_DISCHARGE_ACTION_1 	R�  GUID_BATTERY_DISCHARGE_LEVEL_1 	S�  GUID_BATTERY_DISCHARGE_FLAGS_1 	T�  GUID_BATTERY_DISCHARGE_ACTION_2 	U�  GUID_BATTERY_DISCHARGE_LEVEL_2 	V�  GUID_BATTERY_DISCHARGE_FLAGS_2 	W�  GUID_BATTERY_DISCHARGE_ACTION_3 	X�  GUID_BATTERY_DISCHARGE_LEVEL_3 	Y�  GUID_BATTERY_DISCHARGE_FLAGS_3 	Z�  GUID_PROCESSOR_SETTINGS_SUBGROUP 	[�  GUID_PROCESSOR_THROTTLE_POLICY 	\�  GUID_PROCESSOR_THROTTLE_MAXIMUM 	]�  GUID_PROCESSOR_THROTTLE_MINIMUM 	^�  GUID_PROCESSOR_ALLOW_THROTTLING 	_�  GUID_PROCESSOR_IDLESTATE_POLICY 	`�  GUID_PROCESSOR_PERFSTATE_POLICY 	a�  GUID_PROCESSOR_PERF_INCREASE_THRESHOLD 	b�  GUID_PROCESSOR_PERF_DECREASE_THRESHOLD 	c�  GUID_PROCESSOR_PERF_INCREASE_POLICY 	d�  GUID_PROCESSOR_PERF_DECREASE_POLICY 	e�  GUID_PROCESSOR_PERF_INCREASE_TIME 	f�  GUID_PROCESSOR_PERF_DECREASE_TIME 	g�  GUID_PROCESSOR_PERF_TIME_CHECK 	h�  GUID_PROCESSOR_PERF_BOOST_POLICY 	i�  GUID_PROCESSOR_PERF_BOOST_MODE 	j�  GUID_PROCESSOR_IDLE_ALLOW_SCALING 	k�  GUID_PROCESSOR_IDLE_DISABLE 	l�  GUID_PROCESSOR_IDLE_STATE_MAXIMUM 	m�  GUID_PROCESSOR_IDLE_TIME_CHECK 	n�  GUID_PROCESSOR_IDLE_DEMOTE_THRESHOLD 	o�  GUID_PROCESSOR_IDLE_PROMOTE_THRESHOLD 	p�  GUID_PROCESSOR_CORE_PARKING_INCREASE_THRESHOLD 	q�  GUID_PROCESSOR_CORE_PARKING_DECREASE_THRESHOLD 	r�  GUID_PROCESSOR_CORE_PARKING_INCREASE_POLICY 	s�  GUID_PROCESSOR_CORE_PARKING_DECREASE_POLICY 	t�  GUID_PROCESSOR_CORE_PARKING_MAX_CORES 	u�  GUID_PROCESSOR_CORE_PARKING_MIN_CORES 	v�  GUID_PROCESSOR_CORE_PARKING_INCREASE_TIME 	w�  GUID_PROCESSOR_CORE_PARKING_DECREASE_TIME 	x�  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_DECREASE_FACTOR 	y�  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_THRESHOLD 	z�  GUID_PROCESSOR_CORE_PARKING_AFFINITY_WEIGHTING 	{�  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_DECREASE_FACTOR 	|�  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_THRESHOLD 	}�  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_WEIGHTING 	~�  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_THRESHOLD 	�  GUID_PROCESSOR_PARKING_CORE_OVERRIDE 	��  GUID_PROCESSOR_PARKING_PERF_STATE 	��  GUID_PROCESSOR_PARKING_CONCURRENCY_THRESHOLD 	��  GUID_PROCESSOR_PARKING_HEADROOM_THRESHOLD 	��  GUID_PROCESSOR_PERF_HISTORY 	��  GUID_PROCESSOR_PERF_LATENCY_HINT 	��  GUID_PROCESSOR_DISTRIBUTE_UTILITY 	��  GUID_SYSTEM_COOLING_POLICY 	��  GUID_LOCK_CONSOLE_ON_WAKE 	��  GUID_DEVICE_IDLE_POLICY 	��  GUID_ACDC_POWER_SOURCE 	��  GUID_LIDSWITCH_STATE_CHANGE 	��  GUID_BATTERY_PERCENTAGE_REMAINING 	��  GUID_GLOBAL_USER_PRESENCE 	��  GUID_SESSION_DISPLAY_STATUS 	��  GUID_SESSION_USER_PRESENCE 	��  GUID_IDLE_BACKGROUND_TASK 	��  GUID_BACKGROUND_TASK_NOTIFICATION 	��  GUID_APPLAUNCH_BUTTON 	��  GUID_PCIEXPRESS_SETTINGS_SUBGROUP 	��  GUID_PCIEXPRESS_ASPM_POLICY 	��  GUID_ENABLE_SWITCH_FORCED_SHUTDOWN 	��  PPM_PERFSTATE_CHANGE_GUID 	��  PPM_PERFSTATE_DOMAIN_CHANGE_GUID 	��  PPM_IDLESTATE_CHANGE_GUID 	��  PPM_PERFSTATES_DATA_GUID 	��  PPM_IDLESTATES_DATA_GUID 	��  PPM_IDLE_ACCOUNTING_GUID 	��  PPM_IDLE_ACCOUNTING_EX_GUID 	��  PPM_THERMALCONSTRAINT_GUID 	��  PPM_PERFMON_PERFSTATE_GUID 	��  PPM_THERMAL_POLICY_CHANGE_GUID 	��  VIRTUAL_STORAGE_TYPE_VENDOR_UNKNOWN 
�  VIRTUAL_STORAGE_TYPE_VENDOR_MICROSOFT 
�  RPC_IF_HANDLE B�  tagCOINITBASE x  �|  COINITBASE_MULTITHREADED   IWinTypesBase_v0_1_c_ifspec *.  IWinTypesBase_v0_1_s_ifspec +.  IID_IUnknown 
a�  IID_AsyncIUnknown 
��  IID_IClassFactory 
8�  IID_IMarshal �  IID_INoMarshal ��  IID_IAgileObject ��  IID_IAgileReference >�  IID_IMarshal2 ��  IID_IMalloc �  IID_IStdMarshalInfo ��  IID_IExternalConnection ��  IID_IMultiQI b�  IID_AsyncIMultiQI ��  IID_IInternalUnknown �  IID_IEnumUnknown b�  IID_IEnumString ��  IID_ISequentialStream p�  IID_IStream �  IID_IRpcChannelBuffer %	�  IID_IRpcChannelBuffer2 �	�  IID_IAsyncRpcChannelBuffer !
�  IID_IRpcChannelBuffer3 �
�  IID_IRpcSyntaxNegotiate ��  IID_IRpcProxyBuffer ��  IID_IRpcStubBuffer I�  IID_IPSFactoryBuffer ��  IID_IChannelHook H
�  IID_IClientSecurity (�  IID_IServerSecurity ��  IID_IRpcOptions 0�  IID_IGlobalOptions ��  IID_ISurrogate �  IID_IGlobalInterfaceTable t�  IID_ISynchronize ��  IID_ISynchronizeHandle F�  IID_ISynchronizeEvent ��  IID_ISynchronizeContainer ��  IID_ISynchronizeMutex J�  IID_ICancelMethodCalls ��  IID_IAsyncManager �  IID_ICallFactory w�  IID_IRpcHelper ��  IID_IReleaseMarshalBuffers &�  IID_IWaitMultiple x�  IID_IAddrTrackingControl ��  IID_IAddrExclusionControl ,�  IID_IPipeByte ��  IID_IPipeLong ��  IID_IPipeDouble E�  IID_IComThreadingInfo ��  IID_IProcessInitControl 1�  IID_IFastRundown �  IID_IMarshalingStream ��  IID_ICallbackWithNoReentrancyToApplicationSTA z�  GUID_NULL 
�  CATID_MARSHALER �  IID_IRpcChannel �  IID_IRpcStub �  IID_IStubManager �  IID_IRpcProxy �  IID_IProxyManager �  IID_IPSFactory �  IID_IInternalMoniker �  IID_IDfReserved1 �  IID_IDfReserved2 �  IID_IDfReserved3 �  CLSID_StdMarshal �  CLSID_AggStdMarshal �  CLSID_StdAsyncActManager �  IID_IStub �  IID_IProxy �  IID_IEnumGeneric �  IID_IEnumHolder �  IID_IEnumCallback  �  IID_IOleManager !�  IID_IOlePresObj "�  IID_IDebug #�  IID_IDebugStream $�  CLSID_PSGenObject %�  CLSID_PSClientSite &�  CLSID_PSClassObject '�  CLSID_PSInPlaceActive (�  CLSID_PSInPlaceFrame )�  CLSID_PSDragDrop *�  CLSID_PSBindCtx +�  CLSID_PSEnumerators ,�  CLSID_StaticMetafile -�  CLSID_StaticDib .�  CID_CDfsVolume /�  CLSID_DCOMAccessControl 0�  CLSID_GlobalOptions 1�  CLSID_StdGlobalInterfaceTable 2�  CLSID_ComBinding 3�  CLSID_StdEvent 4�  CLSID_ManualResetEvent 5�  CLSID_SynchronizeContainer 6�  CLSID_AddrControl 7�  CLSID_CCDFormKrnl 8�  CLSID_CCDPropertyPage 9�  CLSID_CCDFormDialog :�  CLSID_CCDCommandButton ;�  CLSID_CCDComboBox <�  CLSID_CCDTextBox =�  CLSID_CCDCheckBox >�  CLSID_CCDLabel ?�  CLSID_CCDOptionButton @�  CLSID_CCDListBox A�  CLSID_CCDScrollBar B�  CLSID_CCDGroupBox C�  CLSID_CCDGeneralPropertyPage D�  CLSID_CCDGenericPropertyPage E�  CLSID_CCDFontPropertyPage F�  CLSID_CCDColorPropertyPage G�  CLSID_CCDLabelPropertyPage H�  CLSID_CCDCheckBoxPropertyPage I�  CLSID_CCDTextBoxPropertyPage J�  CLSID_CCDOptionButtonPropertyPage K�  CLSID_CCDListBoxPropertyPage L�  CLSID_CCDCommandButtonPropertyPage M�  CLSID_CCDComboBoxPropertyPage N�  CLSID_CCDScrollBarPropertyPage O�  CLSID_CCDGroupBoxPropertyPage P�  CLSID_CCDXObjectPropertyPage Q�  CLSID_CStdPropertyFrame R�  CLSID_CFormPropertyPage S�  CLSID_CGridPropertyPage T�  CLSID_CWSJArticlePage U�  CLSID_CSystemPage V�  CLSID_IdentityUnmarshal W�  CLSID_InProcFreeMarshaler X�  CLSID_Picture_Metafile Y�  CLSID_Picture_EnhMetafile Z�  CLSID_Picture_Dib [�  GUID_TRISTATE \�  IWinTypes_v0_1_c_ifspec ).  IWinTypes_v0_1_s_ifspec *.  VARENUM x  �1  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � IID_IMallocSpy ��  IID_IBindCtx :�  IID_IEnumMoniker J �  IID_IRunnableObject � �  IID_IRunningObjectTable �!�  IID_IPersist i"�  IID_IPersistStream �"�  IID_IMoniker j#�  IID_IROTData X%�  IID_IEnumSTATSTG �%�  IID_IStorage X&�  IID_IPersistFile A(�  IID_IPersistStorage �(�  IID_ILockBytes �)�  IID_IEnumFORMATETC �*�  IID_IEnumSTATDATA l+�  IID_IRootStorage ,�  IID_IAdviseSink �,�  IID_AsyncIAdviseSink s-�  IID_IAdviseSink2 �.�  IID_AsyncIAdviseSink2 ./�  IID_IDataObject �/�  IID_IDataAdviseHolder 1�  IID_IMessageFilter �1�  FMTID_SummaryInformation ]2  FMTID_DocSummaryInformation _2  FMTID_UserDefinedProperties a2  FMTID_DiscardableInformation c2  FMTID_ImageSummaryInformation e2  FMTID_AudioSummaryInformation g2  FMTID_VideoSummaryInformation i2  FMTID_MediaFileSummaryInformation k2  IID_IClassActivator s2�  IID_IFillLockBytes �2�  IID_IProgressNotify �3�  IID_ILayoutStorage �3�  IID_IBlockingLock �4�  IID_ITimeAndNoticeControl �4�  IID_IOplockStorage N5�  IID_IDirectWriterLock �5�  IID_IUrlMon M6�  IID_IForegroundTransfer �6�  IID_IThumbnailExtractor 7�  IID_IDummyHICONIncluder �7�  IID_IProcessLock �7�  IID_ISurrogateService H8�  IID_IInitializeSpy �8�  IID_IApartmentShutdown �9�  IID_IOleAdviseHolder ��  IID_IOleCache ��  IID_IOleCache2 �  IID_IOleCacheControl ��  IID_IParseDisplayName �  IID_IOleContainer p�  IID_IOleClientSite ��  IID_IOleObject ��  IOLETypes_v0_0_c_ifspec �.  IOLETypes_v0_0_s_ifspec �.  IID_IOleWindow �  IID_IOleLink k�  IID_IOleItemContainer 9�  IID_IOleInPlaceUIWindow ��  IID_IOleInPlaceActiveObject X�  IID_IOleInPlaceFrame 	�  IID_IOleInPlaceObject �	�  IID_IOleInPlaceSite f
�  IID_IContinue ,�  IID_IViewObject {�  IID_IViewObject2 ��  IID_IDropSource 9
�  IID_IDropTarget �
�  IID_IDropSourceNotify 0�  IID_IEnumOLEVERB ��  IID_IServiceProvider T�  IOleAutomationTypes_v1_0_c_ifspec d.  IOleAutomationTypes_v1_0_s_ifspec e.  IID_ICreateTypeInfo ��  IID_ICreateTypeInfo2 �  IID_ICreateTypeLib ��  IID_ICreateTypeLib2 ��  IID_IDispatch l�  IID_IEnumVARIANT  	�  IID_ITypeComp �	�  IID_ITypeInfo ]
�  IID_ITypeInfo2 ��  IID_ITypeLib ��  IID_ITypeLib2 ��  IID_ITypeChangeEvents �  IID_IErrorInfo l�  IID_ICreateErrorInfo ��  IID_ISupportErrorInfo d�  IID_ITypeFactory ��  IID_ITypeMarshal �  IID_IRecordInfo ��  IID_IErrorLog ��  IID_IPropertyBag ��  __MIDL_itf_msxml_0000_v0_0_c_ifspec �.  __MIDL_itf_msxml_0000_v0_0_s_ifspec �.  LIBID_MSXML ��  IID_IXMLDOMImplementation �  IID_IXMLDOMNode (�  IID_IXMLDOMDocumentFragment ��  IID_IXMLDOMDocument g�  IID_IXMLDOMNodeList v�  IID_IXMLDOMNamedNodeMap ��  IID_IXMLDOMCharacterData �  IID_IXMLDOMAttribute ��  IID_IXMLDOMElement �  IID_IXMLDOMText ��  IID_IXMLDOMComment &�  IID_IXMLDOMProcessingInstruction ��  IID_IXMLDOMCDATASection �  IID_IXMLDOMDocumentType ��  IID_IXMLDOMNotation �  IID_IXMLDOMEntity ��  IID_IXMLDOMEntityReference ��  IID_IXMLDOMParseError b	�  IID_IXTLRuntime �	�  DIID_XMLDOMDocumentEvents >
�  CLSID_DOMDocument ]
�  CLSID_DOMFreeThreadedDocument a
�  IID_IXMLHttpRequest h
�  CLSID_XMLHTTPRequest �
�  IID_IXMLDSOControl �
�  CLSID_XMLDSOControl �  IID_IXMLElementCollection �  IID_IXMLDocument K�  IID_IXMLDocument2 ��  IID_IXMLElement %�  IID_IXMLElement2 ��  IID_IXMLAttribute ��  IID_IXMLError 
�  CLSID_XMLDocument /
�  CLSID_SBS_StdURLMoniker K�  CLSID_SBS_HttpProtocol L�  CLSID_SBS_FtpProtocol M�  CLSID_SBS_GopherProtocol N�  CLSID_SBS_HttpSProtocol O�  CLSID_SBS_FileProtocol P�  CLSID_SBS_MkProtocol Q�  CLSID_SBS_UrlMkBindCtx R�  CLSID_SBS_SoftDistExt S�  CLSID_SBS_CdlProtocol T�  CLSID_SBS_ClassInstallFilter U�  CLSID_SBS_InternetSecurityManager V�  CLSID_SBS_InternetZoneManager W�  IID_IAsyncMoniker `�  CLSID_StdURLMoniker a�  CLSID_HttpProtocol b�  CLSID_FtpProtocol c�  CLSID_GopherProtocol d�  CLSID_HttpSProtocol e�  CLSID_FileProtocol f�  CLSID_MkProtocol g�  CLSID_StdURLProtocol h�  CLSID_UrlMkBindCtx i�  CLSID_CdlProtocol j�  CLSID_ClassInstallFilter k�  IID_IAsyncBindCtx l�  IID_IPersistMoniker �  IID_IMonikerProp ��  IID_IBindProtocol �  IID_IBinding h�  IID_IBindStatusCallback ��  IID_IBindStatusCallbackEx ��  IID_IAuthenticate ��  IID_IAuthenticateEx ��  IID_IHttpNegotiate ^�  IID_IHttpNegotiate2 ��  IID_IHttpNegotiate3 :	�  IID_IWinInetFileStream �	�  IID_IWindowForBindingUI 
�  IID_ICodeInstall z
�  IID_IUri �  IID_IUriContainer ��  IID_IUriBuilder ��  IID_IUriBuilderFactory ^�  IID_IWinInetInfo ��  IID_IHttpSecurity 6�  IID_IWinInetHttpInfo ��  IID_IWinInetHttpTimeouts �  IID_IWinInetCacheHints o�  IID_IWinInetCacheHints2 ��  SID_BindHost 4�  IID_IBindHost >�  IID_IInternet `�  IID_IInternetBindInfo ��  IID_IInternetBindInfoEx '�  IID_IInternetProtocolRoot ��  IID_IInternetProtocol I�  IID_IInternetProtocolEx ��  IID_IInternetProtocolSink ��  IID_IInternetProtocolSinkStackable 1�  IID_IInternetSession ��  IID_IInternetThreadSwitch \�  IID_IInternetPriority ��  IID_IInternetProtocolInfo F�  CLSID_InternetSecurityManager w�  CLSID_InternetZoneManager x�  CLSID_PersistentZoneIdentifier {�  IID_IInternetSecurityMgrSite ��  IID_IInternetSecurityManager �  IID_IInternetSecurityManagerEx ��  IID_IInternetSecurityManagerEx2 ��  IID_IZoneIdentifier ��  IID_IInternetHostSecurityManager �  GUID_CUSTOM_LOCALMACHINEZONEUNLOCKED X�  IID_IInternetZoneManager ��  IID_IInternetZoneManagerEx ��  IID_IInternetZoneManagerEx2 � �  CLSID_SoftDistExt �!�  IID_ISoftDistExt �!�  IID_ICatalogFileInfo x"�  IID_IDataFilter �"�  IID_IEncodingFilterFactory p#�  GUID_CUSTOM_CONFIRMOBJECTSAFETY �#�  IID_IWrappedProtocol �#�  IID_IGetBindHandle R$�  IID_IBindCallbackRedirect �$�  IID_IPropertyStorage ��  IID_IPropertySetStorage ��  IID_IEnumSTATPROPSTG *�  IID_IEnumSTATPROPSETSTG ��  IID_StdOle �  GUID_DEVINTERFACE_DISK �  GUID_DEVINTERFACE_CDROM 
�  GUID_DEVINTERFACE_PARTITION �  GUID_DEVINTERFACE_TAPE �  GUID_DEVINTERFACE_WRITEONCEDISK �  GUID_DEVINTERFACE_VOLUME �  GUID_DEVINTERFACE_MEDIUMCHANGER �  GUID_DEVINTERFACE_FLOPPY �  GUID_DEVINTERFACE_CDCHANGER �  GUID_DEVINTERFACE_STORAGEPORT �  GUID_DEVINTERFACE_COMPORT �  GUID_DEVINTERFACE_SERENUM_BUS_ENUMERATOR �  _SCARD_IO_REQUEST ��M  dwProtocol �    cbPciLength �    SCARD_IO_REQUEST ��M  �M  g_rgSCardT0Pci %.�M  g_rgSCardT1Pci %=�M  g_rgSCardRawPci %L�M  IID_IPrintDialogCallback �  IID_IPrintDialogServices �  func_ptr *  |N  �N   __CTOR_LIST__ �N  __DTOR_LIST__ �N  initialized KP  	@�h    __main N9h           �O  /9h    O    __do_global_ctors 7�8h    j       ��O  !nptrs 9�  �
  �
  !i :�  �
  �
  "�8h    �O  #R	`8h       __do_global_dtors `8h    :       ��O  p �O  	Ph     |N  $atexit atexit � �S   _  GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt/natstart.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt �  char long long unsigned int long long int intptr_t C#�   wchar_t g%  short unsigned int %  int long int pthreadlocinfo �(k  q  threadlocaleinfostruct `�  .  �@   	lc_codepage �h  	lc_collate_cp �h  	lc_handle �}  	lc_id �	�  $	lc_category ��  H
lc_clike �@  
mb_cur_max �@  
lconv_intl_refcount �b  
lconv_num_refcount �b  
lconv_mon_refcount �b   
lconv ��  (
ctype1_refcount �b  0
ctype1 ��  8
pctype ��  @
pclmap ��  H
pcumap ��  P
lc_time_curr �  X pthreadmbcinfo �%3  9  threadmbcinfostruct localeinfo_struct ��  	locinfo �S   	mbcinfo �   _locale_tstruct �N  tagLC_ID ��  	wLanguage �%   	wCountry �%  	wCodePage �%   LC_ID ��  
 �V  	locale �V   	wlocale �\  .  �
b  	wrefcount �
b   �     @  unsigned int h  �  �  �    long unsigned int �  �  �      �  �    lconv �  %  ;  �  unsigned char �  __lc_time_data �  _PHNDLR B  #  .  @   _XCPT_ACTION D
z  XcptNum E�   SigNum F	@  XcptAction G

   .  �   _XcptActTab Jz  _XcptActTabCount K@  _XcptActTabSize L@  _First_FPE_Indx M@  _Num_FPE N@  WORD �%  DWORD ��  float h  __imp__pctype +5  �  __imp__wctype ;5  __imp__pwctype G5  �  s   h  __newclmap Ps  __newcumap Qs  __ptlocinfo RS  __ptmbcinfo S  __globallocalestatus T@  __locale_changed U@  __initiallocinfo V(q  __initiallocalestructinfo W�  __imp___mb_cur_max �b  signed char short int ULONG_PTR 1.�   LONG G  HANDLE ��  _LIST_ENTRY d�  	Flink e�   	Blink f�   �  LIST_ENTRY g�  _GUID ;  Data1 �   Data2 %  Data3 %  Data4 ;   �  K  �    GUID �  K  IID XK  ]  CLSID `K  n  FMTID gK  �  double long double V  �  �     _sys_errlist �&�  _sys_nerr �$@  __imp___argc b  __imp___argv 	  	  V  __imp___wargv "3	  9	  \  __imp__environ B	  __imp__wenviron G3	  __imp__pgmptr N	  __imp__wpgmptr S9	  __imp__osplatform X  __imp__osver ]  __imp__winver b  __imp__winmajor g  __imp__winminor l  _amblksiz 	5h  GUID_MAX_POWER_SAVINGS X  GUID_MIN_POWER_SAVINGS X  GUID_TYPICAL_POWER_SAVINGS X  NO_SUBGROUP_GUID X  ALL_POWERSCHEMES_GUID  X  GUID_POWERSCHEME_PERSONALITY !X  GUID_ACTIVE_POWERSCHEME "X  GUID_IDLE_RESILIENCY_SUBGROUP #X  GUID_IDLE_RESILIENCY_PERIOD $X  GUID_DISK_COALESCING_POWERDOWN_TIMEOUT %X  GUID_EXECUTION_REQUIRED_REQUEST_TIMEOUT &X  GUID_VIDEO_SUBGROUP 'X  GUID_VIDEO_POWERDOWN_TIMEOUT (X  GUID_VIDEO_ANNOYANCE_TIMEOUT )X  GUID_VIDEO_ADAPTIVE_PERCENT_INCREASE *X  GUID_VIDEO_DIM_TIMEOUT +X  GUID_VIDEO_ADAPTIVE_POWERDOWN ,X  GUID_MONITOR_POWER_ON -X  GUID_DEVICE_POWER_POLICY_VIDEO_BRIGHTNESS .X  GUID_DEVICE_POWER_POLICY_VIDEO_DIM_BRIGHTNESS /X  GUID_VIDEO_CURRENT_MONITOR_BRIGHTNESS 0X  GUID_VIDEO_ADAPTIVE_DISPLAY_BRIGHTNESS 1X  GUID_CONSOLE_DISPLAY_STATE 2X  GUID_ALLOW_DISPLAY_REQUIRED 3X  GUID_VIDEO_CONSOLE_LOCK_TIMEOUT 4X  GUID_ADAPTIVE_POWER_BEHAVIOR_SUBGROUP 5X  GUID_NON_ADAPTIVE_INPUT_TIMEOUT 6X  GUID_DISK_SUBGROUP 7X  GUID_DISK_POWERDOWN_TIMEOUT 8X  GUID_DISK_IDLE_TIMEOUT 9X  GUID_DISK_BURST_IGNORE_THRESHOLD :X  GUID_DISK_ADAPTIVE_POWERDOWN ;X  GUID_SLEEP_SUBGROUP <X  GUID_SLEEP_IDLE_THRESHOLD =X  GUID_STANDBY_TIMEOUT >X  GUID_UNATTEND_SLEEP_TIMEOUT ?X  GUID_HIBERNATE_TIMEOUT @X  GUID_HIBERNATE_FASTS4_POLICY AX  GUID_CRITICAL_POWER_TRANSITION BX  GUID_SYSTEM_AWAYMODE CX  GUID_ALLOW_AWAYMODE DX  GUID_ALLOW_STANDBY_STATES EX  GUID_ALLOW_RTC_WAKE FX  GUID_ALLOW_SYSTEM_REQUIRED GX  GUID_SYSTEM_BUTTON_SUBGROUP HX  GUID_POWERBUTTON_ACTION IX  GUID_SLEEPBUTTON_ACTION JX  GUID_USERINTERFACEBUTTON_ACTION KX  GUID_LIDCLOSE_ACTION LX  GUID_LIDOPEN_POWERSTATE MX  GUID_BATTERY_SUBGROUP NX  GUID_BATTERY_DISCHARGE_ACTION_0 OX  GUID_BATTERY_DISCHARGE_LEVEL_0 PX  GUID_BATTERY_DISCHARGE_FLAGS_0 QX  GUID_BATTERY_DISCHARGE_ACTION_1 RX  GUID_BATTERY_DISCHARGE_LEVEL_1 SX  GUID_BATTERY_DISCHARGE_FLAGS_1 TX  GUID_BATTERY_DISCHARGE_ACTION_2 UX  GUID_BATTERY_DISCHARGE_LEVEL_2 VX  GUID_BATTERY_DISCHARGE_FLAGS_2 WX  GUID_BATTERY_DISCHARGE_ACTION_3 XX  GUID_BATTERY_DISCHARGE_LEVEL_3 YX  GUID_BATTERY_DISCHARGE_FLAGS_3 ZX  GUID_PROCESSOR_SETTINGS_SUBGROUP [X  GUID_PROCESSOR_THROTTLE_POLICY \X  GUID_PROCESSOR_THROTTLE_MAXIMUM ]X  GUID_PROCESSOR_THROTTLE_MINIMUM ^X  GUID_PROCESSOR_ALLOW_THROTTLING _X  GUID_PROCESSOR_IDLESTATE_POLICY `X  GUID_PROCESSOR_PERFSTATE_POLICY aX  GUID_PROCESSOR_PERF_INCREASE_THRESHOLD bX  GUID_PROCESSOR_PERF_DECREASE_THRESHOLD cX  GUID_PROCESSOR_PERF_INCREASE_POLICY dX  GUID_PROCESSOR_PERF_DECREASE_POLICY eX  GUID_PROCESSOR_PERF_INCREASE_TIME fX  GUID_PROCESSOR_PERF_DECREASE_TIME gX  GUID_PROCESSOR_PERF_TIME_CHECK hX  GUID_PROCESSOR_PERF_BOOST_POLICY iX  GUID_PROCESSOR_PERF_BOOST_MODE jX  GUID_PROCESSOR_IDLE_ALLOW_SCALING kX  GUID_PROCESSOR_IDLE_DISABLE lX  GUID_PROCESSOR_IDLE_STATE_MAXIMUM mX  GUID_PROCESSOR_IDLE_TIME_CHECK nX  GUID_PROCESSOR_IDLE_DEMOTE_THRESHOLD oX  GUID_PROCESSOR_IDLE_PROMOTE_THRESHOLD pX  GUID_PROCESSOR_CORE_PARKING_INCREASE_THRESHOLD qX  GUID_PROCESSOR_CORE_PARKING_DECREASE_THRESHOLD rX  GUID_PROCESSOR_CORE_PARKING_INCREASE_POLICY sX  GUID_PROCESSOR_CORE_PARKING_DECREASE_POLICY tX  GUID_PROCESSOR_CORE_PARKING_MAX_CORES uX  GUID_PROCESSOR_CORE_PARKING_MIN_CORES vX  GUID_PROCESSOR_CORE_PARKING_INCREASE_TIME wX  GUID_PROCESSOR_CORE_PARKING_DECREASE_TIME xX  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_DECREASE_FACTOR yX  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_THRESHOLD zX  GUID_PROCESSOR_CORE_PARKING_AFFINITY_WEIGHTING {X  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_DECREASE_FACTOR |X  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_THRESHOLD }X  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_WEIGHTING ~X  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_THRESHOLD X  GUID_PROCESSOR_PARKING_CORE_OVERRIDE �X  GUID_PROCESSOR_PARKING_PERF_STATE �X  GUID_PROCESSOR_PARKING_CONCURRENCY_THRESHOLD �X  GUID_PROCESSOR_PARKING_HEADROOM_THRESHOLD �X  GUID_PROCESSOR_PERF_HISTORY �X  GUID_PROCESSOR_PERF_LATENCY_HINT �X  GUID_PROCESSOR_DISTRIBUTE_UTILITY �X  GUID_SYSTEM_COOLING_POLICY �X  GUID_LOCK_CONSOLE_ON_WAKE �X  GUID_DEVICE_IDLE_POLICY �X  GUID_ACDC_POWER_SOURCE �X  GUID_LIDSWITCH_STATE_CHANGE �X  GUID_BATTERY_PERCENTAGE_REMAINING �X  GUID_GLOBAL_USER_PRESENCE �X  GUID_SESSION_DISPLAY_STATUS �X  GUID_SESSION_USER_PRESENCE �X  GUID_IDLE_BACKGROUND_TASK �X  GUID_BACKGROUND_TASK_NOTIFICATION �X  GUID_APPLAUNCH_BUTTON �X  GUID_PCIEXPRESS_SETTINGS_SUBGROUP �X  GUID_PCIEXPRESS_ASPM_POLICY �X  GUID_ENABLE_SWITCH_FORCED_SHUTDOWN �X  PPM_PERFSTATE_CHANGE_GUID �X  PPM_PERFSTATE_DOMAIN_CHANGE_GUID �X  PPM_IDLESTATE_CHANGE_GUID �X  PPM_PERFSTATES_DATA_GUID �X  PPM_IDLESTATES_DATA_GUID �X  PPM_IDLE_ACCOUNTING_GUID �X  PPM_IDLE_ACCOUNTING_EX_GUID �X  PPM_THERMALCONSTRAINT_GUID �X  PPM_PERFMON_PERFSTATE_GUID �X  PPM_THERMAL_POLICY_CHANGE_GUID �X  _RTL_CRITICAL_SECTION_DEBUG 05P   	Type 6�   	CreatorBackTraceIndex 7�  	CriticalSection 8%�   	ProcessLocksList 9�  	EntryCount :
   	ContentionCount ;
  $	Flags <
  (	CreatorBackTraceIndexHigh =�  ,	SpareWORD >�  . _RTL_CRITICAL_SECTION (P�   	DebugInfo Q#�    	LockCount R�  	RecursionCount S�  	OwningThread T�  	LockSemaphore U�  	SpinCount Vn    P   PRTL_CRITICAL_SECTION_DEBUG ?# !  N  RTL_CRITICAL_SECTION WP   CRITICAL_SECTION 
� &!  VIRTUAL_STORAGE_TYPE_VENDOR_UNKNOWN X  VIRTUAL_STORAGE_TYPE_VENDOR_MICROSOFT X  RPC_IF_HANDLE B�  tagCOINITBASE h  �"  COINITBASE_MULTITHREADED   IWinTypesBase_v0_1_c_ifspec 
*�!  IWinTypesBase_v0_1_s_ifspec 
+�!  IID_IUnknown aX  IID_AsyncIUnknown �X  IID_IClassFactory 8X  IID_IMarshal X  IID_INoMarshal �X  IID_IAgileObject �X  IID_IAgileReference >X  IID_IMarshal2 �X  IID_IMalloc X  IID_IStdMarshalInfo �X  IID_IExternalConnection �X  IID_IMultiQI bX  IID_AsyncIMultiQI �X  IID_IInternalUnknown X  IID_IEnumUnknown bX  IID_IEnumString �X  IID_ISequentialStream pX  IID_IStream X  IID_IRpcChannelBuffer %	X  IID_IRpcChannelBuffer2 �	X  IID_IAsyncRpcChannelBuffer !
X  IID_IRpcChannelBuffer3 �
X  IID_IRpcSyntaxNegotiate �X  IID_IRpcProxyBuffer �X  IID_IRpcStubBuffer IX  IID_IPSFactoryBuffer �X  IID_IChannelHook H
X  IID_IClientSecurity (X  IID_IServerSecurity �X  IID_IRpcOptions 0X  IID_IGlobalOptions �X  IID_ISurrogate X  IID_IGlobalInterfaceTable tX  IID_ISynchronize �X  IID_ISynchronizeHandle FX  IID_ISynchronizeEvent �X  IID_ISynchronizeContainer �X  IID_ISynchronizeMutex JX  IID_ICancelMethodCalls �X  IID_IAsyncManager X  IID_ICallFactory wX  IID_IRpcHelper �X  IID_IReleaseMarshalBuffers &X  IID_IWaitMultiple xX  IID_IAddrTrackingControl �X  IID_IAddrExclusionControl ,X  IID_IPipeByte �X  IID_IPipeLong �X  IID_IPipeDouble EX  IID_IComThreadingInfo �X  IID_IProcessInitControl 1X  IID_IFastRundown X  IID_IMarshalingStream �X  IID_ICallbackWithNoReentrancyToApplicationSTA zX  GUID_NULL 
i  CATID_MARSHALER i  IID_IRpcChannel i  IID_IRpcStub i  IID_IStubManager i  IID_IRpcProxy i  IID_IProxyManager i  IID_IPSFactory i  IID_IInternalMoniker i  IID_IDfReserved1 i  IID_IDfReserved2 i  IID_IDfReserved3 i  CLSID_StdMarshal |  CLSID_AggStdMarshal |  CLSID_StdAsyncActManager |  IID_IStub i  IID_IProxy i  IID_IEnumGeneric i  IID_IEnumHolder i  IID_IEnumCallback  i  IID_IOleManager !i  IID_IOlePresObj "i  IID_IDebug #i  IID_IDebugStream $i  CLSID_PSGenObject %|  CLSID_PSClientSite &|  CLSID_PSClassObject '|  CLSID_PSInPlaceActive (|  CLSID_PSInPlaceFrame )|  CLSID_PSDragDrop *|  CLSID_PSBindCtx +|  CLSID_PSEnumerators ,|  CLSID_StaticMetafile -|  CLSID_StaticDib .|  CID_CDfsVolume /|  CLSID_DCOMAccessControl 0|  CLSID_GlobalOptions 1|  CLSID_StdGlobalInterfaceTable 2|  CLSID_ComBinding 3|  CLSID_StdEvent 4|  CLSID_ManualResetEvent 5|  CLSID_SynchronizeContainer 6|  CLSID_AddrControl 7|  CLSID_CCDFormKrnl 8|  CLSID_CCDPropertyPage 9|  CLSID_CCDFormDialog :|  CLSID_CCDCommandButton ;|  CLSID_CCDComboBox <|  CLSID_CCDTextBox =|  CLSID_CCDCheckBox >|  CLSID_CCDLabel ?|  CLSID_CCDOptionButton @|  CLSID_CCDListBox A|  CLSID_CCDScrollBar B|  CLSID_CCDGroupBox C|  CLSID_CCDGeneralPropertyPage D|  CLSID_CCDGenericPropertyPage E|  CLSID_CCDFontPropertyPage F|  CLSID_CCDColorPropertyPage G|  CLSID_CCDLabelPropertyPage H|  CLSID_CCDCheckBoxPropertyPage I|  CLSID_CCDTextBoxPropertyPage J|  CLSID_CCDOptionButtonPropertyPage K|  CLSID_CCDListBoxPropertyPage L|  CLSID_CCDCommandButtonPropertyPage M|  CLSID_CCDComboBoxPropertyPage N|  CLSID_CCDScrollBarPropertyPage O|  CLSID_CCDGroupBoxPropertyPage P|  CLSID_CCDXObjectPropertyPage Q|  CLSID_CStdPropertyFrame R|  CLSID_CFormPropertyPage S|  CLSID_CGridPropertyPage T|  CLSID_CWSJArticlePage U|  CLSID_CSystemPage V|  CLSID_IdentityUnmarshal W|  CLSID_InProcFreeMarshaler X|  CLSID_Picture_Metafile Y|  CLSID_Picture_EnhMetafile Z|  CLSID_Picture_Dib [|  GUID_TRISTATE \X  IWinTypes_v0_1_c_ifspec )�!  IWinTypes_v0_1_s_ifspec *�!  VARENUM h  4  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM IVT_BSTR_BLOB �VT_VECTOR  VT_ARRAY   VT_BYREF  @VT_RESERVED  �VT_ILLEGAL ��VT_ILLEGALMASKED �VT_TYPEMASK � IID_IMallocSpy �X  IID_IBindCtx :X  IID_IEnumMoniker J X  IID_IRunnableObject � X  IID_IRunningObjectTable �!X  IID_IPersist i"X  IID_IPersistStream �"X  IID_IMoniker j#X  IID_IROTData X%X  IID_IEnumSTATSTG �%X  IID_IStorage X&X  IID_IPersistFile A(X  IID_IPersistStorage �(X  IID_ILockBytes �)X  IID_IEnumFORMATETC �*X  IID_IEnumSTATDATA l+X  IID_IRootStorage ,X  IID_IAdviseSink �,X  IID_AsyncIAdviseSink s-X  IID_IAdviseSink2 �.X  IID_AsyncIAdviseSink2 ./X  IID_IDataObject �/X  IID_IDataAdviseHolder 1X  IID_IMessageFilter �1X  FMTID_SummaryInformation ]2�  FMTID_DocSummaryInformation _2�  FMTID_UserDefinedProperties a2�  FMTID_DiscardableInformation c2�  FMTID_ImageSummaryInformation e2�  FMTID_AudioSummaryInformation g2�  FMTID_VideoSummaryInformation i2�  FMTID_MediaFileSummaryInformation k2�  IID_IClassActivator s2X  IID_IFillLockBytes �2X  IID_IProgressNotify �3X  IID_ILayoutStorage �3X  IID_IBlockingLock �4X  IID_ITimeAndNoticeControl �4X  IID_IOplockStorage N5X  IID_IDirectWriterLock �5X  IID_IUrlMon M6X  IID_IForegroundTransfer �6X  IID_IThumbnailExtractor 7X  IID_IDummyHICONIncluder �7X  IID_IProcessLock �7X  IID_ISurrogateService H8X  IID_IInitializeSpy �8X  IID_IApartmentShutdown �9X  IID_IOleAdviseHolder �X  IID_IOleCache �X  IID_IOleCache2 X  IID_IOleCacheControl �X  IID_IParseDisplayName X  IID_IOleContainer pX  IID_IOleClientSite �X  IID_IOleObject �X  IOLETypes_v0_0_c_ifspec ��!  IOLETypes_v0_0_s_ifspec ��!  IID_IOleWindow X  IID_IOleLink kX  IID_IOleItemContainer 9X  IID_IOleInPlaceUIWindow �X  IID_IOleInPlaceActiveObject XX  IID_IOleInPlaceFrame 	X  IID_IOleInPlaceObject �	X  IID_IOleInPlaceSite f
X  IID_IContinue ,X  IID_IViewObject {X  IID_IViewObject2 �X  IID_IDropSource 9
X  IID_IDropTarget �
X  IID_IDropSourceNotify 0X  IID_IEnumOLEVERB �X  IID_IServiceProvider TX  IOleAutomationTypes_v1_0_c_ifspec d�!  IOleAutomationTypes_v1_0_s_ifspec e�!  IID_ICreateTypeInfo �X  IID_ICreateTypeInfo2 X  IID_ICreateTypeLib �X  IID_ICreateTypeLib2 �X  IID_IDispatch lX  IID_IEnumVARIANT  	X  IID_ITypeComp �	X  IID_ITypeInfo ]
X  IID_ITypeInfo2 �X  IID_ITypeLib �X  IID_ITypeLib2 �X  IID_ITypeChangeEvents X  IID_IErrorInfo lX  IID_ICreateErrorInfo �X  IID_ISupportErrorInfo dX  IID_ITypeFactory �X  IID_ITypeMarshal X  IID_IRecordInfo �X  IID_IErrorLog �X  IID_IPropertyBag �X  __MIDL_itf_msxml_0000_v0_0_c_ifspec ��!  __MIDL_itf_msxml_0000_v0_0_s_ifspec ��!  LIBID_MSXML �i  IID_IXMLDOMImplementation i  IID_IXMLDOMNode (i  IID_IXMLDOMDocumentFragment �i  IID_IXMLDOMDocument gi  IID_IXMLDOMNodeList vi  IID_IXMLDOMNamedNodeMap �i  IID_IXMLDOMCharacterData i  IID_IXMLDOMAttribute �i  IID_IXMLDOMElement i  IID_IXMLDOMText �i  IID_IXMLDOMComment &i  IID_IXMLDOMProcessingInstruction �i  IID_IXMLDOMCDATASection i  IID_IXMLDOMDocumentType �i  IID_IXMLDOMNotation i  IID_IXMLDOMEntity �i  IID_IXMLDOMEntityReference �i  IID_IXMLDOMParseError b	i  IID_IXTLRuntime �	i  DIID_XMLDOMDocumentEvents >
i  CLSID_DOMDocument ]
|  CLSID_DOMFreeThreadedDocument a
|  IID_IXMLHttpRequest h
i  CLSID_XMLHTTPRequest �
|  IID_IXMLDSOControl �
i  CLSID_XMLDSOControl |  IID_IXMLElementCollection i  IID_IXMLDocument Ki  IID_IXMLDocument2 �i  IID_IXMLElement %i  IID_IXMLElement2 �i  IID_IXMLAttribute �i  IID_IXMLError 
i  CLSID_XMLDocument /
|  CLSID_SBS_StdURLMoniker Ki  CLSID_SBS_HttpProtocol Li  CLSID_SBS_FtpProtocol Mi  CLSID_SBS_GopherProtocol Ni  CLSID_SBS_HttpSProtocol Oi  CLSID_SBS_FileProtocol Pi  CLSID_SBS_MkProtocol Qi  CLSID_SBS_UrlMkBindCtx Ri  CLSID_SBS_SoftDistExt Si  CLSID_SBS_CdlProtocol Ti  CLSID_SBS_ClassInstallFilter Ui  CLSID_SBS_InternetSecurityManager Vi  CLSID_SBS_InternetZoneManager Wi  IID_IAsyncMoniker `i  CLSID_StdURLMoniker ai  CLSID_HttpProtocol bi  CLSID_FtpProtocol ci  CLSID_GopherProtocol di  CLSID_HttpSProtocol ei  CLSID_FileProtocol fi  CLSID_MkProtocol gi  CLSID_StdURLProtocol hi  CLSID_UrlMkBindCtx ii  CLSID_CdlProtocol ji  CLSID_ClassInstallFilter ki  IID_IAsyncBindCtx li  IID_IPersistMoniker X  IID_IMonikerProp �X  IID_IBindProtocol X  IID_IBinding hX  IID_IBindStatusCallback �X  IID_IBindStatusCallbackEx �X  IID_IAuthenticate �X  IID_IAuthenticateEx �X  IID_IHttpNegotiate ^X  IID_IHttpNegotiate2 �X  IID_IHttpNegotiate3 :	X  IID_IWinInetFileStream �	X  IID_IWindowForBindingUI 
X  IID_ICodeInstall z
X  IID_IUri X  IID_IUriContainer �X  IID_IUriBuilder �X  IID_IUriBuilderFactory ^X  IID_IWinInetInfo �X  IID_IHttpSecurity 6X  IID_IWinInetHttpInfo �X  IID_IWinInetHttpTimeouts X  IID_IWinInetCacheHints oX  IID_IWinInetCacheHints2 �X  SID_BindHost 4X  IID_IBindHost >X  IID_IInternet `X  IID_IInternetBindInfo �X  IID_IInternetBindInfoEx 'X  IID_IInternetProtocolRoot �X  IID_IInternetProtocol IX  IID_IInternetProtocolEx �X  IID_IInternetProtocolSink �X  IID_IInternetProtocolSinkStackable 1X  IID_IInternetSession �X  IID_IInternetThreadSwitch \X  IID_IInternetPriority �X  IID_IInternetProtocolInfo FX  CLSID_InternetSecurityManager wi  CLSID_InternetZoneManager xi  CLSID_PersistentZoneIdentifier {i  IID_IInternetSecurityMgrSite �X  IID_IInternetSecurityManager X  IID_IInternetSecurityManagerEx �X  IID_IInternetSecurityManagerEx2 �X  IID_IZoneIdentifier �X  IID_IInternetHostSecurityManager X  GUID_CUSTOM_LOCALMACHINEZONEUNLOCKED XX  IID_IInternetZoneManager �X  IID_IInternetZoneManagerEx �X  IID_IInternetZoneManagerEx2 � X  CLSID_SoftDistExt �!i  IID_ISoftDistExt �!X  IID_ICatalogFileInfo x"X  IID_IDataFilter �"X  IID_IEncodingFilterFactory p#X  GUID_CUSTOM_CONFIRMOBJECTSAFETY �#X  IID_IWrappedProtocol �#X  IID_IGetBindHandle R$X  IID_IBindCallbackRedirect �$X  IID_IPropertyStorage �X  IID_IPropertySetStorage �X  IID_IEnumSTATPROPSTG *X  IID_IEnumSTATPROPSETSTG �X  IID_StdOle i  GUID_DEVINTERFACE_DISK X  GUID_DEVINTERFACE_CDROM 
X  GUID_DEVINTERFACE_PARTITION X  GUID_DEVINTERFACE_TAPE X  GUID_DEVINTERFACE_WRITEONCEDISK X  GUID_DEVINTERFACE_VOLUME X  GUID_DEVINTERFACE_MEDIUMCHANGER X  GUID_DEVINTERFACE_FLOPPY X  GUID_DEVINTERFACE_CDCHANGER X  GUID_DEVINTERFACE_STORAGEPORT X  GUID_DEVINTERFACE_COMPORT X  GUID_DEVINTERFACE_SERENUM_BUS_ENUMERATOR X  _SCARD_IO_REQUEST �aP  dwProtocol �   cbPciLength �   SCARD_IO_REQUEST �P  aP  g_rgSCardT0Pci %.zP  g_rgSCardT1Pci %=zP  g_rgSCardRawPci %LzP  IID_IPrintDialogCallback X  IID_IPrintDialogServices X  _commode $@  @%�Q  osfhnd &   osfile '
�   pipech (
�   	lockinitflag )	@  lock *D!  textmode +
�   8unicode ,
�    8pipech2 -
�Q  9 �   �Q  �    ioinfo .Q  �Q  �Q   �Q  �Q  __imp___badioinfo D�Q  __imp___pioinfo I�Q  _dowildcard f@  _newmode g@  __imp___winitenv j3	  __imp___initenv o	    h  ��R  __uninitialized  __initializing __initialized     �nR  �R  __native_startup_state �+�R  __native_startup_lock �S  S  !__native_dllmain_reason � x  __native_vcclrit_reason � x  "	S   	$Ph    ")S   	 Ph    "�R   
"	0�h    "�R   	8�h     �-   ,	  GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt/gs_support.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt 09h    �      B  char long long unsigned int long long int wchar_t g&  short unsigned int &  int long int pthreadlocinfo �(l  r  threadlocaleinfostruct `�  4  �A   	lc_codepage �i  	lc_collate_cp �i  	lc_handle �y  	lc_id �	�  $	lc_category ��  H
lc_clike �A  
mb_cur_max �A  
lconv_intl_refcount �c  
lconv_num_refcount �c  
lconv_mon_refcount �c   
lconv ��  (
ctype1_refcount �c  0
ctype1 ��  8
pctype ��  @
pclmap ��  H
pcumap ��  P
lc_time_curr �  X pthreadmbcinfo �%4  :  threadmbcinfostruct localeinfo_struct ��  	locinfo �T   	mbcinfo �   _locale_tstruct �O  tagLC_ID ��  	wLanguage �&   	wCountry �&  	wCodePage �&   LC_ID ��  
 �W  	locale �W   	wlocale �]  4  �
c  	wrefcount �
c   �     A  unsigned int �  �  �    long unsigned int �  �  �      �  �    lconv �  &  <  �  unsigned char �  __lc_time_data �  double float long double __imp___mb_cur_max tc  W  V  �     _sys_errlist �&F  _sys_nerr �$A  __imp___argc c  __imp___argv �  �  W  __imp___wargv "�  �  ]  __imp__environ B�  __imp__wenviron G�  __imp__pgmptr N�  __imp__wpgmptr S�  __imp__osplatform XS  i  __imp__osver ]S  __imp__winver bS  __imp__winmajor gS  __imp__winminor lS  _amblksiz 5i  _PHNDLR B�  �  �  A   _XCPT_ACTION D
7  XcptNum E�   SigNum F	A  XcptAction G
�   �  B   _XcptActTab J7  _XcptActTabCount KA  _XcptActTabSize LA  _First_FPE_Indx MA  _Num_FPE NA  �  _EXCEPTION_RECORD ��
g  	ExceptionCode �

�   	ExceptionFlags �

�  p  �
!�  	ExceptionAddress �

1
  	NumberParameters �

�  	ExceptionInformation �
�    o  _CONTEXT ��%�  	P1Home �
!
   	P2Home �
!
  	P3Home �
!
  	P4Home �
!
  	P5Home �
!
   	P6Home �
!
  (	ContextFlags ��  0	MxCsr ��  4	SegCs �
�  8	SegDs �
�  :	SegEs �
�  <	SegFs �
�  >	SegGs �
�  @	SegSs �
�  B	EFlags ��  D	Dr0 �
!
  H	Dr1 �
!
  P	Dr2 �
!
  X	Dr3 �
!
  `	Dr6 �
!
  h	Dr7 �
!
  p	Rax �
!
  x	Rcx �
!
  �	Rdx �
!
  �	Rbx �
!
  �	Rsp �
!
  �	Rbp �
!
  �	Rsi �
!
  �	Rdi �
!
  �	R8 �
!
  �	R9 �
!
  �	R10 �
!
  �	R11 �
!
  �	R12 �
!
  �	R13 �
!
  �	R14 �
!
  �	R15 �
!
  �	Rip �
!
  ��   VectorRegister �   
VectorControl �
!
  �
DebugControl �
!
  �
LastBranchToRip �
!
  �
LastBranchFromRip �
!
  �
LastExceptionToRip �
!
  �
LastExceptionFromRip �
!
  � BYTE ��  WORD �&  DWORD ��  __imp__pctype +�  �  __imp__wctype ;�  __imp__pwctype G�  �  	   �  __newclmap P	  __newcumap Q	  __ptlocinfo RT  __ptmbcinfo S  __globallocalestatus TA  __locale_changed UA  __initiallocinfo V(r  __initiallocalestructinfo W�  signed char short int UINT_PTR 	/.�   �  ULONG_PTR 	1.�   ULONG64 	�.�   DWORD64 	�.�   PVOID g  LONG H  LONGLONG �%  ULONGLONG �.�   
��
  	LowPart �
�   g  �@
   
��
  	LowPart �
�   g   @
   _LARGE_INTEGER �  s
  u �
  QuadPart N
   LARGE_INTEGER �
  _GUID 
h  Data1 
�   Data2 
&  Data3 
&  Data4 
h   �  x  �    GUID 
  x  _M128A j(�  	Low k`
   	High lN
    M128A m�  !�  �  �    !�  �  �    �  �  �   _ _XMM_SAVE_AREA32  �o  	ControlWord �
�   	StatusWord �
�  	TagWord �
�  	Reserved1 �
�  	ErrorOpcode �
�  	ErrorOffset ��  	ErrorSelector �
�  	Reserved2 �
�  	DataOffset ��  	DataSelector �
�  	Reserved3 �
�  	MxCsr ��  	MxCsr_Mask ��  "FloatRegisters ��   "XmmRegisters ��  �
Reserved4 �
�  �  XMM_SAVE_AREA32 ��  #���  "Header ��   "Legacy ��   "Xmm0 ��  �"Xmm1 ��  �"Xmm2 ��  �"Xmm3 ��  �"Xmm4 ��  �"Xmm5 ��  �Xmm6 ��   Xmm7 ��  Xmm8 ��   Xmm9 ��  0Xmm10 ��  @Xmm11 ��  PXmm12 ��  `Xmm13 ��  pXmm14 ��  �Xmm15 ��  � !�  �  �    $ �  %FltSave �o  %FloatSave �o  &�   !�  %  �     CONTEXT �o  PCONTEXT �i  _RUNTIME_FUNCTION ��  	BeginAddress ��   	EndAddress ��  	UnwindData ��   PRUNTIME_FUNCTION ��  I  �  �  �    EXCEPTION_RECORD �
�  PEXCEPTION_RECORD �

  �  _EXCEPTION_POINTERS �
X  p  �
�   	ContextRecord �
7   EXCEPTION_POINTERS �
  X  GUID_MAX_POWER_SAVINGS �  GUID_MIN_POWER_SAVINGS �  GUID_TYPICAL_POWER_SAVINGS �  NO_SUBGROUP_GUID �  ALL_POWERSCHEMES_GUID  �  GUID_POWERSCHEME_PERSONALITY !�  GUID_ACTIVE_POWERSCHEME "�  GUID_IDLE_RESILIENCY_SUBGROUP #�  GUID_IDLE_RESILIENCY_PERIOD $�  GUID_DISK_COALESCING_POWERDOWN_TIMEOUT %�  GUID_EXECUTION_REQUIRED_REQUEST_TIMEOUT &�  GUID_VIDEO_SUBGROUP '�  GUID_VIDEO_POWERDOWN_TIMEOUT (�  GUID_VIDEO_ANNOYANCE_TIMEOUT )�  GUID_VIDEO_ADAPTIVE_PERCENT_INCREASE *�  GUID_VIDEO_DIM_TIMEOUT +�  GUID_VIDEO_ADAPTIVE_POWERDOWN ,�  GUID_MONITOR_POWER_ON -�  GUID_DEVICE_POWER_POLICY_VIDEO_BRIGHTNESS .�  GUID_DEVICE_POWER_POLICY_VIDEO_DIM_BRIGHTNESS /�  GUID_VIDEO_CURRENT_MONITOR_BRIGHTNESS 0�  GUID_VIDEO_ADAPTIVE_DISPLAY_BRIGHTNESS 1�  GUID_CONSOLE_DISPLAY_STATE 2�  GUID_ALLOW_DISPLAY_REQUIRED 3�  GUID_VIDEO_CONSOLE_LOCK_TIMEOUT 4�  GUID_ADAPTIVE_POWER_BEHAVIOR_SUBGROUP 5�  GUID_NON_ADAPTIVE_INPUT_TIMEOUT 6�  GUID_DISK_SUBGROUP 7�  GUID_DISK_POWERDOWN_TIMEOUT 8�  GUID_DISK_IDLE_TIMEOUT 9�  GUID_DISK_BURST_IGNORE_THRESHOLD :�  GUID_DISK_ADAPTIVE_POWERDOWN ;�  GUID_SLEEP_SUBGROUP <�  GUID_SLEEP_IDLE_THRESHOLD =�  GUID_STANDBY_TIMEOUT >�  GUID_UNATTEND_SLEEP_TIMEOUT ?�  GUID_HIBERNATE_TIMEOUT @�  GUID_HIBERNATE_FASTS4_POLICY A�  GUID_CRITICAL_POWER_TRANSITION B�  GUID_SYSTEM_AWAYMODE C�  GUID_ALLOW_AWAYMODE D�  GUID_ALLOW_STANDBY_STATES E�  GUID_ALLOW_RTC_WAKE F�  GUID_ALLOW_SYSTEM_REQUIRED G�  GUID_SYSTEM_BUTTON_SUBGROUP H�  GUID_POWERBUTTON_ACTION I�  GUID_SLEEPBUTTON_ACTION J�  GUID_USERINTERFACEBUTTON_ACTION K�  GUID_LIDCLOSE_ACTION L�  GUID_LIDOPEN_POWERSTATE M�  GUID_BATTERY_SUBGROUP N�  GUID_BATTERY_DISCHARGE_ACTION_0 O�  GUID_BATTERY_DISCHARGE_LEVEL_0 P�  GUID_BATTERY_DISCHARGE_FLAGS_0 Q�  GUID_BATTERY_DISCHARGE_ACTION_1 R�  GUID_BATTERY_DISCHARGE_LEVEL_1 S�  GUID_BATTERY_DISCHARGE_FLAGS_1 T�  GUID_BATTERY_DISCHARGE_ACTION_2 U�  GUID_BATTERY_DISCHARGE_LEVEL_2 V�  GUID_BATTERY_DISCHARGE_FLAGS_2 W�  GUID_BATTERY_DISCHARGE_ACTION_3 X�  GUID_BATTERY_DISCHARGE_LEVEL_3 Y�  GUID_BATTERY_DISCHARGE_FLAGS_3 Z�  GUID_PROCESSOR_SETTINGS_SUBGROUP [�  GUID_PROCESSOR_THROTTLE_POLICY \�  GUID_PROCESSOR_THROTTLE_MAXIMUM ]�  GUID_PROCESSOR_THROTTLE_MINIMUM ^�  GUID_PROCESSOR_ALLOW_THROTTLING _�  GUID_PROCESSOR_IDLESTATE_POLICY `�  GUID_PROCESSOR_PERFSTATE_POLICY a�  GUID_PROCESSOR_PERF_INCREASE_THRESHOLD b�  GUID_PROCESSOR_PERF_DECREASE_THRESHOLD c�  GUID_PROCESSOR_PERF_INCREASE_POLICY d�  GUID_PROCESSOR_PERF_DECREASE_POLICY e�  GUID_PROCESSOR_PERF_INCREASE_TIME f�  GUID_PROCESSOR_PERF_DECREASE_TIME g�  GUID_PROCESSOR_PERF_TIME_CHECK h�  GUID_PROCESSOR_PERF_BOOST_POLICY i�  GUID_PROCESSOR_PERF_BOOST_MODE j�  GUID_PROCESSOR_IDLE_ALLOW_SCALING k�  GUID_PROCESSOR_IDLE_DISABLE l�  GUID_PROCESSOR_IDLE_STATE_MAXIMUM m�  GUID_PROCESSOR_IDLE_TIME_CHECK n�  GUID_PROCESSOR_IDLE_DEMOTE_THRESHOLD o�  GUID_PROCESSOR_IDLE_PROMOTE_THRESHOLD p�  GUID_PROCESSOR_CORE_PARKING_INCREASE_THRESHOLD q�  GUID_PROCESSOR_CORE_PARKING_DECREASE_THRESHOLD r�  GUID_PROCESSOR_CORE_PARKING_INCREASE_POLICY s�  GUID_PROCESSOR_CORE_PARKING_DECREASE_POLICY t�  GUID_PROCESSOR_CORE_PARKING_MAX_CORES u�  GUID_PROCESSOR_CORE_PARKING_MIN_CORES v�  GUID_PROCESSOR_CORE_PARKING_INCREASE_TIME w�  GUID_PROCESSOR_CORE_PARKING_DECREASE_TIME x�  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_DECREASE_FACTOR y�  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_THRESHOLD z�  GUID_PROCESSOR_CORE_PARKING_AFFINITY_WEIGHTING {�  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_DECREASE_FACTOR |�  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_THRESHOLD }�  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_WEIGHTING ~�  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_THRESHOLD �  GUID_PROCESSOR_PARKING_CORE_OVERRIDE ��  GUID_PROCESSOR_PARKING_PERF_STATE ��  GUID_PROCESSOR_PARKING_CONCURRENCY_THRESHOLD ��  GUID_PROCESSOR_PARKING_HEADROOM_THRESHOLD ��  GUID_PROCESSOR_PERF_HISTORY ��  GUID_PROCESSOR_PERF_LATENCY_HINT ��  GUID_PROCESSOR_DISTRIBUTE_UTILITY ��  GUID_SYSTEM_COOLING_POLICY ��  GUID_LOCK_CONSOLE_ON_WAKE ��  GUID_DEVICE_IDLE_POLICY ��  GUID_ACDC_POWER_SOURCE ��  GUID_LIDSWITCH_STATE_CHANGE ��  GUID_BATTERY_PERCENTAGE_REMAINING ��  GUID_GLOBAL_USER_PRESENCE ��  GUID_SESSION_DISPLAY_STATUS ��  GUID_SESSION_USER_PRESENCE ��  GUID_IDLE_BACKGROUND_TASK ��  GUID_BACKGROUND_TASK_NOTIFICATION ��  GUID_APPLAUNCH_BUTTON ��  GUID_PCIEXPRESS_SETTINGS_SUBGROUP ��  GUID_PCIEXPRESS_ASPM_POLICY ��  GUID_ENABLE_SWITCH_FORCED_SHUTDOWN ��  PPM_PERFSTATE_CHANGE_GUID ��  PPM_PERFSTATE_DOMAIN_CHANGE_GUID ��  PPM_IDLESTATE_CHANGE_GUID ��  PPM_PERFSTATES_DATA_GUID ��  PPM_IDLESTATES_DATA_GUID ��  PPM_IDLE_ACCOUNTING_GUID ��  PPM_IDLE_ACCOUNTING_EX_GUID ��  PPM_THERMALCONSTRAINT_GUID ��  PPM_PERFMON_PERFSTATE_GUID ��  PPM_THERMAL_POLICY_CHANGE_GUID ��  _FILETIME ��(  dwLowDateTime ��   dwHighDateTime ��   FILETIME ��(  VIRTUAL_STORAGE_TYPE_VENDOR_UNKNOWN �  VIRTUAL_STORAGE_TYPE_VENDOR_MICROSOFT �  NTSTATUS @
  '	�)  (ft_scalar !�   (ft_struct "�(   FT #])  )GS_ExceptionRecord %�  	@�h    )GS_ContextRecord &%  	`�h    )GS_ExceptionPointers (!t  	�ah    *__security_cookie ,�  	pPh    *__security_cookie_complement -�  	�Ph    +__report_gsfailure h:h    �       �,  ,StackCookie h�  5  1  )cookie j',  �P-controlPC l
  m  k  )imgBase l
  ��)establisherFrame l
  �@-fctEntry m�  �  �  )hndData n	1
  �H.*:h    �,  H+  /R	`�h     .A:h    �,  k+  /Rt /Qvh/X0 .�:h    -  �+  /R0/Xt /w 	`�h    /w(vx/w0vp/w80 .�:h    -  �+  /R0 .�:h    !-  �+  /R	�ah     0�:h    --  .�:h    9-  	,  /Q����| 0�:h    E-   �  ',  �    ,  1__security_init_cookie 209h    �       ��,  -cookie 4�    �  )systime 5�)  �@)perfctr 6  �H.{9h    V-  �,  /R�@ 0�9h    b-  0�9h    n-  0�9h    z-  2�9h    �-  /R�H  3�  �  �3�  �  �&3�  �  �'4K  K  44    4=  =  
�4�  �  
5abort abort �(4�  �  &47  7  
�4�  �  
�4'  '  >4�  �   Q$   Q  GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt/tlssup.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt ;h    �       �
  char long long unsigned int long long int uintptr_t P,�   wchar_t g4  short unsigned int 4  int long int pthreadlocinfo �(z  �  threadlocaleinfostruct `�*  X  �O   	lc_codepage �w  	lc_collate_cp �w  	lc_handle ��  	lc_id �	�  $	lc_category ��  H
lc_clike �O  
mb_cur_max �O  
lconv_intl_refcount �q  
lconv_num_refcount �q  
lconv_mon_refcount �q   
lconv ��  (
ctype1_refcount �q  0
ctype1 ��  8
pctype ��  @
pclmap ��  H
pcumap ��  P
lc_time_curr �  X pthreadmbcinfo �%B  H  threadmbcinfostruct localeinfo_struct ��  	locinfo �b   	mbcinfo �*   _locale_tstruct �]  tagLC_ID �  	wLanguage �4   	wCountry �4  	wCodePage �4   LC_ID ��  
 �e  	locale �e   	wlocale �k  X  �
q  	wrefcount �
q   �   $  O  unsigned int �  �  �    long unsigned int   �  �      �  �    lconv �  4  J  �  unsigned char �  __lc_time_data   _PHNDLR B'  -  8  O   _XCPT_ACTION D
�  XcptNum E�   SigNum F	O  XcptAction G
   8  �   _XcptActTab J�  _XcptActTabCount KO  _XcptActTabSize LO  _First_FPE_Indx MO  _Num_FPE NO  ULONG �  BOOL �O  DWORD ��  float LPVOID ��  w  __imp__pctype +\  �  __imp__wctype ;\  __imp__pwctype G\  �  �   �  __newclmap P�  __newcumap Q�  __ptlocinfo Rb  __ptmbcinfo S*  __globallocalestatus TO  __locale_changed UO  __initiallocinfo V(�  __initiallocalestructinfo W�  __imp___mb_cur_max �q  signed char short int ULONG_PTR 1.�   PVOID �  HANDLE ��  ULONGLONG �.�   _GUID %  Data1 �   Data2 4  Data3 4  Data4 %   �  5  �    GUID �  5  double long double f  e  w  �     _sys_errlist 	�&g  _sys_nerr 	�$O  __imp___argc 	q  __imp___argv 	�  �  e  __imp___wargv 	"�  �  k  __imp__environ 	B�  __imp__wenviron 	G�  __imp__pgmptr 	N�  __imp__wpgmptr 	S�  __imp__osplatform 	X@  __imp__osver 	]@  __imp__winver 	b@  __imp__winmajor 	g@  __imp__winminor 	l@  _amblksiz 
5w  GUID_MAX_POWER_SAVINGS B  GUID_MIN_POWER_SAVINGS B  GUID_TYPICAL_POWER_SAVINGS B  NO_SUBGROUP_GUID B  ALL_POWERSCHEMES_GUID  B  GUID_POWERSCHEME_PERSONALITY !B  GUID_ACTIVE_POWERSCHEME "B  GUID_IDLE_RESILIENCY_SUBGROUP #B  GUID_IDLE_RESILIENCY_PERIOD $B  GUID_DISK_COALESCING_POWERDOWN_TIMEOUT %B  GUID_EXECUTION_REQUIRED_REQUEST_TIMEOUT &B  GUID_VIDEO_SUBGROUP 'B  GUID_VIDEO_POWERDOWN_TIMEOUT (B  GUID_VIDEO_ANNOYANCE_TIMEOUT )B  GUID_VIDEO_ADAPTIVE_PERCENT_INCREASE *B  GUID_VIDEO_DIM_TIMEOUT +B  GUID_VIDEO_ADAPTIVE_POWERDOWN ,B  GUID_MONITOR_POWER_ON -B  GUID_DEVICE_POWER_POLICY_VIDEO_BRIGHTNESS .B  GUID_DEVICE_POWER_POLICY_VIDEO_DIM_BRIGHTNESS /B  GUID_VIDEO_CURRENT_MONITOR_BRIGHTNESS 0B  GUID_VIDEO_ADAPTIVE_DISPLAY_BRIGHTNESS 1B  GUID_CONSOLE_DISPLAY_STATE 2B  GUID_ALLOW_DISPLAY_REQUIRED 3B  GUID_VIDEO_CONSOLE_LOCK_TIMEOUT 4B  GUID_ADAPTIVE_POWER_BEHAVIOR_SUBGROUP 5B  GUID_NON_ADAPTIVE_INPUT_TIMEOUT 6B  GUID_DISK_SUBGROUP 7B  GUID_DISK_POWERDOWN_TIMEOUT 8B  GUID_DISK_IDLE_TIMEOUT 9B  GUID_DISK_BURST_IGNORE_THRESHOLD :B  GUID_DISK_ADAPTIVE_POWERDOWN ;B  GUID_SLEEP_SUBGROUP <B  GUID_SLEEP_IDLE_THRESHOLD =B  GUID_STANDBY_TIMEOUT >B  GUID_UNATTEND_SLEEP_TIMEOUT ?B  GUID_HIBERNATE_TIMEOUT @B  GUID_HIBERNATE_FASTS4_POLICY AB  GUID_CRITICAL_POWER_TRANSITION BB  GUID_SYSTEM_AWAYMODE CB  GUID_ALLOW_AWAYMODE DB  GUID_ALLOW_STANDBY_STATES EB  GUID_ALLOW_RTC_WAKE FB  GUID_ALLOW_SYSTEM_REQUIRED GB  GUID_SYSTEM_BUTTON_SUBGROUP HB  GUID_POWERBUTTON_ACTION IB  GUID_SLEEPBUTTON_ACTION JB  GUID_USERINTERFACEBUTTON_ACTION KB  GUID_LIDCLOSE_ACTION LB  GUID_LIDOPEN_POWERSTATE MB  GUID_BATTERY_SUBGROUP NB  GUID_BATTERY_DISCHARGE_ACTION_0 OB  GUID_BATTERY_DISCHARGE_LEVEL_0 PB  GUID_BATTERY_DISCHARGE_FLAGS_0 QB  GUID_BATTERY_DISCHARGE_ACTION_1 RB  GUID_BATTERY_DISCHARGE_LEVEL_1 SB  GUID_BATTERY_DISCHARGE_FLAGS_1 TB  GUID_BATTERY_DISCHARGE_ACTION_2 UB  GUID_BATTERY_DISCHARGE_LEVEL_2 VB  GUID_BATTERY_DISCHARGE_FLAGS_2 WB  GUID_BATTERY_DISCHARGE_ACTION_3 XB  GUID_BATTERY_DISCHARGE_LEVEL_3 YB  GUID_BATTERY_DISCHARGE_FLAGS_3 ZB  GUID_PROCESSOR_SETTINGS_SUBGROUP [B  GUID_PROCESSOR_THROTTLE_POLICY \B  GUID_PROCESSOR_THROTTLE_MAXIMUM ]B  GUID_PROCESSOR_THROTTLE_MINIMUM ^B  GUID_PROCESSOR_ALLOW_THROTTLING _B  GUID_PROCESSOR_IDLESTATE_POLICY `B  GUID_PROCESSOR_PERFSTATE_POLICY aB  GUID_PROCESSOR_PERF_INCREASE_THRESHOLD bB  GUID_PROCESSOR_PERF_DECREASE_THRESHOLD cB  GUID_PROCESSOR_PERF_INCREASE_POLICY dB  GUID_PROCESSOR_PERF_DECREASE_POLICY eB  GUID_PROCESSOR_PERF_INCREASE_TIME fB  GUID_PROCESSOR_PERF_DECREASE_TIME gB  GUID_PROCESSOR_PERF_TIME_CHECK hB  GUID_PROCESSOR_PERF_BOOST_POLICY iB  GUID_PROCESSOR_PERF_BOOST_MODE jB  GUID_PROCESSOR_IDLE_ALLOW_SCALING kB  GUID_PROCESSOR_IDLE_DISABLE lB  GUID_PROCESSOR_IDLE_STATE_MAXIMUM mB  GUID_PROCESSOR_IDLE_TIME_CHECK nB  GUID_PROCESSOR_IDLE_DEMOTE_THRESHOLD oB  GUID_PROCESSOR_IDLE_PROMOTE_THRESHOLD pB  GUID_PROCESSOR_CORE_PARKING_INCREASE_THRESHOLD qB  GUID_PROCESSOR_CORE_PARKING_DECREASE_THRESHOLD rB  GUID_PROCESSOR_CORE_PARKING_INCREASE_POLICY sB  GUID_PROCESSOR_CORE_PARKING_DECREASE_POLICY tB  GUID_PROCESSOR_CORE_PARKING_MAX_CORES uB  GUID_PROCESSOR_CORE_PARKING_MIN_CORES vB  GUID_PROCESSOR_CORE_PARKING_INCREASE_TIME wB  GUID_PROCESSOR_CORE_PARKING_DECREASE_TIME xB  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_DECREASE_FACTOR yB  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_THRESHOLD zB  GUID_PROCESSOR_CORE_PARKING_AFFINITY_WEIGHTING {B  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_DECREASE_FACTOR |B  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_THRESHOLD }B  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_WEIGHTING ~B  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_THRESHOLD B  GUID_PROCESSOR_PARKING_CORE_OVERRIDE �B  GUID_PROCESSOR_PARKING_PERF_STATE �B  GUID_PROCESSOR_PARKING_CONCURRENCY_THRESHOLD �B  GUID_PROCESSOR_PARKING_HEADROOM_THRESHOLD �B  GUID_PROCESSOR_PERF_HISTORY �B  GUID_PROCESSOR_PERF_LATENCY_HINT �B  GUID_PROCESSOR_DISTRIBUTE_UTILITY �B  GUID_SYSTEM_COOLING_POLICY �B  GUID_LOCK_CONSOLE_ON_WAKE �B  GUID_DEVICE_IDLE_POLICY �B  GUID_ACDC_POWER_SOURCE �B  GUID_LIDSWITCH_STATE_CHANGE �B  GUID_BATTERY_PERCENTAGE_REMAINING �B  GUID_GLOBAL_USER_PRESENCE �B  GUID_SESSION_DISPLAY_STATUS �B  GUID_SESSION_USER_PRESENCE �B  GUID_IDLE_BACKGROUND_TASK �B  GUID_BACKGROUND_TASK_NOTIFICATION �B  GUID_APPLAUNCH_BUTTON �B  GUID_PCIEXPRESS_SETTINGS_SUBGROUP �B  GUID_PCIEXPRESS_ASPM_POLICY �B  GUID_ENABLE_SWITCH_FORCED_SHUTDOWN �B  PPM_PERFSTATE_CHANGE_GUID �B  PPM_PERFSTATE_DOMAIN_CHANGE_GUID �B  PPM_IDLESTATE_CHANGE_GUID �B  PPM_PERFSTATES_DATA_GUID �B  PPM_IDLESTATES_DATA_GUID �B  PPM_IDLE_ACCOUNTING_GUID �B  PPM_IDLE_ACCOUNTING_EX_GUID �B  PPM_THERMALCONSTRAINT_GUID �B  PPM_PERFMON_PERFSTATE_GUID �B  PPM_THERMAL_POLICY_CHANGE_GUID �B  PIMAGE_TLS_CALLBACK �*    0  E  �    �   _IMAGE_TLS_DIRECTORY64 (�   	StartAddressOfRawData ��   	EndAddressOfRawData ��  	AddressOfIndex ��  	AddressOfCallBacks ��  	SizeOfZeroFill �
   	Characteristics �
  $ IMAGE_TLS_DIRECTORY64 �E  IMAGE_TLS_DIRECTORY �#   -   VIRTUAL_STORAGE_TYPE_VENDOR_UNKNOWN B  VIRTUAL_STORAGE_TYPE_VENDOR_MICROSOFT B  _PVFV `  �   _tls_index #�  	�h    _tls_start )e  	 �h    _tls_end *e  	�h    __xl_a ,+  	(�h    __xl_z -+  	@�h    _tls_used /J   	�ah    __xd_a ?$�   	H�h    __xd_z @$�   	P�h    _CRT_MT GO  __dyn_tls_init_callback g%  	�ah    __xl_c h+  	0�h    __xl_d �+  	8�h    mingw_initltsdrot_force �O  	�h    mingw_initltsdyn_force �O  	�h    mingw_initltssuo_force �O  	��h    __dyn_tls_dtor �
  ;h    /       ��"  a  ��  Q
  M
  O  �*  �
  �
  l  �;1  �
  �
  5;h    H$   __tlregdtor mO  �;h           �-#  func m�   R  __dyn_tls_init L
  �#  !a  L�  !O  L*  !l  L;1  "pfunc N
�   "ps O
   #-#  @;h    �       �H$  $I#      $U#  v  n  $a#  �  �  %m#  %{#  &-#  p;h     p;h    +       L:$  $I#  F  B  $U#  �    $a#  �  �  'm#  �  �  '{#  "     �;h    H$   (w  w   �   �  GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt/cinitexe.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt �  char long long unsigned int long long int short unsigned int int long int unsigned int long unsigned int unsigned char _PVFV q  w  c  �  �     __xi_a 
x  	�h    __xi_z x  	 �h    __xc_a x  	 �h    __xc_z 
x  	�h     �      GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt/mingw_helpers.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt   mingw_app_type �   	�h    int  g`   4  GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt/pseudo-reloc.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt �;h    e      |  __gnuc_va_list �   __builtin_va_list   char   va_list �   size_t (,@  long long unsigned int long long int intptr_t C#Z  ptrdiff_t ]#Z  wchar_t g�  short unsigned int �  int long int pthreadlocinfo �(�  �  threadlocaleinfostruct `��  	3  ��   
lc_codepage ��  
lc_collate_cp ��  
lc_handle ��  
lc_id �	  $
lc_category �+  Hlc_clike ��  mb_cur_max ��  lconv_intl_refcount ��  lconv_num_refcount ��  lconv_mon_refcount ��   lconv �B  (ctype1_refcount ��  0ctype1 �H  8pctype �N  @pclmap �T  Hpcumap �T  Plc_time_curr ��  X pthreadmbcinfo �%�  �  threadmbcinfostruct 
localeinfo_struct �  
locinfo ��   
mbcinfo ��   _locale_tstruct ��  
tagLC_ID �p  
wLanguage ��   
wCountry ��  
wCodePage ��   LC_ID �!   ��  
locale ��   
wlocale ��  	3  �
�  
wrefcount �
�     �  �  unsigned int �      @   long unsigned int p  +  @     ;  @   lconv ;  �  �  k  unsigned char Z  __lc_time_data p  _PHNDLR B�  �  �  �   _XCPT_ACTION D
�  XcptNum E   SigNum F	�  XcptAction G
�   �  �   _XcptActTab J�  _XcptActTabCount K�  _XcptActTabSize L�  _First_FPE_Indx M�  _Num_FPE N�  BYTE �Z  WORD ��  DWORD �  float PBYTE ��  n  LPBYTE ��  LPVOID �l  �  �  __imp__pctype +�  H  __imp__wctype ;�  __imp__pwctype G�  k  2   '  __newclmap P2  __newcumap Q2  __ptlocinfo R�  __ptmbcinfo S�  __globallocalestatus T�  __locale_changed U�  __initiallocinfo V(�  __initiallocalestructinfo W  __imp___mb_cur_max ��  signed char short int ULONG_PTR 1.@  SIZE_T �'-  PVOID l  LONG �  HANDLE �l  
_LIST_ENTRY d�  
Flink e�   
Blink f�   {  LIST_ENTRY g{  _GUID 		  Data1 	   Data2 	�  Data3 	�  Data4 		   Z  (	  @   GUID 	�  (	  IID 	X(	  :	  CLSID 	`(	  K	  FMTID 	g(	  ^	  double long double �  �	  @    _sys_errlist 
�&�	  _sys_nerr 
�$�  __imp___argc 
�  __imp___argv 
�	  �	  �  __imp___wargv 
"
  
  �  __imp__environ 
B�	  __imp__wenviron 
G
  __imp__pgmptr 
N�	  __imp__wpgmptr 
S
  __imp__osplatform 
X�  __imp__osver 
]�  __imp__winver 
b�  __imp__winmajor 
g�  __imp__winminor 
l�  _amblksiz 5�  
_MEMORY_BASIC_INFORMATION 0��  
BaseAddress �
N   
AllocationBase �
N  
AllocationProtect �
�  
RegionSize �?  
State �
�   
Protect �
�  $
Type �
�  ( MEMORY_BASIC_INFORMATION �  GUID_MAX_POWER_SAVINGS 5	  GUID_MIN_POWER_SAVINGS 5	  GUID_TYPICAL_POWER_SAVINGS 5	  NO_SUBGROUP_GUID 5	  ALL_POWERSCHEMES_GUID  5	  GUID_POWERSCHEME_PERSONALITY !5	  GUID_ACTIVE_POWERSCHEME "5	  GUID_IDLE_RESILIENCY_SUBGROUP #5	  GUID_IDLE_RESILIENCY_PERIOD $5	  GUID_DISK_COALESCING_POWERDOWN_TIMEOUT %5	  GUID_EXECUTION_REQUIRED_REQUEST_TIMEOUT &5	  GUID_VIDEO_SUBGROUP '5	  GUID_VIDEO_POWERDOWN_TIMEOUT (5	  GUID_VIDEO_ANNOYANCE_TIMEOUT )5	  GUID_VIDEO_ADAPTIVE_PERCENT_INCREASE *5	  GUID_VIDEO_DIM_TIMEOUT +5	  GUID_VIDEO_ADAPTIVE_POWERDOWN ,5	  GUID_MONITOR_POWER_ON -5	  GUID_DEVICE_POWER_POLICY_VIDEO_BRIGHTNESS .5	  GUID_DEVICE_POWER_POLICY_VIDEO_DIM_BRIGHTNESS /5	  GUID_VIDEO_CURRENT_MONITOR_BRIGHTNESS 05	  GUID_VIDEO_ADAPTIVE_DISPLAY_BRIGHTNESS 15	  GUID_CONSOLE_DISPLAY_STATE 25	  GUID_ALLOW_DISPLAY_REQUIRED 35	  GUID_VIDEO_CONSOLE_LOCK_TIMEOUT 45	  GUID_ADAPTIVE_POWER_BEHAVIOR_SUBGROUP 55	  GUID_NON_ADAPTIVE_INPUT_TIMEOUT 65	  GUID_DISK_SUBGROUP 75	  GUID_DISK_POWERDOWN_TIMEOUT 85	  GUID_DISK_IDLE_TIMEOUT 95	  GUID_DISK_BURST_IGNORE_THRESHOLD :5	  GUID_DISK_ADAPTIVE_POWERDOWN ;5	  GUID_SLEEP_SUBGROUP <5	  GUID_SLEEP_IDLE_THRESHOLD =5	  GUID_STANDBY_TIMEOUT >5	  GUID_UNATTEND_SLEEP_TIMEOUT ?5	  GUID_HIBERNATE_TIMEOUT @5	  GUID_HIBERNATE_FASTS4_POLICY A5	  GUID_CRITICAL_POWER_TRANSITION B5	  GUID_SYSTEM_AWAYMODE C5	  GUID_ALLOW_AWAYMODE D5	  GUID_ALLOW_STANDBY_STATES E5	  GUID_ALLOW_RTC_WAKE F5	  GUID_ALLOW_SYSTEM_REQUIRED G5	  GUID_SYSTEM_BUTTON_SUBGROUP H5	  GUID_POWERBUTTON_ACTION I5	  GUID_SLEEPBUTTON_ACTION J5	  GUID_USERINTERFACEBUTTON_ACTION K5	  GUID_LIDCLOSE_ACTION L5	  GUID_LIDOPEN_POWERSTATE M5	  GUID_BATTERY_SUBGROUP N5	  GUID_BATTERY_DISCHARGE_ACTION_0 O5	  GUID_BATTERY_DISCHARGE_LEVEL_0 P5	  GUID_BATTERY_DISCHARGE_FLAGS_0 Q5	  GUID_BATTERY_DISCHARGE_ACTION_1 R5	  GUID_BATTERY_DISCHARGE_LEVEL_1 S5	  GUID_BATTERY_DISCHARGE_FLAGS_1 T5	  GUID_BATTERY_DISCHARGE_ACTION_2 U5	  GUID_BATTERY_DISCHARGE_LEVEL_2 V5	  GUID_BATTERY_DISCHARGE_FLAGS_2 W5	  GUID_BATTERY_DISCHARGE_ACTION_3 X5	  GUID_BATTERY_DISCHARGE_LEVEL_3 Y5	  GUID_BATTERY_DISCHARGE_FLAGS_3 Z5	  GUID_PROCESSOR_SETTINGS_SUBGROUP [5	  GUID_PROCESSOR_THROTTLE_POLICY \5	  GUID_PROCESSOR_THROTTLE_MAXIMUM ]5	  GUID_PROCESSOR_THROTTLE_MINIMUM ^5	  GUID_PROCESSOR_ALLOW_THROTTLING _5	  GUID_PROCESSOR_IDLESTATE_POLICY `5	  GUID_PROCESSOR_PERFSTATE_POLICY a5	  GUID_PROCESSOR_PERF_INCREASE_THRESHOLD b5	  GUID_PROCESSOR_PERF_DECREASE_THRESHOLD c5	  GUID_PROCESSOR_PERF_INCREASE_POLICY d5	  GUID_PROCESSOR_PERF_DECREASE_POLICY e5	  GUID_PROCESSOR_PERF_INCREASE_TIME f5	  GUID_PROCESSOR_PERF_DECREASE_TIME g5	  GUID_PROCESSOR_PERF_TIME_CHECK h5	  GUID_PROCESSOR_PERF_BOOST_POLICY i5	  GUID_PROCESSOR_PERF_BOOST_MODE j5	  GUID_PROCESSOR_IDLE_ALLOW_SCALING k5	  GUID_PROCESSOR_IDLE_DISABLE l5	  GUID_PROCESSOR_IDLE_STATE_MAXIMUM m5	  GUID_PROCESSOR_IDLE_TIME_CHECK n5	  GUID_PROCESSOR_IDLE_DEMOTE_THRESHOLD o5	  GUID_PROCESSOR_IDLE_PROMOTE_THRESHOLD p5	  GUID_PROCESSOR_CORE_PARKING_INCREASE_THRESHOLD q5	  GUID_PROCESSOR_CORE_PARKING_DECREASE_THRESHOLD r5	  GUID_PROCESSOR_CORE_PARKING_INCREASE_POLICY s5	  GUID_PROCESSOR_CORE_PARKING_DECREASE_POLICY t5	  GUID_PROCESSOR_CORE_PARKING_MAX_CORES u5	  GUID_PROCESSOR_CORE_PARKING_MIN_CORES v5	  GUID_PROCESSOR_CORE_PARKING_INCREASE_TIME w5	  GUID_PROCESSOR_CORE_PARKING_DECREASE_TIME x5	  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_DECREASE_FACTOR y5	  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_THRESHOLD z5	  GUID_PROCESSOR_CORE_PARKING_AFFINITY_WEIGHTING {5	  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_DECREASE_FACTOR |5	  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_THRESHOLD }5	  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_WEIGHTING ~5	  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_THRESHOLD 5	  GUID_PROCESSOR_PARKING_CORE_OVERRIDE �5	  GUID_PROCESSOR_PARKING_PERF_STATE �5	  GUID_PROCESSOR_PARKING_CONCURRENCY_THRESHOLD �5	  GUID_PROCESSOR_PARKING_HEADROOM_THRESHOLD �5	  GUID_PROCESSOR_PERF_HISTORY �5	  GUID_PROCESSOR_PERF_LATENCY_HINT �5	  GUID_PROCESSOR_DISTRIBUTE_UTILITY �5	  GUID_SYSTEM_COOLING_POLICY �5	  GUID_LOCK_CONSOLE_ON_WAKE �5	  GUID_DEVICE_IDLE_POLICY �5	  GUID_ACDC_POWER_SOURCE �5	  GUID_LIDSWITCH_STATE_CHANGE �5	  GUID_BATTERY_PERCENTAGE_REMAINING �5	  GUID_GLOBAL_USER_PRESENCE �5	  GUID_SESSION_DISPLAY_STATUS �5	  GUID_SESSION_USER_PRESENCE �5	  GUID_IDLE_BACKGROUND_TASK �5	  GUID_BACKGROUND_TASK_NOTIFICATION �5	  GUID_APPLAUNCH_BUTTON �5	  GUID_PCIEXPRESS_SETTINGS_SUBGROUP �5	  GUID_PCIEXPRESS_ASPM_POLICY �5	  GUID_ENABLE_SWITCH_FORCED_SHUTDOWN �5	  PPM_PERFSTATE_CHANGE_GUID �5	  PPM_PERFSTATE_DOMAIN_CHANGE_GUID �5	  PPM_IDLESTATE_CHANGE_GUID �5	  PPM_PERFSTATES_DATA_GUID �5	  PPM_IDLESTATES_DATA_GUID �5	  PPM_IDLE_ACCOUNTING_GUID �5	  PPM_IDLE_ACCOUNTING_EX_GUID �5	  PPM_THERMALCONSTRAINT_GUID �5	  PPM_PERFMON_PERFSTATE_GUID �5	  PPM_THERMAL_POLICY_CHANGE_GUID �5	  n  !  @   �L!  PhysicalAddress ��  VirtualSize  �   
_IMAGE_SECTION_HEADER (�k"  
Name �!   
Misc 	!  
VirtualAddress 
�  
SizeOfRawData 
�  
PointerToRawData 
�  
PointerToRelocations 
�  
PointerToLinenumbers 
�  
NumberOfRelocations {   
NumberOfLinenumbers {  "
Characteristics 	
�  $ PIMAGE_SECTION_HEADER 
�"  L!  
_RTL_CRITICAL_SECTION_DEBUG 05�#  
Type 6{   
CreatorBackTraceIndex 7{  
CriticalSection 8%7$  
ProcessLocksList 9�  
EntryCount :
�   
ContentionCount ;
�  $
Flags <
�  (
CreatorBackTraceIndexHigh ={  ,
SpareWORD >{  . 
_RTL_CRITICAL_SECTION (P7$  
DebugInfo Q#=$   
LockCount R]  
RecursionCount S]  
OwningThread Tk  
LockSemaphore Uk  
SpinCount V-    �#  PRTL_CRITICAL_SECTION_DEBUG ?#b$  �"  RTL_CRITICAL_SECTION W�#  CRITICAL_SECTION � h$  VIRTUAL_STORAGE_TYPE_VENDOR_UNKNOWN 
5	  VIRTUAL_STORAGE_TYPE_VENDOR_MICROSOFT 
5	  RPC_IF_HANDLE Bl  tagCOINITBASE �  �I%  COINITBASE_MULTITHREADED   IWinTypesBase_v0_1_c_ifspec *�$  IWinTypesBase_v0_1_s_ifspec +�$  IID_IUnknown a5	  IID_AsyncIUnknown �5	  IID_IClassFactory 85	  IID_IMarshal 5	  IID_INoMarshal �5	  IID_IAgileObject �5	  IID_IAgileReference >5	  IID_IMarshal2 �5	  IID_IMalloc 5	  IID_IStdMarshalInfo �5	  IID_IExternalConnection �5	  IID_IMultiQI b5	  IID_AsyncIMultiQI �5	  IID_IInternalUnknown 5	  IID_IEnumUnknown b5	  IID_IEnumString �5	  IID_ISequentialStream p5	  IID_IStream 5	  IID_IRpcChannelBuffer %	5	  IID_IRpcChannelBuffer2 �	5	  IID_IAsyncRpcChannelBuffer !
5	  IID_IRpcChannelBuffer3 �
5	  IID_IRpcSyntaxNegotiate �5	  IID_IRpcProxyBuffer �5	  IID_IRpcStubBuffer I5	  IID_IPSFactoryBuffer �5	  IID_IChannelHook H
5	  IID_IClientSecurity (5	  IID_IServerSecurity �5	  IID_IRpcOptions 05	  IID_IGlobalOptions �5	  IID_ISurrogate 5	  IID_IGlobalInterfaceTable t5	  IID_ISynchronize �5	  IID_ISynchronizeHandle F5	  IID_ISynchronizeEvent �5	  IID_ISynchronizeContainer �5	  IID_ISynchronizeMutex J5	  IID_ICancelMethodCalls �5	  IID_IAsyncManager 5	  IID_ICallFactory w5	  IID_IRpcHelper �5	  IID_IReleaseMarshalBuffers &5	  IID_IWaitMultiple x5	  IID_IAddrTrackingControl �5	  IID_IAddrExclusionControl ,5	  IID_IPipeByte �5	  IID_IPipeLong �5	  IID_IPipeDouble E5	  IID_IComThreadingInfo �5	  IID_IProcessInitControl 15	  IID_IFastRundown 5	  IID_IMarshalingStream �5	  IID_ICallbackWithNoReentrancyToApplicationSTA z5	  GUID_NULL 
F	  CATID_MARSHALER F	  IID_IRpcChannel F	  IID_IRpcStub F	  IID_IStubManager F	  IID_IRpcProxy F	  IID_IProxyManager F	  IID_IPSFactory F	  IID_IInternalMoniker F	  IID_IDfReserved1 F	  IID_IDfReserved2 F	  IID_IDfReserved3 F	  CLSID_StdMarshal Y	  CLSID_AggStdMarshal Y	  CLSID_StdAsyncActManager Y	  IID_IStub F	  IID_IProxy F	  IID_IEnumGeneric F	  IID_IEnumHolder F	  IID_IEnumCallback  F	  IID_IOleManager !F	  IID_IOlePresObj "F	  IID_IDebug #F	  IID_IDebugStream $F	  CLSID_PSGenObject %Y	  CLSID_PSClientSite &Y	  CLSID_PSClassObject 'Y	  CLSID_PSInPlaceActive (Y	  CLSID_PSInPlaceFrame )Y	  CLSID_PSDragDrop *Y	  CLSID_PSBindCtx +Y	  CLSID_PSEnumerators ,Y	  CLSID_StaticMetafile -Y	  CLSID_StaticDib .Y	  CID_CDfsVolume /Y	  CLSID_DCOMAccessControl 0Y	  CLSID_GlobalOptions 1Y	  CLSID_StdGlobalInterfaceTable 2Y	  CLSID_ComBinding 3Y	  CLSID_StdEvent 4Y	  CLSID_ManualResetEvent 5Y	  CLSID_SynchronizeContainer 6Y	  CLSID_AddrControl 7Y	  CLSID_CCDFormKrnl 8Y	  CLSID_CCDPropertyPage 9Y	  CLSID_CCDFormDialog :Y	  CLSID_CCDCommandButton ;Y	  CLSID_CCDComboBox <Y	  CLSID_CCDTextBox =Y	  CLSID_CCDCheckBox >Y	  CLSID_CCDLabel ?Y	  CLSID_CCDOptionButton @Y	  CLSID_CCDListBox AY	  CLSID_CCDScrollBar BY	  CLSID_CCDGroupBox CY	  CLSID_CCDGeneralPropertyPage DY	  CLSID_CCDGenericPropertyPage EY	  CLSID_CCDFontPropertyPage FY	  CLSID_CCDColorPropertyPage GY	  CLSID_CCDLabelPropertyPage HY	  CLSID_CCDCheckBoxPropertyPage IY	  CLSID_CCDTextBoxPropertyPage JY	  CLSID_CCDOptionButtonPropertyPage KY	  CLSID_CCDListBoxPropertyPage LY	  CLSID_CCDCommandButtonPropertyPage MY	  CLSID_CCDComboBoxPropertyPage NY	  CLSID_CCDScrollBarPropertyPage OY	  CLSID_CCDGroupBoxPropertyPage PY	  CLSID_CCDXObjectPropertyPage QY	  CLSID_CStdPropertyFrame RY	  CLSID_CFormPropertyPage SY	  CLSID_CGridPropertyPage TY	  CLSID_CWSJArticlePage UY	  CLSID_CSystemPage VY	  CLSID_IdentityUnmarshal WY	  CLSID_InProcFreeMarshaler XY	  CLSID_Picture_Metafile YY	  CLSID_Picture_EnhMetafile ZY	  CLSID_Picture_Dib [Y	  GUID_TRISTATE \5	  IWinTypes_v0_1_c_ifspec )�$  IWinTypes_v0_1_s_ifspec *�$  VARENUM �  _7  VT_EMPTY  VT_NULL VT_I2 VT_I4 VT_R4 VT_R8 VT_CY VT_DATE VT_BSTR VT_DISPATCH 	VT_ERROR 
VT_BOOL VT_VARIANT VT_UNKNOWN 
VT_DECIMAL VT_I1 VT_UI1 VT_UI2 VT_UI4 VT_I8 VT_UI8 VT_INT VT_UINT VT_VOID VT_HRESULT VT_PTR VT_SAFEARRAY VT_CARRAY VT_USERDEFINED VT_LPSTR VT_LPWSTR VT_RECORD $VT_INT_PTR %VT_UINT_PTR &VT_FILETIME @VT_BLOB AVT_STREAM BVT_STORAGE CVT_STREAMED_OBJECT DVT_STORED_OBJECT EVT_BLOB_OBJECT FVT_CF GVT_CLSID HVT_VERSIONED_STREAM I VT_BSTR_BLOB � VT_VECTOR   VT_ARRAY    VT_BYREF  @ VT_RESERVED  � VT_ILLEGAL �� VT_ILLEGALMASKED � VT_TYPEMASK � IID_IMallocSpy �5	  IID_IBindCtx :5	  IID_IEnumMoniker J 5	  IID_IRunnableObject � 5	  IID_IRunningObjectTable �!5	  IID_IPersist i"5	  IID_IPersistStream �"5	  IID_IMoniker j#5	  IID_IROTData X%5	  IID_IEnumSTATSTG �%5	  IID_IStorage X&5	  IID_IPersistFile A(5	  IID_IPersistStorage �(5	  IID_ILockBytes �)5	  IID_IEnumFORMATETC �*5	  IID_IEnumSTATDATA l+5	  IID_IRootStorage ,5	  IID_IAdviseSink �,5	  IID_AsyncIAdviseSink s-5	  IID_IAdviseSink2 �.5	  IID_AsyncIAdviseSink2 ./5	  IID_IDataObject �/5	  IID_IDataAdviseHolder 15	  IID_IMessageFilter �15	  FMTID_SummaryInformation ]2l	  FMTID_DocSummaryInformation _2l	  FMTID_UserDefinedProperties a2l	  FMTID_DiscardableInformation c2l	  FMTID_ImageSummaryInformation e2l	  FMTID_AudioSummaryInformation g2l	  FMTID_VideoSummaryInformation i2l	  FMTID_MediaFileSummaryInformation k2l	  IID_IClassActivator s25	  IID_IFillLockBytes �25	  IID_IProgressNotify �35	  IID_ILayoutStorage �35	  IID_IBlockingLock �45	  IID_ITimeAndNoticeControl �45	  IID_IOplockStorage N55	  IID_IDirectWriterLock �55	  IID_IUrlMon M65	  IID_IForegroundTransfer �65	  IID_IThumbnailExtractor 75	  IID_IDummyHICONIncluder �75	  IID_IProcessLock �75	  IID_ISurrogateService H85	  IID_IInitializeSpy �85	  IID_IApartmentShutdown �95	  IID_IOleAdviseHolder �5	  IID_IOleCache �5	  IID_IOleCache2 5	  IID_IOleCacheControl �5	  IID_IParseDisplayName 5	  IID_IOleContainer p5	  IID_IOleClientSite �5	  IID_IOleObject �5	  IOLETypes_v0_0_c_ifspec ��$  IOLETypes_v0_0_s_ifspec ��$  IID_IOleWindow 5	  IID_IOleLink k5	  IID_IOleItemContainer 95	  IID_IOleInPlaceUIWindow �5	  IID_IOleInPlaceActiveObject X5	  IID_IOleInPlaceFrame 	5	  IID_IOleInPlaceObject �	5	  IID_IOleInPlaceSite f
5	  IID_IContinue ,5	  IID_IViewObject {5	  IID_IViewObject2 �5	  IID_IDropSource 9
5	  IID_IDropTarget �
5	  IID_IDropSourceNotify 05	  IID_IEnumOLEVERB �5	  IID_IServiceProvider T5	  IOleAutomationTypes_v1_0_c_ifspec d�$  IOleAutomationTypes_v1_0_s_ifspec e�$  IID_ICreateTypeInfo �5	  IID_ICreateTypeInfo2 5	  IID_ICreateTypeLib �5	  IID_ICreateTypeLib2 �5	  IID_IDispatch l5	  IID_IEnumVARIANT  	5	  IID_ITypeComp �	5	  IID_ITypeInfo ]
5	  IID_ITypeInfo2 �5	  IID_ITypeLib �5	  IID_ITypeLib2 �5	  IID_ITypeChangeEvents 5	  IID_IErrorInfo l5	  IID_ICreateErrorInfo �5	  IID_ISupportErrorInfo d5	  IID_ITypeFactory �5	  IID_ITypeMarshal 5	  IID_IRecordInfo �5	  IID_IErrorLog �5	  IID_IPropertyBag �5	  __MIDL_itf_msxml_0000_v0_0_c_ifspec ��$  __MIDL_itf_msxml_0000_v0_0_s_ifspec ��$  LIBID_MSXML �F	  IID_IXMLDOMImplementation F	  IID_IXMLDOMNode (F	  IID_IXMLDOMDocumentFragment �F	  IID_IXMLDOMDocument gF	  IID_IXMLDOMNodeList vF	  IID_IXMLDOMNamedNodeMap �F	  IID_IXMLDOMCharacterData F	  IID_IXMLDOMAttribute �F	  IID_IXMLDOMElement F	  IID_IXMLDOMText �F	  IID_IXMLDOMComment &F	  IID_IXMLDOMProcessingInstruction �F	  IID_IXMLDOMCDATASection F	  IID_IXMLDOMDocumentType �F	  IID_IXMLDOMNotation F	  IID_IXMLDOMEntity �F	  IID_IXMLDOMEntityReference �F	  IID_IXMLDOMParseError b	F	  IID_IXTLRuntime �	F	  DIID_XMLDOMDocumentEvents >
F	  CLSID_DOMDocument ]
Y	  CLSID_DOMFreeThreadedDocument a
Y	  IID_IXMLHttpRequest h
F	  CLSID_XMLHTTPRequest �
Y	  IID_IXMLDSOControl �
F	  CLSID_XMLDSOControl Y	  IID_IXMLElementCollection F	  IID_IXMLDocument KF	  IID_IXMLDocument2 �F	  IID_IXMLElement %F	  IID_IXMLElement2 �F	  IID_IXMLAttribute �F	  IID_IXMLError 
F	  CLSID_XMLDocument /
Y	  CLSID_SBS_StdURLMoniker KF	  CLSID_SBS_HttpProtocol LF	  CLSID_SBS_FtpProtocol MF	  CLSID_SBS_GopherProtocol NF	  CLSID_SBS_HttpSProtocol OF	  CLSID_SBS_FileProtocol PF	  CLSID_SBS_MkProtocol QF	  CLSID_SBS_UrlMkBindCtx RF	  CLSID_SBS_SoftDistExt SF	  CLSID_SBS_CdlProtocol TF	  CLSID_SBS_ClassInstallFilter UF	  CLSID_SBS_InternetSecurityManager VF	  CLSID_SBS_InternetZoneManager WF	  IID_IAsyncMoniker `F	  CLSID_StdURLMoniker aF	  CLSID_HttpProtocol bF	  CLSID_FtpProtocol cF	  CLSID_GopherProtocol dF	  CLSID_HttpSProtocol eF	  CLSID_FileProtocol fF	  CLSID_MkProtocol gF	  CLSID_StdURLProtocol hF	  CLSID_UrlMkBindCtx iF	  CLSID_CdlProtocol jF	  CLSID_ClassInstallFilter kF	  IID_IAsyncBindCtx lF	  IID_IPersistMoniker 5	  IID_IMonikerProp �5	  IID_IBindProtocol 5	  IID_IBinding h5	  IID_IBindStatusCallback �5	  IID_IBindStatusCallbackEx �5	  IID_IAuthenticate �5	  IID_IAuthenticateEx �5	  IID_IHttpNegotiate ^5	  IID_IHttpNegotiate2 �5	  IID_IHttpNegotiate3 :	5	  IID_IWinInetFileStream �	5	  IID_IWindowForBindingUI 
5	  IID_ICodeInstall z
5	  IID_IUri 5	  IID_IUriContainer �5	  IID_IUriBuilder �5	  IID_IUriBuilderFactory ^5	  IID_IWinInetInfo �5	  IID_IHttpSecurity 65	  IID_IWinInetHttpInfo �5	  IID_IWinInetHttpTimeouts 5	  IID_IWinInetCacheHints o5	  IID_IWinInetCacheHints2 �5	  SID_BindHost 45	  IID_IBindHost >5	  IID_IInternet `5	  IID_IInternetBindInfo �5	  IID_IInternetBindInfoEx '5	  IID_IInternetProtocolRoot �5	  IID_IInternetProtocol I5	  IID_IInternetProtocolEx �5	  IID_IInternetProtocolSink �5	  IID_IInternetProtocolSinkStackable 15	  IID_IInternetSession �5	  IID_IInternetThreadSwitch \5	  IID_IInternetPriority �5	  IID_IInternetProtocolInfo F5	  CLSID_InternetSecurityManager wF	  CLSID_InternetZoneManager xF	  CLSID_PersistentZoneIdentifier {F	  IID_IInternetSecurityMgrSite �5	  IID_IInternetSecurityManager 5	  IID_IInternetSecurityManagerEx �5	  IID_IInternetSecurityManagerEx2 �5	  IID_IZoneIdentifier �5	  IID_IInternetHostSecurityManager 5	  GUID_CUSTOM_LOCALMACHINEZONEUNLOCKED X5	  IID_IInternetZoneManager �5	  IID_IInternetZoneManagerEx �5	  IID_IInternetZoneManagerEx2 � 5	  CLSID_SoftDistExt �!F	  IID_ISoftDistExt �!5	  IID_ICatalogFileInfo x"5	  IID_IDataFilter �"5	  IID_IEncodingFilterFactory p#5	  GUID_CUSTOM_CONFIRMOBJECTSAFETY �#5	  IID_IWrappedProtocol �#5	  IID_IGetBindHandle R$5	  IID_IBindCallbackRedirect �$5	  IID_IPropertyStorage �5	  IID_IPropertySetStorage �5	  IID_IEnumSTATPROPSTG *5	  IID_IEnumSTATPROPSETSTG �5	  IID_StdOle F	  GUID_DEVINTERFACE_DISK 5	  GUID_DEVINTERFACE_CDROM 
5	  GUID_DEVINTERFACE_PARTITION 5	  GUID_DEVINTERFACE_TAPE 5	  GUID_DEVINTERFACE_WRITEONCEDISK 5	  GUID_DEVINTERFACE_VOLUME 5	  GUID_DEVINTERFACE_MEDIUMCHANGER 5	  GUID_DEVINTERFACE_FLOPPY 5	  GUID_DEVINTERFACE_CDCHANGER 5	  GUID_DEVINTERFACE_STORAGEPORT 5	  GUID_DEVINTERFACE_COMPORT 5	  GUID_DEVINTERFACE_SERENUM_BUS_ENUMERATOR 5	  _SCARD_IO_REQUEST ��S  dwProtocol ��   cbPciLength ��   SCARD_IO_REQUEST �^S  �S  g_rgSCardT0Pci %.�S  g_rgSCardT1Pci %=�S  g_rgSCardRawPci %L�S  IID_IPrintDialogCallback  5	  IID_IPrintDialogServices  5	  _commode !$�  !@!%�T  osfhnd !&k   osfile !'
  pipech !(
  	lockinitflag !)	�  lock !*�$  "textmode !+
  8"unicode !,
   8pipech2 !-
�T  9   U  @   ioinfo !.ZT  U  U   "U  U  __imp___badioinfo !DU  __imp___pioinfo !IU  _dowildcard !f�  _newmode !g�  __imp___winitenv !j
  __imp___initenv !o�	  #  �  !��U  __uninitialized  __initializing __initialized  $  !��U  �U  __native_startup_state !�+V  __native_startup_lock !�DV  JV  %__native_dllmain_reason !� �  __native_vcclrit_reason !� �  __RUNTIME_PSEUDO_RELOC_LIST__ 0
  __RUNTIME_PSEUDO_RELOC_LIST_END__ 1
  __image_base__ 2
  !;	W  addend <	�   target =	�   runtime_pseudo_reloc_item_v1 >�V  !F	wW  sym G	�   target H	�  flags I	�   runtime_pseudo_reloc_item_v2 JAW  !L	�W  magic1 M	�   magic2 N	�  version O	�   runtime_pseudo_reloc_v2 P�W  &�  (�fX  old_protect �	�   base_address �	N  region_size �
?  sec_start �	�  hash �k"    $�  ��W  'the_secs ��X  	�h    fX  'maxSections ��  	�h    (_pei386_runtime_relocator �`>h    �      �\  )was_init ��  	 �h    *mSecs ��  c  a  +\  �>h    p  ��[  ,-\  ,<\  ,I\  -p  .W\  �  �  /i\  ��.z\  .    .�\  n  h  .�\  �  �  +�\  �@h    �  ��Y  0]  6  4  0]  \  Z  0�\  �    1�@h    ^  2Qu 2X4  +�\  e?h      �:Z  0]  �  �  0]  �  �  0�\  �  �  1p?h    ^  2Qu 2X1  +�\  �?h    P  ��Z  0]      0]  <  :  0�\  a  _  1�?h    ^  2Qu 2X2  +�\  �@h    �  ��Z  0]  �  �  0]  �  �  0�\  �  �  1�@h    ^  2Qu 2X8  3�\  �@h    Q       �[  .�\  �  �  4�\  �  /�\  ��5�\  Ah       l0]  G  E  0]  m  k  0�\  �  �  1Ah    ^  2Qu 2X4    68Ah    �]  �[  2R	�bh     1EAh    �]  2R	�bh       7]  �?h    R       �\  .B]  �  �  /L]  ��10@h    �_  2Yu   8�>h    �_   9do_pseudo_reloc /�\  :start /l  :end /'l  :base /3l  ;addr_imp 1
|  ;reldata 1|  ;reloc_target 2
|  ;v2_hdr 3�\  ;r 4!�\  <;o d&�\  <;newval i
�     �W  wW  W  9__write_memory ]  :addr l  :src )�  :len 51   =restore_modified_sections �]]  >i ��  >oldprot �	�   =mark_section_writable ��]  ?addr ��  >b ��  >h �k"  >i ��   @__report_error S�;h    j       �y^  Amsg Sy^  �  �  B'argp �!  �X6
<h    �_  
^  2R2 6'<h    `  6^  2R	 bh    2Q12XK 66<h    �_  M^  2R2 6D<h    `  k^  2Q| 2Xs  8J<h    )`     C�\  P<h    
      ��_  0�\  :  *  0]  �  �  0]  �  �  5]]  d<h       	0|]  �  �  -  /�]  ��.�]    
  .�]  �  �  6�<h    :`  $_  2R|  8�<h    F`  6�<h    R`  P_  2Q��2X0 6�=h    �_  n_  2X@2Yu  8�=h    ^`  6�=h    �]  �_  2R	�bh     6A>h    �]  �_  2R	@bh    2Q|  1]>h    �]  2R	`bh        D�  �  ">D�  �  �D�  �  #PEfwrite __builtin_fwrite % F�  �  #2Gabort abort 
�(D�  �  �D�  �  �D&  &  "3D    $. �a   1  GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt/crt_handler.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt PAh    p      �  char size_t (,�   long long unsigned int long long int wchar_t g6  short unsigned int 6  int long int pthreadlocinfo �(|  �  threadlocaleinfostruct `�,    �Q   	lc_codepage �y  	lc_collate_cp �y  	lc_handle ��  	lc_id �	�  $	lc_category ��  H
lc_clike �Q  
mb_cur_max �Q  
lconv_intl_refcount �s  
lconv_num_refcount �s  
lconv_mon_refcount �s   
lconv ��  (
ctype1_refcount �s  0
ctype1 ��  8
pctype ��  @
pclmap ��  H
pcumap ��  P
lc_time_curr �  X pthreadmbcinfo �%D  J  threadmbcinfostruct localeinfo_struct ��  	locinfo �d   	mbcinfo �,   _locale_tstruct �_  tagLC_ID �  	wLanguage �6   	wCountry �6  	wCodePage �6   LC_ID ��  
 �g  	locale �g   	wlocale �m    �
s  	wrefcount �
s   �   &  Q  unsigned int �  �  �    long unsigned int   �  �      �  �    lconv �  6  L  �  unsigned char �  __lc_time_data   _PHNDLR B)  /  :  Q   _XCPT_ACTION D
�  XcptNum E�   SigNum F	Q  XcptAction G
   :  �   _XcptActTab J�  _XcptActTabCount KQ  _XcptActTabSize LQ  _First_FPE_Indx MQ  _Num_FPE NQ    _EXCEPTION_RECORD ��
�  	ExceptionCode �


   	ExceptionFlags �


  �  �
!�  	ExceptionAddress �

�  	NumberParameters �


  	ExceptionInformation �
X    �  _CONTEXT ��%�	  	P1Home �
�   	P2Home �
�  	P3Home �
�  	P4Home �
�  	P5Home �
�   	P6Home �
�  (	ContextFlags �
  0	MxCsr �
  4	SegCs �
�	  8	SegDs �
�	  :	SegEs �
�	  <	SegFs �
�	  >	SegGs �
�	  @	SegSs �
�	  B	EFlags �
  D	Dr0 �
�  H	Dr1 �
�  P	Dr2 �
�  X	Dr3 �
�  `	Dr6 �
�  h	Dr7 �
�  p	Rax �
�  x	Rcx �
�  �	Rdx �
�  �	Rbx �
�  �	Rsp �
�  �	Rbp �
�  �	Rsi �
�  �	Rdi �
�  �	R8 �
�  �	R9 �
�  �	R10 �
�  �	R11 �
�  �	R12 �
�  �	R13 �
�  �	R14 �
�  �	R15 �
�  �	Rip �
�  ��   VectorRegister ��   
VectorControl �
�  �
DebugControl �
�  �
LastBranchToRip �
�  �
LastBranchFromRip �
�  �
LastExceptionToRip �
�  �
LastExceptionFromRip �
�  � ULONG �  BYTE ��  WORD �6  DWORD ��  float PBYTE �)
  �	  LPBYTE �)
  y  __imp__pctype +Z
  �  __imp__wctype ;Z
  __imp__pwctype GZ
  �  �
   �
  __newclmap P�
  __newcumap Q�
  __ptlocinfo Rd  __ptmbcinfo S,  __globallocalestatus TQ  __locale_changed UQ  __initiallocinfo V(�  __initiallocalestructinfo W�  __imp___mb_cur_max �s  signed char short int ULONG_PTR 1.�   DWORD64 �.�   PVOID �  LONG X  LONGLONG �%  ULONGLONG �.�   _GUID C  Data1 �   Data2 6  Data3 6  Data4 C   �  S  �    GUID �  S  IID XS  e  CLSID `S  v  FMTID gS  �  _M128A j(�  	Low k�   	High l�   M128A m�  �  �  �    �  �  �    �	  
  �   _ double long double g  7
  �     _sys_errlist 	�&'
  _sys_nerr 	�$Q  __imp___argc 	s  __imp___argv 	�
  �
  g  __imp___wargv 	"�
  �
  m  __imp__environ 	B�
  __imp__wenviron 	G�
  __imp__pgmptr 	N�
  __imp__wpgmptr 	S�
  __imp__osplatform 	X>
  __imp__osver 	]>
  __imp__winver 	b>
  __imp__winmajor 	g>
  __imp__winminor 	l>
  _amblksiz 
5y  _XMM_SAVE_AREA32  �  	ControlWord �
�	   	StatusWord �
�	  	TagWord �
�	  	Reserved1 �
�	  	ErrorOpcode �
�	  	ErrorOffset �
  	ErrorSelector �
�	  	Reserved2 �
�	  	DataOffset �
  	DataSelector �
�	  	Reserved3 �
�	  	MxCsr �
  	MxCsr_Mask �
  FloatRegisters ��   XmmRegisters ��  �
Reserved4 �
�  � XMM_SAVE_AREA32 ��  ��s  Header �s   Legacy ��   Xmm0 ��  �Xmm1 ��  �Xmm2 ��  �Xmm3 ��  �Xmm4 ��  �Xmm5 ��  �Xmm6 ��   Xmm7 ��  Xmm8 ��   Xmm9 ��  0Xmm10 ��  @Xmm11 ��  PXmm12 ��  `Xmm13 ��  pXmm14 ��  �Xmm15 ��  � �  �  �      ��  !FltSave �  !FloatSave �  "2   �  �  �    PCONTEXT ��  _RUNTIME_FUNCTION �>  	BeginAddress �
   	EndAddress �
  	UnwindData �
   RUNTIME_FUNCTION ��  �  h  �    EXCEPTION_RECORD �
  PEXCEPTION_RECORD �
�  h  _EXCEPTION_POINTERS �
�  �  �
�   �  �
�   EXCEPTION_POINTERS �
�  �  GUID_MAX_POWER_SAVINGS `  GUID_MIN_POWER_SAVINGS `  GUID_TYPICAL_POWER_SAVINGS `  NO_SUBGROUP_GUID `  ALL_POWERSCHEMES_GUID  `  GUID_POWERSCHEME_PERSONALITY !`  GUID_ACTIVE_POWERSCHEME "`  GUID_IDLE_RESILIENCY_SUBGROUP #`  GUID_IDLE_RESILIENCY_PERIOD $`  GUID_DISK_COALESCING_POWERDOWN_TIMEOUT %`  GUID_EXECUTION_REQUIRED_REQUEST_TIMEOUT &`  GUID_VIDEO_SUBGROUP '`  GUID_VIDEO_POWERDOWN_TIMEOUT (`  GUID_VIDEO_ANNOYANCE_TIMEOUT )`  GUID_VIDEO_ADAPTIVE_PERCENT_INCREASE *`  GUID_VIDEO_DIM_TIMEOUT +`  GUID_VIDEO_ADAPTIVE_POWERDOWN ,`  GUID_MONITOR_POWER_ON -`  GUID_DEVICE_POWER_POLICY_VIDEO_BRIGHTNESS .`  GUID_DEVICE_POWER_POLICY_VIDEO_DIM_BRIGHTNESS /`  GUID_VIDEO_CURRENT_MONITOR_BRIGHTNESS 0`  GUID_VIDEO_ADAPTIVE_DISPLAY_BRIGHTNESS 1`  GUID_CONSOLE_DISPLAY_STATE 2`  GUID_ALLOW_DISPLAY_REQUIRED 3`  GUID_VIDEO_CONSOLE_LOCK_TIMEOUT 4`  GUID_ADAPTIVE_POWER_BEHAVIOR_SUBGROUP 5`  GUID_NON_ADAPTIVE_INPUT_TIMEOUT 6`  GUID_DISK_SUBGROUP 7`  GUID_DISK_POWERDOWN_TIMEOUT 8`  GUID_DISK_IDLE_TIMEOUT 9`  GUID_DISK_BURST_IGNORE_THRESHOLD :`  GUID_DISK_ADAPTIVE_POWERDOWN ;`  GUID_SLEEP_SUBGROUP <`  GUID_SLEEP_IDLE_THRESHOLD =`  GUID_STANDBY_TIMEOUT >`  GUID_UNATTEND_SLEEP_TIMEOUT ?`  GUID_HIBERNATE_TIMEOUT @`  GUID_HIBERNATE_FASTS4_POLICY A`  GUID_CRITICAL_POWER_TRANSITION B`  GUID_SYSTEM_AWAYMODE C`  GUID_ALLOW_AWAYMODE D`  GUID_ALLOW_STANDBY_STATES E`  GUID_ALLOW_RTC_WAKE F`  GUID_ALLOW_SYSTEM_REQUIRED G`  GUID_SYSTEM_BUTTON_SUBGROUP H`  GUID_POWERBUTTON_ACTION I`  GUID_SLEEPBUTTON_ACTION J`  GUID_USERINTERFACEBUTTON_ACTION K`  GUID_LIDCLOSE_ACTION L`  GUID_LIDOPEN_POWERSTATE M`  GUID_BATTERY_SUBGROUP N`  GUID_BATTERY_DISCHARGE_ACTION_0 O`  GUID_BATTERY_DISCHARGE_LEVEL_0 P`  GUID_BATTERY_DISCHARGE_FLAGS_0 Q`  GUID_BATTERY_DISCHARGE_ACTION_1 R`  GUID_BATTERY_DISCHARGE_LEVEL_1 S`  GUID_BATTERY_DISCHARGE_FLAGS_1 T`  GUID_BATTERY_DISCHARGE_ACTION_2 U`  GUID_BATTERY_DISCHARGE_LEVEL_2 V`  GUID_BATTERY_DISCHARGE_FLAGS_2 W`  GUID_BATTERY_DISCHARGE_ACTION_3 X`  GUID_BATTERY_DISCHARGE_LEVEL_3 Y`  GUID_BATTERY_DISCHARGE_FLAGS_3 Z`  GUID_PROCESSOR_SETTINGS_SUBGROUP [`  GUID_PROCESSOR_THROTTLE_POLICY \`  GUID_PROCESSOR_THROTTLE_MAXIMUM ]`  GUID_PROCESSOR_THROTTLE_MINIMUM ^`  GUID_PROCESSOR_ALLOW_THROTTLING _`  GUID_PROCESSOR_IDLESTATE_POLICY ``  GUID_PROCESSOR_PERFSTATE_POLICY a`  GUID_PROCESSOR_PERF_INCREASE_THRESHOLD b`  GUID_PROCESSOR_PERF_DECREASE_THRESHOLD c`  GUID_PROCESSOR_PERF_INCREASE_POLICY d`  GUID_PROCESSOR_PERF_DECREASE_POLICY e`  GUID_PROCESSOR_PERF_INCREASE_TIME f`  GUID_PROCESSOR_PERF_DECREASE_TIME g`  GUID_PROCESSOR_PERF_TIME_CHECK h`  GUID_PROCESSOR_PERF_BOOST_POLICY i`  GUID_PROCESSOR_PERF_BOOST_MODE j`  GUID_PROCESSOR_IDLE_ALLOW_SCALING k`  GUID_PROCESSOR_IDLE_DISABLE l`  GUID_PROCESSOR_IDLE_STATE_MAXIMUM m`  GUID_PROCESSOR_IDLE_TIME_CHECK n`  GUID_PROCESSOR_IDLE_DEMOTE_THRESHOLD o`  GUID_PROCESSOR_IDLE_PROMOTE_THRESHOLD p`  GUID_PROCESSOR_CORE_PARKING_INCREASE_THRESHOLD q`  GUID_PROCESSOR_CORE_PARKING_DECREASE_THRESHOLD r`  GUID_PROCESSOR_CORE_PARKING_INCREASE_POLICY s`  GUID_PROCESSOR_CORE_PARKING_DECREASE_POLICY t`  GUID_PROCESSOR_CORE_PARKING_MAX_CORES u`  GUID_PROCESSOR_CORE_PARKING_MIN_CORES v`  GUID_PROCESSOR_CORE_PARKING_INCREASE_TIME w`  GUID_PROCESSOR_CORE_PARKING_DECREASE_TIME x`  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_DECREASE_FACTOR y`  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_THRESHOLD z`  GUID_PROCESSOR_CORE_PARKING_AFFINITY_WEIGHTING {`  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_DECREASE_FACTOR |`  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_THRESHOLD }`  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_WEIGHTING ~`  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_THRESHOLD `  GUID_PROCESSOR_PARKING_CORE_OVERRIDE �`  GUID_PROCESSOR_PARKING_PERF_STATE �`  GUID_PROCESSOR_PARKING_CONCURRENCY_THRESHOLD �`  GUID_PROCESSOR_PARKING_HEADROOM_THRESHOLD �`  GUID_PROCESSOR_PERF_HISTORY �`  GUID_PROCESSOR_PERF_LATENCY_HINT �`  GUID_PROCESSOR_DISTRIBUTE_UTILITY �`  GUID_SYSTEM_COOLING_POLICY �`  GUID_LOCK_CONSOLE_ON_WAKE �`  GUID_DEVICE_IDLE_POLICY �`  GUID_ACDC_POWER_SOURCE �`  GUID_LIDSWITCH_STATE_CHANGE �`  GUID_BATTERY_PERCENTAGE_REMAINING �`  GUID_GLOBAL_USER_PRESENCE �`  GUID_SESSION_DISPLAY_STATUS �`  GUID_SESSION_USER_PRESENCE �`  GUID_IDLE_BACKGROUND_TASK �`  GUID_BACKGROUND_TASK_NOTIFICATION �`  GUID_APPLAUNCH_BUTTON �`  GUID_PCIEXPRESS_SETTINGS_SUBGROUP �`  GUID_PCIEXPRESS_ASPM_POLICY �`  GUID_ENABLE_SWITCH_FORCED_SHUTDOWN �`  PPM_PERFSTATE_CHANGE_GUID �`  PPM_PERFSTATE_DOMAIN_CHANGE_GUID �`  PPM_IDLESTATE_CHANGE_GUID �`  PPM_PERFSTATES_DATA_GUID �`  PPM_IDLESTATES_DATA_GUID �`  PPM_IDLE_ACCOUNTING_GUID �`  PPM_IDLE_ACCOUNTING_EX_GUID �`  PPM_THERMALCONSTRAINT_GUID �`  PPM_PERFMON_PERFSTATE_GUID �`  PPM_THERMAL_POLICY_CHANGE_GUID �`  �	  3(  �    _IMAGE_DOS_HEADER @q�)  	e_magic r�	   	e_cblp s�	  	e_cp t�	  	e_crlc u�	  	e_cparhdr v�	  	e_minalloc w�	  
	e_maxalloc x�	  	e_ss y�	  	e_sp z�	  	e_csum {�	  	e_ip |�	  	e_cs }�	  	e_lfarlc ~�	  	e_ovno �	  	e_res ��)  	e_oemid ��	  $	e_oeminfo ��	  &	e_res2 ��)  (	e_lfanew ��  < �	  �)  �    �	  �)  �   	 IMAGE_DOS_HEADER �3(  #�*  $PhysicalAddress �
  $VirtualSize  
   _IMAGE_SECTION_HEADER (�.+  	Name �#(   	Misc 	�)  	VirtualAddress 

  	SizeOfRawData 

  	PointerToRawData 

  	PointerToRelocations 

  	PointerToLinenumbers 

  	NumberOfRelocations �	   	NumberOfLinenumbers �	  "	Characteristics 	

  $ PIMAGE_SECTION_HEADER 
M+  *  Y+  %�  h+  �   PTOP_LEVEL_EXCEPTION_FILTER S+  LPTOP_LEVEL_EXCEPTION_FILTER %h+  VIRTUAL_STORAGE_TYPE_VENDOR_UNKNOWN `  VIRTUAL_STORAGE_TYPE_VENDOR_MICROSOFT `  RPC_IF_HANDLE 
B�  &tagCOINITBASE y  �[,  'COINITBASE_MULTITHREADED   IWinTypesBase_v0_1_c_ifspec *
,  IWinTypesBase_v0_1_s_ifspec +
,  IID_IUnknown a`  IID_AsyncIUnknown �`  IID_IClassFactory 8`  IID_IMarshal `  IID_INoMarshal �`  IID_IAgileObject �`  IID_IAgileReference >`  IID_IMarshal2 �`  IID_IMalloc `  IID_IStdMarshalInfo �`  IID_IExternalConnection �`  IID_IMultiQI b`  IID_AsyncIMultiQI �`  IID_IInternalUnknown `  IID_IEnumUnknown b`  IID_IEnumString �`  IID_ISequentialStream p`  IID_IStream `  IID_IRpcChannelBuffer %	`  IID_IRpcChannelBuffer2 �	`  IID_IAsyncRpcChannelBuffer !
`  IID_IRpcChannelBuffer3 �
`  IID_IRpcSyntaxNegotiate �`  IID_IRpcProxyBuffer �`  IID_IRpcStubBuffer I`  IID_IPSFactoryBuffer �`  IID_IChannelHook H
`  IID_IClientSecurity (`  IID_IServerSecurity �`  IID_IRpcOptions 0`  IID_IGlobalOptions �`  IID_ISurrogate `  IID_IGlobalInterfaceTable t`  IID_ISynchronize �`  IID_ISynchronizeHandle F`  IID_ISynchronizeEvent �`  IID_ISynchronizeContainer �`  IID_ISynchronizeMutex J`  IID_ICancelMethodCalls �`  IID_IAsyncManager `  IID_ICallFactory w`  IID_IRpcHelper �`  IID_IReleaseMarshalBuffers &`  IID_IWaitMultiple x`  IID_IAddrTrackingControl �`  IID_IAddrExclusionControl ,`  IID_IPipeByte �`  IID_IPipeLong �`  IID_IPipeDouble E`  IID_IComThreadingInfo �`  IID_IProcessInitControl 1`  IID_IFastRundown `  IID_IMarshalingStream �`  IID_ICallbackWithNoReentrancyToApplicationSTA z`  GUID_NULL 
q  CATID_MARSHALER q  IID_IRpcChannel q  IID_IRpcStub q  IID_IStubManager q  IID_IRpcProxy q  IID_IProxyManager q  IID_IPSFactory q  IID_IInternalMoniker q  IID_IDfReserved1 q  IID_IDfReserved2 q  IID_IDfReserved3 q  CLSID_StdMarshal �  CLSID_AggStdMarshal �  CLSID_StdAsyncActManager �  IID_IStub q  IID_IProxy q  IID_IEnumGeneric q  IID_IEnumHolder q  IID_IEnumCallback  q  IID_IOleManager !q  IID_IOlePresObj "q  IID_IDebug #q  IID_IDebugStream $q  CLSID_PSGenObject %�  CLSID_PSClientSite &�  CLSID_PSClassObject '�  CLSID_PSInPlaceActive (�  CLSID_PSInPlaceFrame )�  CLSID_PSDragDrop *�  CLSID_PSBindCtx +�  CLSID_PSEnumerators ,�  CLSID_StaticMetafile -�  CLSID_StaticDib .�  CID_CDfsVolume /�  CLSID_DCOMAccessControl 0�  CLSID_GlobalOptions 1�  CLSID_StdGlobalInterfaceTable 2�  CLSID_ComBinding 3�  CLSID_StdEvent 4�  CLSID_ManualResetEvent 5�  CLSID_SynchronizeContainer 6�  CLSID_AddrControl 7�  CLSID_CCDFormKrnl 8�  CLSID_CCDPropertyPage 9�  CLSID_CCDFormDialog :�  CLSID_CCDCommandButton ;�  CLSID_CCDComboBox <�  CLSID_CCDTextBox =�  CLSID_CCDCheckBox >�  CLSID_CCDLabel ?�  CLSID_CCDOptionButton @�  CLSID_CCDListBox A�  CLSID_CCDScrollBar B�  CLSID_CCDGroupBox C�  CLSID_CCDGeneralPropertyPage D�  CLSID_CCDGenericPropertyPage E�  CLSID_CCDFontPropertyPage F�  CLSID_CCDColorPropertyPage G�  CLSID_CCDLabelPropertyPage H�  CLSID_CCDCheckBoxPropertyPage I�  CLSID_CCDTextBoxPropertyPage J�  CLSID_CCDOptionButtonPropertyPage K�  CLSID_CCDListBoxPropertyPage L�  CLSID_CCDCommandButtonPropertyPage M�  CLSID_CCDComboBoxPropertyPage N�  CLSID_CCDScrollBarPropertyPage O�  CLSID_CCDGroupBoxPropertyPage P�  CLSID_CCDXObjectPropertyPage Q�  CLSID_CStdPropertyFrame R�  CLSID_CFormPropertyPage S�  CLSID_CGridPropertyPage T�  CLSID_CWSJArticlePage U�  CLSID_CSystemPage V�  CLSID_IdentityUnmarshal W�  CLSID_InProcFreeMarshaler X�  CLSID_Picture_Metafile Y�  CLSID_Picture_EnhMetafile Z�  CLSID_Picture_Dib [�  GUID_TRISTATE \`  IWinTypes_v0_1_c_ifspec )
,  IWinTypes_v0_1_s_ifspec *
,  (VARENUM y  q>  'VT_EMPTY  'VT_NULL 'VT_I2 'VT_I4 'VT_R4 'VT_R8 'VT_CY 'VT_DATE 'VT_BSTR 'VT_DISPATCH 	'VT_ERROR 
'VT_BOOL 'VT_VARIANT 'VT_UNKNOWN 
'VT_DECIMAL 'VT_I1 'VT_UI1 'VT_UI2 'VT_UI4 'VT_I8 'VT_UI8 'VT_INT 'VT_UINT 'VT_VOID 'VT_HRESULT 'VT_PTR 'VT_SAFEARRAY 'VT_CARRAY 'VT_USERDEFINED 'VT_LPSTR 'VT_LPWSTR 'VT_RECORD $'VT_INT_PTR %'VT_UINT_PTR &'VT_FILETIME @'VT_BLOB A'VT_STREAM B'VT_STORAGE C'VT_STREAMED_OBJECT D'VT_STORED_OBJECT E'VT_BLOB_OBJECT F'VT_CF G'VT_CLSID H'VT_VERSIONED_STREAM I)VT_BSTR_BLOB �)VT_VECTOR  )VT_ARRAY   )VT_BYREF  @)VT_RESERVED  �)VT_ILLEGAL ��)VT_ILLEGALMASKED �)VT_TYPEMASK � IID_IMallocSpy �`  IID_IBindCtx :`  IID_IEnumMoniker J `  IID_IRunnableObject � `  IID_IRunningObjectTable �!`  IID_IPersist i"`  IID_IPersistStream �"`  IID_IMoniker j#`  IID_IROTData X%`  IID_IEnumSTATSTG �%`  IID_IStorage X&`  IID_IPersistFile A(`  IID_IPersistStorage �(`  IID_ILockBytes �)`  IID_IEnumFORMATETC �*`  IID_IEnumSTATDATA l+`  IID_IRootStorage ,`  IID_IAdviseSink �,`  IID_AsyncIAdviseSink s-`  IID_IAdviseSink2 �.`  IID_AsyncIAdviseSink2 ./`  IID_IDataObject �/`  IID_IDataAdviseHolder 1`  IID_IMessageFilter �1`  FMTID_SummaryInformation ]2�  FMTID_DocSummaryInformation _2�  FMTID_UserDefinedProperties a2�  FMTID_DiscardableInformation c2�  FMTID_ImageSummaryInformation e2�  FMTID_AudioSummaryInformation g2�  FMTID_VideoSummaryInformation i2�  FMTID_MediaFileSummaryInformation k2�  IID_IClassActivator s2`  IID_IFillLockBytes �2`  IID_IProgressNotify �3`  IID_ILayoutStorage �3`  IID_IBlockingLock �4`  IID_ITimeAndNoticeControl �4`  IID_IOplockStorage N5`  IID_IDirectWriterLock �5`  IID_IUrlMon M6`  IID_IForegroundTransfer �6`  IID_IThumbnailExtractor 7`  IID_IDummyHICONIncluder �7`  IID_IProcessLock �7`  IID_ISurrogateService H8`  IID_IInitializeSpy �8`  IID_IApartmentShutdown �9`  IID_IOleAdviseHolder �`  IID_IOleCache �`  IID_IOleCache2 `  IID_IOleCacheControl �`  IID_IParseDisplayName `  IID_IOleContainer p`  IID_IOleClientSite �`  IID_IOleObject �`  IOLETypes_v0_0_c_ifspec �
,  IOLETypes_v0_0_s_ifspec �
,  IID_IOleWindow `  IID_IOleLink k`  IID_IOleItemContainer 9`  IID_IOleInPlaceUIWindow �`  IID_IOleInPlaceActiveObject X`  IID_IOleInPlaceFrame 	`  IID_IOleInPlaceObject �	`  IID_IOleInPlaceSite f
`  IID_IContinue ,`  IID_IViewObject {`  IID_IViewObject2 �`  IID_IDropSource 9
`  IID_IDropTarget �
`  IID_IDropSourceNotify 0`  IID_IEnumOLEVERB �`  IID_IServiceProvider T`  IOleAutomationTypes_v1_0_c_ifspec d
,  IOleAutomationTypes_v1_0_s_ifspec e
,  IID_ICreateTypeInfo �`  IID_ICreateTypeInfo2 `  IID_ICreateTypeLib �`  IID_ICreateTypeLib2 �`  IID_IDispatch l`  IID_IEnumVARIANT  	`  IID_ITypeComp �	`  IID_ITypeInfo ]
`  IID_ITypeInfo2 �`  IID_ITypeLib �`  IID_ITypeLib2 �`  IID_ITypeChangeEvents `  IID_IErrorInfo l`  IID_ICreateErrorInfo �`  IID_ISupportErrorInfo d`  IID_ITypeFactory �`  IID_ITypeMarshal `  IID_IRecordInfo �`  IID_IErrorLog �`  IID_IPropertyBag �`  __MIDL_itf_msxml_0000_v0_0_c_ifspec �
,  __MIDL_itf_msxml_0000_v0_0_s_ifspec �
,  LIBID_MSXML �q  IID_IXMLDOMImplementation q  IID_IXMLDOMNode (q  IID_IXMLDOMDocumentFragment �q  IID_IXMLDOMDocument gq  IID_IXMLDOMNodeList vq  IID_IXMLDOMNamedNodeMap �q  IID_IXMLDOMCharacterData q  IID_IXMLDOMAttribute �q  IID_IXMLDOMElement q  IID_IXMLDOMText �q  IID_IXMLDOMComment &q  IID_IXMLDOMProcessingInstruction �q  IID_IXMLDOMCDATASection q  IID_IXMLDOMDocumentType �q  IID_IXMLDOMNotation q  IID_IXMLDOMEntity �q  IID_IXMLDOMEntityReference �q  IID_IXMLDOMParseError b	q  IID_IXTLRuntime �	q  DIID_XMLDOMDocumentEvents >
q  CLSID_DOMDocument ]
�  CLSID_DOMFreeThreadedDocument a
�  IID_IXMLHttpRequest h
q  CLSID_XMLHTTPRequest �
�  IID_IXMLDSOControl �
q  CLSID_XMLDSOControl �  IID_IXMLElementCollection q  IID_IXMLDocument Kq  IID_IXMLDocument2 �q  IID_IXMLElement %q  IID_IXMLElement2 �q  IID_IXMLAttribute �q  IID_IXMLError 
q  CLSID_XMLDocument /
�  CLSID_SBS_StdURLMoniker Kq  CLSID_SBS_HttpProtocol Lq  CLSID_SBS_FtpProtocol Mq  CLSID_SBS_GopherProtocol Nq  CLSID_SBS_HttpSProtocol Oq  CLSID_SBS_FileProtocol Pq  CLSID_SBS_MkProtocol Qq  CLSID_SBS_UrlMkBindCtx Rq  CLSID_SBS_SoftDistExt Sq  CLSID_SBS_CdlProtocol Tq  CLSID_SBS_ClassInstallFilter Uq  CLSID_SBS_InternetSecurityManager Vq  CLSID_SBS_InternetZoneManager Wq  IID_IAsyncMoniker `q  CLSID_StdURLMoniker aq  CLSID_HttpProtocol bq  CLSID_FtpProtocol cq  CLSID_GopherProtocol dq  CLSID_HttpSProtocol eq  CLSID_FileProtocol fq  CLSID_MkProtocol gq  CLSID_StdURLProtocol hq  CLSID_UrlMkBindCtx iq  CLSID_CdlProtocol jq  CLSID_ClassInstallFilter kq  IID_IAsyncBindCtx lq  IID_IPersistMoniker `  IID_IMonikerProp �`  IID_IBindProtocol `  IID_IBinding h`  IID_IBindStatusCallback �`  IID_IBindStatusCallbackEx �`  IID_IAuthenticate �`  IID_IAuthenticateEx �`  IID_IHttpNegotiate ^`  IID_IHttpNegotiate2 �`  IID_IHttpNegotiate3 :	`  IID_IWinInetFileStream �	`  IID_IWindowForBindingUI 
`  IID_ICodeInstall z
`  IID_IUri `  IID_IUriContainer �`  IID_IUriBuilder �`  IID_IUriBuilderFactory ^`  IID_IWinInetInfo �`  IID_IHttpSecurity 6`  IID_IWinInetHttpInfo �`  IID_IWinInetHttpTimeouts `  IID_IWinInetCacheHints o`  IID_IWinInetCacheHints2 �`  SID_BindHost 4`  IID_IBindHost >`  IID_IInternet ``  IID_IInternetBindInfo �`  IID_IInternetBindInfoEx '`  IID_IInternetProtocolRoot �`  IID_IInternetProtocol I`  IID_IInternetProtocolEx �`  IID_IInternetProtocolSink �`  IID_IInternetProtocolSinkStackable 1`  IID_IInternetSession �`  IID_IInternetThreadSwitch \`  IID_IInternetPriority �`  IID_IInternetProtocolInfo F`  CLSID_InternetSecurityManager wq  CLSID_InternetZoneManager xq  CLSID_PersistentZoneIdentifier {q  IID_IInternetSecurityMgrSite �`  IID_IInternetSecurityManager `  IID_IInternetSecurityManagerEx �`  IID_IInternetSecurityManagerEx2 �`  IID_IZoneIdentifier �`  IID_IInternetHostSecurityManager `  GUID_CUSTOM_LOCALMACHINEZONEUNLOCKED X`  IID_IInternetZoneManager �`  IID_IInternetZoneManagerEx �`  IID_IInternetZoneManagerEx2 � `  CLSID_SoftDistExt �!q  IID_ISoftDistExt �!`  IID_ICatalogFileInfo x"`  IID_IDataFilter �"`  IID_IEncodingFilterFactory p#`  GUID_CUSTOM_CONFIRMOBJECTSAFETY �#`  IID_IWrappedProtocol �#`  IID_IGetBindHandle R$`  IID_IBindCallbackRedirect �$`  IID_IPropertyStorage �`  IID_IPropertySetStorage �`  IID_IEnumSTATPROPSTG *`  IID_IEnumSTATPROPSETSTG �`  IID_StdOle q  GUID_DEVINTERFACE_DISK `  GUID_DEVINTERFACE_CDROM 
`  GUID_DEVINTERFACE_PARTITION `  GUID_DEVINTERFACE_TAPE `  GUID_DEVINTERFACE_WRITEONCEDISK `  GUID_DEVINTERFACE_VOLUME `  GUID_DEVINTERFACE_MEDIUMCHANGER `  GUID_DEVINTERFACE_FLOPPY `  GUID_DEVINTERFACE_CDCHANGER `  GUID_DEVINTERFACE_STORAGEPORT `  GUID_DEVINTERFACE_COMPORT `  GUID_DEVINTERFACE_SERENUM_BUS_ENUMERATOR `  _SCARD_IO_REQUEST ��Z  dwProtocol �
   cbPciLength �
   SCARD_IO_REQUEST �pZ  �Z  g_rgSCardT0Pci %.�Z  g_rgSCardT1Pci %=�Z  g_rgSCardRawPci %L�Z  IID_IPrintDialogCallback `  IID_IPrintDialogServices `  __p_sig_fn_t  0)  __image_base__ �)  _UNWIND_INFO *\  VersionAndFlags �	   PrologSize �	  CountOfUnwindCodes �	  FrameRegisterAndOffset �	  AddressOfExceptionHandler  	�	   UNWIND_INFO !�[  >  N\  �    *emu_pdata />\  	@�h    *\  z\  �    *emu_xdata 0j\  	@�h    +__mingw_oldexcpt_handler ��+  	 �h    ,_gnu_exception_handler �X  Dh    �      ��^  -exception_data �-�^  �  �  .L  �
)  �  �  /action �X  j  P  .�  �Q  �  �  0jDh    �a  p]  1R81Q0 0�Dh    �a  �]  1R81Q1 2�Dh    �a  0�Dh    �a  �]  1R41Q0 3�Dh    �]  1R4 4�Dh    �]  1R�R 0%Eh    �a  �]  1R81Q0 37Eh    ^  1R8 0TEh    �a  (^  1R;1Q0 3fEh    ;^  1R; 0Eh    �a  W^  1R41Q1 0�Eh    �a  s^  1R81Q1 5�Eh    �a  1R;1Q1  �  ,__mingw_SEH_error_handler ^Q  PAh    �      ��`  6�  ^6�  +    -EstablisherFrame _
�      6�  `�  
  �  -DispatcherContext a
�  �  �  /action cQ  �   �   .L  d
)  �!  �!  .�  eQ  �"  �"  0�Ah    �a  �_  1R81Q0 0�Ah    �a  �_  1R81Q1 2�Ah    �a  0�Ah    �a  �_  1R41Q0 3Bh    �_  1R4 03Bh    �a  `  1R81Q0 3SBh    `  1R8 0lBh    �a  3`  1R;1Q0 3~Bh    F`  1R; 0�Bh    �a  b`  1R41Q1 0�Bh    �a  ~`  1R81Q1 5�Bh    �a  1R;1Q1  ,__mingw_init_ehandler 3Q  Ch    �       ��a  *was_here 5Q  	(�h    /e 6
�   �"  �"  /pSec 7.+  i#  e#  /_ImageBase 8	
  �#  �#  2Ch    �a  0GCh    �a  Ta  1R	Xch     0�Ch    �a  la  1R|  5Dh    �a  1R	@�h    1Xt   7signal signal  <8�  �  [
8<  <  &8�  �  $8X  X  %9k  k  � '   �  GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt/tlsthrd.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt �Eh    `      N  char long long unsigned int long long int wchar_t g#  short unsigned int #  int >  long int pthreadlocinfo �(n  t  threadlocaleinfostruct `�  	  �>   
lc_codepage �k  
lc_collate_cp �k  
lc_handle �{  
lc_id �	�  $
lc_category ��  Hlc_clike �>  mb_cur_max �>  lconv_intl_refcount �e  lconv_num_refcount �e  lconv_mon_refcount �e   lconv ��  (ctype1_refcount �e  0ctype1 ��  8pctype ��  @pclmap ��  Hpcumap ��  Plc_time_curr �  X pthreadmbcinfo �%6  <  threadmbcinfostruct 
localeinfo_struct ��  
locinfo �V   
mbcinfo �   _locale_tstruct �Q  
tagLC_ID ��  
wLanguage �#   
wCountry �#  
wCodePage �#   LC_ID ��   �Y  
locale �Y   
wlocale �_  	  �
e  
wrefcount �
e   �     >  unsigned int �  �  �    long unsigned int �  �  �    	  �  �    lconv �  #  9  �  unsigned char �  __lc_time_data �  _PHNDLR B  !  ,  >   _XCPT_ACTION D
x  XcptNum E�   SigNum F	>  XcptAction G
   ,  �   _XcptActTab Jx  _XcptActTabCount K>  _XcptActTabSize L>  _First_FPE_Indx M>  _Num_FPE N>  WINBOOL 
>  WORD �#  DWORD ��  float LPVOID ��  k  __imp__pctype +R  �  __imp__wctype ;R  __imp__pwctype GR  �  �   �  __newclmap P�  __newcumap Q�  __ptlocinfo RV  __ptmbcinfo S  __globallocalestatus T>  __locale_changed U>  __initiallocinfo V(t  __initiallocalestructinfo W�  __imp___mb_cur_max �e  signed char short int ULONG_PTR 1.�   LONG J  HANDLE ��  
_LIST_ENTRY d�  
Flink e�   
Blink f�   �  LIST_ENTRY g�  _GUID X  Data1 �   Data2 #  Data3 #  Data4 X   �  h  �    GUID   h  double long double Y  �  �     _sys_errlist 	�&�  _sys_nerr 	�$>  __imp___argc 	e  __imp___argv 	�  �  Y  __imp___wargv 	"	  	  _  __imp__environ 	B�  __imp__wenviron 	G	  __imp__pgmptr 	N�  __imp__wpgmptr 	S	  __imp__osplatform 	X6  __imp__osver 	]6  __imp__winver 	b6  __imp__winmajor 	g6  __imp__winminor 	l6  _amblksiz 
5k  GUID_MAX_POWER_SAVINGS u  GUID_MIN_POWER_SAVINGS u  GUID_TYPICAL_POWER_SAVINGS u  NO_SUBGROUP_GUID u  ALL_POWERSCHEMES_GUID  u  GUID_POWERSCHEME_PERSONALITY !u  GUID_ACTIVE_POWERSCHEME "u  GUID_IDLE_RESILIENCY_SUBGROUP #u  GUID_IDLE_RESILIENCY_PERIOD $u  GUID_DISK_COALESCING_POWERDOWN_TIMEOUT %u  GUID_EXECUTION_REQUIRED_REQUEST_TIMEOUT &u  GUID_VIDEO_SUBGROUP 'u  GUID_VIDEO_POWERDOWN_TIMEOUT (u  GUID_VIDEO_ANNOYANCE_TIMEOUT )u  GUID_VIDEO_ADAPTIVE_PERCENT_INCREASE *u  GUID_VIDEO_DIM_TIMEOUT +u  GUID_VIDEO_ADAPTIVE_POWERDOWN ,u  GUID_MONITOR_POWER_ON -u  GUID_DEVICE_POWER_POLICY_VIDEO_BRIGHTNESS .u  GUID_DEVICE_POWER_POLICY_VIDEO_DIM_BRIGHTNESS /u  GUID_VIDEO_CURRENT_MONITOR_BRIGHTNESS 0u  GUID_VIDEO_ADAPTIVE_DISPLAY_BRIGHTNESS 1u  GUID_CONSOLE_DISPLAY_STATE 2u  GUID_ALLOW_DISPLAY_REQUIRED 3u  GUID_VIDEO_CONSOLE_LOCK_TIMEOUT 4u  GUID_ADAPTIVE_POWER_BEHAVIOR_SUBGROUP 5u  GUID_NON_ADAPTIVE_INPUT_TIMEOUT 6u  GUID_DISK_SUBGROUP 7u  GUID_DISK_POWERDOWN_TIMEOUT 8u  GUID_DISK_IDLE_TIMEOUT 9u  GUID_DISK_BURST_IGNORE_THRESHOLD :u  GUID_DISK_ADAPTIVE_POWERDOWN ;u  GUID_SLEEP_SUBGROUP <u  GUID_SLEEP_IDLE_THRESHOLD =u  GUID_STANDBY_TIMEOUT >u  GUID_UNATTEND_SLEEP_TIMEOUT ?u  GUID_HIBERNATE_TIMEOUT @u  GUID_HIBERNATE_FASTS4_POLICY Au  GUID_CRITICAL_POWER_TRANSITION Bu  GUID_SYSTEM_AWAYMODE Cu  GUID_ALLOW_AWAYMODE Du  GUID_ALLOW_STANDBY_STATES Eu  GUID_ALLOW_RTC_WAKE Fu  GUID_ALLOW_SYSTEM_REQUIRED Gu  GUID_SYSTEM_BUTTON_SUBGROUP Hu  GUID_POWERBUTTON_ACTION Iu  GUID_SLEEPBUTTON_ACTION Ju  GUID_USERINTERFACEBUTTON_ACTION Ku  GUID_LIDCLOSE_ACTION Lu  GUID_LIDOPEN_POWERSTATE Mu  GUID_BATTERY_SUBGROUP Nu  GUID_BATTERY_DISCHARGE_ACTION_0 Ou  GUID_BATTERY_DISCHARGE_LEVEL_0 Pu  GUID_BATTERY_DISCHARGE_FLAGS_0 Qu  GUID_BATTERY_DISCHARGE_ACTION_1 Ru  GUID_BATTERY_DISCHARGE_LEVEL_1 Su  GUID_BATTERY_DISCHARGE_FLAGS_1 Tu  GUID_BATTERY_DISCHARGE_ACTION_2 Uu  GUID_BATTERY_DISCHARGE_LEVEL_2 Vu  GUID_BATTERY_DISCHARGE_FLAGS_2 Wu  GUID_BATTERY_DISCHARGE_ACTION_3 Xu  GUID_BATTERY_DISCHARGE_LEVEL_3 Yu  GUID_BATTERY_DISCHARGE_FLAGS_3 Zu  GUID_PROCESSOR_SETTINGS_SUBGROUP [u  GUID_PROCESSOR_THROTTLE_POLICY \u  GUID_PROCESSOR_THROTTLE_MAXIMUM ]u  GUID_PROCESSOR_THROTTLE_MINIMUM ^u  GUID_PROCESSOR_ALLOW_THROTTLING _u  GUID_PROCESSOR_IDLESTATE_POLICY `u  GUID_PROCESSOR_PERFSTATE_POLICY au  GUID_PROCESSOR_PERF_INCREASE_THRESHOLD bu  GUID_PROCESSOR_PERF_DECREASE_THRESHOLD cu  GUID_PROCESSOR_PERF_INCREASE_POLICY du  GUID_PROCESSOR_PERF_DECREASE_POLICY eu  GUID_PROCESSOR_PERF_INCREASE_TIME fu  GUID_PROCESSOR_PERF_DECREASE_TIME gu  GUID_PROCESSOR_PERF_TIME_CHECK hu  GUID_PROCESSOR_PERF_BOOST_POLICY iu  GUID_PROCESSOR_PERF_BOOST_MODE ju  GUID_PROCESSOR_IDLE_ALLOW_SCALING ku  GUID_PROCESSOR_IDLE_DISABLE lu  GUID_PROCESSOR_IDLE_STATE_MAXIMUM mu  GUID_PROCESSOR_IDLE_TIME_CHECK nu  GUID_PROCESSOR_IDLE_DEMOTE_THRESHOLD ou  GUID_PROCESSOR_IDLE_PROMOTE_THRESHOLD pu  GUID_PROCESSOR_CORE_PARKING_INCREASE_THRESHOLD qu  GUID_PROCESSOR_CORE_PARKING_DECREASE_THRESHOLD ru  GUID_PROCESSOR_CORE_PARKING_INCREASE_POLICY su  GUID_PROCESSOR_CORE_PARKING_DECREASE_POLICY tu  GUID_PROCESSOR_CORE_PARKING_MAX_CORES uu  GUID_PROCESSOR_CORE_PARKING_MIN_CORES vu  GUID_PROCESSOR_CORE_PARKING_INCREASE_TIME wu  GUID_PROCESSOR_CORE_PARKING_DECREASE_TIME xu  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_DECREASE_FACTOR yu  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_THRESHOLD zu  GUID_PROCESSOR_CORE_PARKING_AFFINITY_WEIGHTING {u  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_DECREASE_FACTOR |u  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_THRESHOLD }u  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_WEIGHTING ~u  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_THRESHOLD u  GUID_PROCESSOR_PARKING_CORE_OVERRIDE �u  GUID_PROCESSOR_PARKING_PERF_STATE �u  GUID_PROCESSOR_PARKING_CONCURRENCY_THRESHOLD �u  GUID_PROCESSOR_PARKING_HEADROOM_THRESHOLD �u  GUID_PROCESSOR_PERF_HISTORY �u  GUID_PROCESSOR_PERF_LATENCY_HINT �u  GUID_PROCESSOR_DISTRIBUTE_UTILITY �u  GUID_SYSTEM_COOLING_POLICY �u  GUID_LOCK_CONSOLE_ON_WAKE �u  GUID_DEVICE_IDLE_POLICY �u  GUID_ACDC_POWER_SOURCE �u  GUID_LIDSWITCH_STATE_CHANGE �u  GUID_BATTERY_PERCENTAGE_REMAINING �u  GUID_GLOBAL_USER_PRESENCE �u  GUID_SESSION_DISPLAY_STATUS �u  GUID_SESSION_USER_PRESENCE �u  GUID_IDLE_BACKGROUND_TASK �u  GUID_BACKGROUND_TASK_NOTIFICATION �u  GUID_APPLAUNCH_BUTTON �u  GUID_PCIEXPRESS_SETTINGS_SUBGROUP �u  GUID_PCIEXPRESS_ASPM_POLICY �u  GUID_ENABLE_SWITCH_FORCED_SHUTDOWN �u  PPM_PERFSTATE_CHANGE_GUID �u  PPM_PERFSTATE_DOMAIN_CHANGE_GUID �u  PPM_IDLESTATE_CHANGE_GUID �u  PPM_PERFSTATES_DATA_GUID �u  PPM_IDLESTATES_DATA_GUID �u  PPM_IDLE_ACCOUNTING_GUID �u  PPM_IDLE_ACCOUNTING_EX_GUID �u  PPM_THERMALCONSTRAINT_GUID �u  PPM_PERFMON_PERFSTATE_GUID �u  PPM_THERMAL_POLICY_CHANGE_GUID �u  
_RTL_CRITICAL_SECTION_DEBUG 056   
Type 6   
CreatorBackTraceIndex 7  
CriticalSection 8%�   
ProcessLocksList 9�  
EntryCount :
   
ContentionCount ;
  $
Flags <
  (
CreatorBackTraceIndexHigh =  ,
SpareWORD >  . 
_RTL_CRITICAL_SECTION (P�   
DebugInfo Q#�    
LockCount R�  
RecursionCount S�  
OwningThread T�  
LockSemaphore U�  
SpinCount V�    6   PRTL_CRITICAL_SECTION_DEBUG ?#!  4  RTL_CRITICAL_SECTION W6   CRITICAL_SECTION � !  I!  T!  �   VIRTUAL_STORAGE_TYPE_VENDOR_UNKNOWN u  VIRTUAL_STORAGE_TYPE_VENDOR_MICROSOFT u  __mingwthr_cs *!  	��h    __mingwthr_cs_init E  	Țh    __mingwthr_key_t "  �!  __mingwthr_key  U"  key !	   dtor "
C!  next #U"   "  key_dtor_list '#U"  	��h    __mingw_TLScallback z�  @Gh    �       ��#  hDllHandle z�  $  �#  reason {  �$  �$  reserved |'  �%  �%  �Gh    V       ]#  keyp �&U"  �&  �&  t �-U"  '  '  �Gh    �&  Hh    �&   R	��h      !�#  }Gh    }Gh           ��#  "$  �Gh    �%   !�#  �Gh    �Gh           ��#  "$  �Gh    �%   #�Gh    �&  �#   R	��h     Hh    �&   $__mingwthr_run_key_dtors c:$  %keyp eU"  &%value m'    ___w64_mingwthr_remove_key_dtor A>  �Fh    �       �%  key A(  ='  5'  prev_key CU"  �'  �'  cur_key DU"  (  (  #�Fh    �&  �$   R	��h     Gh    �&   Gh    �&   R	��h      ___w64_mingwthr_add_key_dtor *>  0Fh    x       ��%  key *%  �(  z(  dtor *1C!  1)  #)  new_key ,�%  �)  �)  #_Fh    �&  �%   R1 QH #zFh    �&  �%   R	��h     �Fh    �&   R	��h      �!  '�#  �Eh    k       ��&  ($  2*  0*  ))$  �Eh           W&  (*$  Y*  U*  �Eh    �&  �Eh    �&  *Fh     R|   #�Eh    �&  v&   R	��h     ++Fh    �&   R	��h      ,free free 	-4  4  
.-�  �  
p-J  J  %-    
+-
  
  
,,calloc calloc 	-�  �  �-�  �  . �    	  GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt/tlsmcrt.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt �  _CRT_MT �   	0Ph    int  C   7  GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt/pseudo-reloc-list.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt    __RUNTIME_PSEUDO_RELOC_LIST_END__   	!�h    char __RUNTIME_PSEUDO_RELOC_LIST__   	 �h     s^   e  GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt/pesect.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt  Hh    �      }   char �   size_t (,�   long long unsigned int long long int wchar_t g6  short unsigned int 6  int long int pthreadlocinfo �(|  �  threadlocaleinfostruct `�,  �  �Q   	lc_codepage �y  	lc_collate_cp �y  	lc_handle ��  	lc_id �	�  $	lc_category ��  H
lc_clike �Q  
mb_cur_max �Q  
lconv_intl_refcount �s  
lconv_num_refcount �s  
lconv_mon_refcount �s   
lconv ��  (
ctype1_refcount �s  0
ctype1 ��  8
pctype ��  @
pclmap ��  H
pcumap ��  P
lc_time_curr �  X pthreadmbcinfo �%D  J  threadmbcinfostruct localeinfo_struct ��  	locinfo �d   	mbcinfo �,   _locale_tstruct �_  tagLC_ID �  	wLanguage �6   	wCountry �6  	wCodePage �6   LC_ID ��  
 �g  	locale �g   	wlocale �m  �  �
s  	wrefcount �
s   �   &  Q  unsigned int �  �  �    long unsigned int   �  �      �  �    lconv �  6  L  �  unsigned char �  __lc_time_data   _PHNDLR B)  /  :  Q   _XCPT_ACTION D
�  XcptNum E�   SigNum F	Q  XcptAction G
   :  �   _XcptActTab J�  _XcptActTabCount KQ  _XcptActTabSize LQ  _First_FPE_Indx MQ  _Num_FPE NQ  WINBOOL 
Q  BYTE ��  WORD �6  DWORD ��  float PBYTE �P    LPVOID ��  y  __imp__pctype +�  �  __imp__wctype ;�  __imp__pwctype G�  �  �   �  __newclmap P�  __newcumap Q�  __ptlocinfo Rd  __ptmbcinfo S,  __globallocalestatus TQ  __locale_changed UQ  __initiallocinfo V(�  __initiallocalestructinfo W�  __imp___mb_cur_max �s  signed char short int ULONG_PTR 1.�   DWORD_PTR �'�  LONG X  ULONGLONG �.�   _GUID K  Data1 �   Data2 6  Data3 6  Data4 K   �  [  �    GUID �  [  IID X[  m  CLSID `[  ~  FMTID g[  �  double long double g  �  �     _sys_errlist 	�&�  _sys_nerr 	�$Q  __imp___argc 	s  __imp___argv 	 	  &	  g  __imp___wargv 	"C	  I	  m  __imp__environ 	B 	  __imp__wenviron 	GC	  __imp__pgmptr 	N&	  __imp__wpgmptr 	SI	  __imp__osplatform 	Xe  __imp__osver 	]e  __imp__winver 	be  __imp__winmajor 	ge  __imp__winminor 	le  _amblksiz 
5y  GUID_MAX_POWER_SAVINGS h  GUID_MIN_POWER_SAVINGS h  GUID_TYPICAL_POWER_SAVINGS h  NO_SUBGROUP_GUID h  ALL_POWERSCHEMES_GUID  h  GUID_POWERSCHEME_PERSONALITY !h  GUID_ACTIVE_POWERSCHEME "h  GUID_IDLE_RESILIENCY_SUBGROUP #h  GUID_IDLE_RESILIENCY_PERIOD $h  GUID_DISK_COALESCING_POWERDOWN_TIMEOUT %h  GUID_EXECUTION_REQUIRED_REQUEST_TIMEOUT &h  GUID_VIDEO_SUBGROUP 'h  GUID_VIDEO_POWERDOWN_TIMEOUT (h  GUID_VIDEO_ANNOYANCE_TIMEOUT )h  GUID_VIDEO_ADAPTIVE_PERCENT_INCREASE *h  GUID_VIDEO_DIM_TIMEOUT +h  GUID_VIDEO_ADAPTIVE_POWERDOWN ,h  GUID_MONITOR_POWER_ON -h  GUID_DEVICE_POWER_POLICY_VIDEO_BRIGHTNESS .h  GUID_DEVICE_POWER_POLICY_VIDEO_DIM_BRIGHTNESS /h  GUID_VIDEO_CURRENT_MONITOR_BRIGHTNESS 0h  GUID_VIDEO_ADAPTIVE_DISPLAY_BRIGHTNESS 1h  GUID_CONSOLE_DISPLAY_STATE 2h  GUID_ALLOW_DISPLAY_REQUIRED 3h  GUID_VIDEO_CONSOLE_LOCK_TIMEOUT 4h  GUID_ADAPTIVE_POWER_BEHAVIOR_SUBGROUP 5h  GUID_NON_ADAPTIVE_INPUT_TIMEOUT 6h  GUID_DISK_SUBGROUP 7h  GUID_DISK_POWERDOWN_TIMEOUT 8h  GUID_DISK_IDLE_TIMEOUT 9h  GUID_DISK_BURST_IGNORE_THRESHOLD :h  GUID_DISK_ADAPTIVE_POWERDOWN ;h  GUID_SLEEP_SUBGROUP <h  GUID_SLEEP_IDLE_THRESHOLD =h  GUID_STANDBY_TIMEOUT >h  GUID_UNATTEND_SLEEP_TIMEOUT ?h  GUID_HIBERNATE_TIMEOUT @h  GUID_HIBERNATE_FASTS4_POLICY Ah  GUID_CRITICAL_POWER_TRANSITION Bh  GUID_SYSTEM_AWAYMODE Ch  GUID_ALLOW_AWAYMODE Dh  GUID_ALLOW_STANDBY_STATES Eh  GUID_ALLOW_RTC_WAKE Fh  GUID_ALLOW_SYSTEM_REQUIRED Gh  GUID_SYSTEM_BUTTON_SUBGROUP Hh  GUID_POWERBUTTON_ACTION Ih  GUID_SLEEPBUTTON_ACTION Jh  GUID_USERINTERFACEBUTTON_ACTION Kh  GUID_LIDCLOSE_ACTION Lh  GUID_LIDOPEN_POWERSTATE Mh  GUID_BATTERY_SUBGROUP Nh  GUID_BATTERY_DISCHARGE_ACTION_0 Oh  GUID_BATTERY_DISCHARGE_LEVEL_0 Ph  GUID_BATTERY_DISCHARGE_FLAGS_0 Qh  GUID_BATTERY_DISCHARGE_ACTION_1 Rh  GUID_BATTERY_DISCHARGE_LEVEL_1 Sh  GUID_BATTERY_DISCHARGE_FLAGS_1 Th  GUID_BATTERY_DISCHARGE_ACTION_2 Uh  GUID_BATTERY_DISCHARGE_LEVEL_2 Vh  GUID_BATTERY_DISCHARGE_FLAGS_2 Wh  GUID_BATTERY_DISCHARGE_ACTION_3 Xh  GUID_BATTERY_DISCHARGE_LEVEL_3 Yh  GUID_BATTERY_DISCHARGE_FLAGS_3 Zh  GUID_PROCESSOR_SETTINGS_SUBGROUP [h  GUID_PROCESSOR_THROTTLE_POLICY \h  GUID_PROCESSOR_THROTTLE_MAXIMUM ]h  GUID_PROCESSOR_THROTTLE_MINIMUM ^h  GUID_PROCESSOR_ALLOW_THROTTLING _h  GUID_PROCESSOR_IDLESTATE_POLICY `h  GUID_PROCESSOR_PERFSTATE_POLICY ah  GUID_PROCESSOR_PERF_INCREASE_THRESHOLD bh  GUID_PROCESSOR_PERF_DECREASE_THRESHOLD ch  GUID_PROCESSOR_PERF_INCREASE_POLICY dh  GUID_PROCESSOR_PERF_DECREASE_POLICY eh  GUID_PROCESSOR_PERF_INCREASE_TIME fh  GUID_PROCESSOR_PERF_DECREASE_TIME gh  GUID_PROCESSOR_PERF_TIME_CHECK hh  GUID_PROCESSOR_PERF_BOOST_POLICY ih  GUID_PROCESSOR_PERF_BOOST_MODE jh  GUID_PROCESSOR_IDLE_ALLOW_SCALING kh  GUID_PROCESSOR_IDLE_DISABLE lh  GUID_PROCESSOR_IDLE_STATE_MAXIMUM mh  GUID_PROCESSOR_IDLE_TIME_CHECK nh  GUID_PROCESSOR_IDLE_DEMOTE_THRESHOLD oh  GUID_PROCESSOR_IDLE_PROMOTE_THRESHOLD ph  GUID_PROCESSOR_CORE_PARKING_INCREASE_THRESHOLD qh  GUID_PROCESSOR_CORE_PARKING_DECREASE_THRESHOLD rh  GUID_PROCESSOR_CORE_PARKING_INCREASE_POLICY sh  GUID_PROCESSOR_CORE_PARKING_DECREASE_POLICY th  GUID_PROCESSOR_CORE_PARKING_MAX_CORES uh  GUID_PROCESSOR_CORE_PARKING_MIN_CORES vh  GUID_PROCESSOR_CORE_PARKING_INCREASE_TIME wh  GUID_PROCESSOR_CORE_PARKING_DECREASE_TIME xh  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_DECREASE_FACTOR yh  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_THRESHOLD zh  GUID_PROCESSOR_CORE_PARKING_AFFINITY_WEIGHTING {h  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_DECREASE_FACTOR |h  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_THRESHOLD }h  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_WEIGHTING ~h  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_THRESHOLD h  GUID_PROCESSOR_PARKING_CORE_OVERRIDE �h  GUID_PROCESSOR_PARKING_PERF_STATE �h  GUID_PROCESSOR_PARKING_CONCURRENCY_THRESHOLD �h  GUID_PROCESSOR_PARKING_HEADROOM_THRESHOLD �h  GUID_PROCESSOR_PERF_HISTORY �h  GUID_PROCESSOR_PERF_LATENCY_HINT �h  GUID_PROCESSOR_DISTRIBUTE_UTILITY �h  GUID_SYSTEM_COOLING_POLICY �h  GUID_LOCK_CONSOLE_ON_WAKE �h  GUID_DEVICE_IDLE_POLICY �h  GUID_ACDC_POWER_SOURCE �h  GUID_LIDSWITCH_STATE_CHANGE �h  GUID_BATTERY_PERCENTAGE_REMAINING �h  GUID_GLOBAL_USER_PRESENCE �h  GUID_SESSION_DISPLAY_STATUS �h  GUID_SESSION_USER_PRESENCE �h  GUID_IDLE_BACKGROUND_TASK �h  GUID_BACKGROUND_TASK_NOTIFICATION �h  GUID_APPLAUNCH_BUTTON �h  GUID_PCIEXPRESS_SETTINGS_SUBGROUP �h  GUID_PCIEXPRESS_ASPM_POLICY �h  GUID_ENABLE_SWITCH_FORCED_SHUTDOWN �h  PPM_PERFSTATE_CHANGE_GUID �h  PPM_PERFSTATE_DOMAIN_CHANGE_GUID �h  PPM_IDLESTATE_CHANGE_GUID �h  PPM_PERFSTATES_DATA_GUID �h  PPM_IDLESTATES_DATA_GUID �h  PPM_IDLE_ACCOUNTING_GUID �h  PPM_IDLE_ACCOUNTING_EX_GUID �h  PPM_THERMALCONSTRAINT_GUID �h  PPM_PERFMON_PERFSTATE_GUID �h  PPM_THERMAL_POLICY_CHANGE_GUID �h    n  �    _IMAGE_DOS_HEADER @q�   	e_magic r   	e_cblp s  	e_cp t  	e_crlc u  	e_cparhdr v  	e_minalloc w  
	e_maxalloc x  	e_ss y  	e_sp z  	e_csum {  	e_ip |  	e_cs }  	e_lfarlc ~  	e_ovno   	e_res ��   	e_oemid �  $	e_oeminfo �  &	e_res2 ��   (	e_lfanew ��  <   �   �      �   �   	 IMAGE_DOS_HEADER �n  PIMAGE_DOS_HEADER �,!  n  _IMAGE_FILE_HEADER ��!  	Machine �   	NumberOfSections �  \  �
+  	PointerToSymbolTable �
+  	NumberOfSymbols �
+  	SizeOfOptionalHeader �  t  �   IMAGE_FILE_HEADER �2!  _IMAGE_DATA_DIRECTORY J"  �  
+   	Size 
+   IMAGE_DATA_DIRECTORY  "  J"  x"  �    _IMAGE_OPTIONAL_HEADER64 �W�%  	Magic X   	MajorLinkerVersion Y  	MinorLinkerVersion Z  	SizeOfCode [
+  	SizeOfInitializedData \
+  	SizeOfUninitializedData ]
+  	AddressOfEntryPoint ^
+  	BaseOfCode _
+  	ImageBase `�  	SectionAlignment a
+   	FileAlignment b
+  $	MajorOperatingSystemVersion c  (	MinorOperatingSystemVersion d  *	MajorImageVersion e  ,	MinorImageVersion f  .	MajorSubsystemVersion g  0	MinorSubsystemVersion h  2	Win32VersionValue i
+  4	SizeOfImage j
+  8	SizeOfHeaders k
+  <	CheckSum l
+  @	Subsystem m  D	DllCharacteristics n  F	SizeOfStackReserve o�  H	SizeOfStackCommit p�  P	SizeOfHeapReserve q�  X	SizeOfHeapCommit r�  `	LoaderFlags s
+  h	NumberOfRvaAndSizes t
+  l	DataDirectory uh"  p IMAGE_OPTIONAL_HEADER64 vx"  PIMAGE_OPTIONAL_HEADER64 v &  x"  PIMAGE_OPTIONAL_HEADER �&�%  _IMAGE_NT_HEADERS64 ��&  	Signature �
+   	FileHeader ��!  	OptionalHeader ��%   PIMAGE_NT_HEADERS64 ��&  .&  PIMAGE_NT_HEADERS �!�&  �'  PhysicalAddress �+  VirtualSize  +   _IMAGE_SECTION_HEADER (�(  	Name �^   	Misc 	�&  �  
+  	SizeOfRawData 
+  	PointerToRawData 
+  	PointerToRelocations 
+  	PointerToLinenumbers 
+  	NumberOfRelocations    	NumberOfLinenumbers   "t  	
+  $ PIMAGE_SECTION_HEADER 
.(  '  �h(  t  �+  OriginalFirstThunk �+   _IMAGE_IMPORT_DESCRIPTOR ��(  4(   \  �
+  	ForwarderChain  
+  	Name 
+  	FirstThunk 
+   IMAGE_IMPORT_DESCRIPTOR h(  PIMAGE_IMPORT_DESCRIPTOR 0 )  �(  VIRTUAL_STORAGE_TYPE_VENDOR_UNKNOWN h  VIRTUAL_STORAGE_TYPE_VENDOR_MICROSOFT h  RPC_IF_HANDLE B�  IWinTypesBase_v0_1_c_ifspec 
*�)  IWinTypesBase_v0_1_s_ifspec 
+�)  IID_IUnknown ah  IID_AsyncIUnknown �h  IID_IClassFactory 8h  IID_IMarshal h  IID_INoMarshal �h  IID_IAgileObject �h  IID_IAgileReference >h  IID_IMarshal2 �h  IID_IMalloc h  IID_IStdMarshalInfo �h  IID_IExternalConnection �h  IID_IMultiQI bh  IID_AsyncIMultiQI �h  IID_IInternalUnknown h  IID_IEnumUnknown bh  IID_IEnumString �h  IID_ISequentialStream ph  IID_IStream h  IID_IRpcChannelBuffer %	h  IID_IRpcChannelBuffer2 �	h  IID_IAsyncRpcChannelBuffer !
h  IID_IRpcChannelBuffer3 �
h  IID_IRpcSyntaxNegotiate �h  IID_IRpcProxyBuffer �h  IID_IRpcStubBuffer Ih  IID_IPSFactoryBuffer �h  IID_IChannelHook H
h  IID_IClientSecurity (h  IID_IServerSecurity �h  IID_IRpcOptions 0h  IID_IGlobalOptions �h  IID_ISurrogate h  IID_IGlobalInterfaceTable th  IID_ISynchronize �h  IID_ISynchronizeHandle Fh  IID_ISynchronizeEvent �h  IID_ISynchronizeContainer �h  IID_ISynchronizeMutex Jh  IID_ICancelMethodCalls �h  IID_IAsyncManager h  IID_ICallFactory wh  IID_IRpcHelper �h  IID_IReleaseMarshalBuffers &h  IID_IWaitMultiple xh  IID_IAddrTrackingControl �h  IID_IAddrExclusionControl ,h  IID_IPipeByte �h  IID_IPipeLong �h  IID_IPipeDouble Eh  IID_IComThreadingInfo �h  IID_IProcessInitControl 1h  IID_IFastRundown h  IID_IMarshalingStream �h  IID_ICallbackWithNoReentrancyToApplicationSTA zh  GUID_NULL 
y  CATID_MARSHALER y  IID_IRpcChannel y  IID_IRpcStub y  IID_IStubManager y  IID_IRpcProxy y  IID_IProxyManager y  IID_IPSFactory y  IID_IInternalMoniker y  IID_IDfReserved1 y  IID_IDfReserved2 y  IID_IDfReserved3 y  CLSID_StdMarshal �  CLSID_AggStdMarshal �  CLSID_StdAsyncActManager �  IID_IStub y  IID_IProxy y  IID_IEnumGeneric y  IID_IEnumHolder y  IID_IEnumCallback  y  IID_IOleManager !y  IID_IOlePresObj "y  IID_IDebug #y  IID_IDebugStream $y  CLSID_PSGenObject %�  CLSID_PSClientSite &�  CLSID_PSClassObject '�  CLSID_PSInPlaceActive (�  CLSID_PSInPlaceFrame )�  CLSID_PSDragDrop *�  CLSID_PSBindCtx +�  CLSID_PSEnumerators ,�  CLSID_StaticMetafile -�  CLSID_StaticDib .�  CID_CDfsVolume /�  CLSID_DCOMAccessControl 0�  CLSID_GlobalOptions 1�  CLSID_StdGlobalInterfaceTable 2�  CLSID_ComBinding 3�  CLSID_StdEvent 4�  CLSID_ManualResetEvent 5�  CLSID_SynchronizeContainer 6�  CLSID_AddrControl 7�  CLSID_CCDFormKrnl 8�  CLSID_CCDPropertyPage 9�  CLSID_CCDFormDialog :�  CLSID_CCDCommandButton ;�  CLSID_CCDComboBox <�  CLSID_CCDTextBox =�  CLSID_CCDCheckBox >�  CLSID_CCDLabel ?�  CLSID_CCDOptionButton @�  CLSID_CCDListBox A�  CLSID_CCDScrollBar B�  CLSID_CCDGroupBox C�  CLSID_CCDGeneralPropertyPage D�  CLSID_CCDGenericPropertyPage E�  CLSID_CCDFontPropertyPage F�  CLSID_CCDColorPropertyPage G�  CLSID_CCDLabelPropertyPage H�  CLSID_CCDCheckBoxPropertyPage I�  CLSID_CCDTextBoxPropertyPage J�  CLSID_CCDOptionButtonPropertyPage K�  CLSID_CCDListBoxPropertyPage L�  CLSID_CCDCommandButtonPropertyPage M�  CLSID_CCDComboBoxPropertyPage N�  CLSID_CCDScrollBarPropertyPage O�  CLSID_CCDGroupBoxPropertyPage P�  CLSID_CCDXObjectPropertyPage Q�  CLSID_CStdPropertyFrame R�  CLSID_CFormPropertyPage S�  CLSID_CGridPropertyPage T�  CLSID_CWSJArticlePage U�  CLSID_CSystemPage V�  CLSID_IdentityUnmarshal W�  CLSID_InProcFreeMarshaler X�  CLSID_Picture_Metafile Y�  CLSID_Picture_EnhMetafile Z�  CLSID_Picture_Dib [�  GUID_TRISTATE \h  IWinTypes_v0_1_c_ifspec )�)  IWinTypes_v0_1_s_ifspec *�)  IID_IMallocSpy �h  IID_IBindCtx :h  IID_IEnumMoniker J h  IID_IRunnableObject � h  IID_IRunningObjectTable �!h  IID_IPersist i"h  IID_IPersistStream �"h  IID_IMoniker j#h  IID_IROTData X%h  IID_IEnumSTATSTG �%h  IID_IStorage X&h  IID_IPersistFile A(h  IID_IPersistStorage �(h  IID_ILockBytes �)h  IID_IEnumFORMATETC �*h  IID_IEnumSTATDATA l+h  IID_IRootStorage ,h  IID_IAdviseSink �,h  IID_AsyncIAdviseSink s-h  IID_IAdviseSink2 �.h  IID_AsyncIAdviseSink2 ./h  IID_IDataObject �/h  IID_IDataAdviseHolder 1h  IID_IMessageFilter �1h  FMTID_SummaryInformation ]2�  FMTID_DocSummaryInformation _2�  FMTID_UserDefinedProperties a2�  FMTID_DiscardableInformation c2�  FMTID_ImageSummaryInformation e2�  FMTID_AudioSummaryInformation g2�  FMTID_VideoSummaryInformation i2�  FMTID_MediaFileSummaryInformation k2�  IID_IClassActivator s2h  IID_IFillLockBytes �2h  IID_IProgressNotify �3h  IID_ILayoutStorage �3h  IID_IBlockingLock �4h  IID_ITimeAndNoticeControl �4h  IID_IOplockStorage N5h  IID_IDirectWriterLock �5h  IID_IUrlMon M6h  IID_IForegroundTransfer �6h  IID_IThumbnailExtractor 7h  IID_IDummyHICONIncluder �7h  IID_IProcessLock �7h  IID_ISurrogateService H8h  IID_IInitializeSpy �8h  IID_IApartmentShutdown �9h  IID_IOleAdviseHolder �h  IID_IOleCache �h  IID_IOleCache2 h  IID_IOleCacheControl �h  IID_IParseDisplayName h  IID_IOleContainer ph  IID_IOleClientSite �h  IID_IOleObject �h  IOLETypes_v0_0_c_ifspec ��)  IOLETypes_v0_0_s_ifspec ��)  IID_IOleWindow h  IID_IOleLink kh  IID_IOleItemContainer 9h  IID_IOleInPlaceUIWindow �h  IID_IOleInPlaceActiveObject Xh  IID_IOleInPlaceFrame 	h  IID_IOleInPlaceObject �	h  IID_IOleInPlaceSite f
h  IID_IContinue ,h  IID_IViewObject {h  IID_IViewObject2 �h  IID_IDropSource 9
h  IID_IDropTarget �
h  IID_IDropSourceNotify 0h  IID_IEnumOLEVERB �h  IID_IServiceProvider Th  IOleAutomationTypes_v1_0_c_ifspec d�)  IOleAutomationTypes_v1_0_s_ifspec e�)  IID_ICreateTypeInfo �h  IID_ICreateTypeInfo2 h  IID_ICreateTypeLib �h  IID_ICreateTypeLib2 �h  IID_IDispatch lh  IID_IEnumVARIANT  	h  IID_ITypeComp �	h  IID_ITypeInfo ]
h  IID_ITypeInfo2 �h  IID_ITypeLib �h  IID_ITypeLib2 �h  IID_ITypeChangeEvents h  IID_IErrorInfo lh  IID_ICreateErrorInfo �h  IID_ISupportErrorInfo dh  IID_ITypeFactory �h  IID_ITypeMarshal h  IID_IRecordInfo �h  IID_IErrorLog �h  IID_IPropertyBag �h  __MIDL_itf_msxml_0000_v0_0_c_ifspec ��)  __MIDL_itf_msxml_0000_v0_0_s_ifspec ��)  LIBID_MSXML �y  IID_IXMLDOMImplementation y  IID_IXMLDOMNode (y  IID_IXMLDOMDocumentFragment �y  IID_IXMLDOMDocument gy  IID_IXMLDOMNodeList vy  IID_IXMLDOMNamedNodeMap �y  IID_IXMLDOMCharacterData y  IID_IXMLDOMAttribute �y  IID_IXMLDOMElement y  IID_IXMLDOMText �y  IID_IXMLDOMComment &y  IID_IXMLDOMProcessingInstruction �y  IID_IXMLDOMCDATASection y  IID_IXMLDOMDocumentType �y  IID_IXMLDOMNotation y  IID_IXMLDOMEntity �y  IID_IXMLDOMEntityReference �y  IID_IXMLDOMParseError b	y  IID_IXTLRuntime �	y  DIID_XMLDOMDocumentEvents >
y  CLSID_DOMDocument ]
�  CLSID_DOMFreeThreadedDocument a
�  IID_IXMLHttpRequest h
y  CLSID_XMLHTTPRequest �
�  IID_IXMLDSOControl �
y  CLSID_XMLDSOControl �  IID_IXMLElementCollection y  IID_IXMLDocument Ky  IID_IXMLDocument2 �y  IID_IXMLElement %y  IID_IXMLElement2 �y  IID_IXMLAttribute �y  IID_IXMLError 
y  CLSID_XMLDocument /
�  CLSID_SBS_StdURLMoniker Ky  CLSID_SBS_HttpProtocol Ly  CLSID_SBS_FtpProtocol My  CLSID_SBS_GopherProtocol Ny  CLSID_SBS_HttpSProtocol Oy  CLSID_SBS_FileProtocol Py  CLSID_SBS_MkProtocol Qy  CLSID_SBS_UrlMkBindCtx Ry  CLSID_SBS_SoftDistExt Sy  CLSID_SBS_CdlProtocol Ty  CLSID_SBS_ClassInstallFilter Uy  CLSID_SBS_InternetSecurityManager Vy  CLSID_SBS_InternetZoneManager Wy  IID_IAsyncMoniker `y  CLSID_StdURLMoniker ay  CLSID_HttpProtocol by  CLSID_FtpProtocol cy  CLSID_GopherProtocol dy  CLSID_HttpSProtocol ey  CLSID_FileProtocol fy  CLSID_MkProtocol gy  CLSID_StdURLProtocol hy  CLSID_UrlMkBindCtx iy  CLSID_CdlProtocol jy  CLSID_ClassInstallFilter ky  IID_IAsyncBindCtx ly  IID_IPersistMoniker h  IID_IMonikerProp �h  IID_IBindProtocol h  IID_IBinding hh  IID_IBindStatusCallback �h  IID_IBindStatusCallbackEx �h  IID_IAuthenticate �h  IID_IAuthenticateEx �h  IID_IHttpNegotiate ^h  IID_IHttpNegotiate2 �h  IID_IHttpNegotiate3 :	h  IID_IWinInetFileStream �	h  IID_IWindowForBindingUI 
h  IID_ICodeInstall z
h  IID_IUri h  IID_IUriContainer �h  IID_IUriBuilder �h  IID_IUriBuilderFactory ^h  IID_IWinInetInfo �h  IID_IHttpSecurity 6h  IID_IWinInetHttpInfo �h  IID_IWinInetHttpTimeouts h  IID_IWinInetCacheHints oh  IID_IWinInetCacheHints2 �h  SID_BindHost 4h  IID_IBindHost >h  IID_IInternet `h  IID_IInternetBindInfo �h  IID_IInternetBindInfoEx 'h  IID_IInternetProtocolRoot �h  IID_IInternetProtocol Ih  IID_IInternetProtocolEx �h  IID_IInternetProtocolSink �h  IID_IInternetProtocolSinkStackable 1h  IID_IInternetSession �h  IID_IInternetThreadSwitch \h  IID_IInternetPriority �h  IID_IInternetProtocolInfo Fh  CLSID_InternetSecurityManager wy  CLSID_InternetZoneManager xy  CLSID_PersistentZoneIdentifier {y  IID_IInternetSecurityMgrSite �h  IID_IInternetSecurityManager h  IID_IInternetSecurityManagerEx �h  IID_IInternetSecurityManagerEx2 �h  IID_IZoneIdentifier �h  IID_IInternetHostSecurityManager h  GUID_CUSTOM_LOCALMACHINEZONEUNLOCKED Xh  IID_IInternetZoneManager �h  IID_IInternetZoneManagerEx �h  IID_IInternetZoneManagerEx2 � h  CLSID_SoftDistExt �!y  IID_ISoftDistExt �!h  IID_ICatalogFileInfo x"h  IID_IDataFilter �"h  IID_IEncodingFilterFactory p#h  GUID_CUSTOM_CONFIRMOBJECTSAFETY �#h  IID_IWrappedProtocol �#h  IID_IGetBindHandle R$h  IID_IBindCallbackRedirect �$h  IID_IPropertyStorage �h  IID_IPropertySetStorage �h  IID_IEnumSTATPROPSTG *h  IID_IEnumSTATPROPSETSTG �h  IID_StdOle y  GUID_DEVINTERFACE_DISK h  GUID_DEVINTERFACE_CDROM 
h  GUID_DEVINTERFACE_PARTITION h  GUID_DEVINTERFACE_TAPE h  GUID_DEVINTERFACE_WRITEONCEDISK h  GUID_DEVINTERFACE_VOLUME h  GUID_DEVINTERFACE_MEDIUMCHANGER h  GUID_DEVINTERFACE_FLOPPY h  GUID_DEVINTERFACE_CDCHANGER h  GUID_DEVINTERFACE_STORAGEPORT h  GUID_DEVINTERFACE_COMPORT h  GUID_DEVINTERFACE_SERENUM_BUS_ENUMERATOR h  _SCARD_IO_REQUEST �fU  dwProtocol �+   cbPciLength �+   SCARD_IO_REQUEST �!U  fU  g_rgSCardT0Pci %.U  g_rgSCardT1Pci %=U  g_rgSCardRawPci %LU  IID_IPrintDialogCallback h  IID_IPrintDialogServices h  __image_base__ �   __mingw_enum_import_library_names ��W  @Kh    �       ��W  i �(Q  �*  �*  �  �	B  j  ��&  �*  �*   importDesc ��(  4+  2+  S  �(   importsStartRVA �	+  _+  W+  !
]  DKh    �  �	$W  "*]  #�  $6]  $I]  $U]  %aKh    i]  &Rr    '�\  vKh    vKh    ?       �(�\  �+  �+  "�\  )�\  ,  �+  )�\  K,  E,  )�\  �,  �,    �   _IsNonwritableInCurrentImage �  �Jh    �       ��X  pTarget �%B  �,  �,  �  �	B   rvaTarget �
�  -  -  S  �(  J-  H-  !
]  �Jh    `  �	[X  "*]  #`  $6]  $I]  $U]  %�Jh    i]  &Rr    *�\  �Jh    �  �(�\  o-  m-  "�\  #�  )�\  �-  �-  )�\  �-  �-  )�\  �-  �-     _GetPEImageBase �B  �Jh    (       �0Y  �  �	B  *
]  �Jh    0  �	"*]  #0  $6]  $I]  $U]  %�Jh    i]  &Rr     _FindPESectionExec �(  Jh    l       �Z  eNo ��   .  .  �  �	B  j  ��&  >.  <.  S  �(  c.  a.  �  �y  �.  �.  *
]  Jh       �	"*]  #   $6]  $I]  $U]  %-Jh    i]  &Rr     __mingw_GetSectionCount pQ  �Ih    +       ��Z  �  r	B  j  s�&  *
]  �Ih    �  v	"*]  #�  $6]  $I]  $U]  %�Ih    i]  &Rr     __mingw_GetSectionForAddress b(  PIh    �       ��[  p b&V  �.  �.  �  d	B   rva e
�   /  �.  !
]  TIh    `  h	J[  "*]  #`  $6]  $I]  $U]  %qIh    i]  &Rr    *�\  Ih    �  l
(�\  %/  #/  "�\  #�  )�\  J/  H/  )�\  o/  m/  )�\  �/  �/     _FindPESectionByName C(  �Hh    �       ��\  pName C#�W  �/  �/  �  E	B  j  F�&  90  70  S  G(  ^0  \0  �  Hy  �0  �0  !
]  �Hh    0  O	y\  "*]  #0  $6]  $I]  $U]  %�Hh    i]  &Rr    +�Hh    P^  �\  &Rs  %*Ih    b^  &R| &Qs &X8  ,_FindPESection -(  
]  -�  -B  .rva --�  j  /�&  S  0(  �  1y   ,_ValidateImageBase   i]  -�  B  /pDOSHeader !  j  �&  /pOptHeader &   0
]   Hh           ��]  1*]  R$6]  )I]  �0  �0  )U]  �0  �0   0
]  @Hh           ��]  1*]  R)6]  �0  �0  $I]  $U]  2IHh    i]  &Rr   0�\  `Hh    H       �P^  (�\  1  1  1�\  Q)�\  Y1  U1  )�\  �1  �1  )�\  �1  �1   3strlen strlen @3strncmp strncmp V �    E  GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt/CRT_fp10.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt  Lh           �&  _fpreset 	 Lh           � *    s  T'  Lh    BLh    �  �  1  �N   �  GNU C17 9.2.0 -mtune=generic -march=x86-64 -g -O2 -O2 -O2 -fbuilding-libgcc -fno-stack-protector ../../../../../src/gcc-git-9.2.0/libgcc/libgcc2.c C:\crossdev\gccmaster\build-tdm64\gcc-seh\x86_64-w64-mingw32\libgcc �'  char �   long long unsigned int long long int uintptr_t P,�   wchar_t gB  short unsigned int int X  long int �   2  X  unsigned int long unsigned int unsigned char long double double float __imp___mb_cur_max t|  �  p    	�     _sys_errlist �&�  _sys_nerr �$X  
__imp___argc |  
__imp___argv _  e  p  
__imp___wargv "�  �  v  
__imp__environ B_  
__imp__wenviron G�  
__imp__pgmptr Ne  
__imp__wpgmptr S�  
__imp__osplatform X	  �  
__imp__osver ]	  
__imp__winver b	  
__imp__winmajor g	  
__imp__winminor l	  _amblksiz 5�  __security_cookie �   optarg #p  optind 1X  opterr 6X  optopt :X  �   �  _daylight zX  _dstbias {d  _timezone |d  p  &  	�    _tzname }  
daylight  X  
timezone d  
tzname   short int hashval_t *�  htab_hash /�  �  w  �  �   �  
htab_eq 6�  �  X  �  �  �   htab_hash_pointer ��  htab_eq_pointer ��  stringop_alg �  
�  no_stringop  libcall rep_prefix_1_byte rep_prefix_4_byte rep_prefix_8_byte loop_1_byte loop unrolled_loop vector_loop last_alg 	   �  �   �  
unspec_strings J�  
unspecv_strings ��  stringop_strategy 	�N  max 	�_   alg 	��  noalign 	�	X     stringop_algs 4	��  unknown_size 	��   size 	��   N  �  	�    �  processor_costs �	�  add 	�
_   lea 	�
_  shift_var 	�
_  shift_const 	�
_  mult_init 	�
0  mult_bit 	�
_  $divide 	�
0  (movsx 	�X  <movzx 	�X  @large_insn 	�
_  Dmove_ratio 	�
_  Hmovzbl_load 	�
_  Lint_load 	�
E  Pint_store 	�
E  \fp_move 	�
_  hfp_load 	 
E  lfp_store 	
E  xmmx_move 	
_  �mmx_load 	
Z  �mmx_store 	
Z  �xmm_move 		
_  �ymm_move 		_  �zmm_move 	
_  �sse_load 	
0  �sse_unaligned_load 	

0  �sse_store 	
0  �sse_unaligned_store 	
0  �mmxsse_to_integer 	
_  �ssemmx_to_integer 	
_  �gather_static 	
_  �gather_per_elt 	_   scatter_static 	
_  scatter_per_elt 	_  l1_cache_size 	
_  l2_cache_size 	
_  prefetch_block 	
_  simultaneous_prefetches 	
_  branch_cost 	
_  fadd 	
_   fmul 	
_  $fdiv 	 
_  (fabs 	!
_  ,fchs 	"
_  0fsqrt 	#
_  4sse_op 	&
_  8addss 	'
_  <mulss 	(
_  @mulsd 	)
_  Dfmass 	*
_  Hfmasd 	+
_  Ldivss 	,
_  Pdivsd 	-
_  Tsqrtss 	.
_  Xsqrtsd 	/
_  \reassoc_int 	0
_  `reassoc_fp 	0_  dreassoc_vec_int 	0&_  hreassoc_vec_fp 	07_  lmemcpy 	7_  pmemset 	7"_  xcond_taken_branch_cost 	8
_  �cond_not_taken_branch_cost 	:
_  �align_loop 	@�  �align_jump 	A�  �align_label 	B�  �align_func 	C�  � �  _  0  	�       _  E  	�    5  _  Z  	�    J  S  
ix86_cost 	F&x    
ix86_size_cost 	G%  ix86_tune_indices �  	��  X86_TUNE_SCHEDULE  X86_TUNE_PARTIAL_REG_DEPENDENCY X86_TUNE_SSE_PARTIAL_REG_DEPENDENCY X86_TUNE_SSE_SPLIT_REGS X86_TUNE_PARTIAL_FLAG_REG_STALL X86_TUNE_MOVX X86_TUNE_MEMORY_MISMATCH_STALL X86_TUNE_FUSE_CMP_AND_BRANCH_32 X86_TUNE_FUSE_CMP_AND_BRANCH_64 X86_TUNE_FUSE_CMP_AND_BRANCH_SOFLAGS 	X86_TUNE_FUSE_ALU_AND_BRANCH 
X86_TUNE_ACCUMULATE_OUTGOING_ARGS X86_TUNE_PROLOGUE_USING_MOVE X86_TUNE_EPILOGUE_USING_MOVE 
X86_TUNE_USE_LEAVE X86_TUNE_PUSH_MEMORY X86_TUNE_SINGLE_PUSH X86_TUNE_DOUBLE_PUSH X86_TUNE_SINGLE_POP X86_TUNE_DOUBLE_POP X86_TUNE_PAD_SHORT_FUNCTION X86_TUNE_PAD_RETURNS X86_TUNE_FOUR_JUMP_LIMIT X86_TUNE_SOFTWARE_PREFETCHING_BENEFICIAL X86_TUNE_LCP_STALL X86_TUNE_READ_MODIFY X86_TUNE_USE_INCDEC X86_TUNE_INTEGER_DFMODE_MOVES X86_TUNE_OPT_AGU X86_TUNE_AVOID_LEA_FOR_ADDR X86_TUNE_SLOW_IMUL_IMM32_MEM X86_TUNE_SLOW_IMUL_IMM8 X86_TUNE_AVOID_MEM_OPND_FOR_CMOVE  X86_TUNE_SINGLE_STRINGOP !X86_TUNE_MISALIGNED_MOVE_STRING_PRO_EPILOGUES "X86_TUNE_USE_SAHF #X86_TUNE_USE_CLTD $X86_TUNE_USE_BT %X86_TUNE_AVOID_FALSE_DEP_FOR_BMI &X86_TUNE_ADJUST_UNROLL 'X86_TUNE_ONE_IF_CONV_INSN (X86_TUNE_USE_HIMODE_FIOP )X86_TUNE_USE_SIMODE_FIOP *X86_TUNE_USE_FFREEP +X86_TUNE_EXT_80387_CONSTANTS ,X86_TUNE_GENERAL_REGS_SSE_SPILL -X86_TUNE_SSE_UNALIGNED_LOAD_OPTIMAL .X86_TUNE_SSE_UNALIGNED_STORE_OPTIMAL /X86_TUNE_SSE_PACKED_SINGLE_INSN_OPTIMAL 0X86_TUNE_SSE_TYPELESS_STORES 1X86_TUNE_SSE_LOAD0_BY_PXOR 2X86_TUNE_INTER_UNIT_MOVES_TO_VEC 3X86_TUNE_INTER_UNIT_MOVES_FROM_VEC 4X86_TUNE_INTER_UNIT_CONVERSIONS 5X86_TUNE_SPLIT_MEM_OPND_FOR_FP_CONVERTS 6X86_TUNE_USE_VECTOR_FP_CONVERTS 7X86_TUNE_USE_VECTOR_CONVERTS 8X86_TUNE_SLOW_PSHUFB 9X86_TUNE_AVOID_4BYTE_PREFIXES :X86_TUNE_USE_GATHER ;X86_TUNE_AVOID_128FMA_CHAINS <X86_TUNE_AVOID_256FMA_CHAINS =X86_TUNE_AVX256_UNALIGNED_LOAD_OPTIMAL >X86_TUNE_AVX256_UNALIGNED_STORE_OPTIMAL ?X86_TUNE_AVX128_OPTIMAL @X86_TUNE_AVX256_OPTIMAL AX86_TUNE_DOUBLE_WITH_ADD BX86_TUNE_ALWAYS_FANCY_MATH_387 CX86_TUNE_UNROLL_STRLEN DX86_TUNE_SHIFT1 EX86_TUNE_ZERO_EXTEND_WITH_AND FX86_TUNE_PROMOTE_HIMODE_IMUL GX86_TUNE_FAST_PREFIX HX86_TUNE_READ_MODIFY_WRITE IX86_TUNE_MOVE_M1_VIA_OR JX86_TUNE_NOT_UNPAIRABLE KX86_TUNE_PARTIAL_REG_STALL LX86_TUNE_PROMOTE_QIMODE MX86_TUNE_PROMOTE_HI_REGS NX86_TUNE_HIMODE_MATH OX86_TUNE_SPLIT_LONG_MOVES PX86_TUNE_USE_XCHGB QX86_TUNE_USE_MOV0 RX86_TUNE_NOT_VECTORMODE SX86_TUNE_AVOID_VECTOR_DECODE TX86_TUNE_BRANCH_PREDICTION_HINTS UX86_TUNE_QIMODE_MATH VX86_TUNE_PROMOTE_QI_REGS WX86_TUNE_EMIT_VZEROUPPER XX86_TUNE_LAST Y �  �  	�   X 
ix86_tune_features 	��  ix86_arch_indices �  	3�  X86_ARCH_CMOV  X86_ARCH_CMPXCHG X86_ARCH_CMPXCHG8B X86_ARCH_XADD X86_ARCH_BSWAP X86_ARCH_LAST  �  �  	�    
ix86_arch_features 	=�  
x86_prefetch_sse 	L�  _dont_use_tree_here_ 
x86_mfence 	j
�  �  reg_class �  	0�  NO_REGS  AREG DREG CREG BREG SIREG DIREG AD_REGS CLOBBERED_REGS Q_REGS 	NON_Q_REGS 
TLS_GOTBASE_REGS INDEX_REGS LEGACY_REGS 
GENERAL_REGS FP_TOP_REG FP_SECOND_REG FLOAT_REGS SSE_FIRST_REG NO_REX_SSE_REGS SSE_REGS ALL_SSE_REGS MMX_REGS FLOAT_SSE_REGS FLOAT_INT_REGS INT_SSE_REGS FLOAT_INT_SSE_REGS MASK_REGS ALL_MASK_REGS ALL_REGS LIM_REG_CLASSES    _  �  	�   K �  
dbx_register_map 	&�  
dbx64_register_map 	'�  
svr4_dbx_register_map 	(�  processor_type �  	�d  PROCESSOR_GENERIC  PROCESSOR_I386 PROCESSOR_I486 PROCESSOR_PENTIUM PROCESSOR_LAKEMONT PROCESSOR_PENTIUMPRO PROCESSOR_PENTIUM4 PROCESSOR_NOCONA PROCESSOR_CORE2 PROCESSOR_NEHALEM 	PROCESSOR_SANDYBRIDGE 
PROCESSOR_HASWELL PROCESSOR_BONNELL PROCESSOR_SILVERMONT 
PROCESSOR_GOLDMONT PROCESSOR_GOLDMONT_PLUS PROCESSOR_TREMONT PROCESSOR_KNL PROCESSOR_KNM PROCESSOR_SKYLAKE PROCESSOR_SKYLAKE_AVX512 PROCESSOR_CANNONLAKE PROCESSOR_ICELAKE_CLIENT PROCESSOR_ICELAKE_SERVER PROCESSOR_CASCADELAKE PROCESSOR_INTEL PROCESSOR_GEODE PROCESSOR_K6 PROCESSOR_ATHLON PROCESSOR_K8 PROCESSOR_AMDFAM10 PROCESSOR_BDVER1 PROCESSOR_BDVER2  PROCESSOR_BDVER3 !PROCESSOR_BDVER4 "PROCESSOR_BTVER1 #PROCESSOR_BTVER2 $PROCESSOR_ZNVER1 %PROCESSOR_ZNVER2 &PROCESSOR_max ' 
ix86_tune 	r	!  
ix86_arch 	s	!  
ix86_preferred_stack_boundary 	z	�  
ix86_incoming_stack_boundary 	{	�  �  �  	�   K �  
regclass_map 	~	�  signed char UQItype {�    __int128 __int128 unsigned complex float complex double  complex long double _Float128  complex _Float128 !  �  	�   � �  
__popcount_tab ��  
__clz_tab �  func_ptr *�  �  �   __CTOR_LIST__ /�  __DTOR_LIST__ 0�  �  
6	
	pOh      
7	
	�Oh     �L   �  GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt/dllentry.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt PLh           q)  char long long unsigned int long long int uintptr_t P,�   wchar_t g6  short unsigned int 6  int long int pthreadlocinfo �(|  �  threadlocaleinfostruct `�,  ?  �Q   	lc_codepage �y  	lc_collate_cp �y  	lc_handle ��  	lc_id �	�  $	lc_category ��  H
lc_clike �Q  
mb_cur_max �Q  
lconv_intl_refcount �s  
lconv_num_refcount �s  
lconv_mon_refcount �s   
lconv ��  (
ctype1_refcount �s  0
ctype1 ��  8
pctype ��  @
pclmap ��  H
pcumap ��  P
lc_time_curr �  X pthreadmbcinfo �%D  J  threadmbcinfostruct localeinfo_struct ��  	locinfo �d   	mbcinfo �,   _locale_tstruct �_  tagLC_ID �  	wLanguage �6   	wCountry �6  	wCodePage �6   LC_ID ��  
 �g  	locale �g   	wlocale �m  ?  �
s  	wrefcount �
s   �   &  Q  unsigned int �  �  �    long unsigned int   �  �      �  �    lconv �  6  L  �  unsigned char �  __lc_time_data   _PHNDLR B)  /  :  Q   _XCPT_ACTION D
�  XcptNum E�   SigNum F	Q  XcptAction G
   :  �   _XcptActTab J�  _XcptActTabCount KQ  _XcptActTabSize LQ  _First_FPE_Indx MQ  _Num_FPE NQ  WINBOOL 
Q  BOOL �Q  DWORD ��  float LPVOID ��  y  __imp__pctype +`  �  __imp__wctype ;`  __imp__pwctype G`  �  �   �  __newclmap P�  __newcumap Q�  __ptlocinfo Rd  __ptmbcinfo S,  __globallocalestatus TQ  __locale_changed UQ  __initiallocinfo V(�  __initiallocalestructinfo W�  __imp___mb_cur_max �s  signed char short int HANDLE ��  _GUID �  Data1 �   Data2 6  Data3 6  Data4 �   �    �    GUID �    IID X    CLSID `  (  FMTID g  ;  double long double g  w  �     _sys_errlist �&g  _sys_nerr �$Q  __imp___argc s  __imp___argv �  �  g  __imp___wargv "�  �  m  __imp__environ B�  __imp__wenviron G�  __imp__pgmptr N�  __imp__wpgmptr S�  __imp__osplatform XD  __imp__osver ]D  __imp__winver bD  __imp__winmajor gD  __imp__winminor lD  _amblksiz 	5y  GUID_MAX_POWER_SAVINGS   GUID_MIN_POWER_SAVINGS   GUID_TYPICAL_POWER_SAVINGS   NO_SUBGROUP_GUID   ALL_POWERSCHEMES_GUID    GUID_POWERSCHEME_PERSONALITY !  GUID_ACTIVE_POWERSCHEME "  GUID_IDLE_RESILIENCY_SUBGROUP #  GUID_IDLE_RESILIENCY_PERIOD $  GUID_DISK_COALESCING_POWERDOWN_TIMEOUT %  GUID_EXECUTION_REQUIRED_REQUEST_TIMEOUT &  GUID_VIDEO_SUBGROUP '  GUID_VIDEO_POWERDOWN_TIMEOUT (  GUID_VIDEO_ANNOYANCE_TIMEOUT )  GUID_VIDEO_ADAPTIVE_PERCENT_INCREASE *  GUID_VIDEO_DIM_TIMEOUT +  GUID_VIDEO_ADAPTIVE_POWERDOWN ,  GUID_MONITOR_POWER_ON -  GUID_DEVICE_POWER_POLICY_VIDEO_BRIGHTNESS .  GUID_DEVICE_POWER_POLICY_VIDEO_DIM_BRIGHTNESS /  GUID_VIDEO_CURRENT_MONITOR_BRIGHTNESS 0  GUID_VIDEO_ADAPTIVE_DISPLAY_BRIGHTNESS 1  GUID_CONSOLE_DISPLAY_STATE 2  GUID_ALLOW_DISPLAY_REQUIRED 3  GUID_VIDEO_CONSOLE_LOCK_TIMEOUT 4  GUID_ADAPTIVE_POWER_BEHAVIOR_SUBGROUP 5  GUID_NON_ADAPTIVE_INPUT_TIMEOUT 6  GUID_DISK_SUBGROUP 7  GUID_DISK_POWERDOWN_TIMEOUT 8  GUID_DISK_IDLE_TIMEOUT 9  GUID_DISK_BURST_IGNORE_THRESHOLD :  GUID_DISK_ADAPTIVE_POWERDOWN ;  GUID_SLEEP_SUBGROUP <  GUID_SLEEP_IDLE_THRESHOLD =  GUID_STANDBY_TIMEOUT >  GUID_UNATTEND_SLEEP_TIMEOUT ?  GUID_HIBERNATE_TIMEOUT @  GUID_HIBERNATE_FASTS4_POLICY A  GUID_CRITICAL_POWER_TRANSITION B  GUID_SYSTEM_AWAYMODE C  GUID_ALLOW_AWAYMODE D  GUID_ALLOW_STANDBY_STATES E  GUID_ALLOW_RTC_WAKE F  GUID_ALLOW_SYSTEM_REQUIRED G  GUID_SYSTEM_BUTTON_SUBGROUP H  GUID_POWERBUTTON_ACTION I  GUID_SLEEPBUTTON_ACTION J  GUID_USERINTERFACEBUTTON_ACTION K  GUID_LIDCLOSE_ACTION L  GUID_LIDOPEN_POWERSTATE M  GUID_BATTERY_SUBGROUP N  GUID_BATTERY_DISCHARGE_ACTION_0 O  GUID_BATTERY_DISCHARGE_LEVEL_0 P  GUID_BATTERY_DISCHARGE_FLAGS_0 Q  GUID_BATTERY_DISCHARGE_ACTION_1 R  GUID_BATTERY_DISCHARGE_LEVEL_1 S  GUID_BATTERY_DISCHARGE_FLAGS_1 T  GUID_BATTERY_DISCHARGE_ACTION_2 U  GUID_BATTERY_DISCHARGE_LEVEL_2 V  GUID_BATTERY_DISCHARGE_FLAGS_2 W  GUID_BATTERY_DISCHARGE_ACTION_3 X  GUID_BATTERY_DISCHARGE_LEVEL_3 Y  GUID_BATTERY_DISCHARGE_FLAGS_3 Z  GUID_PROCESSOR_SETTINGS_SUBGROUP [  GUID_PROCESSOR_THROTTLE_POLICY \  GUID_PROCESSOR_THROTTLE_MAXIMUM ]  GUID_PROCESSOR_THROTTLE_MINIMUM ^  GUID_PROCESSOR_ALLOW_THROTTLING _  GUID_PROCESSOR_IDLESTATE_POLICY `  GUID_PROCESSOR_PERFSTATE_POLICY a  GUID_PROCESSOR_PERF_INCREASE_THRESHOLD b  GUID_PROCESSOR_PERF_DECREASE_THRESHOLD c  GUID_PROCESSOR_PERF_INCREASE_POLICY d  GUID_PROCESSOR_PERF_DECREASE_POLICY e  GUID_PROCESSOR_PERF_INCREASE_TIME f  GUID_PROCESSOR_PERF_DECREASE_TIME g  GUID_PROCESSOR_PERF_TIME_CHECK h  GUID_PROCESSOR_PERF_BOOST_POLICY i  GUID_PROCESSOR_PERF_BOOST_MODE j  GUID_PROCESSOR_IDLE_ALLOW_SCALING k  GUID_PROCESSOR_IDLE_DISABLE l  GUID_PROCESSOR_IDLE_STATE_MAXIMUM m  GUID_PROCESSOR_IDLE_TIME_CHECK n  GUID_PROCESSOR_IDLE_DEMOTE_THRESHOLD o  GUID_PROCESSOR_IDLE_PROMOTE_THRESHOLD p  GUID_PROCESSOR_CORE_PARKING_INCREASE_THRESHOLD q  GUID_PROCESSOR_CORE_PARKING_DECREASE_THRESHOLD r  GUID_PROCESSOR_CORE_PARKING_INCREASE_POLICY s  GUID_PROCESSOR_CORE_PARKING_DECREASE_POLICY t  GUID_PROCESSOR_CORE_PARKING_MAX_CORES u  GUID_PROCESSOR_CORE_PARKING_MIN_CORES v  GUID_PROCESSOR_CORE_PARKING_INCREASE_TIME w  GUID_PROCESSOR_CORE_PARKING_DECREASE_TIME x  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_DECREASE_FACTOR y  GUID_PROCESSOR_CORE_PARKING_AFFINITY_HISTORY_THRESHOLD z  GUID_PROCESSOR_CORE_PARKING_AFFINITY_WEIGHTING {  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_DECREASE_FACTOR |  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_HISTORY_THRESHOLD }  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_WEIGHTING ~  GUID_PROCESSOR_CORE_PARKING_OVER_UTILIZATION_THRESHOLD   GUID_PROCESSOR_PARKING_CORE_OVERRIDE �  GUID_PROCESSOR_PARKING_PERF_STATE �  GUID_PROCESSOR_PARKING_CONCURRENCY_THRESHOLD �  GUID_PROCESSOR_PARKING_HEADROOM_THRESHOLD �  GUID_PROCESSOR_PERF_HISTORY �  GUID_PROCESSOR_PERF_LATENCY_HINT �  GUID_PROCESSOR_DISTRIBUTE_UTILITY �  GUID_SYSTEM_COOLING_POLICY �  GUID_LOCK_CONSOLE_ON_WAKE �  GUID_DEVICE_IDLE_POLICY �  GUID_ACDC_POWER_SOURCE �  GUID_LIDSWITCH_STATE_CHANGE �  GUID_BATTERY_PERCENTAGE_REMAINING �  GUID_GLOBAL_USER_PRESENCE �  GUID_SESSION_DISPLAY_STATUS �  GUID_SESSION_USER_PRESENCE �  GUID_IDLE_BACKGROUND_TASK �  GUID_BACKGROUND_TASK_NOTIFICATION �  GUID_APPLAUNCH_BUTTON �  GUID_PCIEXPRESS_SETTINGS_SUBGROUP �  GUID_PCIEXPRESS_ASPM_POLICY �  GUID_ENABLE_SWITCH_FORCED_SHUTDOWN �  PPM_PERFSTATE_CHANGE_GUID �  PPM_PERFSTATE_DOMAIN_CHANGE_GUID �  PPM_IDLESTATE_CHANGE_GUID �  PPM_PERFSTATES_DATA_GUID �  PPM_IDLESTATES_DATA_GUID �  PPM_IDLE_ACCOUNTING_GUID �  PPM_IDLE_ACCOUNTING_EX_GUID �  PPM_THERMALCONSTRAINT_GUID �  PPM_PERFMON_PERFSTATE_GUID �  PPM_THERMAL_POLICY_CHANGE_GUID �  VIRTUAL_STORAGE_TYPE_VENDOR_UNKNOWN 
  VIRTUAL_STORAGE_TYPE_VENDOR_MICROSOFT 
  RPC_IF_HANDLE B�  IWinTypesBase_v0_1_c_ifspec *d  IWinTypesBase_v0_1_s_ifspec +d  IID_IUnknown 
a  IID_AsyncIUnknown 
�  IID_IClassFactory 
8  IID_IMarshal   IID_INoMarshal �  IID_IAgileObject �  IID_IAgileReference >  IID_IMarshal2 �  IID_IMalloc   IID_IStdMarshalInfo �  IID_IExternalConnection �  IID_IMultiQI b  IID_AsyncIMultiQI �  IID_IInternalUnknown   IID_IEnumUnknown b  IID_IEnumString �  IID_ISequentialStream p  IID_IStream   IID_IRpcChannelBuffer %	  IID_IRpcChannelBuffer2 �	  IID_IAsyncRpcChannelBuffer !
  IID_IRpcChannelBuffer3 �
  IID_IRpcSyntaxNegotiate �  IID_IRpcProxyBuffer �  IID_IRpcStubBuffer I  IID_IPSFactoryBuffer �  IID_IChannelHook H
  IID_IClientSecurity (  IID_IServerSecurity �  IID_IRpcOptions 0  IID_IGlobalOptions �  IID_ISurrogate   IID_IGlobalInterfaceTable t  IID_ISynchronize �  IID_ISynchronizeHandle F  IID_ISynchronizeEvent �  IID_ISynchronizeContainer �  IID_ISynchronizeMutex J  IID_ICancelMethodCalls �  IID_IAsyncManager   IID_ICallFactory w  IID_IRpcHelper �  IID_IReleaseMarshalBuffers &  IID_IWaitMultiple x  IID_IAddrTrackingControl �  IID_IAddrExclusionControl ,  IID_IPipeByte �  IID_IPipeLong �  IID_IPipeDouble E  IID_IComThreadingInfo �  IID_IProcessInitControl 1  IID_IFastRundown   IID_IMarshalingStream �  IID_ICallbackWithNoReentrancyToApplicationSTA z  GUID_NULL 
#  CATID_MARSHALER #  IID_IRpcChannel #  IID_IRpcStub #  IID_IStubManager #  IID_IRpcProxy #  IID_IProxyManager #  IID_IPSFactory #  IID_IInternalMoniker #  IID_IDfReserved1 #  IID_IDfReserved2 #  IID_IDfReserved3 #  CLSID_StdMarshal 6  CLSID_AggStdMarshal 6  CLSID_StdAsyncActManager 6  IID_IStub #  IID_IProxy #  IID_IEnumGeneric #  IID_IEnumHolder #  IID_IEnumCallback  #  IID_IOleManager !#  IID_IOlePresObj "#  IID_IDebug ##  IID_IDebugStream $#  CLSID_PSGenObject %6  CLSID_PSClientSite &6  CLSID_PSClassObject '6  CLSID_PSInPlaceActive (6  CLSID_PSInPlaceFrame )6  CLSID_PSDragDrop *6  CLSID_PSBindCtx +6  CLSID_PSEnumerators ,6  CLSID_StaticMetafile -6  CLSID_StaticDib .6  CID_CDfsVolume /6  CLSID_DCOMAccessControl 06  CLSID_GlobalOptions 16  CLSID_StdGlobalInterfaceTable 26  CLSID_ComBinding 36  CLSID_StdEvent 46  CLSID_ManualResetEvent 56  CLSID_SynchronizeContainer 66  CLSID_AddrControl 76  CLSID_CCDFormKrnl 86  CLSID_CCDPropertyPage 96  CLSID_CCDFormDialog :6  CLSID_CCDCommandButton ;6  CLSID_CCDComboBox <6  CLSID_CCDTextBox =6  CLSID_CCDCheckBox >6  CLSID_CCDLabel ?6  CLSID_CCDOptionButton @6  CLSID_CCDListBox A6  CLSID_CCDScrollBar B6  CLSID_CCDGroupBox C6  CLSID_CCDGeneralPropertyPage D6  CLSID_CCDGenericPropertyPage E6  CLSID_CCDFontPropertyPage F6  CLSID_CCDColorPropertyPage G6  CLSID_CCDLabelPropertyPage H6  CLSID_CCDCheckBoxPropertyPage I6  CLSID_CCDTextBoxPropertyPage J6  CLSID_CCDOptionButtonPropertyPage K6  CLSID_CCDListBoxPropertyPage L6  CLSID_CCDCommandButtonPropertyPage M6  CLSID_CCDComboBoxPropertyPage N6  CLSID_CCDScrollBarPropertyPage O6  CLSID_CCDGroupBoxPropertyPage P6  CLSID_CCDXObjectPropertyPage Q6  CLSID_CStdPropertyFrame R6  CLSID_CFormPropertyPage S6  CLSID_CGridPropertyPage T6  CLSID_CWSJArticlePage U6  CLSID_CSystemPage V6  CLSID_IdentityUnmarshal W6  CLSID_InProcFreeMarshaler X6  CLSID_Picture_Metafile Y6  CLSID_Picture_EnhMetafile Z6  CLSID_Picture_Dib [6  GUID_TRISTATE \  IWinTypes_v0_1_c_ifspec )d  IWinTypes_v0_1_s_ifspec *d  IID_IMallocSpy �  IID_IBindCtx :  IID_IEnumMoniker J   IID_IRunnableObject �   IID_IRunningObjectTable �!  IID_IPersist i"  IID_IPersistStream �"  IID_IMoniker j#  IID_IROTData X%  IID_IEnumSTATSTG �%  IID_IStorage X&  IID_IPersistFile A(  IID_IPersistStorage �(  IID_ILockBytes �)  IID_IEnumFORMATETC �*  IID_IEnumSTATDATA l+  IID_IRootStorage ,  IID_IAdviseSink �,  IID_AsyncIAdviseSink s-  IID_IAdviseSink2 �.  IID_AsyncIAdviseSink2 ./  IID_IDataObject �/  IID_IDataAdviseHolder 1  IID_IMessageFilter �1  FMTID_SummaryInformation ]2I  FMTID_DocSummaryInformation _2I  FMTID_UserDefinedProperties a2I  FMTID_DiscardableInformation c2I  FMTID_ImageSummaryInformation e2I  FMTID_AudioSummaryInformation g2I  FMTID_VideoSummaryInformation i2I  FMTID_MediaFileSummaryInformation k2I  IID_IClassActivator s2  IID_IFillLockBytes �2  IID_IProgressNotify �3  IID_ILayoutStorage �3  IID_IBlockingLock �4  IID_ITimeAndNoticeControl �4  IID_IOplockStorage N5  IID_IDirectWriterLock �5  IID_IUrlMon M6  IID_IForegroundTransfer �6  IID_IThumbnailExtractor 7  IID_IDummyHICONIncluder �7  IID_IProcessLock �7  IID_ISurrogateService H8  IID_IInitializeSpy �8  IID_IApartmentShutdown �9  IID_IOleAdviseHolder �  IID_IOleCache �  IID_IOleCache2   IID_IOleCacheControl �  IID_IParseDisplayName   IID_IOleContainer p  IID_IOleClientSite �  IID_IOleObject �  IOLETypes_v0_0_c_ifspec �d  IOLETypes_v0_0_s_ifspec �d  IID_IOleWindow   IID_IOleLink k  IID_IOleItemContainer 9  IID_IOleInPlaceUIWindow �  IID_IOleInPlaceActiveObject X  IID_IOleInPlaceFrame 	  IID_IOleInPlaceObject �	  IID_IOleInPlaceSite f
  IID_IContinue ,  IID_IViewObject {  IID_IViewObject2 �  IID_IDropSource 9
  IID_IDropTarget �
  IID_IDropSourceNotify 0  IID_IEnumOLEVERB �  IID_IServiceProvider T  IOleAutomationTypes_v1_0_c_ifspec dd  IOleAutomationTypes_v1_0_s_ifspec ed  IID_ICreateTypeInfo �  IID_ICreateTypeInfo2   IID_ICreateTypeLib �  IID_ICreateTypeLib2 �  IID_IDispatch l  IID_IEnumVARIANT  	  IID_ITypeComp �	  IID_ITypeInfo ]
  IID_ITypeInfo2 �  IID_ITypeLib �  IID_ITypeLib2 �  IID_ITypeChangeEvents   IID_IErrorInfo l  IID_ICreateErrorInfo �  IID_ISupportErrorInfo d  IID_ITypeFactory �  IID_ITypeMarshal   IID_IRecordInfo �  IID_IErrorLog �  IID_IPropertyBag �  __MIDL_itf_msxml_0000_v0_0_c_ifspec �d  __MIDL_itf_msxml_0000_v0_0_s_ifspec �d  LIBID_MSXML �#  IID_IXMLDOMImplementation #  IID_IXMLDOMNode (#  IID_IXMLDOMDocumentFragment �#  IID_IXMLDOMDocument g#  IID_IXMLDOMNodeList v#  IID_IXMLDOMNamedNodeMap �#  IID_IXMLDOMCharacterData #  IID_IXMLDOMAttribute �#  IID_IXMLDOMElement #  IID_IXMLDOMText �#  IID_IXMLDOMComment &#  IID_IXMLDOMProcessingInstruction �#  IID_IXMLDOMCDATASection #  IID_IXMLDOMDocumentType �#  IID_IXMLDOMNotation #  IID_IXMLDOMEntity �#  IID_IXMLDOMEntityReference �#  IID_IXMLDOMParseError b	#  IID_IXTLRuntime �	#  DIID_XMLDOMDocumentEvents >
#  CLSID_DOMDocument ]
6  CLSID_DOMFreeThreadedDocument a
6  IID_IXMLHttpRequest h
#  CLSID_XMLHTTPRequest �
6  IID_IXMLDSOControl �
#  CLSID_XMLDSOControl 6  IID_IXMLElementCollection #  IID_IXMLDocument K#  IID_IXMLDocument2 �#  IID_IXMLElement %#  IID_IXMLElement2 �#  IID_IXMLAttribute �#  IID_IXMLError 
#  CLSID_XMLDocument /
6  CLSID_SBS_StdURLMoniker K#  CLSID_SBS_HttpProtocol L#  CLSID_SBS_FtpProtocol M#  CLSID_SBS_GopherProtocol N#  CLSID_SBS_HttpSProtocol O#  CLSID_SBS_FileProtocol P#  CLSID_SBS_MkProtocol Q#  CLSID_SBS_UrlMkBindCtx R#  CLSID_SBS_SoftDistExt S#  CLSID_SBS_CdlProtocol T#  CLSID_SBS_ClassInstallFilter U#  CLSID_SBS_InternetSecurityManager V#  CLSID_SBS_InternetZoneManager W#  IID_IAsyncMoniker `#  CLSID_StdURLMoniker a#  CLSID_HttpProtocol b#  CLSID_FtpProtocol c#  CLSID_GopherProtocol d#  CLSID_HttpSProtocol e#  CLSID_FileProtocol f#  CLSID_MkProtocol g#  CLSID_StdURLProtocol h#  CLSID_UrlMkBindCtx i#  CLSID_CdlProtocol j#  CLSID_ClassInstallFilter k#  IID_IAsyncBindCtx l#  IID_IPersistMoniker   IID_IMonikerProp �  IID_IBindProtocol   IID_IBinding h  IID_IBindStatusCallback �  IID_IBindStatusCallbackEx �  IID_IAuthenticate �  IID_IAuthenticateEx �  IID_IHttpNegotiate ^  IID_IHttpNegotiate2 �  IID_IHttpNegotiate3 :	  IID_IWinInetFileStream �	  IID_IWindowForBindingUI 
  IID_ICodeInstall z
  IID_IUri   IID_IUriContainer �  IID_IUriBuilder �  IID_IUriBuilderFactory ^  IID_IWinInetInfo �  IID_IHttpSecurity 6  IID_IWinInetHttpInfo �  IID_IWinInetHttpTimeouts   IID_IWinInetCacheHints o  IID_IWinInetCacheHints2 �  SID_BindHost 4  IID_IBindHost >  IID_IInternet `  IID_IInternetBindInfo �  IID_IInternetBindInfoEx '  IID_IInternetProtocolRoot �  IID_IInternetProtocol I  IID_IInternetProtocolEx �  IID_IInternetProtocolSink �  IID_IInternetProtocolSinkStackable 1  IID_IInternetSession �  IID_IInternetThreadSwitch \  IID_IInternetPriority �  IID_IInternetProtocolInfo F  CLSID_InternetSecurityManager w#  CLSID_InternetZoneManager x#  CLSID_PersistentZoneIdentifier {#  IID_IInternetSecurityMgrSite �  IID_IInternetSecurityManager   IID_IInternetSecurityManagerEx �  IID_IInternetSecurityManagerEx2 �  IID_IZoneIdentifier �  IID_IInternetHostSecurityManager   GUID_CUSTOM_LOCALMACHINEZONEUNLOCKED X  IID_IInternetZoneManager �  IID_IInternetZoneManagerEx �  IID_IInternetZoneManagerEx2 �   CLSID_SoftDistExt �!#  IID_ISoftDistExt �!  IID_ICatalogFileInfo x"  IID_IDataFilter �"  IID_IEncodingFilterFactory p#  GUID_CUSTOM_CONFIRMOBJECTSAFETY �#  IID_IWrappedProtocol �#  IID_IGetBindHandle R$  IID_IBindCallbackRedirect �$  IID_IPropertyStorage �  IID_IPropertySetStorage �  IID_IEnumSTATPROPSTG *  IID_IEnumSTATPROPSETSTG �  IID_StdOle #  GUID_DEVINTERFACE_DISK   GUID_DEVINTERFACE_CDROM 
  GUID_DEVINTERFACE_PARTITION   GUID_DEVINTERFACE_TAPE   GUID_DEVINTERFACE_WRITEONCEDISK   GUID_DEVINTERFACE_VOLUME   GUID_DEVINTERFACE_MEDIUMCHANGER   GUID_DEVINTERFACE_FLOPPY   GUID_DEVINTERFACE_CDCHANGER   GUID_DEVINTERFACE_STORAGEPORT   GUID_DEVINTERFACE_COMPORT   GUID_DEVINTERFACE_SERENUM_BUS_ENUMERATOR   _SCARD_IO_REQUEST �HK  dwProtocol �   cbPciLength �   SCARD_IO_REQUEST �K  HK  g_rgSCardT0Pci %.aK  g_rgSCardT1Pci %=aK  g_rgSCardRawPci %LaK  IID_IPrintDialogCallback   IID_IPrintDialogServices   __security_cookie �    !L  �    5   _pRawDllMain �!<L  L  6L  DllEntryPoint 

  PLh           �hDllHandle 
#�  RdwReason   Qlpreserved 5  X  �   $  GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/misc/onexit_table.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt �Lh    �      �+  char size_t (,�   long long unsigned int long long int uintptr_t P,�   wchar_t gJ  short unsigned int int long int �   :  `  unsigned int long unsigned int unsigned char _PVFV �  �  �  `  H    _first    _last   _end    �  	H  �  _onexit_t �  
__security_cookie �(  double float long double 
__imp___mb_cur_max t  s  �  �     
_sys_errlist �&�  
_sys_nerr �$`  
__imp___argc   
__imp___argv �  �  s  
__imp___wargv "    y  
__imp__environ B�  
__imp__wenviron G  
__imp__pgmptr N�  
__imp__wpgmptr S  
__imp__osplatform X�  �  
__imp__osver ]�  
__imp__winver b�  
__imp__winmajor g�  
__imp__winminor l�  
_amblksiz 5�  `  $  $     __imp__initialize_onexit_table K#[  	PPh      `  u  $  (   __imp__register_onexit_function L$�  	HPh    a  __imp__execute_onexit_table M [  	@Ph    _execute_onexit_table 7
`  �Mh    q       ��  table 74$  �1  �1  first 9  ?2  =2  last 9  d2  b2  �  �Mh      >w  �  �2  �2   �Mh    *  �  R8 Nh    :  �  R8 FNh    N  R|   _register_onexit_function 
`   Mh    �       ��  table 8$  �2  �2  func I(  I3  =3  XMh    8       s  len '�   �3  �3  new_buf (  4  4  tMh    ]  Qv   !Mh    *  �  R8 IMh    :  �  R8 �Mh    r  �  R Q8 �Mh    :  R8  _initialize_onexit_table 
`    table 7$   �  �Lh    %       �*  �  R _lock _lock _unlock _unlock 
 free free  realloc realloc  calloc calloc  �   !  GNU C99 9.2.0 -m64 -mtune=generic -march=x86-64 -g -O2 -std=gnu99 C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/stdio/acrt_iob_func.c C:\crossdev\gccmaster\build-tdm64\runtime\mingw-w64-crt `Nh           �-  char long long unsigned int long long int short unsigned int int long int �   unsigned int long unsigned int unsigned char _iobuf 0
  _ptr D   _cnt 	1  _base D  _flag 	1  _file 	1  _charbuf 	1   _bufsiz 	1  $_tmpfname  D  ( FILE "�  _f__acrt_iob_func 7  =  L  L  J     	__imp___acrt_iob_func   	`Ph    
__acrt_iob_func 	L  `Nh           ��  index 	(J  Q4  K4  lNh    �   
X  X  S                                                                                            %  $ >   :;9I  & I   :;9I   I  :;9  
 :;9I8  	
 :;9I8  

 :;9I8   <  :;9  
:;9  5 I  I  ! I/  :;9  
 :;9I8  '   I  !   4 :;9I?<     �:;9  
 I�8  
 :;9I�8  'I  �:;9   :;9I�  I�   'I    '  !4 :;9I?<  "
 :;9I�8  #�:;9  $�:;9  %
 :;9I�  &
 I�  ':;9  (
 :;9I  )
 I8  *>I:;9  +(   ,>I:;9  -(   .:;9  /
 :;9I
8  0>I:;9  1 :;9I  25   3:;9  44 :;9I  54 :;9I?  6.?:;9'I@�B  7 :;9I�B  8���B1  9�� �B  :.:;9'I@�B  ; :;9I�B  <4 :;9I�B  =
 :;9  >�� 1  ?��1  @��1  A.?:;9'I@�B  B���B1  C.?:;9'I   D :;9I  E  F4 :;9I  G4 :;9I  H  I. ?:;9'I   J.?:;9'I   K :;9I  L4 :;9I  M.1@�B  N 1�B  O1U  P4 1�B  Q1R�BUXYW  R 1  S1R�BUXYW  T1U  U1R�BXYW  V1R�BXYW  W��  X��   Y. ?<n:;9  Z. ?<n:;9   %  $ >   :;9I  & I   :;9I   I  :;9  
 :;9I8  	
 :;9I8  

 :;9I8   <  :;9  
:;9  I  ! I/  '   I  :;9  
 :;9I8  !   4 :;9I?<      '  4 :;9I?<  >I:;9  (   >I:;9  (   4 :;9I  .?:;9'@�B  �� �B1   .?:;9'@�B  !4 :;9I�B  "���B1  #�� �B  $. ?<n:;9   %  $ >   :;9I  & I   :;9I   I  :;9  
 :;9I8  	
 :;9I8  

 :;9I8   <  :;9  
:;9  5 I  I  ! I/  '   I  :;9  
 :;9I8  !   4 :;9I?<     4 :;9I?<  >I:;9  (   >I:;9  (   :;9  
 :;9I
8  >I:;9    :;9I  !5   "4 G:;9   %  $ >   :;9I  & I   :;9I   I  :;9  
 :;9I8  	
 :;9I8  

 :;9I8   <  :;9  
:;9  I  ! I/  4 :;9I?<  4 :;9I?<  '   I  :;9  
 :;9I8  !      �:;9  
 I�8  
 :;9I�8  5 I  :;9  
 I  
 :;9I  �:;9    :;9I�  !I�  "
 :;9I�8  #�:;9  $�:;9  %
 :;9I�  &
 I�  ':;9  (
 :;9I  )4 :;9I  *4 :;9I?  +.?:;9'�@�B  , :;9I�B  -4 :;9I�B  .��1  /�� �B  0�� 1  1.?:;9'@�B  2��1  3. ?<n:;9  4. ?<n:;9  5. ?<n:;9   %  $ >   :;9I  & I   :;9I   I  :;9  
 :;9I8  	
 :;9I8  

 :;9I8   <  :;9  
:;9  I  ! I/  '   I  :;9  
 :;9I8  !   4 :;9I?<      '  4 :;9I?<  4 :;9I?  4 :;9I  .:;9'I@�B   :;9I�B  �� 1  .?:;9'I@�B   :;9I   .?:;9'I   ! :;9I  "4 :;9I  #.1@�B  $ 1�B  %4 1  &1R�BXYW  '4 1�B  (. ?<n:;9   %  $ >   :;9I   I   '  I  ! I/  4 :;9I?   %  4 :;9I?  $ >   %   :;9I   I  $ >  & I   :;9I   I  :;9  	
 :;9I8  

 :;9I8  
 :;9I8   <  
:;9  :;9  5 I  I  ! I/  '   I  :;9  
 :;9I8  !   4 :;9I?<     &   4 :;9I?<  :;9  
 :;9I  >I:;9  (   >I:;9   (   !:;9  "
 :;9I
8  #>I:;9  $ :;9I  %5   &:;9  '4 :;9I  (.?:;9'@�B  )4 :;9I  *4 :;9I�B  +1R�BUXYW  , 1  -U  .4 1�B  /4 1  0 1�B  1��1  2�� �B  31  41U  51R�BUXYW  6��1  71XYW  8�� 1  9.:;9'   : :;9I  ;4 :;9I  <  =.:;9'   >4 :;9I  ? :;9I  @.:;9'�@�B  A :;9I�B  B   C.1@�B  D. ?<n:;9  E. ?<n:;  F. ?<n:;9  G. ?<n:;9   %  $ >   :;9I  & I   :;9I   I  :;9  
 :;9I8  	
 :;9I8  

 :;9I8   <  :;9  
:;9  I  ! I/  '   I  :;9  
 :;9I8  !   4 :;9I?<     �:;9  
 I�8  
 :;9I�8  �:;9   :;9I�  I�  4 :;9I?<  
 :;9I�8  �:;9   �:;9  !
 :;9I�  "
 I�  #:;9  $
 :;9I  %'I  &>I:;9  '(   (>I:;9  )(   *4 :;9I  +4 :;9I?  ,.?:;9'I@�B  - :;9I�B  .4 :;9I�B  /4 :;9I�B  0��1  1�� �B  2�� 1  3��  4���B  5��1  6 :;9I�B  7. ?<n:;9  8. ?<n:;9  9. ?<n:;9   %  $ >   :;9I  & I  5 I   :;9I   I  :;9  	
 :;9I8  

 :;9I8  
 :;9I8   <  
:;9  :;9  I  ! I/  '   I  :;9  
 :;9I8  !   4 :;9I?<     4 :;9I?<  4 :;9I  .?:;9'I@�B   :;9I�B    4 :;9I�B  �� 1  ��1   �� �B  !1R�BXYW  "4 1  #��1  $.:;9'   %4 :;9I  &  '.1@�B  (4 1�B  )1  *��  +���B1  ,. ?<n:;9  -. ?<n:;9   %  4 :;9I?  $ >   %  4 :;9I?  $ >   %  $ >  & I   :;9I   :;9I   I  :;9  
 :;9I8  	
 :;9I8  

 :;9I8   <  :;9  
:;9  I  ! I/  '   I  :;9  
 :;9I8  !   4 :;9I?<     4 :;9I?<  :;9  
 :;9I  
 :;9I  
 I8  .?:;9'I@�B   :;9I�B  4 :;9I  4 :;9I�B   4 :;9I�B  !1R�BUXYW  " 1  #U  $4 1  %��1  &�� �B  '1R�BXYW  ( 1�B  )4 1�B  *1R�BUXYW  +��1  ,.?:;9'I   - :;9I  . :;9I  /4 :;9I  0.1@�B  1 1  2���B1  3. ?<n:;9   %  . ?:;9'@�B    %   %  $ >  & I   :;9I   I  4 :;9I?<   '  I  	! I/  
4 :;9I?<  'I   I  
&   >I:;9  (   !   :;9  
 :;9I8  :;9  
 :;9I8  
 :;9I8  >I:;9   <  4 G:;9   %  $ >   :;9I  & I   :;9I   I  :;9  
 :;9I8  	
 :;9I8  

 :;9I8   <  :;9  
:;9  I  ! I/  '   I  :;9  
 :;9I8  !   4 :;9I?<     4 :;9I?<  'I  .?:;9'I@�B   :;9I   %  $ >   :;9I   I   '   'I  :;9  
 :;9I8  	 :;9I  
4 :;9I?<  I  ! I/  
4 :;9I?<  'I   I  4 :;9I?  .?:;9'I@�B   :;9I�B  4 :;9I�B  1R�BUXYW   1�B  ��1  �� �B  ��1  .?:;9'I@�B    .?:;9'I    :;9I  .1@�B   1  . ?<n:;9   . ?<n:;9   %  $ >   I  :;9  
 :;9I8   :;9I  'I   I  	4 :;9I?  
.?:;9'I@�B   :;9I�B  �� 1  
. ?<n:;9                                �   �  �
      C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt C:/mingw64tdm/x86_64-w64-mingw32/include/psdk_inc C:/mingw64tdm/x86_64-w64-mingw32/include C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/include  crtdll.c   intrin-impl.h   winnt.h   corecrt.h   locale.h   excpt.h   minwindef.h   ctype.h   basetsd.h   guiddef.h   stdlib.h   malloc.h   minwinbase.h   virtdisk.h   rpcdce.h   wtypesbase.h   unknwnbase.h   objidlbase.h   cguid.h   wtypes.h   combaseapi.h   objidl.h   oleidl.h   servprov.h   oaidl.h   msxml.h   urlmon.h   propidl.h   oleauto.h   winioctl.h   winsmcrd.h   winscard.h   commdlg.h   internal.h   corecrt_startup.h   process.h   synchapi.h     	 h    � 
u
��gL	i
+K���t�j)�
r��
�t\"
��
	�Y�DJw��� 3�A�
�ztK��t.
xt��
xt��
�tV."t
.�'.�"
.�
�#
�Z�tX�.
eYz.�g�<
�t�Xf�t����t�h�/u�� ��q?/j& J�Y
�	��=XK?:gX<..`tY&Z�	Z	Y	L�=QgY
/L���y�
X=r�	�
�=RK�_
If�Z1
I2f=uX �   �  �
      C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt C:/mingw64tdm/x86_64-w64-mingw32/include  gccmain.c   corecrt.h   excpt.h   minwindef.h   ctype.h   guiddef.h   stdlib.h   malloc.h   winnt.h   virtdisk.h   rpcdce.h   wtypesbase.h   unknwnbase.h   objidlbase.h   cguid.h   wtypes.h   combaseapi.h   objidl.h   oleidl.h   servprov.h   oaidl.h   msxml.h   urlmon.h   propidl.h   oleauto.h   winioctl.h   winsmcrd.h   winscard.h   commdlg.h     	`8h    K	
	��/
�M	q]�gBtY ]  J X t � ,  J]uex� �+  f+ X <��c� a   [  �
      C:/mingw64tdm/x86_64-w64-mingw32/include C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/include C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt  corecrt.h   excpt.h   minwindef.h   ctype.h   basetsd.h   winnt.h   guiddef.h   stdlib.h   malloc.h   minwinbase.h   virtdisk.h   rpcdce.h   wtypesbase.h   unknwnbase.h   objidlbase.h   cguid.h   wtypes.h   combaseapi.h   objidl.h   oleidl.h   servprov.h   oaidl.h   msxml.h   urlmon.h   propidl.h   oleauto.h   winioctl.h   winsmcrd.h   winscard.h   commdlg.h   internal.h   natstart.c    �   O  �
      C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt C:/mingw64tdm/x86_64-w64-mingw32/include  gs_support.c   corecrt.h   stdlib.h   malloc.h   excpt.h   winnt.h   minwindef.h   ctype.h   basetsd.h   guiddef.h   virtdisk.h   errhandlingapi.h   processthreadsapi.h   sysinfoapi.h   profileapi.h     	09h    2�t��Z&�dȮ
^
�
f/
h
V>h
zX

.
t�Y
@C
?�u u����yCtg
u/�J2'
tt.'su$
�
�g�=dX*JuD �     �
      C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt C:/mingw64tdm/x86_64-w64-mingw32/include  tlssup.c   corecrt.h   excpt.h   minwindef.h   ctype.h   basetsd.h   winnt.h   guiddef.h   stdlib.h   malloc.h   virtdisk.h   corecrt_startup.h     	;h    �P�,Y��gtYhZ
Xg�ut�
<Y&*)JX	sJ
X	� b    \   �
      C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt  cinitexe.c    g    a   �
      C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt  mingw_helpers.c    r   �  �
      C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt C:/mingw64tdm/x86_64-w64-mingw32/include C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/include  pseudo-reloc.c   vadefs.h   corecrt.h   excpt.h   minwindef.h   ctype.h   basetsd.h   winnt.h   guiddef.h   stdlib.h   malloc.h   minwinbase.h   virtdisk.h   rpcdce.h   wtypesbase.h   unknwnbase.h   objidlbase.h   cguid.h   wtypes.h   combaseapi.h   objidl.h   oleidl.h   servprov.h   oaidl.h   msxml.h   urlmon.h   propidl.h   oleauto.h   winioctl.h   winsmcrd.h   winscard.h   commdlg.h   internal.h   memoryapi.h   stdio.h   errhandlingapi.h   <built-in>      	�;h    � >tA?<Z�X?�Z��� ���� t�<� �f
�.Z
<OY7�< �!<JP��t�Kg1X>.�<�f�+J.=+W.=
�u� 	"w��<<	��J#"YYd tK=K
f�
e
u-
a��� J�.��	���ufJnX�Y.J
�~����u�~
gf.���
<�L
!�_��K=gw�~�4� 5
J[&*)<wX7AD�`K= 5N1Jgw�~�4� 
�f�~�p��
<Z �!<J� '�=

�J7��!w
�~X����egw	�~Xf� ���'(=
�M(=.=�X� �� 
��IX
 X   !  �
      C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt C:/mingw64tdm/x86_64-w64-mingw32/include  crt_handler.c   corecrt.h   excpt.h   winnt.h   minwindef.h   ctype.h   basetsd.h   guiddef.h   stdlib.h   malloc.h   errhandlingapi.h   virtdisk.h   rpcdce.h   wtypesbase.h   unknwnbase.h   objidlbase.h   cguid.h   wtypes.h   combaseapi.h   objidl.h   oleidl.h   servprov.h   oaidl.h   msxml.h   urlmon.h   propidl.h   oleauto.h   winioctl.h   winsmcrd.h   winscard.h   commdlg.h   signal.h     	PAh    � K.#/�tY
��Y<�� tY
��u+<��<� �e�tY
�@� f�uD�.XY
m[ul �
���O�C��f JZs�X
XYfh��:s5	<wt=5�x<>	�$=JE='!<=6(><r==	w+ f  �XXY� �g'<z.7B.f�-,�
��YXf�X��
�[u,T]�+ � t�>d�J
�� .f` �
m[uoF�
m[u?A��2N�Q��� � W   J  �
      C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt C:/mingw64tdm/x86_64-w64-mingw32/include  tlsthrd.c   corecrt.h   excpt.h   minwindef.h   ctype.h   basetsd.h   winnt.h   guiddef.h   stdlib.h   malloc.h   minwinbase.h   virtdisk.h   synchapi.h   processthreadsapi.h   errhandlingapi.h     	�Eh    � ��
t0u0J
Y�JxXuXI��uc[Jf r<"XX�[/rLhwrsKg
/X  t <�Yb2Ji�thv	Ut	s�>
.$1
G0
[LY�t
s�*�Y�
fL�a�XJf�J	tf@]f� 
fZ&t
�<
KY&^r��.Y a    [   �
      C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt  tlsmcrt.c    k    e   �
      C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt  pseudo-reloc-list.c    O   �  �
      C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt C:/mingw64tdm/x86_64-w64-mingw32/include  pesect.c   corecrt.h   excpt.h   minwindef.h   ctype.h   basetsd.h   winnt.h   guiddef.h   stdlib.h   malloc.h   virtdisk.h   rpcdce.h   wtypesbase.h   unknwnbase.h   objidlbase.h   cguid.h   wtypes.h   objidl.h   oleidl.h   servprov.h   oaidl.h   msxml.h   urlmon.h   propidl.h   oleauto.h   winioctl.h   winsmcrd.h   winscard.h   commdlg.h   string.h     	 Hh    	C
JK-��q<	t�Q
J>!JY%I@
t&Y<UI
`/��yCXiI-tS<�0MQ
J>!JY%I�I\
N�p�<�_K�� t�<� f�<XX� MQH"8JgA
>!XY%Wj
t&YJUIX4@jK�� t�.�� MQ
LY�K�� x<�<t�� MQ
J>!JY%I2
vZzJIT<�K�~�t�~<�
�g�K�~�t�~.�f�~<XX�LQ�~�Jg�~
>!%YWY�
t&Y<UIX� 4�<<Y�K�~�t�~<�f�~<XX�MQ
J>gM�~!JY%IN
t&Y<UIX�<m�=
XK	xJ
* tw
Y0JB< �    \   �
      C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt  CRT_fp10.c     	 Lh    		3 {    S   �
      ../../../../../src/gcc-git-9.2.0/libgcc/config/i386  cygwin.S     	Lh    � ""gY0uKgg0=L"" �   �  �
      C:/mingw64tdm/x86_64-w64-mingw32/include C:/crossdev/src/gcc-git-9.2.0/include ../.././gcc C:/crossdev/src/gcc-git-9.2.0/gcc/config/i386 C:/crossdev/src/gcc-git-9.2.0/libgcc ../../../../../src/gcc-git-9.2.0/libgcc  corecrt.h   stdlib.h   malloc.h   process.h   getopt.h   time.h   hashtab.h   insn-constants.h   i386.h   i386-opts.h   libgcc2.h   gbl-ctors.h   libgcc2.c       �  �
      C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/crt C:/mingw64tdm/x86_64-w64-mingw32/include  dllentry.c   corecrt.h   excpt.h   minwindef.h   ctype.h   winnt.h   guiddef.h   stdlib.h   malloc.h   virtdisk.h   rpcdce.h   wtypesbase.h   unknwnbase.h   objidlbase.h   cguid.h   wtypes.h   objidl.h   oleidl.h   servprov.h   oaidl.h   msxml.h   urlmon.h   propidl.h   oleauto.h   winioctl.h   winsmcrd.h   winscard.h   commdlg.h   process.h     	PLh     K   �   �
      C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/misc C:/mingw64tdm/x86_64-w64-mingw32/include  onexit_table.c   corecrt.h   corecrt_startup.h   process.h   stdlib.h   malloc.h     	�Lh    Y0-"��u[��g�XZ
�XKWJ=Y/X.	rf)"<	�"K�	=	\:	= 	K�	n�	=	\%s	Ku�
{�<�?XY=
K
Q0"�-�SXu,ZZJ	Y
<
Y,J\�t . �    �   �
      C:/crossdev/src/mingw-w64-v7-git20191109/mingw-w64-crt/stdio C:/mingw64tdm/x86_64-w64-mingw32/include  acrt_iob_func.c   stdio.h     	`Nh    	Y/X�=X                                                                                                                                                                                                                                                                                                                                                                ���� x �              h           �       h    �      B�B�A �A(�A0�A8�D`x
8A�0A�(A� A�B�B�D�
8A�0A�(A� A�B�B�FI
8A�0A�(A� A�B�B�G   L        h    D      B�B�B �A(�A0�DPe
0A�(A� B�B�B�G $       Ph    O       DPV
Fj           �h              ���� x �         X  `8h    :       D0u  4   X  �8h    j       A�A�D@@
A�A�H       X  9h              ���� x �      l   �  09h    �       B�A�A �A(�A0�D`m
0A�(A� A�A�B�Cs
0A�(A� A�A�B�G       ,   �  :h    �       A�A�A �C
         ���� x �      $   �  ;h    /       D0R
JN    L   �  @;h    �       A�A�D@e
A�A�Ct
A�A�JNA�A�       �  �;h              ���� x �      $   @  �;h    j       B�A�DP   l   @  P<h    
      B�A�A �A(�A0�D��
0A�(A� A�A�B�EG
0A�(A� A�A�B�K     \   @  `>h    �      A�B�B �B(�B0�A8�A@�AH�	D�H O
�A�A�B�B�B�B�A�F     ���� x �      ,   P  PAh    �      D0b
JR
AS
A L   P  Ch    �       B�A�A �A(�A0�DP{
0A�(A� A�A�B�E  <   P  Dh    �      B�D0{
B�Eb
B�DV
B�A   ���� x �      D   (  �Eh    k       B�A�A �A(�DPV(A� A�A�B�     L   (  0Fh    x       A�A�A �D@S
 A�A�A�CO
 A�A�A�A 4   (  �Fh    �       A�D0R
A�HV
A�I   4   (  @Gh    �       A�D0l
A�F�A�         ���� x �         H   Hh              H  @Hh              H  `Hh    H       L   H  �Hh    �       B�A�A �D@}
 A�A�B�GJ A�A�B�       H  PIh    �       D0|    H  �Ih    +       D0f     H  Jh    l       D0g    H  �Jh    (       D0c  $   H  �Jh    �       D0o
EL   $   H  @Kh    �       D0{
Aq      ���� x �         �   Lh              ���� x �      ,   �  Lh    2       A�A�n�A�         ���� x �         @  PLh              ���� x �         p  �Lh    %       D   p   Mh    �       A�A�A �A(�DPG
(A� A�A�A�E  <   p  �Mh    q       B�A�A �D@d A�A�B�         ���� x �      $   (	  `Nh           A�D0XA�                                                                                                                                                         hDllHandle _pei386_runtime_relocator DllEntryPoint __mingw_init_ehandler lock_free __enative_startup_state dwReason _onexit_table_t __security_init_cookie _amsg_exit _execute_onexit_table _register_onexit_function lpreserved _initialize_onexit_table refcount _initterm refcount __enative_startup_state refcount GetCurrentProcessId SetUnhandledExceptionFilter HighPart ExceptionRecord RtlCaptureContext RtlVirtualUnwind TerminateProcess RtlLookupFunctionEntry GetCurrentThreadId GetSystemTimeAsFileTime QueryPerformanceCounter UnhandledExceptionFilter GetTickCount refcount GetCurrentProcess dwReason refcount hDllHandle lpreserved __mingw_TLScallback _GetPEImageBase VirtualProtect __acrt_iob_func __mingw_GetSectionCount sSecInfo vfprintf __mingw_GetSectionForAddress __enative_startup_state GetLastError VirtualQuery refcount _GetPEImageBase old_handler _FindPESectionExec RtlAddFunctionTable refcount ContextRecord reset_fpu _FindPESectionByName ExceptionRecord _fpreset InitializeCriticalSection GetLastError TlsGetValue refcount LeaveCriticalSection EnterCriticalSection DeleteCriticalSection _fpreset pSection TimeDateStamp pNTHeader Characteristics pImageBase VirtualAddress iSection refcount ../../../../../src/gcc-git-9.2.0/libgcc/config/i386/cygwin.S C:\crossdev\gccmaster\build-tdm64\gcc-seh\x86_64-w64-mingw32\libgcc GNU AS 2.33.1 refcount _onexit_table_t __iob_func                                                                                                                                                                    �      �       R�      �       Q�      �       �R�                                         0       R0      w       ]w      z       �R�z      �       R�      �       ]�      �       R�             ]             R      D       ]                                         0       Q0      u       \u      z       �Q�z      �       Q�      �       \�      �       Q�             \             Q      D       \                               0       X0      r       Sr      z       �X�z      D       S                                                    >       1�X      \       P\      a       ^a      d       0�d      y       ^y      z       Pz      �       1��      �       P�      �       P�      �       ^�      �       P�             ^             1�      ;       P;      ?       ^?      D       P                        P      n       Rn      o       �R�o      �       R�      �       �X                        P      n       Qn      o       �Q�o      �       Q�      �       �d                        P      n       Xn      o       �X�o      �       X�      �       �h                                       S        RS       �        �R��       �        R�       P       \P      S       �R�S      j       Rj      �       �R��      �       \                                   ,        Q,       �        �Q��       �        Q�       S       �Q�S      j       Qj      �       �Q�                                       S        XS       �        ]�       �        �X��       �        X�       R       ]R      S       �X�S      j       Xj      �       ]                            S       ^        P^       _        Tj       x        Px       �        Tj      �       T�      �       s                  _       j        0�                 _       j        1�                 �      �       0�                   �       A       1��      �       1�                      �       �        X�       A       ]�      �       ]                      �       �        R�       A       \�      �       \                       �       �        0��       �        P�       �        P�      �       P                    �       �        T�      �       T                       �       �        0��       A       U�      �       0��      �       U                �       �        0�                 �       �        P                 �       �        0�                 �       �        T                 �      �       0�                      R       W        P�       �        P�       �        R                    W       `        R`       g        P                    �       �        R�       �       S                        �       T                              !       P!      R       Y�      �       P�      �       Y                                  P       X        TX       ]        p ����t '�]       `        v ����t '�`       e        v ����p ����'t '�e       n        v ����u ����'t '�n       s        v ����u ����'p ����'t '�s       y        v ����u ����'| ����'t '��       �        P�       �        | ����t '������?�                            $        R$       /        �R�                            $        Q$       /        �Q�                            $        X$       /        �X�                        0       s        Rs       �        �R��       �        R�       �        �R�                        0       s        Qs       �        �Q��       �        Q�       �        �Q�                        0       s        Xs       �        �X��       �        X�       �        �X�                   `       s        Rs       �        �R�                 `       �        2�                   `       s        Xs       �        �X�                   s       �        S�       �        sx�                   `       g       
 H�h    �g       �        S                  �      �       P                           _      �       Y�      �       Q�              Y�      �       Y�      �       YA      W       Y                                                      P_      �       R�              Rd      s       Ps      �       | s ��      �       | s #��      �       R�      �       R      *       s�����} "�*      -       s|�����} "�-      6       RA      K       RK      W       s�����} "�                            @       S@      _       st�X      e       S                        @      �       S�             st�             S�      �       SA      X       S                 �      �       4�                 �      �       _                 �      �       R                 �      �       1�                 �      �       _                 �      �       R                 �             2�                 �             _                 �              R                 �      �       8�                 �      �       _                 �      �       R                    �      *       S*      7       sx�7      A       S                 2      7       4�                 2      7       U                 2      6       R                          '       0�'      d       T                                    R       j        \                                p       �        R�       i       \i      t       �R�t      �       \�      �       �R��             \             R      }       \                                p       �        Q�       i       Ti      t       �Q�t      �       T�      �       �Q��             T             Q      }       T                                    p       �        X�       i       Si      t       �X�t      �       S�      �       �X��      $       S$      6       �X�6      B       SB      R       �X�R      }       S                         �       �        R�       G       \�             \             RR      }       \                         �       �        P�       G       V�             VR      `       P`      }       V                     �       �        0��       �        R             0�                                  �      �       R�      C       \C      D       �R�D      �       \�      �       R�      �       �R��      �       \�      �       �R��      p       \                                    2       Po      �       P�      �       P             P       .       P7      N       PY      g       P                               �      =       0�D      �       0��      �       	���      �       0��      �       0��      �       0��      �       	���             0�              	��       /       0�/      7       	��7      h       0�h      p       	��                         �             0�      8       1�D      �       0��      �       0��      �       0��      p       0�                                            9        R9       g        �R�g       �        R�       �        �R��       �        R�              �R�             R      6       �R�6      K       RK      �       �R�                                            )        Q)       g        �Q�g       �        Q�       �        �Q��       �        Q�              �Q�             Q      6       �Q�6      K       QK      �       �Q�                                            =        X=       g        �X�g       �        X�       �        �X��       �        X�       �        �X��       �        X�              �X�             X      �       �X�                                            =        Y=       g        �Y�g       �        Y�       �        �Y��       �        Y�              �Y�             Y      6       �Y�6      K       YK      �       �Y�                                  _        1�g       �        1��       �        0��              1�             0�      .       1�.      6       0�6      o       1�o      w       0�w      �       1��      �       0�                              >       V        P�       �        P�              P      -       PK      n       Pw      �       P�      �       P                            2        0�2       \        1�g       �        0�      �       0�                       �      �       0�      F       0�F      W       \W      w       |�w      �       \                    F      j       P�      �       P                      �      �       P�             T      �       T                                    �      �       R�      �       �R��      �       R�      �       �R��      �       R�      �       �R��      �       R�      H       �R�H      T       RT      `       �R�                                    �      �       Q�      �       �Q��      �       Q�      �       �Q��      �       Q�      �       �Q��      �       Q�      H       �Q�H      T       QT      `       �Q�                                    �      �       X�      �       �X��      �       X�      �       �X��      �       X�      �       �X��      �       X�      H       �X�H      T       XT      `       �X�                                  S             R      H       S                        !       S                        �              R      g       Sg      h       �R�h      y       S                             -       0�-      -       Q-      8       R8      R       Qh      y       Q                      $      -       R-      ;       P;      R       Rh      y       R                              p       �        R�       �        U�       �        �R��       �        R�       �        U�       �        �R��       �        U                              p       �        Q�       �        T�       �        �Q��       �        Q�       �        T�       �        �Q��       �        T                        �       �        P�       �        S�       �        P�       �        S                         `        S                    7       8        P8       T        \                           <       R<      �       X                      L      c       Rc      �       {<� $ &{ "��      �       {<� $ &{ "�                  �      �       P                        R      �       P�      �       {<� $ &{ "#��      �       P�      �       {<� $ &{ "#�                 V      �       P                   V      c       Rc      �       {<� $ &{ "�                     _      s       Q�      �       q(��      �       Q                 V      s       0�                      �      �       R�      �       X�             �R�                    �      �       R             R                               P                 �      �       R                  �      �       Y                  �      �       P                 �      �       0�                    �             R      \       Y                        T       R                  !      T       X                       5       0�                      0      L       RL      �       Y�      �       �R�                  _      �       R                 _      �       R                  b      �       Z                  l      �       X                 b      �       0�                          �       �        R�              S             �R�      +       S+      /       �R�                  �       �        R                  �              \                 �       �        0�                                 Q                                q�                         3        R                    @       G        RG       �        �R�                    G       T        RT       �        �R#<� $ &�R"�                  P       �        P                 G       d        0�                                    R       ~       T~      �       �R�                  (      �       \                  ,      }       S                ,      H       T                            0       L        RL       �        S�       �        �R��       �        S�       �        R�              S                            0       P        QP       �        U�       �        �Q��       �        U�       �        Q�              U                    �       �        p 8��       �        t 8�                    �       �        P�       �        s                                       R               S               R                                                                                                                                                                                                                                                                                                                                                                                      <       D       J       �       p      �      �      �                      <       D       _       j                       �      �      �      �                      �       F      �      �                      �       �       �       �                       z       �       �       �       �       G      �             R      }                      �      �                  h      e                      @      K      �      �      �      �      �      �      �      �                      d      g      {      ~      �      �                      �      �      �      �      �                            �      �      �      �      �      �                            &      *      7                            &      2      7                      �       �       �       �                       4      ;      >      D      G      Q                      U      Y      _      �                      �      �      �      �                      �      �      �      
                      d      k      n      z                      �      �      �      �      �      �                      �      �      �      �                      $      +      .      4      7      A                      ,      <      A      H                                                                                                                                                                                                                      .file   :   ��  gcrtdll.c              j                                u               �              �              �                            �                           �   �                          P                        $  @                        :  0                        P                           f             z  �                        �  P          �  `                    atexit  �      .text          �  (             .data                           .bss                            .xdata         4                 .pdata         <                    �     	                       �      
   �i  �                 �         T                    �         �
                              0                                                          �                   )         
                    4  p                          ?         X  
             .file   K   ��  gcygming-crtbegin.c    L  �                           a  �      .text   �                     .data                           .bss                             .xdata  4                       .pdata  <                          4  �                      .file   c   ��  gdllmain.c             x  �                       DllMain       testdll B          �  `          �  �          �  �          �  �      .text   �                    .data                           .bss                             .rdata                          .xdata  <      P                 .pdata  T      T                    4  �                      .file   �   ��  gsha256_hkdf.c         �  �                           �  "          �  I          �  s            �      K                     �          /  g          E             T  �          j             y  s          �  �          �  �          �  �          �  <!          �  u!          �   "            H"            "#      .text   �     s                .data                           .bss                             .xdata  �      �                 .pdata  �      �   6             .rdata         @                    4  �                      .file   �   ��  gtest.c            test_sum`%                       .text   `%     1                .data                           .bss                             .rdata  `     
                 .xdata  h                      .pdata  �                         4  �                      .file   �   ��  g    t            pwd_otp @          )  �          9  �      cur_utc �          J  �      otp_str �          ^  �%                       set_ltmk3&      get_ltmk�&      set_utc �&      gen_opt �&      .text   �%     �               .data                           .bss                           .xdata  t     8                 .pdata  �     <                .rdata  p     *                     4                        .file   �   ��  ggccmain.c             �  `(                       p.92473            �  �(          �  �                    __main  )          �  @      .text   `(     �                .data                         .bss    @                      .xdata  �                      .pdata  �     $   	                 �  �i  
   �O                   �  T                         �  �
     �                        0      0                      �     �                   )  
     	                     4  0                          ?  X     �                .file   �   ��  gnatstart.c        .text   0)                       .data                           .bss    P                           �  ��  
   �S  
                 �  _     �                       `                             �     e                    )       !                     4  P                      .file     ��  ggs_support.c          �  0)                           �  p                            �                          (  *          ;  `          L  @          _  �      .text   0)     �               .data   0                        .bss    `     x                .xdata  �                       .pdata  �                     .rdata  �                         �  G
 
   �-  @                 �  ,	     %                    �  1                            �      0                      B     �                   )  7                         4  p                          ?  �     �                .file   L  ��  gtlssup.c              t  +                           �  @+          �  �                    __xd_a  H   	    __xd_z  P   	        �  �+      .text   +     �                .data   0                        .bss    �                      .xdata  �                      .pdata       $   	             .CRT$XLD8   	                   .CRT$XLC0   	                   .rdata  �     H                .CRT$XDZP   	                    .CRT$XDAH   	                    .CRT$XLZ@   	                    .CRT$XLA(   	                    .tls$ZZZ   
                    .tls        
                        �  �: 
   U$  8                 �  Q     V                    �  M
                           �      0                      �
     �                   )  O     <                     4  �                          ?  �     �                .file   f  ��  gcinitexe.c        .text   �+                       .data   0                        .bss    �                       .CRT$XCZ   	                    .CRT$XCA    	                    .CRT$XIZ    	                    .CRT$XIA   	                        �  /_ 
   �                   �  �     _                        �                             �     f                     4  �                      .file   x  ��  gmingw_helpers.c   .text   �+                       .data   0                        .bss    �                          �  a 
   �                    �       .                                                          k                     4  �                      .file   �  ��  gpseudo-reloc.c        �  �+                           �  P,          �        the_secs          �  `.                          �                        @  �                        m  �                    .text   �+     e  %             .data   0                        .bss                           .rdata                        .xdata  �     4                 .pdata  (     $   	                 �  b 
   k`  �                 �  4     �                    �  a     v	                             0                                                  |     v                   )  �     �                     4  �                          ?  @                    .file   �  ��  gcrt_handler.c         �  P1                           �  3          �  (          �  @	          �  @          �  4      .text   P1     p               .data   0                        .bss          �                .xdata  0                       .rdata  0     X                .pdata  L     $   	                 �  �� 
   �a  Z                 �  1     b                    �  �     
                       P     0                      �     \                   )  <     �                     4                            ?  P     �                .file   �  ��  gtlsthrd.c             �  �5                             �
          #  �
          1  06          N  �
          a  �6          �  @7      .text   �5     `  %             .data   0                        .bss    �
     H                 .xdata  P     ,                 .pdata  p     0                    �  d$ 
   	'  S                 �  �     v                    �  �#     �                       �     0                      N     [                   )  �     �                     4  0                          ?  (                     .file      ��  gtlsmcrt.c         .text    8                       .data   0                       .bss                                �  mK 
   �                    �  	     .                        �                            �     e                     4  P                      .file     ��  g    �            .text    8                       .data   @                        .bss                               �  _L 
   G                   �  7     .                        �                                  o                     4  p                      .file   ;  ��  gpesect.c              �   8                           �  @8          �  `8          �  �8          �  P9            �9          /  :          B  �:          R  �:          o  @;      .text    8     �  	             .data   @                        .bss    0                       .xdata  |     H                 .pdata  �     x                    �  �M 
   w^  �                 �  e     �                    �  �*     \                       �     0                      0     �                      }      S                   )  S     ]                     4  �                          ?  H     �               .file   V  ��  gCRT_fp10.c        _fpreset <                       fpreset  <      .text    <                      .data   @                        .bss    0                       .xdata  �                      .pdata                           �  � 
                      �  E     .                              0                      �&     �                    4  �                          ?  �     0                .file   j  ��  gfake                  �  � 
   .                    �  s                            T'                     .text   <     2                 .data   @                        .bss    0                              P     0                    )  �     �                     ?  �     H                .file   |  ��  glibgcc2.c         .text   P<                       .data   @                        .bss    0                           �  L� 
   R                   �  �     5                       �                            �'     �                    4  �                      .file   �  ��  gdllentry.c            �  P<                       .text   P<                      .data   @                        .bss    0                       .xdata  �                      .pdata  $                         �  �� 
   �L                   �  �     h                       �     0                      q)                        )  ?     	                     4  �                          ?  @     0                .text   `<      .data   @       .bss    0      .idata$7X      .idata$5�      .idata$4�      .idata$6�      .text   h<      .data   @       .bss    0      .idata$7T      .idata$5�      .idata$4�      .idata$6�      .text   p<      .data   @       .bss    0      .idata$7P      .idata$5�      .idata$4�      .idata$6~      .text   x<      .data   @       .bss    0      .idata$7L      .idata$5�      .idata$4x      .idata$6t      .text   �<      .data   @       .bss    0      .idata$7D      .idata$5�      .idata$4h      .idata$6b      .text   �<      .data   @       .bss    0      .idata$7@      .idata$5�      .idata$4`      .idata$6X      .text   �<      .data   @       .bss    0      .idata$7<      .idata$5�      .idata$4X      .idata$6N      .text   �<      .data   @       .bss    0      .idata$78      .idata$5�      .idata$4P      .idata$6D      .text   �<      .data   @       .bss    0      .idata$74      .idata$5�      .idata$4H      .idata$6:      .text   �<      .data   @       .bss    0      .idata$70      .idata$5�      .idata$4@      .idata$62      .text   �<      .data   @       .bss    0      .idata$7,      .idata$5�      .idata$48      .idata$6(      .text   �<      .data   @       .bss    0      .idata$7(      .idata$5�      .idata$40      .idata$6       .text   �<      .data   @       .bss    0      .idata$7      .idata$5x      .idata$4      .idata$6      .text   �<      .data   @       .bss    0      .idata$7      .idata$5p      .idata$4      .idata$6�      .file     ��  gonexit_table.c        �  �<                           �   =          �  �=      .text   �<     �               .data   @                      .bss    0                       .xdata  �                       .pdata  0     $   	                 �  F 
   �  &                 �  $     �                    �  �1     `                       �     0                           0                       �+     O                   )  H                          4                            ?  p     �                .file   :  ��  gacrt_iob_func.c       �  `>                       .text   `>                     .data   `                      .bss    0                       .xdata  �                      .pdata  T                         �  �  
   �  
                 �  !     �                     �  K4     O                              0                      �-     �                    )  X                          4  0                          ?  (	     @                .file   d  ��  gfake              hname         fthunk  h      .text   �>                       .data   p                        .bss    0                       .idata$2                      .idata$4      .idata$5h      .text   �>      .data   p       .bss    0      .idata$7H      .idata$5�      .idata$4p      .idata$6j      .text   �>      .data   p       .bss    0      .idata$7$      .idata$5�      .idata$4(      .idata$6      .text   �>      .data   p       .bss    0      .idata$7       .idata$5�      .idata$4       .idata$6      .text   �>      .data   p       .bss    0      .idata$7      .idata$5h      .idata$4      .idata$6�      .file   y  ��  gfake              .text   �>                       .data   p                        .bss    0                       .idata$4�                      .idata$5�                      .idata$7\                      .text   �>      .data   p       .bss    0      .idata$7h      .idata$5       .idata$4�      .idata$6�      .file   �  ��  gfake              hname   �      fthunk         .text   �>                       .data   p                        .bss    0                       .idata$2(                      .idata$4�      .idata$5       .file   /  ��  gfake              .text   �>                       .data   p                        .bss    0                       .idata$4�                      .idata$5                      .idata$7l                      .text   �>      .data   p       .bss    0      .idata$7       .idata$5X      .idata$4�       .idata$6�      .text   �>      .data   p       .bss    0      .idata$7�      .idata$5P      .idata$4�       .idata$6�      .text   �>      .data   p       .bss    0      .idata$7�      .idata$5H      .idata$4�       .idata$6�      .text   �>      .data   p       .bss    0      .idata$7�      .idata$5@      .idata$4�       .idata$6�      .text   �>      .data   p       .bss    0      .idata$7�      .idata$58      .idata$4�       .idata$6�      .text   �>      .data   p       .bss    0      .idata$7�      .idata$50      .idata$4�       .idata$6~      .text   �>      .data   p       .bss    0      .idata$7�      .idata$5(      .idata$4�       .idata$6`      .text   �>      .data   p       .bss    0      .idata$7�      .idata$5       .idata$4�       .idata$6L      .text   �>      .data   p       .bss    0      .idata$7�      .idata$5      .idata$4�       .idata$62      .text   �>      .data   p       .bss    0      .idata$7�      .idata$5      .idata$4�       .idata$6      .text    ?      .data   p       .bss    0      .idata$7�      .idata$5      .idata$4�       .idata$6      .text   ?      .data   p       .bss    0      .idata$7�      .idata$5       .idata$4�       .idata$6�      .text   ?      .data   p       .bss    0      .idata$7�      .idata$5�      .idata$4�       .idata$6�      .text   ?      .data   p       .bss    0      .idata$7�      .idata$5�      .idata$4�       .idata$6�      .text    ?      .data   p       .bss    0      .idata$7�      .idata$5�      .idata$4�       .idata$6�      .text   (?      .data   p       .bss    0      .idata$7�      .idata$5�      .idata$4�       .idata$6�      .text   0?      .data   p       .bss    0      .idata$7�      .idata$5�      .idata$4x       .idata$6�      .text   8?      .data   p       .bss    0      .idata$7�      .idata$5�      .idata$4p       .idata$6j      .text   @?      .data   p       .bss    0      .idata$7�      .idata$5�      .idata$4h       .idata$6T      .text   H?      .data   p       .bss    0      .idata$7�      .idata$5�      .idata$4`       .idata$6@      .text   P?      .data   p       .bss    0      .idata$7�      .idata$5�      .idata$4X       .idata$6(      .text   X?      .data   p       .bss    0      .idata$7�      .idata$5�      .idata$4P       .idata$6      .file   =  ��  gfake              hname   P       fthunk  �      .text   `?                       .data   p                        .bss    0                       .idata$2                       .idata$4P       .idata$5�      .file   K  ��  gfake              .text   `?                       .data   p                        .bss    0                       .idata$4                       .idata$5`                      .idata$7     
                 .file   _  ��  gcygming-crtend.c      �  `?                       .text   `?                       .data   p                        .bss    0                           	  `?                         	  �                          )	  `                         8	  x?                         4  P                      __xc_z     	        E	  p          d	  �          p	            �	              �	  �?          �	  �      printf  �<          �	             �	  �>          �	  �      _lock   �>          �	      
        
            $
    h��   __xl_a  (   	        0
  �>          <
  0?          I
             [
  (?          s
  �          �
  p          �
      ��       �
     ��       �
              �
  �>          �
      ��       
     ��       &  (   	        8  �      __xl_d  8   	    _tls_end   
        T  �          j  �>          w     	        �  p          �  (   	        �      
    memcpy  �<      puts    �<          �  �          �             �  �          
  @?      _CRT_MT 0           !  �>          -  �>          >              L  p          o  �>          �     ��       �  �          �  �          �            �  �>          �  �          �  H   	        	
  �          $
  �      abort   �<          7
  �          a
  H   	    __dll__     ��       q
      ��       �
  �          �
  P?          �
  �          �
  @          �
    h��       �
  �          �
  �>               ��         $           2  l      calloc  �<          L  �          V  �          c  �>          |            �  �          �  p          �        Sleep   �>          �  �           �  �          �  p?          �                @      __xi_z      	           ?      pcinit     	        (              @     	        P   ?          d             �  �          �  �      signal  x<          �  0          �      	        �  �      strncmp h<          �  8            \            p?          .  �          N  �          [  H       realloc �>          {      ��       �             �  �          �     ��       �  �          �  �          �  ?      memset  �<            �          #     ��       8  �          F  H      __xl_z  @   	    __end__              e  (          �  `          �  �?          �  �>          �  P       __xi_a     	        �  0          �  ?      __xc_a      	        �     ��         H   	        (     ��       6  �<          A  �           ^  @          p  H?          �  @           �  P      __xl_c  0   	        �     
        �  ?          �  X          �  x          �  �            h          #  �          ;  (           S  �          j  �<      fwrite  �<          t  �          �             �  `           �      ��       �      ��       �  <          �  8          �  �            8?          2  p          D     ��       `      ��       x  �          �  �          �  �          �         strlen  p<          �  P          �  X?          �            �  p      _unlock �>            �          1  0          @  H   	    vfprintf`<      free    �<          P  p       b  .debug_aranges .debug_info .debug_abbrev .debug_line .debug_frame .debug_str .debug_loc .debug_ranges pre_c_init atexit_table _CRT_INIT __proc_attached .rdata$.refptr.__native_startup_lock .rdata$.refptr.__native_startup_state .rdata$.refptr.__dyn_tls_init_callback .rdata$.refptr.__xi_z .rdata$.refptr.__xi_a .rdata$.refptr.__xc_z .rdata$.refptr.__xc_a __DllMainCRTStartup .rdata$.refptr.__native_dllmain_reason DllMainCRTStartup .rdata$.refptr.mingw_app_type .CRT$XIAA .debug_info .debug_abbrev .debug_loc .debug_aranges .debug_ranges .debug_line .debug_str .rdata$zzz .debug_frame __gcc_register_frame __gcc_deregister_frame HelloWorld dll_set_ltmk dll_get_ltmk dll_set_utc dll_gen_opt mbedtls_zeroize mbedtls_sha256_init mbedtls_sha256_free mbedtls_sha256_clone mbedtls_sha256_starts mbedtls_sha256_process mbedtls_sha256_update sha256_padding mbedtls_sha256_finish mbedtls_sha256 mbedtls_md_init mbedtls_md_free mbedtls_md_setup mbedtls_md_hmac_starts mbedtls_md_hmac_update mbedtls_md_hmac_finish mbedtls_md_hmac_reset mbedtls_md_hmac mbedtls_sha256_hkdf prev_verify_otp pwd_otp_gen_time pwd_otp_ltmk_is_set pwd_convert_from_uint cert_otp_generator.c __do_global_dtors __do_global_ctors .rdata$.refptr.__CTOR_LIST__ initialized __security_init_cookie .data$__security_cookie .data$__security_cookie_complement __report_gsfailure GS_ContextRecord GS_ExceptionRecord GS_ExceptionPointers __dyn_tls_dtor __dyn_tls_init .rdata$.refptr._CRT_MT __tlregdtor __report_error __write_memory.part.0 maxSections _pei386_runtime_relocator was_init.93799 .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ .rdata$.refptr.__RUNTIME_PSEUDO_RELOC_LIST__ .rdata$.refptr.__image_base__ __mingw_SEH_error_handler __mingw_init_ehandler was_here.93643 emu_pdata emu_xdata _gnu_exception_handler __mingwthr_run_key_dtors.part.0 __mingwthr_cs key_dtor_list ___w64_mingwthr_add_key_dtor __mingwthr_cs_init ___w64_mingwthr_remove_key_dtor __mingw_TLScallback pseudo-reloc-list.c _ValidateImageBase.part.0 _ValidateImageBase _FindPESection _FindPESectionByName __mingw_GetSectionForAddress __mingw_GetSectionCount _FindPESectionExec _GetPEImageBase _IsNonwritableInCurrentImage __mingw_enum_import_library_names DllEntryPoint _initialize_onexit_table _register_onexit_function _execute_onexit_table __acrt_iob_func register_frame_ctor .text.startup .xdata.startup .pdata.startup .ctors.65535 ___RUNTIME_PSEUDO_RELOC_LIST__ __imp_abort __lib64_libkernel32_a_iname __data_start__ ___DTOR_LIST__ __imp__lock __imp_RtlVirtualUnwind SetUnhandledExceptionFilter __imp_calloc ___tls_start__ .refptr.__native_startup_state __ImageBase MessageBoxA GetLastError __imp_MessageBoxA GetSystemTimeAsFileTime mingw_initltssuo_force __rt_psrelocs_start __dll_characteristics__ __size_of_stack_commit__ __mingw_module_is_dll __iob_func __size_of_stack_reserve__ __major_subsystem_version__ ___crt_xl_start__ __imp_DeleteCriticalSection .refptr.__CTOR_LIST__ VirtualQuery ___crt_xi_start__ __imp__amsg_exit ___crt_xi_end__ _tls_start .refptr.__RUNTIME_PSEUDO_RELOC_LIST__ __mingw_oldexcpt_handler __imp_GetCurrentThreadId GetCurrentProcessId TlsGetValue TerminateProcess __bss_start__ ___RUNTIME_PSEUDO_RELOC_LIST_END__ RtlLookupFunctionEntry __size_of_heap_commit__ __imp_GetLastError __imp_free __imp_RtlLookupFunctionEntry VirtualProtect mingw_app_type ___crt_xp_start__ __imp_LeaveCriticalSection __imp_GetTickCount .refptr.__RUNTIME_PSEUDO_RELOC_LIST_END__ ___crt_xp_end__ __minor_os_version__ __imp_GetSystemTimeAsFileTime EnterCriticalSection __imp_puts .refptr.__xi_a __image_base__ .refptr._CRT_MT RtlCaptureContext __section_alignment__ __native_dllmain_reason __lib64_libuser32_a_iname _tls_used __imp_memset UnhandledExceptionFilter __IAT_end__ __imp_memcpy __RUNTIME_PSEUDO_RELOC_LIST__ __imp_RtlAddFunctionTable __data_end__ __imp_fwrite __CTOR_LIST__ _head_lib64_libkernel32_a __bss_end__ GetTickCount __native_vcclrit_reason ___crt_xc_end__ RtlAddFunctionTable .refptr.__native_startup_lock __imp_EnterCriticalSection _tls_index __native_startup_state ___crt_xc_start__ __imp_GetCurrentProcessId __imp_TerminateProcess __lib64_libmsvcrt_os_a_iname ___CTOR_LIST__ .refptr.__dyn_tls_init_callback __imp_signal __imp__register_onexit_function __rt_psrelocs_size __imp_QueryPerformanceCounter __imp_strlen __file_alignment__ __imp_InitializeCriticalSection __imp_realloc InitializeCriticalSection __imp_vfprintf __major_os_version__ __IAT_start__ __imp_UnhandledExceptionFilter __imp_SetUnhandledExceptionFilter .refptr.mingw_app_type __DTOR_LIST__ RtlVirtualUnwind __imp__initialize_onexit_table __imp_Sleep LeaveCriticalSection __size_of_heap_reserve__ ___crt_xt_start__ __subsystem__ _amsg_exit __security_cookie_complement __imp_TlsGetValue GetCurrentProcess __imp__execute_onexit_table __imp_VirtualProtect ___tls_end__ QueryPerformanceCounter __imp_VirtualQuery __imp__initterm mingw_initltsdyn_force __imp___iob_func __dyn_tls_init_callback _head_lib64_libuser32_a .refptr.__image_base__ _initterm __imp_strncmp _head_lib64_libmsvcrt_os_a __imp___acrt_iob_func __major_image_version__ __loader_flags__ ___chkstk_ms __native_startup_lock .refptr.__native_dllmain_reason GetCurrentThreadId __rt_psrelocs_end __minor_subsystem_version__ __minor_image_version__ __imp__unlock mingw_initltsdrot_force __imp_printf .refptr.__xc_a .refptr.__xi_z DeleteCriticalSection __imp_RtlCaptureContext __RUNTIME_PSEUDO_RELOC_LIST_END__ __imp_GetCurrentProcess .refptr.__xc_z ___crt_xt_end__ __security_cookie 