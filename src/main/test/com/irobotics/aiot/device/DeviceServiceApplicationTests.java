package com.irobotics.aiot.device;

import com.irobotics.aiot.device.remote.IContentServiceRemote;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @description:
 * @author: huangwa1911
 * @time: 2021/9/16 10:24
 */
@SpringBootTest
public class DeviceServiceApplicationTests {

    @Resource
    private IContentServiceRemote contentServiceRemote;

    @Test
    void Test() {
        contentServiceRemote.delByDeviceId("aaa");
    }
}
