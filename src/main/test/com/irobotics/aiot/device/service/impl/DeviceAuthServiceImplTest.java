package com.irobotics.aiot.device.service.impl;

import com.irobotics.aiot.bravo.basic.base.CountryCity;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.config.LogConfig;
import com.irobotics.aiot.bravo.log.util.SpringUtil;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.common.RobotVersion;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.entity.ProductModeEntity;
import com.irobotics.aiot.device.mapper.DeviceInfoMapper;
import com.irobotics.aiot.device.remote.ProductServiceRemote;
import com.irobotics.aiot.device.remote.SmartHomeServiceRemote;
import com.irobotics.aiot.device.service.IDeviceInfoService;
import com.irobotics.aiot.device.vo.LoginEntityVO;
import com.irobotics.aiot.device.vo.LoginKeyReq;
import com.irobotics.aiot.device.vo.ProductSnDetailEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

class DeviceAuthServiceImplTest {

    @InjectMocks
    DeviceAuthServiceImpl deviceAuthService;

    @Mock
    private ProductServiceRemote productServiceRemote;

    @Mock
    private GeoIPRemote ipRemote;

    @Mock
    private IDeviceInfoService deviceInfoService;

    @Mock
    private DeviceInfoMapper deviceInfoMapper;

    @Mock
    private SmartHomeServiceRemote smartHomeServiceRemote;

    @Value("${application.type}")
    private String applicationType;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private DeviceInfoCache deviceInfoCache;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        Mockito.when(applicationContext.getBean(LogConfig.class)).thenReturn(new LogConfig());
        ApplicationContextAware applicationContextAware = new SpringUtil();
        applicationContextAware.setApplicationContext(applicationContext);
        SystemContextUtils.setContextValue(ContextKey.TENANT_ID, "0");
        SystemContextUtils.setContextValue(ContextKey.CLIENT_IP, "127.0.0.1");
    }

    public CountryCity geoip(String ip) {
        CountryCity countryCity = new CountryCity();
        countryCity.setContinent("亚洲");
        countryCity.setCountry("中国");
        countryCity.setCity("深圳");
        return countryCity;
    }

    public DeviceInfoEntity getDevice() {
        DeviceInfoEntity device = new DeviceInfoEntity();
        device.setId("1");
        device.setSn("test123456789");
        device.setMac("test123456789");
        device.setNickname("test789");
        device.setDefaultNickname("test789");
        device.setProductId("1");
        device.setProductModeCode("testCode");
        return device;
    }

    public ProductSnDetailEntity getSnDetail() {
        ProductSnDetailEntity snDetail = new ProductSnDetailEntity();
        snDetail.setId("1");
        snDetail.setSn("test123456789");
        snDetail.setKey("testkey");
        return snDetail;
    }

    @Test
    void loginOfNormalLogin() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(getSnDetail()));
        Mockito.when(smartHomeServiceRemote.getBindList(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(Arrays.asList("1")));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfEmptyKey() {
        LoginKeyReq req = new LoginKeyReq();
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfEmptyProductModeCode() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfEmptySn() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setProductModeCode("testCode");
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfEmptyMac() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfEmptyTenantId() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfEmptyPageVersions() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfErrorRemoteProductService() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfEmptyProduct() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess());
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfWrongMacOfEqualWrongMac() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("00:00:00:00:00:00");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");

        DeviceInfoEntity device = getDevice();
        device.setMac("00:00:00:00:00:00");

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(deviceInfoCache.getCacheBySn(Mockito.anyString(), Mockito.anyString())).thenReturn(device);
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(getSnDetail()));
        Mockito.when(smartHomeServiceRemote.getBindList(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(Arrays.asList("1")));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfWrongMacOfStartWith() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("00:00:00:00:00:00");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");

        DeviceInfoEntity device = getDevice();
        device.setMac("00:00:00:00:00:00:00");

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(deviceInfoCache.getCacheBySn(Mockito.anyString(), Mockito.anyString())).thenReturn(device);
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(getSnDetail()));
        Mockito.when(smartHomeServiceRemote.getBindList(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(Arrays.asList("1")));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfWrongMac4() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("00:00:00:00:00:00");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");

        DeviceInfoEntity device = getDevice();
        device.setMac("10:00:00:00:00:00:00");

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(deviceInfoCache.getCacheBySn(Mockito.anyString(), Mockito.anyString())).thenReturn(device);
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(getSnDetail()));
        Mockito.when(smartHomeServiceRemote.getBindList(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(Arrays.asList("1")));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfNormalLoginWithSnRepeat() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");

        DeviceInfoEntity device = getDevice();
        device.setSn("test123456");

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfNormalLoginWithErrorGetProductSn() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        Mockito.when(smartHomeServiceRemote.getBindList(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(Arrays.asList("1")));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfNormalLoginWithSnNotExists() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess());
        Mockito.when(smartHomeServiceRemote.getBindList(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(Arrays.asList("1")));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfNormalLoginWithErrorKey() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey1");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(getSnDetail()));
        Mockito.when(smartHomeServiceRemote.getBindList(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(Arrays.asList("1")));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfNormalLoginWithEmptyKey() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");

        ProductSnDetailEntity snDetail = getSnDetail();
        snDetail.setKey("");

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(snDetail));
        Mockito.when(smartHomeServiceRemote.getBindList(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(Arrays.asList("1")));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfRegisterWithNormal() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");
        productModeEntity.setAliasPrefix("test");
        productModeEntity.setSnSuffixBit(3);

        ProductSnDetailEntity snDetail = getSnDetail();

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(snDetail));
        Mockito.when(productServiceRemote.updateSnDetailForActive(Mockito.any(), Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(ResponseMessage.buildSuccess(1));
        Mockito.when(deviceInfoMapper.insert(Mockito.any())).thenReturn(1);

        Mockito.when(smartHomeServiceRemote.getBindList(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(Arrays.asList("1")));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfRegisterErrorGetSn() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");
        productModeEntity.setAliasPrefix("test");
        productModeEntity.setSnSuffixBit(3);

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfRegisterWithSnNotExists() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");
        productModeEntity.setAliasPrefix("test");
        productModeEntity.setSnSuffixBit(3);

        ProductSnDetailEntity snDetail = getSnDetail();

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess());
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfRegisterWithErrorKey() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");
        productModeEntity.setAliasPrefix("test");
        productModeEntity.setSnSuffixBit(3);

        ProductSnDetailEntity snDetail = getSnDetail();
        snDetail.setKey("key");

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(snDetail));
        Mockito.when(productServiceRemote.updateSnDetailForActive(Mockito.any(), Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(ResponseMessage.buildSuccess(1));
        Mockito.when(deviceInfoMapper.insert(Mockito.any())).thenReturn(1);

        Mockito.when(smartHomeServiceRemote.getBindList(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(Arrays.asList("1")));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfRegisterWithEmptyKey() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");
        productModeEntity.setAliasPrefix("test");
        productModeEntity.setSnSuffixBit(3);

        ProductSnDetailEntity snDetail = getSnDetail();
        snDetail.setKey("");

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(snDetail));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfRegisterWithSnAlreadyActive() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");
        productModeEntity.setAliasPrefix("test");
        productModeEntity.setSnSuffixBit(3);

        ProductSnDetailEntity snDetail = getSnDetail();

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(snDetail));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfRegisterWithErrorUpdateActive() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");
        productModeEntity.setAliasPrefix("test");
        productModeEntity.setSnSuffixBit(3);

        ProductSnDetailEntity snDetail = getSnDetail();

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(snDetail));
        Mockito.when(productServiceRemote.updateSnDetailForActive(Mockito.any(), Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfRegisterWithActiveError() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");
        productModeEntity.setAliasPrefix("test");
        productModeEntity.setSnSuffixBit(3);

        ProductSnDetailEntity snDetail = getSnDetail();

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(snDetail));
        Mockito.when(productServiceRemote.updateSnDetailForActive(Mockito.any(), Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(ResponseMessage.buildSuccess(0));
        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }

    @Test
    void loginOfRegisterError() {
        LoginKeyReq req = new LoginKeyReq();
        req.setKeyt("testkey");
        req.setTenantId("0");
        req.setProductModeCode("testCode");
        req.setSn("test123456789");
        req.setMac("test123456798");
        RobotVersion version = new RobotVersion();
        version.setVersionName("testVersion");
        version.setCtrlVersion("10");
        List<RobotVersion> list = new ArrayList<>();
        list.add(version);
        req.setPackageVersions(list);

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");
        productModeEntity.setAliasPrefix("test");
        productModeEntity.setSnSuffixBit(3);

        ProductSnDetailEntity snDetail = getSnDetail();

        Mockito.when(productServiceRemote.getByCode(Mockito.any(), Mockito.any())).thenReturn(ResponseMessage.buildSuccess(productModeEntity));
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(geoip(null)));
        Mockito.when(productServiceRemote.getBySn(Mockito.any(), Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(snDetail));
        Mockito.when(productServiceRemote.updateSnDetailForActive(Mockito.any(), Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(ResponseMessage.buildSuccess(1));
        Mockito.when(deviceInfoMapper.insert(Mockito.any())).thenReturn(0);

        LoginEntityVO login = deviceAuthService.login(req);
        System.out.println(login);
    }


    @Test
    void getCountryCity() {
        String ip = "127.0.0.1";
        Mockito.when(ipRemote.getIpInfo(Mockito.any())).thenThrow(new RuntimeException());
        System.out.println(deviceAuthService.getCountryCity(ip));
    }
}