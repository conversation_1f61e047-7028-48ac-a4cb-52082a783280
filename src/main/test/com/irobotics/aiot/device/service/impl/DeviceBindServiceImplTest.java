package com.irobotics.aiot.device.service.impl;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.config.LogConfig;
import com.irobotics.aiot.bravo.log.util.SpringUtil;
import com.irobotics.aiot.device.cache.BindKeyCache;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.remote.SmartHomeServiceRemote;
import com.irobotics.aiot.device.service.IDeviceInviteShareService;
import com.irobotics.aiot.device.vo.BindKey;
import com.irobotics.aiot.device.vo.OrCodeBindVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import static org.mockito.Mockito.mock;

class DeviceBindServiceImplTest {

    @InjectMocks
    private DeviceBindServiceImpl bindService;

    @Mock
    RedisTemplate<String, Object> redisTemplate;

    @Mock
    private SmartHomeServiceRemote smartHomeServiceRemote;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private DeviceInfoCache deviceInfoCache;

    @Mock
    private BindKeyCache bindKeyCache;

    @Mock
    private IDeviceInviteShareService inviteShareService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        Mockito.when(applicationContext.getBean(LogConfig.class)).thenReturn(new LogConfig());
        ApplicationContextAware applicationContextAware = new SpringUtil();
        applicationContextAware.setApplicationContext(applicationContext);
        ValueOperations<String, Object> hashOperations = mock(ValueOperations.class);
        Mockito.when(redisTemplate.opsForValue()).thenReturn(hashOperations);
        SystemContextUtils.setContextValue(ContextKey.TENANT_ID, "0");
        SystemContextUtils.setContextValue(ContextKey.ID, "0");
        SystemContextUtils.setContextValue(ContextKey.USERNAME, "test");

    }

    @Test
    void bindApp() {
        String deviceId = "1";
        String userId = "1";
        String key = "key";
        bindService.bindApp(deviceId, userId, key);
    }

    @Test
    void bindAppWithEmptyParams() {
        String deviceId = "1";
        String userId = "1";
        String key = "key";
        bindService.bindApp(deviceId, null, key);
    }

    @Test
    void bindAppConfirm() {
        String userId = "1";
        String key = "key";
        String familyId = "1";
        String roomId = "1";

        BindKey bindKey = new BindKey();
        bindKey.setBindKey("key");
        bindKey.setDeviceId("1");

        DeviceInfoEntity device = new DeviceInfoEntity();
        device.setId("1");
        device.setSn("sn");
        device.setNickname("nickname");
        device.setProductId("1");


        Mockito.when(bindKeyCache.getBindKey(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(bindKey);
        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString())).thenReturn(device);
        Mockito.when(smartHomeServiceRemote.bindDevice(Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(true));
        Mockito.when(inviteShareService.delShare(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(true));
        System.out.println(bindService.bindAppConfirm(userId, key, familyId, roomId));
    }

    @Test
    void bindAppConfirmWithNullKey() {
        String userId = "1";
        String key = "key";
        String familyId = "1";
        String roomId = "1";

        Mockito.when(bindKeyCache.getBindKey(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(null);
        System.out.println(bindService.bindAppConfirm(userId, key, familyId, roomId));
    }

    @Test
    void bindAppConfirmWithErrorKey() {
        String userId = "1";
        String key = "key1";
        String familyId = "1";
        String roomId = "1";

        BindKey bindKey = new BindKey();
        bindKey.setBindKey("key");
        bindKey.setDeviceId("1");

        DeviceInfoEntity device = new DeviceInfoEntity();
        device.setId("1");
        device.setSn("sn");
        device.setNickname("nickname");
        device.setProductId("1");

        Mockito.when(bindKeyCache.getBindKey(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(bindKey);
        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString())).thenReturn(device);
        System.out.println(bindService.bindAppConfirm(userId, key, familyId, roomId));
    }

    @Test
    void bindAppConfirmWithNotExistsDevice() {
        String userId = "1";
        String key = "key";
        String familyId = "1";
        String roomId = "1";

        BindKey bindKey = new BindKey();
        bindKey.setBindKey("key");
        bindKey.setDeviceId("1");

        DeviceInfoEntity device = new DeviceInfoEntity();
        device.setId("1");
        device.setSn("sn");
        device.setNickname("nickname");
        device.setProductId("1");

        Mockito.when(bindKeyCache.getBindKey(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(bindKey);
        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString())).thenReturn(null);
        System.out.println(bindService.bindAppConfirm(userId, key, familyId, roomId));
    }

    @Test
    void bindAppConfirmWithErrorRemote() {
        String userId = "1";
        String key = "key";
        String familyId = "1";
        String roomId = "1";

        BindKey bindKey = new BindKey();
        bindKey.setBindKey("key");
        bindKey.setDeviceId("1");

        DeviceInfoEntity device = new DeviceInfoEntity();
        device.setId("1");
        device.setSn("sn");
        device.setNickname("nickname");
        device.setProductId("1");

        Mockito.when(bindKeyCache.getBindKey(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(bindKey);
        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString())).thenReturn(device);
        Mockito.when(smartHomeServiceRemote.bindDevice(Mockito.any()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        System.out.println(bindService.bindAppConfirm(userId, key, familyId, roomId));
    }

    @Test
    void getBidKey() {
        bindService.getBindKey("1", "0");
    }

    @Test
    void orCodeBind() {
        BindKey bindKey = new BindKey();
        bindKey.setBindKey("key");
        bindKey.setDeviceId("1");

        DeviceInfoEntity device = new DeviceInfoEntity();
        device.setId("1");
        device.setSn("sn");
        device.setNickname("nickname");
        device.setProductId("1");

        OrCodeBindVo vo = new OrCodeBindVo();
        vo.setBindKey("key");
        vo.setFamilyId("1");
        vo.setDeviceId("1");

        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(device);
        Mockito.when(bindKeyCache.getBindKey(Mockito.anyString(), Mockito.anyString(), Mockito.any()))
                .thenReturn(bindKey);
        Mockito.when(smartHomeServiceRemote.bindShareDevice(Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(true));
        System.out.println(bindService.orCodeBind(vo));
    }

    @Test
    void orCodeBindOfBlankBindKey() {
        OrCodeBindVo vo = new OrCodeBindVo();
        vo.setFamilyId("1");
        vo.setDeviceId("1");
        System.out.println(bindService.orCodeBind(vo));
    }

    @Test
    void orCodeBindOfBlankFamilyId() {
        OrCodeBindVo vo = new OrCodeBindVo();
        vo.setBindKey("key");
        vo.setDeviceId("1");
        System.out.println(bindService.orCodeBind(vo));
    }

    @Test
    void orCodeBindOfBlankDeviceId() {
        OrCodeBindVo vo = new OrCodeBindVo();
        vo.setBindKey("key");
        vo.setFamilyId("1");
        System.out.println(bindService.orCodeBind(vo));
    }

    @Test
    void orCodeBindOfNullDeviceInfo() {
        OrCodeBindVo vo = new OrCodeBindVo();
        vo.setBindKey("key");
        vo.setFamilyId("1");
        vo.setDeviceId("1");

        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(null);
        System.out.println(bindService.orCodeBind(vo));
    }

    @Test
    void orCodeBindOfNullBindKey() {
        OrCodeBindVo vo = new OrCodeBindVo();
        vo.setBindKey("key");
        vo.setFamilyId("1");
        vo.setDeviceId("1");
        DeviceInfoEntity device = new DeviceInfoEntity();
        device.setId("1");
        device.setSn("sn");
        device.setNickname("nickname");
        device.setProductId("1");

        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(device);
        Mockito.when(bindKeyCache.getBindKey(Mockito.anyString(), Mockito.anyString(), Mockito.any()))
                .thenReturn(null);
        System.out.println(bindService.orCodeBind(vo));
    }

    @Test
    void orCodeBindOfNotEqualBindKey() {
        BindKey bindKey = new BindKey();
        bindKey.setBindKey("key1");
        bindKey.setDeviceId("1");

        DeviceInfoEntity device = new DeviceInfoEntity();
        device.setId("1");
        device.setSn("sn");
        device.setNickname("nickname");
        device.setProductId("1");

        OrCodeBindVo vo = new OrCodeBindVo();
        vo.setBindKey("key");
        vo.setFamilyId("1");
        vo.setDeviceId("1");

        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(device);
        Mockito.when(bindKeyCache.getBindKey(Mockito.anyString(), Mockito.anyString(), Mockito.any()))
                .thenReturn(bindKey);
        System.out.println(bindService.orCodeBind(vo));
    }

    @Test
    void orCodeBindOfFailRemote() {
        BindKey bindKey = new BindKey();
        bindKey.setBindKey("key");
        bindKey.setDeviceId("1");

        DeviceInfoEntity device = new DeviceInfoEntity();
        device.setId("1");
        device.setSn("sn");
        device.setNickname("nickname");
        device.setProductId("1");

        OrCodeBindVo vo = new OrCodeBindVo();
        vo.setBindKey("key");
        vo.setFamilyId("1");
        vo.setDeviceId("1");

        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(device);
        Mockito.when(bindKeyCache.getBindKey(Mockito.anyString(), Mockito.anyString(), Mockito.any()))
                .thenReturn(bindKey);
        Mockito.when(smartHomeServiceRemote.bindShareDevice(Mockito.any()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        System.out.println(bindService.orCodeBind(vo));
    }
}