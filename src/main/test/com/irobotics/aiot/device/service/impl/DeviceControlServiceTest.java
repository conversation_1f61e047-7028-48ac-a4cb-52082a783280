package com.irobotics.aiot.device.service.impl;

import com.irobotics.aiot.bravo.log.config.LogConfig;
import com.irobotics.aiot.bravo.log.util.SpringUtil;
import com.irobotics.aiot.device.remote.DeviceShadowRemote;
import com.irobotics.aiot.device.service.DeviceControlService;
import com.irobotics.aiot.device.service.IDeviceInfoService;
import com.irobotics.aiot.device.service.KafkaService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import static org.junit.jupiter.api.Assertions.*;

class DeviceControlServiceTest {

    @InjectMocks
    DeviceControlService deviceControlService;

    @Mock
    private IDeviceInfoService deviceInfoService;

    @Mock
    private DeviceShadowRemote deviceShadowRemote;

    @Mock
    private KafkaService kafkaService;

    @Mock
    private ApplicationContext applicationContext;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        Mockito.when(applicationContext.getBean(LogConfig.class)).thenReturn(new LogConfig());
        ApplicationContextAware applicationContextAware = new SpringUtil();
        applicationContextAware.setApplicationContext(applicationContext);
    }

    @Test
    void controlDevice() {
    }
}