package com.irobotics.aiot.device.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.bravo.basic.base.CountryCity;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.config.LogConfig;
import com.irobotics.aiot.bravo.log.util.SpringUtil;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.config.BloomFilterConfig;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.entity.DeviceShadowEntity;
import com.irobotics.aiot.device.entity.ProductModeEntity;
import com.irobotics.aiot.device.mapper.DeviceInfoMapper;
import com.irobotics.aiot.device.remote.DeviceShadowRemote;
import com.irobotics.aiot.device.remote.ProductServiceRemote;
import com.irobotics.aiot.device.service.KafkaService;
import com.irobotics.aiot.device.service.LogService;
import com.irobotics.aiot.device.utils.EMQJwtUtil;
import com.irobotics.aiot.device.vo.*;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

class DeviceInfoServiceImplTest {

    @InjectMocks
    private DeviceInfoServiceImpl deviceInfoService;

    @Mock
    private DeviceInfoMapper deviceInfoMapper;

    @Mock
    private DeviceShadowRemote deviceShadowRemote;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private DeviceInfoCache deviceInfoCache;

    @Mock
    private LogService logService;

    @Mock
    private EMQJwtUtil emqJwtUtil;

    @Mock
    private ProductServiceRemote productServiceRemote;

    @Mock
    private BloomFilterConfig bloomFilterConfig;

    @Mock
    private KafkaService kafkaService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), DeviceInfoEntity.class);
        Mockito.when(applicationContext.getBean(LogConfig.class)).thenReturn(new LogConfig());
        ApplicationContextAware applicationContextAware = new SpringUtil();
        applicationContextAware.setApplicationContext(applicationContext);

        SystemContextUtils.setContextValue(ContextKey.TENANT_ID, "0");
    }

    public DeviceInfoEntity getDevice() {
        DeviceInfoEntity device = new DeviceInfoEntity();
        device.setId("1");
        device.setSn("test123");
        device.setMac("test123");
        device.setResetStatus("1");
        device.setNickname("test123");
        return device;
    }

    @Test
    void checkSnOfNullSn() {
        deviceInfoService.checkSn(null, "sn", "key");
    }

    @Test
    void checkSnOfBlankKeyt() {
        ProductSnDetailEntity sn = new ProductSnDetailEntity();
        sn.setSn("sn");
        deviceInfoService.checkSn(sn, "sn", "key");
    }

    @Test
    void checkSnOfNotEqualKey() {
        ProductSnDetailEntity sn = new ProductSnDetailEntity();
        sn.setKey("key1");
        sn.setSn("sn");
        deviceInfoService.checkSn(sn, "sn", "key");
    }

    @Test
    void doLogin() {
        String tenantId = "1";
        String mac = "abc";
        String sn = "abc";
        String key = "abc";
        String ip = "123";
        String countryLoc = "asdf";
        String productModeCode = "abc";
        String curVersions = "abc";
        CountryCity countryCity = new CountryCity();
        countryCity.setCity("abc");
        countryCity.setContinent("abc");
        countryCity.setCountry("abc");

        DeviceInfoEntity deviceInfo = new DeviceInfoEntity();
        deviceInfo.setSn("abc");
        deviceInfo.setProductModeCode("abc");
        deviceInfo.setKey("abc");
        deviceInfo.setPhotoUrl("abc");
        deviceInfo.setResetStatus("1");

        System.out.println(deviceInfoService.doLogin(tenantId, mac, sn, key, ip, deviceInfo, countryLoc, productModeCode, curVersions, countryCity));
    }

    @Test
    void doLoginOfVerifyKey() {
        String tenantId = "1";
        String mac = "abc";
        String sn = "abc";
        String key = "abc";
        String ip = "123";
        String countryLoc = "asdf";
        String productModeCode = "abc";
        String curVersions = "abc";
        CountryCity countryCity = new CountryCity();
        countryCity.setCity("abc");
        countryCity.setContinent("abc");
        countryCity.setCountry("abc");

        DeviceInfoEntity deviceInfo = new DeviceInfoEntity();
        deviceInfo.setSn("abc");
        deviceInfo.setProductModeCode("abc");
        deviceInfo.setPhotoUrl("abc");
        deviceInfo.setResetStatus("1");

        ProductSnDetailEntity snDetail = new ProductSnDetailEntity();
        snDetail.setSn("abc");
        snDetail.setKey("abc");

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setPhotoUrl("abc");

        InnerDeviceLoginVo vo = new InnerDeviceLoginVo();
        vo.setProductModeEntity(productModeEntity);
        vo.setSnDetailEntity(snDetail);

        Mockito.when(productServiceRemote.getByCodeAdSn(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean()))
                .thenReturn(ResponseMessage.buildSuccess(vo));

        System.out.println(deviceInfoService.doLogin(tenantId, mac, sn, key, ip, deviceInfo, countryLoc, productModeCode, curVersions, countryCity));
    }

    @Test
    void doLoginOfVerifyKeyOfErrorRemote() {
        String tenantId = "1";
        String mac = "abc";
        String sn = "abc";
        String key = "abc";
        String ip = "123";
        String countryLoc = "asdf";
        String productModeCode = "abc";
        String curVersions = "abc";
        CountryCity countryCity = new CountryCity();
        countryCity.setCity("abc");
        countryCity.setContinent("abc");
        countryCity.setCountry("abc");

        DeviceInfoEntity deviceInfo = new DeviceInfoEntity();
        deviceInfo.setSn("abc");
        deviceInfo.setProductModeCode("abc");
        deviceInfo.setPhotoUrl("abc");
        deviceInfo.setResetStatus("1");

        ProductSnDetailEntity snDetail = new ProductSnDetailEntity();
        snDetail.setSn("abc");
        snDetail.setKey("abc");

        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setPhotoUrl("abc");

        InnerDeviceLoginVo vo = new InnerDeviceLoginVo();
        vo.setProductModeEntity(productModeEntity);
        vo.setSnDetailEntity(snDetail);

        Mockito.when(productServiceRemote.getByCodeAdSn(Mockito.anyString(), Mockito.anyString(), Mockito.anyBoolean()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));

        System.out.println(deviceInfoService.doLogin(tenantId, mac, sn, key, ip, deviceInfo, countryLoc, productModeCode, curVersions, countryCity));
    }

    @Test
    void doLoginOfNotEqualSn() {
        String tenantId = "1";
        String mac = "abc";
        String sn = "abcd";
        String key = "abc";
        String ip = "123";
        String countryLoc = "asdf";
        String productModeCode = "abc";
        String curVersions = "abc";
        CountryCity countryCity = new CountryCity();
        countryCity.setCity("abc");
        countryCity.setContinent("abc");
        countryCity.setCountry("abc");

        DeviceInfoEntity deviceInfo = new DeviceInfoEntity();
        deviceInfo.setSn("abc");
        deviceInfo.setProductModeCode("abc");
        deviceInfo.setKey("abc");
        deviceInfo.setPhotoUrl("abc");
        deviceInfo.setResetStatus("1");

        System.out.println(deviceInfoService.doLogin(tenantId, mac, sn, key, ip, deviceInfo, countryLoc, productModeCode, curVersions, countryCity));
    }

    @Test
    void doLoginOfNotEqualCode() {
        String tenantId = "1";
        String mac = "abc";
        String sn = "abc";
        String key = "abc";
        String ip = "123";
        String countryLoc = "asdf";
        String productModeCode = "abcd";
        String curVersions = "abc";
        CountryCity countryCity = new CountryCity();
        countryCity.setCity("abc");
        countryCity.setContinent("abc");
        countryCity.setCountry("abc");

        DeviceInfoEntity deviceInfo = new DeviceInfoEntity();
        deviceInfo.setSn("abc");
        deviceInfo.setProductModeCode("abc");
        deviceInfo.setKey("abc");
        deviceInfo.setPhotoUrl("abc");
        deviceInfo.setResetStatus("1");

        System.out.println(deviceInfoService.doLogin(tenantId, mac, sn, key, ip, deviceInfo, countryLoc, productModeCode, curVersions, countryCity));
    }

    @Test
    void doLoginOrNotEqualKey() {
        String tenantId = "1";
        String mac = "abc";
        String sn = "abc";
        String key = "abcd";
        String ip = "123";
        String countryLoc = "asdf";
        String productModeCode = "abc";
        String curVersions = "abc";
        CountryCity countryCity = new CountryCity();
        countryCity.setCity("abc");
        countryCity.setContinent("abc");
        countryCity.setCountry("abc");

        DeviceInfoEntity deviceInfo = new DeviceInfoEntity();
        deviceInfo.setSn("abc");
        deviceInfo.setProductModeCode("abc");
        deviceInfo.setKey("abc");
        deviceInfo.setPhotoUrl("abc");
        deviceInfo.setResetStatus("1");

        System.out.println(deviceInfoService.doLogin(tenantId, mac, sn, key, ip, deviceInfo, countryLoc, productModeCode, curVersions, countryCity));
    }

    @Test
    void getOneById() {
        String deviceId = "1";

        Mockito.when(deviceInfoMapper.selectOne(Mockito.any())).thenReturn(getDevice());
        DeviceInfoEntity device = deviceInfoCache.getCacheById("1", deviceId);
        assertEquals(deviceId, device.getId());
        System.out.println(device);
    }

    @Test
    void doRegister() {
        String tenantId = "1";
        String mac = "abc";
        String sn = "abc";
        String key = "abc";
        String ip = "123";
        String countryLoc = "asdf";
        String productModeCode = "abc";
        String curVersions = "abc";
        CountryCity countryCity = new CountryCity();
        countryCity.setCity("abc");
        countryCity.setContinent("abc");
        countryCity.setCountry("abc");

        Mockito.when(productServiceRemote.deviceRegister(Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(getDevice()));
        Mockito.when(deviceInfoMapper.insert(Mockito.any()))
                .thenReturn(1);
        Mockito.when(bloomFilterConfig.getUsed()).thenReturn(true);


        System.out.println(deviceInfoService.doRegister(tenantId, sn, mac, key, ip, productModeCode, countryLoc, curVersions, countryCity));
    }

    @Test
    void doRegisterOfRemoteFail1() {
        String tenantId = "1";
        String mac = "abc";
        String sn = "abc";
        String key = "abc";
        String ip = "123";
        String countryLoc = "asdf";
        String productModeCode = "abc";
        String curVersions = "abc";
        CountryCity countryCity = new CountryCity();
        countryCity.setCity("abc");
        countryCity.setContinent("abc");
        countryCity.setCountry("abc");

        Mockito.when(productServiceRemote.deviceRegister(Mockito.any()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        Mockito.when(deviceInfoMapper.insert(Mockito.any()))
                .thenReturn(1);
        Mockito.when(bloomFilterConfig.getUsed()).thenReturn(true);


        System.out.println(deviceInfoService.doRegister(tenantId, sn, mac, key, ip, productModeCode, countryLoc, curVersions, countryCity));
    }

    @Test
    void doRegisterOfInsertFail() {
        String tenantId = "1";
        String mac = "abc";
        String sn = "abc";
        String key = "abc";
        String ip = "123";
        String countryLoc = "asdf";
        String productModeCode = "abc";
        String curVersions = "abc";
        CountryCity countryCity = new CountryCity();
        countryCity.setCity("abc");
        countryCity.setContinent("abc");
        countryCity.setCountry("abc");

        Mockito.when(productServiceRemote.deviceRegister(Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(getDevice()));
        Mockito.when(deviceInfoMapper.insert(Mockito.any()))
                .thenReturn(0);
        Mockito.when(bloomFilterConfig.getUsed()).thenReturn(true);


        System.out.println(deviceInfoService.doRegister(tenantId, sn, mac, key, ip, productModeCode, countryLoc, curVersions, countryCity));
    }

    @Test
    void doRegisterOfException() {
        String tenantId = "1";
        String mac = "abc";
        String sn = "abc";
        String key = "abc";
        String ip = "123";
        String countryLoc = "asdf";
        String productModeCode = "abc";
        String curVersions = "abc";
        CountryCity countryCity = new CountryCity();
        countryCity.setCity("abc");
        countryCity.setContinent("abc");
        countryCity.setCountry("abc");

        Mockito.when(productServiceRemote.deviceRegister(Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(getDevice()));
        Mockito.when(deviceInfoMapper.insert(Mockito.any()))
                .thenThrow(new RuntimeException());
        Mockito.when(bloomFilterConfig.getUsed()).thenReturn(true);


        System.out.println(deviceInfoService.doRegister(tenantId, sn, mac, key, ip, productModeCode, countryLoc, curVersions, countryCity));
    }

    @Test
    void doRegisterOfException2() {
        String tenantId = "1";
        String mac = "abc";
        String sn = "abc";
        String key = "abc";
        String ip = "123";
        String countryLoc = "asdf";
        String productModeCode = "abc";
        String curVersions = "abc";
        CountryCity countryCity = new CountryCity();
        countryCity.setCity("abc");
        countryCity.setContinent("abc");
        countryCity.setCountry("abc");

        Mockito.when(productServiceRemote.deviceRegister(Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(getDevice()));
        Mockito.when(deviceInfoMapper.insert(Mockito.any()))
                .thenThrow(new RuntimeException());
        Mockito.when(productServiceRemote.delBySn(Mockito.anyString()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        Mockito.when(bloomFilterConfig.getUsed()).thenReturn(true);


        System.out.println(deviceInfoService.doRegister(tenantId, sn, mac, key, ip, productModeCode, countryLoc, curVersions, countryCity));
    }

    @Test
    void doRegisterOfException3() {
        String tenantId = "1";
        String mac = "abc";
        String sn = "abc";
        String key = "abc";
        String ip = "123";
        String countryLoc = "asdf";
        String productModeCode = "abc";
        String curVersions = "abc";
        CountryCity countryCity = new CountryCity();
        countryCity.setCity("abc");
        countryCity.setContinent("abc");
        countryCity.setCountry("abc");

        Mockito.when(productServiceRemote.deviceRegister(Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(getDevice()));
        Mockito.when(deviceInfoMapper.insert(Mockito.any()))
                .thenThrow(new RuntimeException());
        Mockito.when(productServiceRemote.delBySn(Mockito.anyString()))
                .thenReturn(ResponseMessage.buildSuccess(false));
        Mockito.when(bloomFilterConfig.getUsed()).thenReturn(true);


        System.out.println(deviceInfoService.doRegister(tenantId, sn, mac, key, ip, productModeCode, countryLoc, curVersions, countryCity));
    }

    @Test
    void doRegisterOfException4() {
        String tenantId = "1";
        String mac = "abc";
        String sn = "abc";
        String key = "abc";
        String ip = "123";
        String countryLoc = "asdf";
        String productModeCode = "abc";
        String curVersions = "abc";
        CountryCity countryCity = new CountryCity();
        countryCity.setCity("abc");
        countryCity.setContinent("abc");
        countryCity.setCountry("abc");

        Mockito.when(productServiceRemote.deviceRegister(Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(getDevice()));
        Mockito.when(deviceInfoMapper.insert(Mockito.any()))
                .thenThrow(new RuntimeException());
        Mockito.when(productServiceRemote.delBySn(Mockito.anyString()))
                .thenReturn(ResponseMessage.buildSuccess(true));
        Mockito.when(bloomFilterConfig.getUsed()).thenReturn(true);


        System.out.println(deviceInfoService.doRegister(tenantId, sn, mac, key, ip, productModeCode, countryLoc, curVersions, countryCity));
    }

    @Test
    void pageList() {
        DeviceInfoPageGetReq pageReq = new DeviceInfoPageGetReq();
        pageReq.setId("1");
        pageReq.setProductModeCode("code");
        pageReq.setSn("test123");
        pageReq.setMac("test123");
        pageReq.setNickname("code123");
        pageReq.setBeginTime(System.currentTimeMillis() - 2 * 24 * 60 * 60 * 1000);
        pageReq.setEndTime(System.currentTimeMillis());

        Page<DeviceInfoEntity> page = new Page<>();
        List<DeviceInfoEntity> devices = new ArrayList<>();
        devices.add(getDevice());
        page.setRecords(devices);

        List<DeviceShadowEntity> list = new ArrayList<>();
        DeviceShadowEntity shadow = new DeviceShadowEntity();
        shadow.setId("test123");
        shadow.setOnlineStatus(true);
        shadow.setTimestamp(123456789L);
        list.add(shadow);

        Mockito.when(deviceShadowRemote.findAllBySN(Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(list));

        when(deviceInfoMapper.selectPage(Mockito.any(), Mockito.any())).thenReturn(page);
        System.out.println(deviceInfoService.pageList(pageReq));
    }

    @Test
    void pageListOfException() {
        DeviceInfoPageGetReq pageReq = new DeviceInfoPageGetReq();
        pageReq.setId("1");
        pageReq.setProductModeCode("code");
        pageReq.setSn("test123");
        pageReq.setMac("test123");
        pageReq.setNickname("code123");
        pageReq.setBeginTime(System.currentTimeMillis() - 2 * 24 * 60 * 60 * 1000);
        pageReq.setEndTime(System.currentTimeMillis());

        Page<DeviceInfoEntity> page = new Page<>();
        List<DeviceInfoEntity> devices = new ArrayList<>();
        devices.add(getDevice());
        page.setRecords(devices);

        List<DeviceShadowEntity> list = new ArrayList<>();
        DeviceShadowEntity shadow = new DeviceShadowEntity();
        shadow.setId("test123");
        shadow.setOnlineStatus(true);
        shadow.setTimestamp(123456789L);

        Mockito.when(deviceShadowRemote.findAllBySN(Mockito.anyList()))
                .thenThrow(new RuntimeException());

        when(deviceInfoMapper.selectPage(Mockito.any(), Mockito.any())).thenReturn(page);
        System.out.println(deviceInfoService.pageList(pageReq));
    }

    @Test
    void pageListOfEmptyPages() {
        DeviceInfoPageGetReq pageReq = new DeviceInfoPageGetReq();
        pageReq.setId("1");
        pageReq.setProductModeCode("code");
        pageReq.setSn("test123");
        pageReq.setMac("test123");
        pageReq.setNickname("code123");
        pageReq.setBeginTime(System.currentTimeMillis() - 2 * 24 * 60 * 60 * 1000);
        pageReq.setEndTime(System.currentTimeMillis());

        Page<DeviceInfoEntity> page = new Page<>();
        List<DeviceInfoEntity> devices = new ArrayList<>();
        DeviceInfoEntity deviceInfoEntity = new DeviceInfoEntity();
        deviceInfoEntity.setSn("test123");
        devices.add(deviceInfoEntity);
        page.setRecords(devices);

        List<DeviceShadowEntity> list = new ArrayList<>();
        DeviceShadowEntity shadow = new DeviceShadowEntity();
        shadow.setId("test123");
        shadow.setOnlineStatus(true);
        shadow.setTimestamp(123456789L);

        Mockito.when(deviceShadowRemote.findAllBySN(Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(list));

        when(deviceInfoMapper.selectPage(Mockito.any(), Mockito.any())).thenReturn(page);
        System.out.println(deviceInfoService.pageList(pageReq));
    }

    @Test
    void pageListOfRemoteFail() {
        DeviceInfoPageGetReq pageReq = new DeviceInfoPageGetReq();
        pageReq.setId("1");
        pageReq.setProductModeCode("code");
        pageReq.setSn("test123");
        pageReq.setMac("test123");
        pageReq.setNickname("code123");
        pageReq.setBeginTime(System.currentTimeMillis() - 2 * 24 * 60 * 60 * 1000);
        pageReq.setEndTime(System.currentTimeMillis());

        Page<DeviceInfoEntity> page = new Page<>();
        List<DeviceInfoEntity> devices = new ArrayList<>();
        devices.add(getDevice());
        page.setRecords(devices);

        List<DeviceShadowEntity> list = new ArrayList<>();
        DeviceShadowEntity shadow = new DeviceShadowEntity();
        shadow.setId("test123");
        shadow.setOnlineStatus(true);
        shadow.setTimestamp(123456789L);

        Mockito.when(deviceShadowRemote.findAllBySN(Mockito.anyList()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        when(deviceInfoMapper.selectPage(Mockito.any(), Mockito.any())).thenReturn(page);
        System.out.println(deviceInfoService.pageList(pageReq));
    }

    @Test
    void pageListOfEmptyResult() {
        DeviceInfoPageGetReq pageReq = new DeviceInfoPageGetReq();
        pageReq.setId("1");
        pageReq.setProductModeCode("code");
        pageReq.setSn("test123");
        pageReq.setMac("test123");
        pageReq.setNickname("code123");
        pageReq.setBeginTime(System.currentTimeMillis() - 2 * 24 * 60 * 60 * 1000);
        pageReq.setEndTime(System.currentTimeMillis());

        Page<DeviceInfoEntity> page = new Page<>();
        List<DeviceInfoEntity> devices = new ArrayList<>();
        devices.add(getDevice());
        page.setRecords(devices);

        List<DeviceShadowEntity> list = new ArrayList<>();
        DeviceShadowEntity shadow = new DeviceShadowEntity();
        shadow.setId("test123");
        shadow.setOnlineStatus(true);
        shadow.setTimestamp(123456789L);

        Mockito.when(deviceShadowRemote.findAllBySN(Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess());

        when(deviceInfoMapper.selectPage(Mockito.any(), Mockito.any())).thenReturn(page);
        System.out.println(deviceInfoService.pageList(pageReq));
    }

    @Test
    void getBySnAndMac() {
        String sn = "test123";
        String mac = "test123";
        when(deviceInfoMapper.selectOne(Mockito.any())).thenReturn(getDevice());
        DeviceInfoEntity device = deviceInfoService.getBySnAndMac(sn, mac);
        assertEquals(sn, device.getSn());
        assertEquals(sn, device.getMac());
        System.out.println(device);
    }

    @Test
    void getBySn() {
        String sn = "test123";
        when(deviceInfoMapper.selectOne(Mockito.any())).thenReturn(getDevice());
        DeviceInfoEntity device = deviceInfoCache.getCacheBySn("1", sn);
        assertEquals(sn, device.getSn());
        System.out.println(device);
    }

    public DeviceShadowEntity getShadow() {
        DeviceShadowEntity shadow = new DeviceShadowEntity();
        shadow.setId("test123");
        shadow.setOnlineStatus(true);
        shadow.setProductKey("1");
        JSONObject jo = new JSONObject();
        jo.put("status", "1");
        jo.put("unit", "pers");
        shadow.setProperties(jo);
        return shadow;
    }

    @Test
    void ifOnline() {
        String sn = "test123";
        when(deviceShadowRemote.findBySN(Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(getShadow()));
        Boolean online = deviceInfoService.ifOnline(sn);
        System.out.println(online);
    }

    @Test
    void ifOnlineOfRemoteFail() {
        String sn = "test123";
        when(deviceShadowRemote.findBySN(Mockito.anyString()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        Boolean online = deviceInfoService.ifOnline(sn);
        System.out.println(online);
    }

    @Test
    void ifOnlineWithBlankSn() {
        String sn = "";
        Boolean online = deviceInfoService.ifOnline(sn);
        System.out.println(online);
    }

    @Test
    void ifOnlineWithNullShadow() {
        String sn = "test123";
        when(deviceShadowRemote.findBySN(Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess());
        Boolean online = deviceInfoService.ifOnline(sn);
        System.out.println(online);
    }

    @Test
    void getDeviceStatus() {
        String sn = "test123";
        when(deviceShadowRemote.findBySN(Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(getShadow()));
        JSONObject properties = deviceInfoService.getDeviceStatus(sn);
        System.out.println(properties);
    }

    @Test
    void getDeviceStatusWithBlankSn() {
        String sn = "";
        JSONObject properties = deviceInfoService.getDeviceStatus(sn);
        System.out.println(properties);
    }

    @Test
    void getDeviceStatusWithErrorRemote() {
        String sn = "test123";
        when(deviceShadowRemote.findBySN(Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(null));
        JSONObject properties = deviceInfoService.getDeviceStatus(sn);
        System.out.println(properties);
    }

    @Test
    void getDevicesByIds() {
        String deviceId = "1";

        List<DeviceInfoEntity> list = new ArrayList<>();
        list.add(getDevice());

        when(deviceInfoMapper.selectList(Mockito.any())).thenReturn(list);
        List<DeviceInfoEntity> devices = deviceInfoService.getDevicesByIds(Arrays.asList(deviceId));
        System.out.println(devices);
    }

    @Test
    void selectAllRobotSn() {
        List<DeviceInfoEntity> list = new ArrayList<>();
        list.add(getDevice());
        when(deviceInfoMapper.selectList(Mockito.any())).thenReturn(list);
        List<String> strings = deviceInfoService.selectAllRobotSn();
        System.out.println(strings);
    }

    @Test
    void selectAllRobotSnWithEmptyResult() {
        when(deviceInfoMapper.selectList(Mockito.any())).thenReturn(null);
        List<String> strings = deviceInfoService.selectAllRobotSn();
        System.out.println(strings);
    }

    @Test
    void getByMac() {
        String mac = "test123";
        when(deviceInfoMapper.selectOne(Mockito.any())).thenReturn(getDevice());
    }

    @Test
    void getDeviceCountByPro() {
        String deviceId = "1";

        List<Map<String, Long>> list = new ArrayList<>();
        Map<String, Long> map = new HashMap<>();
        map.put("product_id", 1L);
        map.put("device_num", 1L);
        list.add(map);

        when(deviceInfoMapper.selectCountByProuctId(Mockito.anyList())).thenReturn(list);
        deviceInfoService.getDeviceCountByPro(Arrays.asList(deviceId));
    }

    @Test
    void getDeviceCountByProWithEmptyResult() {
        String deviceId = "1";

        when(deviceInfoMapper.selectCountByProuctId(Mockito.anyList())).thenReturn(null);
        deviceInfoService.getDeviceCountByPro(Arrays.asList(deviceId));
    }

    @Test
    void getDeviceInfosByIds() {
        String deviceId = "1";

        List<DeviceInfoEntity> list = new ArrayList<>();
        DeviceInfoEntity device = getDevice();
        list.add(device);


        when(deviceInfoMapper.selectList(Mockito.any())).thenReturn(list);
        ResponseMessage<List<DeviceInfoEntity>> devices = deviceInfoService.getDeviceInfosByIds(Arrays.asList(deviceId));
        System.out.println(devices);
    }

    @Test
    void modifyNickname() {
        when(deviceInfoMapper.update(Mockito.any(), Mockito.any())).thenReturn(1);
        System.out.println(deviceInfoService.modifyNickname("1", "1", "nickname", "a", "a"));
    }

    @Test
    void modifyNicknameWithNullParam() {
        when(deviceInfoMapper.update(Mockito.any(), Mockito.any())).thenReturn(1);
        System.out.println(deviceInfoService.modifyNickname("1", "1", "nickname", "a", "a"));
    }

    @Test
    void modifyNicknameWithBlankDeviceId() {
        String deviceId = "";
        when(deviceInfoMapper.update(Mockito.any(), Mockito.any())).thenReturn(1);
        System.out.println(deviceInfoService.modifyNickname("1", deviceId, "nickname", "a", "a"));
    }

    @Test
    void modifyNicknameWithBlankNickname() {
        String nickname = "";
        when(deviceInfoMapper.update(Mockito.any(), Mockito.any())).thenReturn(1);
        System.out.println(deviceInfoService.modifyNickname("1", "1", nickname, "a", "a"));
    }

    @Test
    void modifyNicknameWithBlanksn() {
        String nickname = "a";
        when(deviceInfoMapper.update(Mockito.any(), Mockito.any())).thenReturn(1);
        System.out.println(deviceInfoService.modifyNickname("1", "1", nickname, "", "a"));
    }

    @Test
    void modifyNicknameWithBlankMac() {
        String nickname = "a";
        when(deviceInfoMapper.update(Mockito.any(), Mockito.any())).thenReturn(1);
        System.out.println(deviceInfoService.modifyNickname("1", "1", nickname, "a", ""));
    }

    @Test
    void getDeviceNickNameByIds() {
        String deviceId = "1";

        List<DeviceInfoEntity> list = new ArrayList<>();
        list.add(getDevice());

        when(deviceInfoService.listByIds(Mockito.anyList())).thenReturn(list);
        System.out.println(deviceInfoService.getDeviceNickNameByIds(Arrays.asList(deviceId)));
    }

    @Test
    void getDeviceNickNameByIdsWithEmptyDeviceIds() {
        String deviceId = "1";

        List<DeviceInfoEntity> list = new ArrayList<>();
        list.add(getDevice());

        when(deviceInfoService.listByIds(Mockito.anyList())).thenReturn(list);
        System.out.println(deviceInfoService.getDeviceNickNameByIds(null));
    }

    @Test
    void getByIds() {
        String tenantId = "0";
        List<String> ids = Arrays.asList("1");
        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(getDevice());
        System.out.println(deviceInfoService.getByIds(tenantId, ids));
    }

    @Test
    void getByIdsOfEmptyIds() {
        String tenantId = "0";
        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(getDevice());
        System.out.println(deviceInfoService.getByIds(tenantId, null));
    }

    @Test
    void getPageByCode() {
        Mockito.when(deviceInfoMapper.getPageByCode(Mockito.any(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(null);
        System.out.println(deviceInfoService.getPageByCode("code", 1, 10));
    }

    @Test
    void getCountByCode() {
        Mockito.when(deviceInfoMapper.selectCount(Mockito.any()))
                .thenReturn(1);
        System.out.println(deviceInfoService.getCountByCode("code"));
    }

    @Test
    void updateVerifyPassword() {
        DeviceVerifyPasswordVo vo = new DeviceVerifyPasswordVo();
        vo.setVerifyPassword(1);
        vo.setDeviceIds(Arrays.asList("1"));

        DeviceInfoEntity device = new DeviceInfoEntity();
        device.setId("1");
        device.setSn("test123");
        device.setMac("test123");
        device.setResetStatus("1");
        device.setNickname("test123");

        Mockito.when(deviceInfoMapper.update(Mockito.any(), Mockito.any()))
                .thenReturn(1);
        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(device);
        System.out.println(deviceInfoService.updateVerifyPassword(vo));
    }

    @Test
    void updateVerifyPasswordOfEmptyDeviceIds() {
        DeviceVerifyPasswordVo vo = new DeviceVerifyPasswordVo();
        vo.setVerifyPassword(1);

        Mockito.when(deviceInfoMapper.update(Mockito.any(), Mockito.any()))
                .thenReturn(1);
        System.out.println(deviceInfoService.updateVerifyPassword(vo));
    }

    @Test
    void updateVerifyPasswordOfNullVerify() {
        DeviceVerifyPasswordVo vo = new DeviceVerifyPasswordVo();
        vo.setDeviceIds(Arrays.asList("1"));

        Mockito.when(deviceInfoMapper.update(Mockito.any(), Mockito.any()))
                .thenReturn(1);
        System.out.println(deviceInfoService.updateVerifyPassword(vo));
    }

    @Test
    void configVerify() {
        DeviceConfigVerifyPasswordVo vo = new DeviceConfigVerifyPasswordVo();
        vo.setVerify(1);
        vo.setDeviceId("1");

        Mockito.when(deviceInfoMapper.update(Mockito.any(), Mockito.any()))
                .thenReturn(1);
        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(getDevice());

        System.out.println(deviceInfoService.configVerify(vo));
    }

    @Test
    void configVerifyOfBlankTenantId() {
        DeviceConfigVerifyPasswordVo vo = new DeviceConfigVerifyPasswordVo();
        vo.setVerify(1);
        vo.setDeviceId("1");
        SystemContextUtils.setContextValue(ContextKey.TENANT_ID, "");

        Mockito.when(deviceInfoMapper.update(Mockito.any(), Mockito.any()))
                .thenReturn(1);
        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(getDevice());

        System.out.println(deviceInfoService.configVerify(vo));
    }

    @Test
    void configVerifyOfBlankDeviceId() {
        DeviceConfigVerifyPasswordVo vo = new DeviceConfigVerifyPasswordVo();
        vo.setVerify(1);

        Mockito.when(deviceInfoMapper.update(Mockito.any(), Mockito.any()))
                .thenReturn(1);
        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(getDevice());

        System.out.println(deviceInfoService.configVerify(vo));
    }

    @Test
    void configVerifyOfBlankVerify() {
        DeviceConfigVerifyPasswordVo vo = new DeviceConfigVerifyPasswordVo();
        vo.setDeviceId("1");

        Mockito.when(deviceInfoMapper.update(Mockito.any(), Mockito.any()))
                .thenReturn(1);
        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(getDevice());

        System.out.println(deviceInfoService.configVerify(vo));
    }

    @Test
    void configVerifyOfNullDevice() {
        DeviceConfigVerifyPasswordVo vo = new DeviceConfigVerifyPasswordVo();
        vo.setVerify(1);
        vo.setDeviceId("1");

        Mockito.when(deviceInfoMapper.update(Mockito.any(), Mockito.any()))
                .thenReturn(1);
        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(null);

        System.out.println(deviceInfoService.configVerify(vo));
    }

    @Test
    void getCount() {
        Mockito.when(deviceInfoMapper.selectCount()).thenReturn(1L);
        System.out.println(deviceInfoService.getCount());

    }

    @Test
    void getDevicePage() {
        Mockito.when(deviceInfoMapper.getPage(Mockito.any()))
                .thenReturn(null);
        System.out.println(deviceInfoService.getDevicePage(1, 10));
    }

    @Test
    void updateIotId() {
        IotIdVo vo = new IotIdVo();
        vo.setDeviceId("1");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("deviceId", "1");
        vo.setIotId(jsonObject);

        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(getDevice());
        Mockito.when(deviceInfoMapper.updateIotId(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(1);
        System.out.println(deviceInfoService.updateIotId(vo));
    }

    @Test
    void updateIotIdOfBlankDeviceId() {
        IotIdVo vo = new IotIdVo();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("deviceId", "1");
        vo.setIotId(jsonObject);

        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(getDevice());
        Mockito.when(deviceInfoMapper.updateIotId(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(1);
        System.out.println(deviceInfoService.updateIotId(vo));
    }

    @Test
    void updateIotIdOfNullIotId() {
        IotIdVo vo = new IotIdVo();
        vo.setDeviceId("1");

        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(getDevice());
        Mockito.when(deviceInfoMapper.updateIotId(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(1);
        System.out.println(deviceInfoService.updateIotId(vo));
    }

    @Test
    void updateIotIdOfNullDevice() {
        IotIdVo vo = new IotIdVo();
        vo.setDeviceId("1");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("deviceId", "1");
        vo.setIotId(jsonObject);

        Mockito.when(deviceInfoCache.getCacheById(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(null);
        Mockito.when(deviceInfoMapper.updateIotId(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(1);
        System.out.println(deviceInfoService.updateIotId(vo));
    }

    @Test
    void genNickName() {
        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setAliasPrefix("aaa");
        productModeEntity.setSnSuffixBit(3);
        System.out.println(deviceInfoService.genNickName("test12345", productModeEntity));
    }

}