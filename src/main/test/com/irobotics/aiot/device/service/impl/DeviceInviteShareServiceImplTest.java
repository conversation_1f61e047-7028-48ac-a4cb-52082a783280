package com.irobotics.aiot.device.service.impl;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.commons.util.SnowflakeUtil;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.config.LogConfig;
import com.irobotics.aiot.bravo.log.util.SpringUtil;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.common.InviteShareEnum;
import com.irobotics.aiot.device.entity.*;
import com.irobotics.aiot.device.mapper.DeviceInfoMapper;
import com.irobotics.aiot.device.mapper.DeviceInviteShareMapper;
import com.irobotics.aiot.device.remote.ProductServiceRemote;
import com.irobotics.aiot.device.remote.SmartHomeServiceRemote;
import com.irobotics.aiot.device.remote.TAuthUserRemote;
import com.irobotics.aiot.device.service.CommonService;
import com.irobotics.aiot.device.vo.*;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.StringUtils;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.*;

class DeviceInviteShareServiceImplTest {

    @InjectMocks
    private DeviceInviteShareServiceImpl deviceInviteShareService;

    @Mock
    private DeviceInfoServiceImpl deviceInfoService;

    @Mock
    private DeviceInviteShareMapper deviceInviteShareMapper;

    @Mock
    private DeviceInfoMapper deviceInfoMapper;

    @Mock
    private TAuthUserRemote userRemote;

    @Mock
    private ProductServiceRemote productServiceRemote;

    @Mock
    private SmartHomeServiceRemote smartHomeServiceRemote;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private DeviceInfoCache deviceInfoCache;

    @Mock
    private CommonService commonService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), DeviceInviteShareEntity.class);
        Mockito.when(applicationContext.getBean(LogConfig.class)).thenReturn(new LogConfig());
        ApplicationContextAware applicationContextAware = new SpringUtil();
        applicationContextAware.setApplicationContext(applicationContext);
        SystemContextUtils.setContextValue(ContextKey.TENANT_ID, "0");
        SystemContextUtils.setContextValue(ContextKey.ID, "1");
        SystemContextUtils.setContextValue(ContextKey.USERNAME, "<EMAIL>");
    }

    public TAuthClientUserInfo getUser(String userId, String username) {
        TAuthClientUserInfo user = new TAuthClientUserInfo();
        user.setId(StringUtils.isBlank(userId) ? "1" : userId);
        user.setUsername(StringUtils.isBlank(username) ? "<EMAIL>" : username);
        user.setAvatarUrl("www.user.com");
        return user;
    }

    public DeviceInfoEntity getDevice(String deviceId) {
        DeviceInfoEntity device = new DeviceInfoEntity();
        device.setId(StringUtils.isBlank(deviceId) ? "1" : deviceId);
        device.setSn("test1234");
        device.setMac("1234");
        device.setNickname("nickname");
        device.setProductId("1");
        return device;
    }

    public List<DeviceInfoEntity> getDevices() {
        List<DeviceInfoEntity> devices = new ArrayList<>();
        devices.add(getDevice(null));
        return devices;
    }


    @Test
    void inviteUserShareRobot() {
        String beInvited = "<EMAIL>";
        String inviterId = "1";
        String inviter = "<EMAIL>";
        String target = "1";
        List<String> targets = Arrays.asList(target);

        Mockito.when(userRemote.getByUsername(Mockito.anyString(), Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(getUser("11", inviter)));
        Mockito.when(deviceInfoService.getByIds(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(Arrays.asList(getDevice("1")));
        Mockito.when(deviceInviteShareMapper.selectCount(Mockito.any()))
                .thenReturn(0);

        DeviceInviteShareServiceImpl spy = Mockito.spy(deviceInviteShareService);
        Mockito.when(spy.saveBatch(Mockito.anyList())).thenReturn(true);
        System.out.println(spy.inviteUserShareRobot(beInvited, inviterId, inviter, targets));
    }

    @Test
    void inviteUserShareRobotAlreadyShare() {
        String beInvited = "<EMAIL>";
        String inviterId = "1";
        String inviter = "<EMAIL>";
        String target = "1";
        List<String> targets = Arrays.asList(target);

        Mockito.when(userRemote.getByUsername(Mockito.anyString(), Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(getUser("11", inviter)));
        Mockito.when(deviceInfoService.getByIds(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(Arrays.asList(getDevice("1")));
        Mockito.when(deviceInviteShareMapper.selectCount(Mockito.any()))
                .thenReturn(1);

        DeviceInviteShareServiceImpl spy = Mockito.spy(deviceInviteShareService);
        Mockito.when(spy.saveBatch(Mockito.anyList())).thenReturn(true);
        System.out.println(spy.inviteUserShareRobot(beInvited, inviterId, inviter, targets));
    }

    @Test
    void inviteUserShareRobotNullBeInviter() {
        String beInvited = "<EMAIL>";
        String inviterId = "1";
        String inviter = "<EMAIL>";
        String target = "1";
        List<String> targets = Arrays.asList(target);

        Mockito.when(userRemote.getByUsername(Mockito.anyString(), Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess());

        DeviceInviteShareServiceImpl spy = Mockito.spy(deviceInviteShareService);
        Mockito.when(spy.saveBatch(Mockito.anyList())).thenReturn(true);
        System.out.println(spy.inviteUserShareRobot(beInvited, inviterId, inviter, targets));
    }

    @Test
    void inviteUserShareRobotRemoteFail() {
        String beInvited = "<EMAIL>";
        String inviterId = "1";
        String inviter = "<EMAIL>";
        String target = "1";
        List<String> targets = Arrays.asList(target);

        Mockito.when(userRemote.getByUsername(Mockito.anyString(), Mockito.any()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));

        DeviceInviteShareServiceImpl spy = Mockito.spy(deviceInviteShareService);
        Mockito.when(spy.saveBatch(Mockito.anyList())).thenReturn(true);
        System.out.println(spy.inviteUserShareRobot(beInvited, inviterId, inviter, targets));
    }

    @Test
    void inviteUserShareRobotShareSelf() {
        String beInvited = "<EMAIL>";
        String inviterId = "1";
        String inviter = "<EMAIL>";
        String target = "1";
        List<String> targets = Arrays.asList(target);

        Mockito.when(userRemote.getByUsername(Mockito.anyString(), Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(getUser("1", inviter)));

        DeviceInviteShareServiceImpl spy = Mockito.spy(deviceInviteShareService);
        Mockito.when(spy.saveBatch(Mockito.anyList())).thenReturn(true);
        System.out.println(spy.inviteUserShareRobot(beInvited, inviterId, inviter, targets));
    }

    @Test
    void inviteUserShareRobotDeviceNotExists() {
        String beInvited = "<EMAIL>";
        String inviterId = "1";
        String inviter = "<EMAIL>";
        String target = "1";
        List<String> targets = Arrays.asList(target);

        Mockito.when(userRemote.getByUsername(Mockito.anyString(), Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(getUser("11", inviter)));

        Mockito.when(deviceInfoService.getByIds(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(Arrays.asList(getDevice("2")));

        DeviceInviteShareServiceImpl spy = Mockito.spy(deviceInviteShareService);
        Mockito.when(spy.saveBatch(Mockito.anyList())).thenReturn(true);
        System.out.println(spy.inviteUserShareRobot(beInvited, inviterId, inviter, targets));
    }

    @Test
    void inviteUserShareRobotNullDevice() {
        String beInvited = "<EMAIL>";
        String inviterId = "1";
        String inviter = "<EMAIL>";
        String target = "1";
        List<String> targets = Arrays.asList(target);

        Mockito.when(userRemote.getByUsername(Mockito.anyString(), Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(getUser("11", inviter)));

        Mockito.when(deviceInfoService.getByIds(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(null);

        DeviceInviteShareServiceImpl spy = Mockito.spy(deviceInviteShareService);
        Mockito.when(spy.saveBatch(Mockito.anyList())).thenReturn(true);
        System.out.println(spy.inviteUserShareRobot(beInvited, inviterId, inviter, targets));
    }


    public DeviceInviteShareEntity getHis(String id, Integer status, Integer type) {
        DeviceInviteShareEntity his = new DeviceInviteShareEntity();
        his.setId(StringUtils.isBlank(id) ? "1" : id);
        his.setStatus(Objects.nonNull(status) ? status : 1);
        his.setType(Objects.isNull(type) ? 0 : type);
        his.setInviteId("1");
        his.setTargetId("1");
        return his;
    }

    @Test
    void replyShared() {
        int type = 0;
        String familyId = "1";
        String familyName = "test的家";
        String beInvitedUserId = "1";
        String inviterId = "1";
        String targetId = "1";
        int delType = 1;

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(getHis(null, null, null));
        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString()))
                .thenReturn(getDevice(null));
        Mockito.when(smartHomeServiceRemote.bindShareDevice(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(true));
        Mockito.when(deviceInviteShareMapper.update(Mockito.any(), Mockito.any())).thenReturn(1);
        System.out.println(deviceInviteShareService.replyShared(type, familyId, familyName, beInvitedUserId, inviterId, targetId, delType));
    }

    @Test
    void replySharedWithReject() {
        int type = 0;
        String familyId = "1";
        String familyName = "test的家";
        String beInvitedUserId = "1";
        String inviterId = "1";
        String targetId = "1";
        int delType = 2;

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(getHis(null, null, null));
        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString()))
                .thenReturn(getDevice(null));
        Mockito.when(smartHomeServiceRemote.bindShareDevice(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(true));
        Mockito.when(deviceInviteShareMapper.update(Mockito.any(), Mockito.any())).thenReturn(1);
        System.out.println(deviceInviteShareService.replyShared(type, familyId, familyName, beInvitedUserId, inviterId, targetId, delType));
    }

    @Test
    void replySharedWithUpdateException() {
        int type = 0;
        String familyId = "1";
        String familyName = "test的家";
        String beInvitedUserId = "1";
        String inviterId = "1";
        String targetId = "1";
        int delType = 1;

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(getHis(null, null, null));
        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString()))
                .thenReturn(getDevice(null));
        Mockito.when(smartHomeServiceRemote.bindShareDevice(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(true));
        Mockito.when(deviceInviteShareMapper.update(Mockito.any(), Mockito.any())).thenThrow(new RuntimeException());
        System.out.println(deviceInviteShareService.replyShared(type, familyId, familyName, beInvitedUserId, inviterId, targetId, delType));
    }

    @Test
    void replySharedWithErrorShare() {
        int type = 0;
        String familyId = "1";
        String familyName = "test的家";
        String beInvitedUserId = "1";
        String inviterId = "1";
        String targetId = "1";
        int delType = 1;

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(null);
        System.out.println(deviceInviteShareService.replyShared(type, familyId, familyName, beInvitedUserId, inviterId, targetId, delType));
    }

    @Test
    void replySharedWithFamily() {
        int type = 1;
        String familyId = "1";
        String familyName = "test的家";
        String beInvitedUserId = "1";
        String inviterId = "1";
        String targetId = "1";
        int delType = 1;

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(getHis(null, null, null));
        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString()))
                .thenReturn(getDevice(null));
        Mockito.when(smartHomeServiceRemote.confirm(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(true));
        Mockito.when(deviceInviteShareMapper.update(Mockito.any(), Mockito.any())).thenReturn(1);
        System.out.println(deviceInviteShareService.replyShared(type, familyId, familyName, beInvitedUserId, inviterId, targetId, delType));
    }

    @Test
    void replySharedWithIllegalType() {
        int type = 3;
        String familyId = "1";
        String familyName = "test的家";
        String beInvitedUserId = "1";
        String inviterId = "1";
        String targetId = "1";
        int delType = 1;

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(getHis(null, null, null));
        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString()))
                .thenReturn(getDevice(null));
        System.out.println(deviceInviteShareService.replyShared(type, familyId, familyName, beInvitedUserId, inviterId, targetId, delType));
    }

    @Test
    void replySharedWithIllegalDealType() {
        int type = 0;
        String familyId = "1";
        String familyName = "test的家";
        String beInvitedUserId = "1";
        String inviterId = "1";
        String targetId = "1";
        int delType = 5;

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(getHis(null, null, null));
        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString()))
                .thenReturn(getDevice(null));
        System.out.println(deviceInviteShareService.replyShared(type, familyId, familyName, beInvitedUserId, inviterId, targetId, delType));
    }

    @Test
    void replySharedWithPushError() {
        int type = 0;
        String familyId = "1";
        String familyName = "test的家";
        String beInvitedUserId = "1";
        String inviterId = "1";
        String targetId = "1";
        int delType = 1;

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(getHis(null, null, null));
        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString()))
                .thenReturn(getDevice(null));
        Mockito.when(smartHomeServiceRemote.bindShareDevice(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(true));
        Mockito.when(deviceInviteShareMapper.update(Mockito.any(), Mockito.any())).thenReturn(1);
        System.out.println(deviceInviteShareService.replyShared(type, familyId, familyName, beInvitedUserId, inviterId, targetId, delType));
    }

    @Test
    void replySharedWithBindRoomException() {
        int type = 0;
        String familyId = "1";
        String familyName = "test的家";
        String beInvitedUserId = "1";
        String inviterId = "1";
        String targetId = "1";
        int delType = 1;

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(getHis(null, null, null));
        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString()))
                .thenReturn(getDevice(null));
        Mockito.when(smartHomeServiceRemote.bindShareDevice(Mockito.any())).thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        System.out.println(deviceInviteShareService.replyShared(type, familyId, familyName, beInvitedUserId, inviterId, targetId, delType));
    }

    @Test
    void replySharedWithBindFamilyError() {
        int type = 1;
        String familyId = "1";
        String familyName = "test的家";
        String beInvitedUserId = "1";
        String inviterId = "1";
        String targetId = "1";
        int delType = 1;

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(getHis(null, null, null));
        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString()))
                .thenReturn(getDevice(null));
        Mockito.when(smartHomeServiceRemote.confirm(Mockito.any())).thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        Mockito.when(deviceInviteShareMapper.update(Mockito.any(), Mockito.any())).thenReturn(1);
        System.out.println(deviceInviteShareService.replyShared(type, familyId, familyName, beInvitedUserId, inviterId, targetId, delType));
    }

    public List<DeviceShareVo> getSharedHis() {
        DeviceShareVo voD = new DeviceShareVo();
        voD.setId("1");
        voD.setUserId("1");
        voD.setUsername("test");
        voD.setProductModeCode("code1");
        voD.setType(0);
        voD.setTargetId("1");
        voD.setPhotoUrl("www.1.com");

        DeviceShareVo voF = new DeviceShareVo();
        voF.setId("2");
        voF.setUserId("2");
        voF.setUsername("test2");
        voF.setProductModeCode("code2");
        voF.setType(1);
        voF.setTargetId("2");
        voD.setPhotoUrl("www.2.com");

        List<DeviceShareVo> list = new ArrayList<>();
        list.add(voD);
        list.add(voF);
        return list;
    }

    public List<TAuthClientUserInfo> getUsers() {
        List<TAuthClientUserInfo> list = new ArrayList<>();
        list.add(getUser("1", "user1"));
        list.add(getUser("2", "user2"));
        return list;
    }

    public List<ProductModeEntity> getProductModes() {
        List<ProductModeEntity> list = new ArrayList<>();
        ProductModeEntity mode1 = new ProductModeEntity();
        mode1.setId("1");
        mode1.setCode("code1");
        mode1.setLabel("label1");
        mode1.setPhotoUrl("www.url1.com");

        ProductModeEntity mode2 = new ProductModeEntity();
        mode2.setId("2");
        mode2.setCode("code2");
        mode2.setLabel("label2");
        mode2.setPhotoUrl("www.url2.com");

        list.add(mode1);
        list.add(mode2);
        return list;
    }

    public List<FamilyInfoEntity> getFamilies() {
        List<FamilyInfoEntity> list = new ArrayList<>();
        FamilyInfoEntity family = new FamilyInfoEntity();
        family.setId("2");
        family.setFamilyName("family");
        list.add(family);
        return list;
    }

    @Test
    void getAllSharedList() {
        GetUserSharedReq req = new GetUserSharedReq();
        req.setTargetId("1");
        req.setInviter(true);

        Page<DeviceShareVo> page = new Page<>();
        page.setRecords(getSharedHis());

        Mockito.when(deviceInviteShareMapper.getListByInviter(Mockito.any(), Mockito.any(), Mockito.anyString()))
                .thenReturn(page);
        Mockito.when(userRemote.getListByIds(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(getUsers()));
        Mockito.when(productServiceRemote.getListByCode(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(getProductModes()));
        Mockito.when(smartHomeServiceRemote.getFamilyInfoByIds(Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(getFamilies()));
        System.out.println(deviceInviteShareService.getAllSharedList(req));
    }

    @Test
    void getAllSharedListWithErrorRemote() {
        GetUserSharedReq req = new GetUserSharedReq();
        req.setTargetId("1");
        req.setInviter(true);

        Page<DeviceShareVo> page = new Page<>();
        page.setRecords(getSharedHis());

        Mockito.when(deviceInviteShareMapper.getListByInviter(Mockito.any(), Mockito.any(), Mockito.anyString()))
                .thenReturn(page);
        Mockito.when(userRemote.getListByIds(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        Mockito.when(productServiceRemote.getListByCode(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        Mockito.when(smartHomeServiceRemote.getFamilyInfoByIds(Mockito.anyList()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        System.out.println(deviceInviteShareService.getAllSharedList(req));
    }

    @Test
    void getAllSharedListWithEmptyRemote() {
        GetUserSharedReq req = new GetUserSharedReq();
        req.setTargetId("1");
        req.setInviter(true);

        Page<DeviceShareVo> page = new Page<>();
        page.setRecords(getSharedHis());

        Mockito.when(deviceInviteShareMapper.getListByInviter(Mockito.any(), Mockito.any(), Mockito.anyString()))
                .thenReturn(page);
        Mockito.when(userRemote.getListByIds(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess());
        Mockito.when(productServiceRemote.getListByCode(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess());
        Mockito.when(smartHomeServiceRemote.getFamilyInfoByIds(Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess());
        System.out.println(deviceInviteShareService.getAllSharedList(req));
    }

    @Test
    void getAllSharedListWithEmptyFamily() {
        GetUserSharedReq req = new GetUserSharedReq();
        req.setTargetId("1");
        req.setInviter(true);

        List<DeviceShareVo> sharedHis = getSharedHis();
        Iterator<DeviceShareVo> iterator = sharedHis.iterator();
        while (iterator.hasNext()) {
            DeviceShareVo next = iterator.next();
            if (next.getType() == InviteShareEnum.FAMILY.getStatus()) {
                iterator.remove();
            }
        }


        Page<DeviceShareVo> page = new Page<>();
        page.setRecords(sharedHis);

        Mockito.when(deviceInviteShareMapper.getListByInviter(Mockito.any(), Mockito.any(), Mockito.anyString()))
                .thenReturn(page);
        Mockito.when(userRemote.getListByIds(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(getUsers()));
        Mockito.when(productServiceRemote.getListByCode(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(getProductModes()));
        Mockito.when(smartHomeServiceRemote.getFamilyInfoByIds(Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(getFamilies()));
        System.out.println(deviceInviteShareService.getAllSharedList(req));
    }

    @Test
    void getAllSharedListWithEmptyFamilyBeInviter() {
        GetUserSharedReq req = new GetUserSharedReq();
        req.setTargetId("1");
        req.setInviter(false);

        List<DeviceShareVo> sharedHis = getSharedHis();
        Iterator<DeviceShareVo> iterator = sharedHis.iterator();
        while (iterator.hasNext()) {
            DeviceShareVo next = iterator.next();
            if (next.getType() == InviteShareEnum.FAMILY.getStatus()) {
                iterator.remove();
            }
        }


        Page<DeviceShareVo> page = new Page<>();
        page.setRecords(sharedHis);

        Mockito.when(deviceInviteShareMapper.getListByInviter(Mockito.any(), Mockito.any(), Mockito.anyString()))
                .thenReturn(page);
        Mockito.when(userRemote.getListByIds(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(getUsers()));
        Mockito.when(productServiceRemote.getListByCode(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(getProductModes()));
        Mockito.when(smartHomeServiceRemote.getFamilyInfoByIds(Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(getFamilies()));
        System.out.println(deviceInviteShareService.getAllSharedList(req));
    }

    @Test
    void cancelShare() {
        String userId = "1";
        String shareId = "1";
        DeviceInviteShareEntity his = getHis("1", 0, null);

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any())).thenReturn(his);
        Mockito.when(deviceInviteShareMapper.deleteById(Mockito.any()))
                .thenReturn(1);
        Mockito.when(smartHomeServiceRemote.userUntieDevice(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(true));
        Mockito.when(smartHomeServiceRemote.revoke(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(true));

        boolean b = deviceInviteShareService.cancelShare(shareId, userId);
        System.out.println(b);
    }

    @Test
    void cancelShareWithNullShare() {
        String userId = "1";
        String shareId = "1";

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any())).thenReturn(null);
        boolean b = deviceInviteShareService.cancelShare(shareId, userId);
        System.out.println(b);
    }

    @Test
    void cancelShareWithFamily() {
        String userId = "1";
        String shareId = "1";
        DeviceInviteShareEntity his = getHis("1", 0, 1);

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any())).thenReturn(his);
        Mockito.when(deviceInviteShareMapper.deleteById(Mockito.any()))
                .thenReturn(1);
        Mockito.when(smartHomeServiceRemote.userUntieDevice(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(true));
        Mockito.when(smartHomeServiceRemote.revoke(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(true));

        boolean b = deviceInviteShareService.cancelShare(shareId, userId);
        System.out.println(b);
    }

    @Test
    void cancelShareWithFamilyFail() {
        String userId = "1";
        String shareId = "1";
        DeviceInviteShareEntity his = getHis("1", 0, 1);

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any())).thenReturn(his);
        Mockito.when(deviceInviteShareMapper.deleteById(Mockito.any()))
                .thenReturn(1);
        Mockito.when(smartHomeServiceRemote.userUntieDevice(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(true));
        Mockito.when(smartHomeServiceRemote.revoke(Mockito.any()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));

        boolean b = deviceInviteShareService.cancelShare(shareId, userId);
        System.out.println(b);
    }

    @Test
    void cancelShareWithUnShareRoom() {
        String userId = "1";
        String shareId = "1";
        DeviceInviteShareEntity his = getHis("1", 0, 0);

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any())).thenReturn(his);
        Mockito.when(deviceInviteShareMapper.deleteById(Mockito.any()))
                .thenReturn(1);
        Mockito.when(smartHomeServiceRemote.userUntieDevice(Mockito.any()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        Mockito.when(smartHomeServiceRemote.revoke(Mockito.any()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));

        boolean b = deviceInviteShareService.cancelShare(shareId, userId);
        System.out.println(b);
    }

    @Test
    void cancelShareWithIllegalType() {
        String userId = "1";
        String shareId = "1";
        DeviceInviteShareEntity his = getHis("1", 0, 2);

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any())).thenReturn(his);
        Mockito.when(deviceInviteShareMapper.deleteById(Mockito.any()))
                .thenReturn(1);
        Mockito.when(smartHomeServiceRemote.userUntieDevice(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(true));
        Mockito.when(smartHomeServiceRemote.revoke(Mockito.any())).thenReturn(ResponseMessage.buildSuccess(true));

        boolean b = deviceInviteShareService.cancelShare(shareId, userId);
        System.out.println(b);
    }

    @Test
    void delShare() {
        DelShareReq req = new DelShareReq();
        req.setDeviceId(Arrays.asList("1"));
        req.setInviter(true);

        Mockito.when(deviceInviteShareMapper.update(Mockito.any(), Mockito.any())).thenReturn(1);
        System.out.println(deviceInviteShareService.delShare(req));
    }

    @Test
    void delShareWithNullParams() {
        System.out.println(deviceInviteShareService.delShare(null));
    }

    @Test
    void delShareWithBlankTenantId() {
        SystemContextUtils.setContextValue(ContextKey.TENANT_ID, "");
        DelShareReq req = new DelShareReq();
        req.setDeviceId(Arrays.asList("1"));
        req.setInviter(true);

        System.out.println(deviceInviteShareService.delShare(req));
    }

    @Test
    void delShareWithEmptyDeviceIds() {
        DelShareReq req = new DelShareReq();
        req.setDeviceId(null);
        req.setInviter(true);

        Mockito.when(deviceInviteShareMapper.update(Mockito.any(), Mockito.any())).thenReturn(1);
        System.out.println(deviceInviteShareService.delShare(req));
    }

    @Test
    void delShareWithNotInviter() {
        DelShareReq req = new DelShareReq();
        req.setDeviceId(Arrays.asList("1"));
        req.setInviter(false);

        Mockito.when(deviceInviteShareMapper.update(Mockito.any(), Mockito.any())).thenReturn(1);
        System.out.println(deviceInviteShareService.delShare(req));
    }

    @Test
    void getShareHis() {
        UserSharedHisReq req = new UserSharedHisReq();
        req.setTargetId("1");
        req.setType(1);

        Page<DeviceShareHisVo> page = new Page<>();
        List<DeviceShareHisVo> list = new ArrayList<>();

        DeviceShareHisVo vo = new DeviceShareHisVo();
        vo.setId("1");
        vo.setInviteId("1");
        vo.setBeInviteId("2");
        vo.setTargetId("1");
        vo.setType(1);
        vo.setStatus(0);
        vo.setRemoved(1);
        vo.setProductModeCode("code1");

        DeviceShareHisVo vo1 = new DeviceShareHisVo();
        vo1.setId("2");
        vo1.setInviteId("2");
        vo1.setBeInviteId("1");
        vo1.setTargetId("2");
        vo1.setType(1);
        vo1.setStatus(0);
        vo1.setRemoved(1);
        vo1.setProductModeCode("code2");

        DeviceShareHisVo vo2 = new DeviceShareHisVo();
        vo2.setId("3");
        vo2.setInviteId("1");
        vo2.setBeInviteId("2");
        vo2.setTargetId("2");
        vo2.setType(0);
        vo2.setStatus(0);
        vo2.setRemoved(1);
        vo2.setProductModeCode("code2");

        DeviceShareHisVo vo3 = new DeviceShareHisVo();
        vo3.setId("4");
        vo3.setInviteId("2");
        vo3.setBeInviteId("1");
        vo3.setTargetId("1");
        vo3.setType(1);
        vo3.setStatus(0);
        vo3.setRemoved(1);
        vo3.setProductModeCode("code1");

        list.add(vo);
        list.add(vo1);
        list.add(vo2);
        list.add(vo3);

        page.setRecords(list);

        List<ProductModeEntity> productModeEntities = new ArrayList<>();
        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");
        productModeEntity.setCode("code1");
        productModeEntity.setLabel("label1");
        productModeEntity.setProductInfoId("1");
        productModeEntity.setAliasPrefix("3i");
        productModeEntity.setProductName("name1");
        productModeEntity.setPhotoUrl("www.product1.com");

        ProductModeEntity productModeEntity1 = new ProductModeEntity();
        productModeEntity.setId("2");
        productModeEntity.setCode("code2");
        productModeEntity.setLabel("label2");
        productModeEntity.setProductInfoId("2");
        productModeEntity.setAliasPrefix("3i");
        productModeEntity.setProductName("name2");
        productModeEntity.setPhotoUrl("www.product2.com");

        productModeEntities.add(productModeEntity);
        productModeEntities.add(productModeEntity1);

        List<TAuthClientUserInfo> users = getUsers();

        List<FamilyInfoEntity> familyInfoEntities = new ArrayList<>();
        FamilyInfoEntity family = new FamilyInfoEntity();
        family.setId("1");
        family.setFamilyName("family1");

        FamilyInfoEntity family1 = new FamilyInfoEntity();
        family1.setId("2");
        family1.setFamilyName("family2");

        familyInfoEntities.add(family);
        familyInfoEntities.add(family1);

        Mockito.when(deviceInviteShareMapper.getDeviceShareHis(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(page);
        Mockito.when(userRemote.getListByIds(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(users));
        Mockito.when(productServiceRemote.getListByCode(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(productModeEntities));
        Mockito.when(smartHomeServiceRemote.getFamilyInfoByIds(Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(familyInfoEntities));
        System.out.println(deviceInviteShareService.getShareHis(req));
    }

    @Test
    void getShareHisWithEmptyPage() {
        UserSharedHisReq req = new UserSharedHisReq();
        req.setTargetId("1");
        req.setType(1);

        Page<DeviceShareHisVo> page = new Page<>();

        Mockito.when(deviceInviteShareMapper.getDeviceShareHis(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(page);
        System.out.println(deviceInviteShareService.getShareHis(req));
    }

    @Test
    void getShareHisWithRemoteFail() {
        UserSharedHisReq req = new UserSharedHisReq();
        req.setTargetId("1");
        req.setType(1);

        Page<DeviceShareHisVo> page = new Page<>();
        List<DeviceShareHisVo> list = new ArrayList<>();

        DeviceShareHisVo vo = new DeviceShareHisVo();
        vo.setId("1");
        vo.setInviteId("1");
        vo.setBeInviteId("2");
        vo.setTargetId("1");
        vo.setType(1);
        vo.setStatus(0);
        vo.setRemoved(1);
        vo.setProductModeCode("code1");

        DeviceShareHisVo vo1 = new DeviceShareHisVo();
        vo1.setId("2");
        vo1.setInviteId("2");
        vo1.setBeInviteId("1");
        vo1.setTargetId("2");
        vo1.setType(1);
        vo1.setStatus(0);
        vo1.setRemoved(1);
        vo1.setProductModeCode("code2");

        list.add(vo);
        list.add(vo1);

        page.setRecords(list);

        List<ProductModeEntity> productModeEntities = new ArrayList<>();
        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");
        productModeEntity.setCode("code1");
        productModeEntity.setLabel("label1");
        productModeEntity.setProductInfoId("1");
        productModeEntity.setAliasPrefix("3i");
        productModeEntity.setProductName("name1");
        productModeEntity.setPhotoUrl("www.product1.com");

        ProductModeEntity productModeEntity1 = new ProductModeEntity();
        productModeEntity.setId("2");
        productModeEntity.setCode("code2");
        productModeEntity.setLabel("label2");
        productModeEntity.setProductInfoId("2");
        productModeEntity.setAliasPrefix("3i");
        productModeEntity.setProductName("name2");
        productModeEntity.setPhotoUrl("www.product2.com");

        productModeEntities.add(productModeEntity);
        productModeEntities.add(productModeEntity1);

        List<TAuthClientUserInfo> users = getUsers();

        List<FamilyInfoEntity> familyInfoEntities = new ArrayList<>();
        FamilyInfoEntity family = new FamilyInfoEntity();
        family.setId("1");
        family.setFamilyName("family1");

        FamilyInfoEntity family1 = new FamilyInfoEntity();
        family1.setId("2");
        family1.setFamilyName("family2");

        familyInfoEntities.add(family);
        familyInfoEntities.add(family1);

        Mockito.when(deviceInviteShareMapper.getDeviceShareHis(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(page);
        Mockito.when(userRemote.getListByIds(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        Mockito.when(productServiceRemote.getListByCode(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        Mockito.when(smartHomeServiceRemote.getFamilyInfoByIds(Mockito.anyList()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        System.out.println(deviceInviteShareService.getShareHis(req));
    }

    @Test
    void getShareHisWithBeInviter() {
        UserSharedHisReq req = new UserSharedHisReq();
        req.setTargetId("1");
        req.setType(1);

        Page<DeviceShareHisVo> page = new Page<>();
        List<DeviceShareHisVo> list = new ArrayList<>();

        DeviceShareHisVo vo = new DeviceShareHisVo();
        vo.setId("1");
        vo.setInviteId("1");
        vo.setBeInviteId("1");
        vo.setTargetId("1");
        vo.setType(1);
        vo.setStatus(0);
        vo.setRemoved(1);
        vo.setProductModeCode("code1");

        DeviceShareHisVo vo1 = new DeviceShareHisVo();
        vo.setId("2");
        vo.setInviteId("1");
        vo.setBeInviteId("2");
        vo.setTargetId("2");
        vo.setType(0);
        vo.setStatus(0);
        vo.setRemoved(1);
        vo.setProductModeCode("code2");
        list.add(vo);
        list.add(vo1);

        page.setRecords(list);

        List<ProductModeEntity> productModeEntities = new ArrayList<>();
        ProductModeEntity productModeEntity = new ProductModeEntity();
        productModeEntity.setId("1");
        productModeEntity.setCode("code1");
        productModeEntity.setLabel("label1");
        productModeEntity.setProductInfoId("1");
        productModeEntity.setAliasPrefix("3i");
        productModeEntity.setProductName("name1");
        productModeEntity.setPhotoUrl("www.product1.com");

        ProductModeEntity productModeEntity1 = new ProductModeEntity();
        productModeEntity.setId("2");
        productModeEntity.setCode("code2");
        productModeEntity.setLabel("label2");
        productModeEntity.setProductInfoId("2");
        productModeEntity.setAliasPrefix("3i");
        productModeEntity.setProductName("name2");
        productModeEntity.setPhotoUrl("www.product2.com");

        productModeEntities.add(productModeEntity);
        productModeEntities.add(productModeEntity1);

        List<TAuthClientUserInfo> users = getUsers();

        List<FamilyInfoEntity> familyInfoEntities = new ArrayList<>();
        FamilyInfoEntity family = new FamilyInfoEntity();
        family.setId("1");
        family.setFamilyName("family1");

        FamilyInfoEntity family1 = new FamilyInfoEntity();
        family1.setId("2");
        family1.setFamilyName("family2");

        familyInfoEntities.add(family);
        familyInfoEntities.add(family1);

        Mockito.when(deviceInviteShareMapper.getDeviceShareHis(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.anyInt()))
                .thenReturn(page);
        Mockito.when(userRemote.getListByIds(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(users));
        Mockito.when(productServiceRemote.getListByCode(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(productModeEntities));
        Mockito.when(smartHomeServiceRemote.getFamilyInfoByIds(Mockito.anyList()))
                .thenReturn(ResponseMessage.buildSuccess(familyInfoEntities));
        System.out.println(deviceInviteShareService.getShareHis(req));
    }

    @Test
    void doShareFamily() {
        ShareFamilyVo vo = new ShareFamilyVo();
        vo.setBeInviteIds(Arrays.asList("1"));
        vo.setTargetId("1");
        vo.setFamilyName("test的家庭");


        Mockito.when(deviceInviteShareMapper.selectCount(Mockito.any())).thenReturn(0);
        DeviceInviteShareServiceImpl spy = Mockito.spy(deviceInviteShareService);
        Mockito.when(spy.saveBatch(Mockito.anyList())).thenReturn(true);
        System.out.println(spy.doShareFamily(vo));
    }

    @Test
    void doShareFamilyWithNullParam() {
        System.out.println(deviceInviteShareService.doShareFamily(null));
    }

    @Test
    void doShareFamilyWithBlankTarget() {
        ShareFamilyVo vo = new ShareFamilyVo();
        vo.setBeInviteIds(Arrays.asList("1"));
        vo.setFamilyName("test的家庭");
        System.out.println(deviceInviteShareService.doShareFamily(vo));
    }

    @Test
    void doShareFamilyWithEmptyBeInvited() {
        ShareFamilyVo vo = new ShareFamilyVo();
        vo.setTargetId("1");
        vo.setFamilyName("test的家庭");
        System.out.println(deviceInviteShareService.doShareFamily(vo));
    }

    @Test
    void doShareFamilyWithAlreadyInvited() {
        ShareFamilyVo vo = new ShareFamilyVo();
        vo.setBeInviteIds(Arrays.asList("1"));
        vo.setTargetId("1");
        vo.setFamilyName("test的家庭");

        Mockito.when(SnowflakeUtil.snowflakeId()).thenReturn("1");
        Mockito.when(deviceInviteShareMapper.selectCount(Mockito.any())).thenReturn(1);
        DeviceInviteShareServiceImpl spy = Mockito.spy(deviceInviteShareService);
        Mockito.when(spy.saveBatch(Mockito.anyList())).thenReturn(true);
        System.out.println(spy.doShareFamily(vo));
    }

    @Test
    void doShareFamilyWithRemoteFail() {
        ShareFamilyVo vo = new ShareFamilyVo();
        vo.setBeInviteIds(Arrays.asList("1"));
        vo.setTargetId("1");
        vo.setFamilyName("test的家庭");

        Mockito.when(SnowflakeUtil.snowflakeId()).thenReturn("1");
        Mockito.when(deviceInviteShareMapper.selectCount(Mockito.any())).thenReturn(0);
        DeviceInviteShareServiceImpl spy = Mockito.spy(deviceInviteShareService);
        Mockito.when(spy.saveBatch(Mockito.anyList())).thenReturn(true);
        System.out.println(spy.doShareFamily(vo));
    }

    @Test
    void changeStatus() {
        DeviceInviteShareEntity his = getHis("1", 0, 1);

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any())).thenReturn(his);
        Mockito.when(smartHomeServiceRemote.confirm(Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(true));
        Mockito.when(deviceInviteShareMapper.update(Mockito.any(), Mockito.any()))
                .thenReturn(1);
        System.out.println(deviceInviteShareService.changeStatus("familyname", "1", "1", 1));
    }

    @Test
    void changeStatusWithNullDevice() {
        DeviceInviteShareEntity his = getHis("1", 0, 0);

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any())).thenReturn(his);
        Mockito.when(smartHomeServiceRemote.confirm(Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(true));
        Mockito.when(deviceInviteShareMapper.update(Mockito.any(), Mockito.any()))
                .thenReturn(1);
        System.out.println(deviceInviteShareService.changeStatus("familyname", "1", "1", 1));
    }

    @Test
    void changeStatusWithEmptyHis() {
        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any())).thenReturn(null);
        System.out.println(deviceInviteShareService.changeStatus("familyname", "1", "1", 1));
    }

    @Test
    void changeStatusWithRejected() {
        DeviceInviteShareEntity his = getHis("1", 1, 1);

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any())).thenReturn(his);
        System.out.println(deviceInviteShareService.changeStatus("familyname", "1", "1", 1));
    }

    @Test
    void changeStatusWithDevice() {
        DeviceInviteShareEntity his = getHis("1", 0, 0);
        DeviceInfoEntity device = getDevice("1");


        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any())).thenReturn(his);
        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.any()))
                .thenReturn(device);
        Mockito.when(smartHomeServiceRemote.bindShareDevice(Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(true));
        Mockito.when(deviceInviteShareMapper.update(Mockito.any(), Mockito.any()))
                .thenReturn(1);
        System.out.println(deviceInviteShareService.changeStatus("familyname", "1", "1", 1));
    }

    @Test
    void changeStatusWithReject() {
        DeviceInviteShareEntity his = getHis("1", 0, 0);

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any())).thenReturn(his);
        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.any()))
                .thenReturn(getDevice("1"));
        Mockito.when(deviceInviteShareMapper.update(Mockito.any(), Mockito.any()))
                .thenReturn(1);
        Mockito.when(smartHomeServiceRemote.confirm(Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(true));
        System.out.println(deviceInviteShareService.changeStatus("familyname", "1", "1", 2));
    }

    @Test
    void changeStatusWithIllegalStatus() {
        DeviceInviteShareEntity his = getHis("1", 0, 0);

        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.any()))
                .thenReturn(getDevice("1"));
        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any())).thenReturn(his);
        System.out.println(deviceInviteShareService.changeStatus("familyname", "1", "1", 3));
    }

    @Test
    void changeStatusWithIllegalType() {
        DeviceInviteShareEntity his = getHis("1", 0, 3);

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any())).thenReturn(his);
        System.out.println(deviceInviteShareService.changeStatus("familyname", "1", "1", 1));
    }

    @Test
    void changeStatusWithUpdateException() {
        DeviceInviteShareEntity his = getHis("1", 0, 1);

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any())).thenReturn(his);
        Mockito.when(smartHomeServiceRemote.confirm(Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(true));
        Mockito.when(deviceInviteShareMapper.update(Mockito.any(), Mockito.any()))
                .thenThrow(new RuntimeException());
        System.out.println(deviceInviteShareService.changeStatus("familyname", "1", "1", 1));
    }

    @Test
    void changeStatusWithPushError() {
        DeviceInviteShareEntity his = getHis("1", 0, 1);

        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any())).thenReturn(his);
        Mockito.when(smartHomeServiceRemote.confirm(Mockito.any()))
                .thenReturn(ResponseMessage.buildSuccess(true));
        Mockito.when(deviceInviteShareMapper.update(Mockito.any(), Mockito.any()))
                .thenReturn(1);
        System.out.println(deviceInviteShareService.changeStatus("familyname", "1", "1", 1));
    }

    @Test
    void changeRecordStatus() {
        ShareRecordChangeStatusReq req = new ShareRecordChangeStatusReq();
        req.setShareId("1");
        req.setRecordId("1");
        req.setStatus(1);
        req.setFamilyId("1");
        req.setFamilyName("a");

        NoticeShareRecordEntity record = new NoticeShareRecordEntity();
        record.setId("1");
        record.setFrom("1");
        record.setMsg("msg");
        record.setName("name");
        record.setPhotoUrl("url");
        record.setTo("1");
        record.setType(1);
        record.setTitle("title");
        record.setTenantId("1");
        record.setTargetId("1");
        record.setStatus(0);
        record.setShareId("1");
        record.setResult(1);

        Mockito.when(commonService.checkShareRecord(Mockito.anyString()))
                .thenReturn(ResponseMessage.buildSuccess(record));
        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(null);
        System.out.println(deviceInviteShareService.changeShareRecordStatus(req));
    }

    @Test
    void changeRecordStatusOrBlankShareId() {
        ShareRecordChangeStatusReq req = new ShareRecordChangeStatusReq();
        req.setRecordId("1");
        req.setStatus(1);
        req.setFamilyId("1");
        req.setFamilyName("a");

        NoticeShareRecordEntity record = new NoticeShareRecordEntity();
        record.setId("1");
        record.setFrom("1");
        record.setMsg("msg");
        record.setName("name");
        record.setPhotoUrl("url");
        record.setTo("1");
        record.setType(1);
        record.setTitle("title");
        record.setTenantId("1");
        record.setTargetId("1");
        record.setStatus(0);
        record.setShareId("1");
        record.setResult(1);

        Mockito.when(commonService.checkShareRecord(Mockito.anyString()))
                .thenReturn(ResponseMessage.buildSuccess(record));
        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(null);
        System.out.println(deviceInviteShareService.changeShareRecordStatus(req));
    }

    @Test
    void changeRecordStatusOfBlankRecordId() {
        ShareRecordChangeStatusReq req = new ShareRecordChangeStatusReq();
        req.setShareId("1");
        req.setStatus(1);
        req.setFamilyId("1");
        req.setFamilyName("a");

        NoticeShareRecordEntity record = new NoticeShareRecordEntity();
        record.setId("1");
        record.setFrom("1");
        record.setMsg("msg");
        record.setName("name");
        record.setPhotoUrl("url");
        record.setTo("1");
        record.setType(1);
        record.setTitle("title");
        record.setTenantId("1");
        record.setTargetId("1");
        record.setStatus(0);
        record.setShareId("1");
        record.setResult(1);

        Mockito.when(commonService.checkShareRecord(Mockito.anyString()))
                .thenReturn(ResponseMessage.buildSuccess(record));
        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(null);
        System.out.println(deviceInviteShareService.changeShareRecordStatus(req));
    }

    @Test
    void changeRecordStatusOfRemoteFail() {
        ShareRecordChangeStatusReq req = new ShareRecordChangeStatusReq();
        req.setShareId("1");
        req.setRecordId("1");
        req.setStatus(1);
        req.setFamilyId("1");
        req.setFamilyName("a");

        NoticeShareRecordEntity record = new NoticeShareRecordEntity();
        record.setId("1");
        record.setFrom("1");
        record.setMsg("msg");
        record.setName("name");
        record.setPhotoUrl("url");
        record.setTo("1");
        record.setType(1);
        record.setTitle("title");
        record.setTenantId("1");
        record.setTargetId("1");
        record.setStatus(0);
        record.setShareId("1");
        record.setResult(1);

        Mockito.when(commonService.checkShareRecord(Mockito.anyString()))
                .thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(null);
        System.out.println(deviceInviteShareService.changeShareRecordStatus(req));
    }

    @Test
    void changeRecordStatusOfRemoteNull() {
        ShareRecordChangeStatusReq req = new ShareRecordChangeStatusReq();
        req.setShareId("1");
        req.setRecordId("1");
        req.setStatus(1);
        req.setFamilyId("1");
        req.setFamilyName("a");

        NoticeShareRecordEntity record = new NoticeShareRecordEntity();
        record.setId("1");
        record.setFrom("1");
        record.setMsg("msg");
        record.setName("name");
        record.setPhotoUrl("url");
        record.setTo("1");
        record.setType(1);
        record.setTitle("title");
        record.setTenantId("1");
        record.setTargetId("1");
        record.setStatus(0);
        record.setShareId("1");
        record.setResult(1);

        Mockito.when(commonService.checkShareRecord(Mockito.anyString()))
                .thenReturn(ResponseMessage.buildSuccess());
        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(null);
        System.out.println(deviceInviteShareService.changeShareRecordStatus(req));
    }

    @Test
    void changeRecordStatusOfIllegalStatus() {
        ShareRecordChangeStatusReq req = new ShareRecordChangeStatusReq();
        req.setShareId("1");
        req.setRecordId("1");
        req.setStatus(5);
        req.setFamilyId("1");
        req.setFamilyName("a");

        NoticeShareRecordEntity record = new NoticeShareRecordEntity();
        record.setId("1");
        record.setFrom("1");
        record.setMsg("msg");
        record.setName("name");
        record.setPhotoUrl("url");
        record.setTo("1");
        record.setType(1);
        record.setTitle("title");
        record.setTenantId("1");
        record.setTargetId("1");
        record.setStatus(0);
        record.setShareId("1");
        record.setResult(1);

        Mockito.when(commonService.checkShareRecord(Mockito.anyString()))
                .thenReturn(ResponseMessage.buildSuccess(record));
        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(null);
        System.out.println(deviceInviteShareService.changeShareRecordStatus(req));
    }

    @Test
    void changeRecordStatusOfBlankFamilyId() {
        ShareRecordChangeStatusReq req = new ShareRecordChangeStatusReq();
        req.setShareId("1");
        req.setRecordId("1");
        req.setStatus(1);
        req.setFamilyName("a");

        NoticeShareRecordEntity record = new NoticeShareRecordEntity();
        record.setId("1");
        record.setFrom("1");
        record.setMsg("msg");
        record.setName("name");
        record.setPhotoUrl("url");
        record.setTo("1");
        record.setType(0);
        record.setTitle("title");
        record.setTenantId("1");
        record.setTargetId("1");
        record.setStatus(0);
        record.setShareId("1");
        record.setResult(1);

        Mockito.when(commonService.checkShareRecord(Mockito.anyString()))
                .thenReturn(ResponseMessage.buildSuccess(record));
        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(null);
        System.out.println(deviceInviteShareService.changeShareRecordStatus(req));
    }

    @Test
    void changeRecordStatusOfBlankFamilyName() {
        ShareRecordChangeStatusReq req = new ShareRecordChangeStatusReq();
        req.setShareId("1");
        req.setRecordId("1");
        req.setStatus(1);
        req.setFamilyId("1");

        NoticeShareRecordEntity record = new NoticeShareRecordEntity();
        record.setId("1");
        record.setFrom("1");
        record.setMsg("msg");
        record.setName("name");
        record.setPhotoUrl("url");
        record.setTo("1");
        record.setType(1);
        record.setTitle("title");
        record.setTenantId("1");
        record.setTargetId("1");
        record.setStatus(0);
        record.setShareId("1");
        record.setResult(1);

        Mockito.when(commonService.checkShareRecord(Mockito.anyString()))
                .thenReturn(ResponseMessage.buildSuccess(record));
        Mockito.when(deviceInviteShareMapper.selectOne(Mockito.any()))
                .thenReturn(null);
        System.out.println(deviceInviteShareService.changeShareRecordStatus(req));
    }

    @Test
    void delByUserId() {
        Mockito.when(deviceInviteShareMapper.deleteByUserId(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(1);
        System.out.println(deviceInviteShareService.delByUserId("1", "1"));
    }
}