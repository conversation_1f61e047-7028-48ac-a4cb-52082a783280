package com.irobotics.aiot.device.service.impl;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.log.config.LogConfig;
import com.irobotics.aiot.bravo.log.util.SpringUtil;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.common.DevicePushTag;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.entity.DeviceShadowEntity;
import com.irobotics.aiot.device.kafka.KafkaProducer;
import com.irobotics.aiot.device.remote.DeviceShadowRemote;
import com.irobotics.aiot.device.service.IDeviceInfoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.Arrays;

class KafkaServiceImplTest {

    @InjectMocks
    private KafkaServiceImpl kafkaService;

    @Mock
    private DeviceShadowRemote deviceShadowRemote;

    @Mock
    private IDeviceInfoService deviceInfoService;

    @Mock
    private KafkaProducer kafkaProducer;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private DeviceInfoCache deviceInfoCache;

    @Mock
    private KafkaTemplate kafkaTemplate;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        Mockito.when(applicationContext.getBean(LogConfig.class)).thenReturn(new LogConfig());
        ApplicationContextAware applicationContextAware = new SpringUtil();
        applicationContextAware.setApplicationContext(applicationContext);
    }

    public DeviceShadowEntity getShadow() {
        DeviceShadowEntity shadow = new DeviceShadowEntity();
        shadow.setProductKey("1");
        shadow.setModelVersion("10");
        return shadow;
    }

    public DeviceInfoEntity getDevice() {
        DeviceInfoEntity device = new DeviceInfoEntity();
        device.setId("1");
        device.setSn("test123");
        device.setMac("test123");
        device.setZone("中国");
        return device;
    }

    @Test
    void genServiceInvokeMsg() {
        String deviceId = "1";
        String tenantId = "0";
        String content = "control";
        DevicePushTag tag = DevicePushTag.CONTROL;

        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString())).thenReturn(getDevice());
        Mockito.when(deviceShadowRemote.findBySN(Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(getShadow()));
        System.out.println(kafkaService.genServiceInvokeMsg(deviceId, tenantId, content, tag));
    }

    @Test
    void genServiceInvokeMsgWithDeviceNotExists() {
        String deviceId = "1";
        String tenantId = "0";
        String content = "control";
        DevicePushTag tag = DevicePushTag.CONTROL;

        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString())).thenReturn(null);
        System.out.println(kafkaService.genServiceInvokeMsg(deviceId, tenantId, content, tag));
    }

    @Test
    void genServiceInvokeMsgWithErrorGetShadow() {
        String deviceId = "1";
        String tenantId = "0";
        String content = "control";
        DevicePushTag tag = DevicePushTag.CONTROL;

        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString())).thenReturn(getDevice());
        Mockito.when(deviceShadowRemote.findBySN(Mockito.anyString())).thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        System.out.println(kafkaService.genServiceInvokeMsg(deviceId, tenantId, content, tag));
    }

    @Test
    void senServiceInvoke() {
        String deviceId = "1";
        String tenantId = "0";
        String content = "control";
        DevicePushTag tag = DevicePushTag.CONTROL;

        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString())).thenReturn(getDevice());
        Mockito.when(deviceShadowRemote.findBySN(Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(getShadow()));
        kafkaService.senServiceInvoke(deviceId, tenantId, content, tag, "topic");
    }

    @Test
    void sendBatchServiceInvoke() {
        String deviceId = "1";
        String tenantId = "0";
        String content = "control";
        DevicePushTag tag = DevicePushTag.CONTROL;

        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString())).thenReturn(getDevice());
        Mockito.when(deviceShadowRemote.findBySN(Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(getShadow()));
        kafkaService.sendBatchServiceInvoke(Arrays.asList(deviceId), tenantId, content, tag, "topic");
    }

    @Test
    void sendBatchServiceInvokeWithEmptyDeviceIds() {
        String tenantId = "0";
        String content = "control";
        DevicePushTag tag = DevicePushTag.CONTROL;

        Mockito.when(deviceInfoCache.getCacheById(Mockito.any(), Mockito.anyString())).thenReturn(getDevice());
        Mockito.when(deviceShadowRemote.findBySN(Mockito.anyString())).thenReturn(ResponseMessage.buildSuccess(getShadow()));
        kafkaService.sendBatchServiceInvoke(null, tenantId, content, tag, "topic");
    }

    @Test
    void deviceRegister() {
        kafkaService.deviceRegister(getDevice());
    }
}