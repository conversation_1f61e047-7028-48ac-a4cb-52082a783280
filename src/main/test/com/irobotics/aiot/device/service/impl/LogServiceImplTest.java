package com.irobotics.aiot.device.service.impl;

import com.irobotics.aiot.bravo.basic.base.ResponseMessage;
import com.irobotics.aiot.bravo.basic.constant.ResponseCode;
import com.irobotics.aiot.bravo.log.config.LogConfig;
import com.irobotics.aiot.bravo.log.util.SpringUtil;
import com.irobotics.aiot.device.common.RobotVersion;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.remote.LogServiceRemote;
import com.irobotics.aiot.device.vo.LoginKeyReq;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.ArrayList;
import java.util.List;

class LogServiceImplTest {

    @InjectMocks
    private LogServiceImpl logService;

    @Mock
    private LogServiceRemote logServiceRemote;

    @Mock
    private ApplicationContext applicationContext;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        Mockito.when(applicationContext.getBean(LogConfig.class)).thenReturn(new LogConfig());
        ApplicationContextAware applicationContextAware = new SpringUtil();
        applicationContextAware.setApplicationContext(applicationContext);
    }

    @Test
    void loginFail() {
        LoginKeyReq req = new LoginKeyReq();
        req.setSn("test123456");
        req.setMac("test123456");
        req.setTenantId("0");
        req.setKeyt("testkey");

        RobotVersion version = new RobotVersion();
        version.setCtrlVersion("10");
        version.setVersionName("test");
        List<RobotVersion> list = new ArrayList<>();

        req.setPackageVersions(list);
        Mockito.when(logServiceRemote.loginLog(Mockito.any())).thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        logService.loginFail(req, null);
    }

    @Test
    void logout() {
        DeviceInfoEntity device = new DeviceInfoEntity();
        device.setSn("test123456");
        device.setMac("test123456");
        device.setProductModeCode("code");

        Mockito.when(logServiceRemote.logoutLog(Mockito.any())).thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));

    }

    @Test
    void loginLog() {
        String productModeCode = "code";
        String sn = "test123456";
        String mac = "test123456";
        String tenantId = "0";
        String key = "key";
        String deviceId = "1";
        String ip = "127.0.0.1";
        String country = "中国";
        String version = "10";

        Mockito.when(logServiceRemote.loginLog(Mockito.any())).thenReturn(ResponseMessage.buildFail(ResponseCode.INNER_SERVER_ERROR));
        logService.loginLog(productModeCode, sn, mac, tenantId, key, deviceId, ip, country, version);

    }
}