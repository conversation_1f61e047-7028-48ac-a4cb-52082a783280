package com.irobotics.aiot.device.service.impl;

import com.irobotics.aiot.bravo.basic.constant.ContextKey;
import com.irobotics.aiot.bravo.context.util.SystemContextUtils;
import com.irobotics.aiot.bravo.log.config.LogConfig;
import com.irobotics.aiot.bravo.log.util.SpringUtil;
import com.irobotics.aiot.device.cache.DeviceInfoCache;
import com.irobotics.aiot.device.entity.DeviceBindEntity;
import com.irobotics.aiot.device.entity.DeviceInfoEntity;
import com.irobotics.aiot.device.mapper.DeviceBindMapper;
import com.irobotics.aiot.device.vo.open.BindDeviceVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.StringUtils;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.Arrays;

class OpenDeviceBindServiceImplTest {

    @InjectMocks
    private OpenDeviceBindServiceImpl openDeviceBindService;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private DeviceInfoCache deviceInfoCache;

    @Mock
    private DeviceBindMapper deviceBindMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        Mockito.when(applicationContext.getBean(LogConfig.class)).thenReturn(new LogConfig());
        ApplicationContextAware applicationContextAware = new SpringUtil();
        applicationContextAware.setApplicationContext(applicationContext);
        SystemContextUtils.setContextValue(ContextKey.ID, "1");
        SystemContextUtils.setContextValue(ContextKey.TENANT_ID, "0");
    }

    public DeviceInfoEntity getDevice(String deviceId) {
        DeviceInfoEntity deviceInfo = new DeviceInfoEntity();
        if (StringUtils.isBlank(deviceId)) {
            deviceInfo.setId("1");
        }
        deviceInfo.setSn("sn");
        deviceInfo.setMac("mac");

        return deviceInfo;
    }

    @Test
    void bindDevice() {
        BindDeviceVo vo = new BindDeviceVo();
        vo.setSn("sn");

        DeviceBindEntity bindEntity = new DeviceBindEntity();
        bindEntity.setId("1");
        bindEntity.setSn("sn");
        bindEntity.setUserId("1");
        bindEntity.setOwner("1");

        Mockito.when(deviceInfoCache.getCacheBySn(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(getDevice("1"));
        Mockito.when(deviceBindMapper.selectList(Mockito.any()))
                .thenReturn(Arrays.asList(bindEntity));
        Mockito.when(deviceBindMapper.delete(Mockito.any()))
                .thenReturn(1);
        Mockito.when(deviceBindMapper.insert(Mockito.any())).thenReturn(1);
        System.out.println(openDeviceBindService.bindDevice(vo));
    }

    @Test
    void bindDeviceOfNullDevice() {
        BindDeviceVo vo = new BindDeviceVo();
        vo.setSn("sn");

        DeviceBindEntity bindEntity = new DeviceBindEntity();
        bindEntity.setId("1");
        bindEntity.setSn("sn");
        bindEntity.setUserId("1");
        bindEntity.setOwner("1");

        Mockito.when(deviceInfoCache.getCacheBySn(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(null);
        Mockito.when(deviceBindMapper.selectList(Mockito.any()))
                .thenReturn(Arrays.asList(bindEntity));
        Mockito.when(deviceBindMapper.delete(Mockito.any()))
                .thenReturn(1);
        Mockito.when(deviceBindMapper.insert(Mockito.any())).thenReturn(1);
        System.out.println(openDeviceBindService.bindDevice(vo));
    }

    @Test
    void bindDeviceOfBlankSn() {
        BindDeviceVo vo = new BindDeviceVo();

        DeviceBindEntity bindEntity = new DeviceBindEntity();
        bindEntity.setId("1");
        bindEntity.setSn("sn");
        bindEntity.setUserId("1");
        bindEntity.setOwner("1");

        Mockito.when(deviceInfoCache.getCacheBySn(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(getDevice("1"));
        Mockito.when(deviceBindMapper.selectList(Mockito.any()))
                .thenReturn(Arrays.asList(bindEntity));
        Mockito.when(deviceBindMapper.delete(Mockito.any()))
                .thenReturn(1);
        Mockito.when(deviceBindMapper.insert(Mockito.any())).thenReturn(1);
        System.out.println(openDeviceBindService.bindDevice(vo));
    }

    @Test
    void bindDeviceOfEmptyBind() {
        BindDeviceVo vo = new BindDeviceVo();
        vo.setSn("sn");

        DeviceBindEntity bindEntity = new DeviceBindEntity();
        bindEntity.setId("1");
        bindEntity.setSn("sn");
        bindEntity.setUserId("1");
        bindEntity.setOwner("1");

        Mockito.when(deviceInfoCache.getCacheBySn(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(getDevice("1"));
        Mockito.when(deviceBindMapper.selectList(Mockito.any()))
                .thenReturn(null);
        Mockito.when(deviceBindMapper.delete(Mockito.any()))
                .thenReturn(1);
        Mockito.when(deviceBindMapper.insert(Mockito.any())).thenReturn(1);
        System.out.println(openDeviceBindService.bindDevice(vo));
    }

    @Test
    void bindDeviceNewBind() {
        BindDeviceVo vo = new BindDeviceVo();
        vo.setSn("sn");

        DeviceBindEntity bindEntity = new DeviceBindEntity();
        bindEntity.setId("2");
        bindEntity.setSn("sn");
        bindEntity.setUserId("2");
        bindEntity.setOwner("2");

        Mockito.when(deviceInfoCache.getCacheBySn(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(getDevice("1"));
        Mockito.when(deviceBindMapper.selectList(Mockito.any()))
                .thenReturn(Arrays.asList(bindEntity));
        Mockito.when(deviceBindMapper.delete(Mockito.any()))
                .thenReturn(1);
        Mockito.when(deviceBindMapper.insert(Mockito.any())).thenReturn(1);
        System.out.println(openDeviceBindService.bindDevice(vo));
    }

    @Test
    void unBindDevice() {
        BindDeviceVo vo = new BindDeviceVo();
        vo.setSn("sn");

        Mockito.when(deviceInfoCache.getCacheBySn(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(getDevice("1"));
        Mockito.when(deviceBindMapper.delete(Mockito.any()))
                .thenReturn(1);
        System.out.println(openDeviceBindService.unBindDevice(vo));
    }

    @Test
    void unBindDeviceOfNullDevice() {
        BindDeviceVo vo = new BindDeviceVo();
        vo.setSn("sn");

        Mockito.when(deviceInfoCache.getCacheBySn(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(null);
        Mockito.when(deviceBindMapper.delete(Mockito.any()))
                .thenReturn(1);
        System.out.println(openDeviceBindService.unBindDevice(vo));
    }

    @Test
    void unBindDeviceOfBlankSn() {
        BindDeviceVo vo = new BindDeviceVo();

        Mockito.when(deviceInfoCache.getCacheBySn(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(getDevice("1"));
        Mockito.when(deviceBindMapper.delete(Mockito.any()))
                .thenReturn(1);
        System.out.println(openDeviceBindService.unBindDevice(vo));
    }
}