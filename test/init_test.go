package test

import (
	"fmt"
	"os"
	"piceacorp.com/device-service/pkg/common/config"
	"piceacorp.com/device-service/pkg/logger"
	"piceacorp.com/device-service/pkg/nacos"
)

var ServerConfig *config.Config

func init() {
	cfg, err := config.LoadConfig("../configs", false)
	if err != nil {
		fmt.Printf("load config failed: %v", err)
		os.Exit(1)
	}
	//release := cfg.Server.Mode == gin.ReleaseMode

	// 2. 初始化日志
	logger.Init(cfg.Server.Name, cfg.Log)
	defer logger.Sync()

	// 4. 初始化Nacos配置
	nacosConfig := &nacos.Config{
		ServerAddr:  cfg.Nacos.Host,
		ServerPort:  cfg.Nacos.Port,
		Namespace:   cfg.Nacos.Namespace,
		DataID:      cfg.Nacos.DataID,
		Group:       cfg.Nacos.Group,
		LogMode:     cfg.Nacos.LogMode,
		ServiceName: cfg.Server.Name,
		ServicePort: cfg.Server.Port,
	}

	// 定义依赖的服务列表
	// 通过扫描包来加载
	dependentServices := []string{
		"user-service",
		"auth-service",
		// 添加其他依赖的服务
	}

	// 启动Nacos
	_, _ = nacos.Bootstrap(nacosConfig, dependentServices)
	ServerConfig = cfg
}
