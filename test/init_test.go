package test

import (
	"piceacorp.com/device-service/pkg/common/config"
	"piceacorp.com/device-service/pkg/logger"
	"piceacorp.com/device-service/pkg/nacos"
)

func init() {
	// 定义依赖的服务列表
	// 通过扫描包来加载
	//dependentServices := []string{
	//	"user-service",
	//	"auth-service",
	//	// 添加其他依赖的服务
	//}
	config.SC.Server.Name = "device-service-go"
	config.SC.Server.Port = 8304
	// 启动Nacos
	_, _, _, _ = nacos.Bootstrap()
	//release := cfg.Server.Mode == gin.ReleaseMode
	// 2. 初始化日志
	logger.Init()
	defer logger.Sync()

}
