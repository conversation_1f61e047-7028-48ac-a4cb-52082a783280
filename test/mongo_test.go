package test

import (
	"context"
	"go.mongodb.org/mongo-driver/v2/bson"
	"piceacorp.com/device-service/pkg/data/mongo"
	"testing"
)

func TestMongo(t *testing.T) {
	mongo.InitMongoDB()
	var result = &map[string]interface{}{}
	err := mongo.DB.Collection("device_log").FindOne(context.Background(), bson.M{"sn": "TESTSN010000000003230"}).Decode(result)
	if err != nil {
		t.Error(err)
	}
	t.Log(result)
}
