package test

import (
	"github.com/gin-gonic/gin"
	"piceacorp.com/device-service/internal/dao/repository"
	"piceacorp.com/device-service/pkg/common"
	"piceacorp.com/device-service/pkg/data/mysql"
	"testing"
)

func TestInit(t *testing.T) {
	//cfg := config.DatabaseConfig{
	//	//*******************************************************************************
	//	Host:            "************",
	//	Port:            3506,
	//	Username:        "aiot",
	//	Password:        "Um6oVAZEZKpjj6LR",
	//	DatabaseName:    "d_device",
	//	MaxIdleConns:    2,
	//	MaxOpenConns:    10,
	//	ConnMaxLifetime: 500,
	//	LogLevel:        "debug",
	//	SlowThreshold:   100,
	//}
	err := mysql.InitMySQL(&ServerConfig.Database)
	if err != nil {
		t.Error(err)
	}

	c := &gin.Context{}
	c.Set(common.TID, "0")

	ids := repository.DeviceRepository
	//ids.Dbh.DB.Set().
	obj, err := ids.Get(c, "1483762578630373376")
	t.Logf("%+v", obj)

	obj, err = ids.FindBySN(c, "TESTsn010000000001xxx")
	t.Logf("%+v", obj)
	//, "1483407309194395648"
	objs, err := ids.List(c, "id in ?", []string{"1483407309211172866"})
	t.Logf("%+v", objs)
	//logger.Debugw("aaa", "k1", "v1", "k2", "v2")
	//t.Logf("%+v", sn)
}
