package test

import (
	"context"
	"os"
	"os/signal"
	"piceacorp.com/device-service/pkg/common/config"
	"piceacorp.com/device-service/pkg/nacos"
	"syscall"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"piceacorp.com/device-service/pkg/logger"
)

// nacos 示例用法
func TestNacosUsage(t *testing.T) {
	var (
		serviceName        = "device-service-go"
		port        uint64 = 8304

		config = &nacos.Config{
			ServerAddr:  "lt",
			ServerPort:  8848,
			Namespace:   "",
			Group:       "DEFAULT_GROUP",
			DataID:      serviceName,
			ServiceName: serviceName,
			ServicePort: port,
		}
	)

	nacos.Bootstrap(config, []string{"device-service", "user-center", "sso-service"})
	initRedis(t)
	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	res, resErr := nacos.CallService[string](context.Background(), "sso-service", "/auth/modifyCountry", "POST", map[string]interface{}{"country": "15", "ssoId": "123456"},
		func() (failRes string) {
			var res = "===fc===="
			return res
		})
	t.Log(res)
	t.Error(resErr)

	// 注销服务实例
	//success, err := nacos.deregisterService(serviceName, port)
	//if err != nil {
	//	logger.Error("注销服务实例失败", err)
	//} else if success {
	//	logger.Info("服务实例注销成功")
	//}
}

func initRedis(t *testing.T) {
	logger.Info("start init redis client")
	redisConfig := &config.RedisConfig{}

	// var hook mapstructure.DecodeHookFuncType = func(f reflect.Type, t reflect.Type, data interface{}) (interface{}, error) {
	// 	if datastr, ok := data.(string); ok {
	// 		if strings.HasPrefix(datastr, "${") && strings.HasSuffix(datastr, "}") {
	// 			envVarName := datastr[2 : len(datastr)-1]
	// 			// 检查是否有默认值（使用 :- 分隔）
	// 			var defaultValue string
	// 			if idx := strings.Index(envVarName, ":"); idx >= 0 {
	// 				defaultValue = envVarName[idx+2:]
	// 				envVarName = envVarName[:idx]
	// 			}
	// 			// 获取环境变量值
	// 			envValue := os.Getenv(envVarName)
	// 			// 如果环境变量存在，使用其值
	// 			if envValue != "" {
	// 				return envValue, nil
	// 			}
	// 			// 如果环境变量不存在但有默认值，使用默认值
	// 			if defaultValue != "" {
	// 				return defaultValue, nil
	// 			}
	// 		}
	// 	}
	// 	return data, nil
	// }

	nacos.UnmarshalConfigKey("redis", redisConfig)

	rdb := redis.NewClusterClient(&redis.ClusterOptions{
		Addrs:        redisConfig.Addresses,
		Password:     redisConfig.Password,
		MaxRetries:   redisConfig.MaxRetries,
		PoolSize:     redisConfig.PoolSize,
		MinIdleConns: 2,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolTimeout:  4 * time.Second,
	})
	res, err := rdb.ClusterNodes(context.Background()).Result()
	if err != nil {
		logger.Error("redis init fail", err)
		// return nil, err
	}
	logger.Info("redis cluster nodes result: ", res)
	// return func() { rdb.Close() }, err
}
