package test

import (
	"context"
	"encoding/json"
	"piceacorp.com/device-service/pkg/data/cache"
	"testing"
)

type res struct {
	Code   uint8  `json:"code"`
	Result region `json:"result"`
}

type region struct {
	ServerConfigurations map[string]map[string]string `json:"serverConfigurations"`
	Countries            []country                    `json:"countries"`
}
type country struct {
	Pinyin          string `json:"pinyin"`
	ServerKey       string `json:"serverKey"`
	Name            string `json:"name"`
	NameFirstLetter string `json:"nameFirstLetter"`
	Id              string `json:"id"`
	Code            string `json:"code"`
	ServerRegionTag string `json:"serverRegionTag"`
}

func TestRedis(t *testing.T) {
	cache.InitRedis()
	rcmd := cache.Rdb.Get(context.Background(), "app-region-service-domain::1-1560565274212143104-en")
	if rcmd != nil {
		result, err := rcmd.Bytes()
		if err != nil {
			t.Error(err)
		}
		var region interface{}
		err = json.Unmarshal(result, &region)
		if err != nil {
			t.Error(err)
		}
		//t.Log(string(result))
		t.Log(region)
	}

}
